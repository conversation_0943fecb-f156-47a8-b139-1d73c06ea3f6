{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "chyrpe",
            "request": "launch",
            "type": "dart",
            "program": "lib/main.dart",
            "args": [
                "--flavor",
                "dev"
            ],
            "toolArgs": [],
            "flutterMode": "debug"
        },
        {
            "name": "chyrpe (profile mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "chyrpe (release mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "hobbies",
            "cwd": "packages/hobbies",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "hobbies (profile mode)",
            "cwd": "packages/hobbies",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "hobbies (release mode)",
            "cwd": "packages/hobbies",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "likes",
            "cwd": "packages/likes",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "likes (profile mode)",
            "cwd": "packages/likes",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "likes (release mode)",
            "cwd": "packages/likes",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "verification",
            "cwd": "packages/verification",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "verification (profile mode)",
            "cwd": "packages/verification",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "verification (release mode)",
            "cwd": "packages/verification",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "example",
            "cwd": "packages/hobbies/example",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "example (profile mode)",
            "cwd": "packages/hobbies/example",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "example (release mode)",
            "cwd": "packages/hobbies/example",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "example",
            "cwd": "packages/verification/example",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "example (profile mode)",
            "cwd": "packages/verification/example",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "example (release mode)",
            "cwd": "packages/verification/example",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        }
    ]
}