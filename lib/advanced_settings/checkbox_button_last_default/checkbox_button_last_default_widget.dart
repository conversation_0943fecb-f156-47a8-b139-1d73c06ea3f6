import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'checkbox_button_last_default_model.dart';
export 'checkbox_button_last_default_model.dart';

class CheckboxButtonLastDefaultWidget extends StatefulWidget {
  const CheckboxButtonLastDefaultWidget({
    super.key,
    this.initialSelectedOptions,
    required this.options,
    this.maxOptions = 10000,
    this.autoSet = true,
  });

  final List<String>? initialSelectedOptions;
  final List<String>? options;
  final int maxOptions;
  final bool autoSet;

  @override
  State<CheckboxButtonLastDefaultWidget> createState() =>
      _CheckboxButtonLastDefaultWidgetState();
}

class _CheckboxButtonLastDefaultWidgetState
    extends State<CheckboxButtonLastDefaultWidget> {
  late CheckboxButtonLastDefaultModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => CheckboxButtonLastDefaultModel());

    // On component load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      _model.selectedOptions =
          widget.initialSelectedOptions!.toList().cast<String>();
      if (_model.selectedOptions.isEmpty) {
        _model.addToSelectedOptions(widget.options!.lastOrNull ?? '');
      }
      safeSetState(() {});
    });
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Builder(
      builder: (context) {
        final option = widget.options!.toList();

        return ListView.builder(
          padding: EdgeInsets.zero,
          primary: false,
          shrinkWrap: true,
          scrollDirection: Axis.vertical,
          itemCount: option.length,
          itemBuilder: (context, optionIndex) {
            final optionItem = option[optionIndex];
            return InkWell(
              splashColor: Colors.transparent,
              focusColor: Colors.transparent,
              hoverColor: Colors.transparent,
              highlightColor: Colors.transparent,
              onTap: () async {
                if (_model.selectedOptions.contains(optionItem)) {
                  if (_model.selectedOptions.length == 1) {
                    _model.selectedOptions = [];
                    safeSetState(() {});
                    _model.addToSelectedOptions(widget.options!.lastOrNull!);
                    safeSetState(() {});
                  } else {
                    _model.removeFromSelectedOptions(optionItem);
                    _model.updatePage(() {});
                  }
                } else {
                  if (optionIndex == ((widget.options?.length ?? 1) - 1)) {
                    _model.selectedOptions = [optionItem];
                    safeSetState(() {});
                    return;
                  } else if (_model.selectedOptions.length < widget.maxOptions) {
                    if ((_model.selectedOptions.length + 1 ==
                        (widget.options!.length - 1)) && !(_model.selectedOptions.contains(widget.options!.lastOrNull!))) {
                          if (!widget.autoSet) {
                            _model.addToSelectedOptions(optionItem); 
                            safeSetState(() {});
                            return;}
                      _model.selectedOptions = [widget.options!.lastOrNull!];
                      safeSetState(() {});
                    } else {
                      _model.removeFromSelectedOptions(widget.options!.lastOrNull!);
                      _model.addToSelectedOptions(optionItem);
                      _model.updatePage(() {});
                    }
                  }
                }
              },
              child: Container(
                decoration: const BoxDecoration(),
                child: Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(0.0, 17.0, 0.0, 19.0),
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        optionItem,
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'BT Beau Sans',
                              letterSpacing: 0.0,
                              useGoogleFonts: false,
                            ),
                      ),
                      Builder(
                        builder: (context) {
                          if (_model.selectedOptions.contains(optionItem)) {
                            return Container(
                              width: 21.0,
                              height: 21.0,
                              decoration: BoxDecoration(
                                color: const Color(0xFFBAABF1),
                                borderRadius: BorderRadius.circular(2.0),
                                shape: BoxShape.rectangle,
                                border: Border.all(
                                  color: const Color(0xFFBAABF1),
                                ),
                              ),
                              child: Align(
                                alignment: const AlignmentDirectional(0.0, 0.0),
                                child: FaIcon(
                                  FontAwesomeIcons.check,
                                  color: FlutterFlowTheme.of(context)
                                      .primaryBackground,
                                  size: 18.0,
                                ),
                              ),
                            );
                          } else {
                            return Container(
                              width: 21.0,
                              height: 21.0,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(2.0),
                                shape: BoxShape.rectangle,
                                border: Border.all(
                                  color: FlutterFlowTheme.of(context)
                                      .secondaryText,
                                ),
                              ),
                            );
                          }
                        },
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}
