import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '/advanced_settings/age_req_screen_search_pref/age_req_screen_search_pref_widget.dart';
import '/advanced_settings/checkbox_screen_search_pref/checkbox_screen_search_pref_widget.dart';
import '/advanced_settings/distance_req_screen_search_pref/distance_req_screen_search_pref_widget.dart';
import '/advanced_settings/height_req_screen_search_pref/height_req_screen_search_pref_widget.dart';
import '/advanced_settings/matching_mode_search_pref/matching_mode_search_pref_widget.dart';
import '/advanced_settings/radio_screen_search_pref/radio_screen_search_pref_widget.dart';
import '../role_req_screen_search_pref/role_req_screen_search_pref_widget.dart';
import '/backend/schema/enums/enums.dart';
import '/backend/schema/structs/index.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'generic_preferences_detail_page_model.dart';
export 'generic_preferences_detail_page_model.dart';

class GenericPreferencesDetailPageWidget extends StatefulWidget {
  const GenericPreferencesDetailPageWidget({
    super.key,
    required this.name,
    required this.editorConfig,
    required this.kind,
    String? title,
    required this.searchPrefScreen,
    required this.searchPref,
  }) : title = title ?? '';

  final String? name;
  final SearchPrefSelectorScreenStruct? editorConfig;
  final SignupScreenType? kind;
  final String title;
  final SearchPrefScreenStruct? searchPrefScreen;
  final SearchPrefStruct? searchPref;

  @override
  State<GenericPreferencesDetailPageWidget> createState() =>
      _GenericPreferencesDetailPageWidgetState();
}

class _GenericPreferencesDetailPageWidgetState
    extends State<GenericPreferencesDetailPageWidget> {
  late GenericPreferencesDetailPageModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => GenericPreferencesDetailPageModel());
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        appBar: AppBar(
          backgroundColor: Colors.white,
          iconTheme:
              IconThemeData(color: FlutterFlowTheme.of(context).primaryText),
          leading: IconButton(
                  icon: const Icon(FontAwesomeIcons.chevronLeft),
                  iconSize: 18,
                  onPressed: () {
                    context.safePop();
                  },
                ),
          title: Text(
            widget.title,
            style: FlutterFlowTheme.of(context).headlineMedium.override(
                  fontFamily: 'BT Beau Sans',
                  color: FlutterFlowTheme.of(context).primaryText,
                  fontSize: 18.0,
                  letterSpacing: 0.0,
                  fontWeight: FontWeight.w500,
                  useGoogleFonts: false,
                ),
          ),
          actions: const [],
          centerTitle: true,
          elevation: 0.0,
        ),
        body: SafeArea(
          top: true,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              Flexible(
                child: Builder(
                  builder: (context) {
             
               
             
                    
                    if (widget.kind == SignupScreenType.radio) {
                      return Padding(
                        padding: const EdgeInsetsDirectional.fromSTEB(
                            30.0, 0.0, 30.0, 40.0),
                        child: wrapWithModel(
                          model: _model.radioScreenSearchPrefModel,
                          updateCallback: () => safeSetState(() {}),
                          updateOnChange: true,
                          child: RadioScreenSearchPrefWidget(
                            name: widget.name!,
                            config: widget.editorConfig!,
                            searchPrefConfig: widget.searchPrefScreen!,
                            searchPref: widget.searchPref!,
                            nextCallback: () async {
                              context.safePop();
                            },
                          ),
                        ),
                      );
                    } else if (widget.kind == SignupScreenType.checkboxes) {
                      return Padding(
                        padding: const EdgeInsetsDirectional.fromSTEB(
                            30.0, 0.0, 30.0, 40.0),
                        child: wrapWithModel(
                          model: _model.checkboxScreenSearchPrefModel,
                          updateCallback: () => safeSetState(() {}),
                          child: CheckboxScreenSearchPrefWidget(
                            name: widget.name!,
                            config: widget.editorConfig!,
                            searchPrefConfig: widget.searchPrefScreen!,
                            searchPref: widget.searchPref!,
                            nextCallback: () async {
                              context.safePop();
                            },
                          ),
                        ),
                      );
                    } else if (widget.kind == SignupScreenType.special) {
                      return Container(
                        width: MediaQuery.sizeOf(context).width * 1.0,
                        height: MediaQuery.sizeOf(context).height * 1.0,
                        decoration: BoxDecoration(
                          color: FlutterFlowTheme.of(context).primaryBackground,
                        ),
                        child: Builder(
                          builder: (context) {
                            if (widget.name == 'distance') {
                              return wrapWithModel(
                                model: _model.distanceReqScreenSearchPrefModel,
                                updateCallback: () => safeSetState(() {}),
                                updateOnChange: true,
                                child: Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                            30.0, 0.0, 30.0, 40.0),
                                  child: DistanceReqScreenSearchPrefWidget(
                                    config: widget.editorConfig!,
                                    name: widget.name!,
                                    searchPrefConfig: widget.searchPrefScreen!,
                                    searchPref: widget.searchPref!,
                                    nextCallback: (updatedSearchPref) async {
                                      context.safePop();
                                    },
                                  ),
                                ),
                              );
                            } else if (widget.name == 'age') {
                              return wrapWithModel(
                                model: _model.ageReqScreenSearchPrefModel,
                                updateCallback: () => safeSetState(() {}),
                                updateOnChange: true,
                                child: Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                            30.0, 0.0, 30.0, 40.0),
                                  child: AgeReqScreenSearchPrefWidget(
                                    name: widget.name!,
                                    config: widget.editorConfig!,
                                    searchPrefConfig: widget.searchPrefScreen!,
                                    searchPref: widget.searchPref!,
                                    nextCallback: (updatedSearchPref) async {
                                      context.safePop();
                                    },
                                  ),
                                ),
                              );
                            } else if (widget.name == 'height') {
                              return wrapWithModel(
                                model: _model.heightReqScreenSearchPrefModel,
                                updateCallback: () => safeSetState(() {}),
                                updateOnChange: true,
                                child: Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                            30.0, 0.0, 30.0, 40.0),
                                  child: HeightReqScreenSearchPrefWidget(
                                    name: widget.name!,
                                    config: widget.editorConfig!,
                                    searchPrefConfig: widget.searchPrefScreen!,
                                    searchPref: widget.searchPref!,
                                    nextCallback: (updatedSearchPref) async {
                                      context.safePop();
                                    },
                                  ),
                                ),
                              );
                            } else if (widget.name == 'role_matching') {
                              return wrapWithModel(
                                model: _model.heightReqScreenSearchPrefModel,
                                updateCallback: () => safeSetState(() {}),
                                updateOnChange: true,
                                child: Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                            30.0, 0.0, 30.0, 40.0),
                                  child: RoleReqScreenSearchPrefWidget(
                                    name: widget.name!,
                                    config: widget.editorConfig!,
                                    searchPrefConfig: widget.searchPrefScreen!,
                                    searchPref: widget.searchPref!,
                                    nextCallback: () async {
                                      context.safePop();
                                    },
                                  ),
                                ),
                              );
                            } else if (widget.name == 'matching_mode') {
                              return Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    30.0, 0.0, 30.0, 40.0),
                                child: wrapWithModel(
                                  model: _model.matchingModeSearchPrefModel,
                                  updateCallback: () => safeSetState(() {}),
                                  updateOnChange: true,
                                  child: MatchingModeSearchPrefWidget(
                                    name: widget.name!,
                                    config: widget.editorConfig!,
                                    searchPrefConfig: widget.searchPrefScreen!,
                                    nextCallback: () async {
                                      context.safePop();
                                    },
                                  ),
                                ),
                              );
                            } else {
                              return Container(
                                width: MediaQuery.sizeOf(context).width * 1.0,
                                height: 400.0,
                                decoration: const BoxDecoration(
                                  color: Colors.white,
                                ),
                                child: Align(
                                  alignment: const AlignmentDirectional(0.0, 0.0),
                                  child: Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        30.0, 0.0, 30.0, 0.0),
                                    child: Text(
                                      'Something went wrong. Please reach <NAME_EMAIL>, so we can solve this issue.',
                                      textAlign: TextAlign.center,
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            fontFamily: 'BT Beau Sans',
                                            letterSpacing: 0.0,
                                            useGoogleFonts: false,
                                          ),
                                    ),
                                  ),
                                ),
                              );
                            }
                          },
                        ),
                      );
                    } else {
                      return Container(
                        width: MediaQuery.sizeOf(context).width * 1.0,
                        height: 400.0,
                        decoration: const BoxDecoration(
                          color: Colors.white,
                        ),
                        child: Align(
                          alignment: const AlignmentDirectional(0.0, 0.0),
                          child: Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                30.0, 0.0, 30.0, 0.0),
                            child: Text(
                              'Something went wrong. Please reach <NAME_EMAIL>, so we can solve this issue.',
                              textAlign: TextAlign.center,
                              style: FlutterFlowTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'BT Beau Sans',
                                    letterSpacing: 0.0,
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ),
                        ),
                      );
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
