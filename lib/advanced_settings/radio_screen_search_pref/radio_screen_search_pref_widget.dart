import '/auth/firebase_auth/auth_util.dart';
import '/advanced_settings/radio_button_list_last_default/radio_button_list_last_default_widget.dart';
import '/backend/backend.dart';
import '/discovery/popup/dealbreaker_bypass_choice_popup/dealbreaker_bypass_choice_popup_widget.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/info_sheet_scrollable/info_sheet_scrollable_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'radio_screen_search_pref_model.dart';
export 'radio_screen_search_pref_model.dart';
import '/flutter_flow/custom_functions.dart' as functions;

class RadioScreenSearchPrefWidget extends StatefulWidget {
  const RadioScreenSearchPrefWidget({
    super.key,
    required this.config,
    required this.nextCallback,
    required this.name,
    required this.searchPrefConfig,
    required this.searchPref,
  });

  final SearchPrefSelectorScreenStruct? config;
  final Future Function()? nextCallback;
  final String? name;
  final SearchPrefScreenStruct? searchPrefConfig;
  final SearchPrefStruct? searchPref;

  @override
  State<RadioScreenSearchPrefWidget> createState() =>
      _RadioScreenSearchPrefWidgetState();
}

class _RadioScreenSearchPrefWidgetState
    extends State<RadioScreenSearchPrefWidget> {
  late RadioScreenSearchPrefModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => RadioScreenSearchPrefModel());

    // On component load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      _model.searchPref = SearchPrefStruct(
        name: widget.name,
        lowerReq: widget.searchPref?.lowerReq ?? 0,
        upperReq: widget.searchPref?.upperReq ?? 10000,
        stringReq: widget.searchPref?.stringReq ?? '',
        stringReqs: widget.searchPref?.stringReqs ?? [],
        dealbreaker: widget.searchPref?.dealbreaker ?? false,
      );
      safeSetState(() {});
    });
  }

  Future<void> nextAction() async {
    if (widget.name == 'gender_preferences') {
                          await currentUserDocument?.reference.update({
                          'genderReq': _model.searchPref?.stringReq,
                          'matchingSuggestions': FieldValue.delete()
                        });

                        } else {

                        await currentUserDocument?.reference.update({
                          '${widget.name}Req': _model.searchPref?.stringReq,
                          '${widget.name}Dealbreaker': _model.searchPref?.dealbreaker,
                          'matchingSuggestions': FieldValue.delete()
                        });
                        }

                        await widget.nextCallback?.call();
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.sizeOf(context).width * 1.0,
      height: MediaQuery.sizeOf(context).height * 1.0,
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).primaryBackground,
      ),
      child: Stack(
        children: [
          Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.config?.hasSubheading() ?? true)
                Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(0.0, 37.0, 40.0, 0.0),
                  child: Text(
                    valueOrDefault<String>(
                      widget.config?.subheading,
                      'Select an option',
                    ),
                    textAlign: TextAlign.start,
                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                          fontFamily: 'BT Beau Sans',
                          color: FlutterFlowTheme.of(context).secondaryText,
                          fontSize: 12.0,
                          letterSpacing: 0.0,
                          useGoogleFonts: false,
                        ),
                  ),
                ),
              Flexible(
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      wrapWithModel(
                        model: _model.radioButtonListLastDefaultModel,
                        updateCallback: () => safeSetState(() {}),
                        updateOnChange: true,
                        child: RadioButtonListLastDefaultWidget(
<<<<<<< HEAD
                          initialSelectedOption: widget.searchPref?.stringReq ?? '',
                          mandatory: false,
                          options: functions.getSearchOptions(widget.name ?? '')
                        ),
=======
                            initialSelectedOption:
                                widget.searchPref?.stringReq ?? '',
                            mandatory: false,
                            options:
                            /// This function call here is responsible for populating the the [RadioButtonListLastDefaultWidget] 
                                functions.getSearchOptions(widget.name ?? '')),
>>>>>>> 7f271f2b8 (Solve roles page error, Solve for empty list data for gender preferences, Get dev alternativePackageId for gold and evolved)
                      ),
                    ].addToEnd(const SizedBox(height: 320.0)),
                  ),
                ),
              ),
            ],
          ),
          Align(
            alignment: const AlignmentDirectional(0.0, 1.0),
            child: Container(
              width: MediaQuery.sizeOf(context).width * 1.0,
              height: 265.0,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [Color(0x00FFFFFF), Colors.white],
                  stops: [0.0, 0.22],
                  begin: AlignmentDirectional(0.0, -1.0),
                  end: AlignmentDirectional(0, 1.0),
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (widget.config?.hasInfoBtnTitle() ?? true)
                    Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 10.0),
                      child: FFButtonWidget(
                        onPressed: () async {
                          await showModalBottomSheet(
                            isScrollControlled: true,
                            backgroundColor: Colors.transparent,
                            enableDrag: false,
                            context: context,
                            builder: (context) {
                              return Padding(
                                padding: MediaQuery.viewInsetsOf(context),
                                child: InfoSheetScrollableWidget(
                                  title: widget.config!.infoBtnTitle,
                                  body: widget.config!.infoBtn,
                                ),
                              );
                            },
                          ).then((value) => safeSetState(() {}));
                        },
                        text: widget.config!.infoBtnTitle,
                        icon: Icon(
                          Icons.info_outlined,
                          color: FlutterFlowTheme.of(context).secondaryText,
                          size: 18.0,
                        ),
                        options: FFButtonOptions(
                          height: 40.0,
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              15.0, 0.0, 15.0, 0.0),
                          iconPadding: const EdgeInsetsDirectional.fromSTEB(
                              0.0, 0.0, 0.0, 0.0),
                          color: Colors.white,
                          textStyle: FlutterFlowTheme.of(context)
                              .titleSmall
                              .override(
                                fontFamily: 'BT Beau Sans',
                                color:
                                    FlutterFlowTheme.of(context).secondaryText,
                                fontSize: 4.0,
                                letterSpacing: 0.0,
                                fontWeight: FontWeight.w500,
                                useGoogleFonts: false,
                              ),
                          elevation: 0.0,
                          borderSide: const BorderSide(
                            color: Color(0x0057636C),
                            width: 0.0,
                          ),
                          borderRadius: BorderRadius.circular(100.0),
                        ),
                      ),
                    ),
                  Padding(
                    padding:
                        const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 25.0),
                    child: InkWell(
                      splashColor: Colors.transparent,
                      focusColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      onTap: () async {
                        _model.updateSearchPrefStruct(
                          (e) => e..dealbreaker = !e.dealbreaker,
                        );
                        safeSetState(() {});
                      },
                      child: Container(
                        decoration: const BoxDecoration(),
                        child: Visibility(
                          visible:
                              widget.searchPrefConfig?.dealbreakerEligible ??
                                  true,
                          child: Column(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              const Divider(
                                thickness: 1.0,
                                color: Color(0xFFD4D8DE),
                              ),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 17.0, 0.0, 19.0),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      getRemoteConfigString(
                                          'generic_search_pref_dealbreaker_string'),
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            fontFamily: 'BT Beau Sans',
                                            letterSpacing: 0.0,
                                            useGoogleFonts: false,
                                          ),
                                    ),
                                    Stack(
                        alignment: Alignment.center,
                        children: [
                          Container(
                            width: 18.0,
                            height: 18.0,
                            decoration: BoxDecoration(
                              color: _model.searchPref?.dealbreaker ?? false
                                  ? const Color(0xFFBAABF1)
                                  : const Color(0xFFF1F0F0),
                              shape: BoxShape.circle,
                            ),
                          ),
                          if (_model.searchPref?.dealbreaker ?? false)
                          Align(
                            alignment: Alignment.center,
                            child: Container(
                              width: 5.5,
                              height: 5.5,
                              decoration: const BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                              ),
                            ),
                          ),
                        ],
                      ),
                                    
                                  ].divide(const SizedBox(width: 8.0)),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  wrapWithModel(
                    model: _model.gradientButtonModel,
                    updateCallback: () => safeSetState(() {}),
                    updateOnChange: true,
                    child: GradientButtonWidget(
                      title: valueOrDefault<String>(
                        widget.config?.nextBtnTitle,
                        'Save',
                      ),
                      action: () async {
                        _model.updateSearchPrefStruct(
                          (e) => e
                            ..stringReq = _model
                                .radioButtonListLastDefaultModel.selectedOption,
                        );
                        safeSetState(() {});

                        if((currentUserDocument?.matchBypassReq ?? false) && _model.searchPref?.dealbreaker == true) {

                        await showDialog(
                                            context: context,
                                            builder: (dialogContext) {
                                              return Dialog(
                                                elevation: 0,
                                                insetPadding: EdgeInsets.zero,
                                                backgroundColor:
                                                    Colors.transparent,
                                                alignment: const AlignmentDirectional(
                                                        0.0, 0.0)
                                                    .resolve(Directionality.of(
                                                        context)),
                                                child: DealbreakerBypassChoicePopupWidget(
                                                  type: "dealbreaker",
                                                  callback: () async {
                                                    nextAction();
                                                  },
                                                ),
                                              );
                                            },
                                          );
                                          return;

                        }

                        nextAction();

                        
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
