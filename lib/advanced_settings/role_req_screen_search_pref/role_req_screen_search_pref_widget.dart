import 'package:chyrpe/advanced_settings/checkbox_button_last_default/checkbox_button_last_default_widget.dart';
import 'package:chyrpe/auth/firebase_auth/auth_util.dart';
import 'package:chyrpe/discovery/popup/general_popup_new/general_popup_new_widget.dart';
import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/info_sheet_scrollable/info_sheet_scrollable_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'role_req_screen_search_pref_model.dart';
export 'role_req_screen_search_pref_model.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import '/flutter_flow/revenue_cat_util.dart' as revenue_cat;
import 'package:flutter_spinkit/flutter_spinkit.dart';

class RoleReqScreenSearchPrefWidget extends StatefulWidget {
  const RoleReqScreenSearchPrefWidget({
    super.key,
    required this.config,
    required this.nextCallback,
    required this.name,
    required this.searchPrefConfig,
    required this.searchPref,
  });

  final SearchPrefSelectorScreenStruct? config;
  final Future Function()? nextCallback;
  final String? name;
  final SearchPrefScreenStruct? searchPrefConfig;
  final SearchPrefStruct? searchPref;

  @override
  State<RoleReqScreenSearchPrefWidget> createState() =>
      _RoleReqScreenSearchPrefWidgetState();
}

class _RoleReqScreenSearchPrefWidgetState
    extends State<RoleReqScreenSearchPrefWidget> {
  late RoleReqScreenSearchPrefModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => RoleReqScreenSearchPrefModel());

    // On component load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      _model.searchPref = SearchPrefStruct(
        name: widget.searchPref?.name,
        lowerReq: widget.searchPref?.lowerReq,
        upperReq: widget.searchPref?.upperReq,
        stringReq: widget.searchPref?.stringReq,
        stringReqs: widget.searchPref?.stringReqs,
        dealbreaker: widget.searchPref?.dealbreaker,
      );
      safeSetState(() {});
    });

  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  Future<void> nextAction() async {
    await currentUserDocument?.reference.update({
                          '${widget.name}Req': _model.searchPref?.stringReqs,
                          '${widget.name}Dealbreaker': _model.searchPref?.dealbreaker,
                          'matchingSuggestions': FieldValue.delete()
                        });

                        safeSetState(() {});
                        await widget.nextCallback?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.sizeOf(context).width * 1.0,
      height: MediaQuery.sizeOf(context).height * 1.0,
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).primaryBackground,
      ),
      child: Stack(
        children: [
          Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.config?.hasSubheading() ?? true)
                Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(0.0, 37.0, 40.0, 40.0),
                  child: Text(
                    valueOrDefault<String>(
                      widget.config?.subheading,
                      'Select an option',
                    ),
                    textAlign: TextAlign.start,
                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                          fontFamily: 'BT Beau Sans',
                          color: FlutterFlowTheme.of(context).secondaryText,
                          fontSize: 12.0,
                          letterSpacing: 0.0,
                          useGoogleFonts: false,
                        ),
                  ),
                ),
              Flexible(
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      AuthUserStreamWidget(
                        builder: (context) =>
                            StreamBuilder<List<OptionsForSelectorsRecord>>(
                          stream: queryOptionsForSelectorsRecord(
                            queryBuilder: (optionsForSelectorsRecord) =>
                                optionsForSelectorsRecord.where(
                              'name',
                              isEqualTo:
                              (valueOrDefault<bool>(
                                      currentUserDocument?.alternativeG,
                                      false) || getRemoteConfigBool('editor_roles_all_available'))
                                  ? 'Alternative Roles Req'
                                  : '${currentUserDocument?.gender?.name}-${currentUserDocument?.position}-${currentUserDocument?.genderReq} Role Req',
                            ),
                            singleRecord: true,
                          ),
                          builder: (context, snapshot) {
                            // Customize what your widget looks like when it's loading.
                            if (!snapshot.hasData) {
                              return Center(
                                child: SizedBox(
                                  width: 40.0,
                                  height: 40.0,
                                  child: SpinKitRing(
                                    color: FlutterFlowTheme.of(context)
                                        .accent1,
                                    size: 40.0,
                                  ),
                                ),
                              );
<<<<<<< HEAD
                            }
                            List<OptionsForSelectorsRecord>
                                columnOptionsForSelectorsRecordList =
                                snapshot.data!;

                               
                            // Return an empty Container when the item does not exist.
                           
                            final columnOptionsForSelectorsRecord =
                                columnOptionsForSelectorsRecordList.isNotEmpty
                                    ? columnOptionsForSelectorsRecordList
                                        .first
                                    : null;

                                    return Builder(
                              builder: (context) {
                                var options = 
                                    !snapshot.data!.isEmpty ? columnOptionsForSelectorsRecord?.options
                                            .toList() ??
                                        [] : functions.getStringListFromJson(getRemoteConfigString('options_femdom_role_req'));

                                bool collectivelyExhaustive = columnOptionsForSelectorsRecord?.snapshotData['collectivelyExhaustive'] ?? true;                                    
                                    
                              return wrapWithModel(
                          model: _model.checkboxButtonLastDefaultModel,
                          updateCallback: () => safeSetState(() {
                            
                          }),
                          updateOnChange: true,
                          child: CheckboxButtonLastDefaultWidget(
                            maxOptions: widget.config!.maxOptionsCheckbox,
                            autoSet: collectivelyExhaustive,
                            initialSelectedOptions:
                                widget.searchPref?.stringReqs,
                            options: options
                          )
                          );
                              }

                        );
=======
                            } else {
                              List<OptionsForSelectorsRecord>
                                  columnOptionsForSelectorsRecordList =
                                  snapshot.data!;

                              // Return an empty Container when the item does not exist.

                              final columnOptionsForSelectorsRecord =
                                  columnOptionsForSelectorsRecordList.isNotEmpty
                                      ? columnOptionsForSelectorsRecordList
                                          .first
                                      : null;

                              return Builder(
                                builder: (context) {
                                  var options = !snapshot.data!.isEmpty
                                      ? columnOptionsForSelectorsRecord?.options
                                              .toList() ??
                                          []
                                      : functions.getStringListFromJson(
                                          getRemoteConfigString(
                                              'options_femdom_role_req'));

                                  bool collectivelyExhaustive =
                                      columnOptionsForSelectorsRecord
                                                  ?.snapshotData[
                                              'collectivelyExhaustive'] ??
                                          true;

                                  return wrapWithModel(
                                    model:
                                        _model.checkboxButtonLastDefaultModel,
                                    updateCallback: () => safeSetState(() {}),
                                    updateOnChange: true,
                                    child: CheckboxButtonLastDefaultWidget(
                                        maxOptions:
                                            widget.config!.maxOptionsCheckbox,
                                        autoSet: collectivelyExhaustive,
                                        initialSelectedOptions:
                                            widget.searchPref?.stringReqs,
                                        options: options),
                                  );
                                },
                              );
                            }
>>>>>>> 7f271f2b8 (Solve roles page error, Solve for empty list data for gender preferences, Get dev alternativePackageId for gold and evolved)
                          },
                      ),
                      )
                    ].addToEnd(const SizedBox(height: 320.0)),
                  ),
                ),
              ),
            ],
          ),
          Align(
            alignment: const AlignmentDirectional(0.0, 1.0),
            child: Container(
              width: MediaQuery.sizeOf(context).width * 1.0,
              height: 265.0,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [Color(0x00FFFFFF), Colors.white],
                  stops: [0.0, 0.22],
                  begin: AlignmentDirectional(0.0, -1.0),
                  end: AlignmentDirectional(0, 1.0),
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (widget.config?.hasInfoBtnTitle() ?? true)
                    Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 10.0),
                      child: FFButtonWidget(
                        onPressed: () async {
                          await showModalBottomSheet(
                            isScrollControlled: true,
                            backgroundColor: Colors.transparent,
                            enableDrag: false,
                            context: context,
                            builder: (context) {
                              return Padding(
                                padding: MediaQuery.viewInsetsOf(context),
                                child: InfoSheetScrollableWidget(
                                  title: widget.config!.infoBtnTitle,
                                  body: widget.config!.infoBtn,
                                ),
                              );
                            },
                          ).then((value) => safeSetState(() {}));
                        },
                        text: widget.config!.infoBtnTitle,
                        icon: Icon(
                          Icons.info_outlined,
                          color: FlutterFlowTheme.of(context).secondaryText,
                          size: 18.0,
                        ),
                        options: FFButtonOptions(
                          height: 40.0,
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              15.0, 0.0, 15.0, 0.0),
                          iconPadding: const EdgeInsetsDirectional.fromSTEB(
                              0.0, 0.0, 0.0, 0.0),
                          color: Colors.white,
                          textStyle: FlutterFlowTheme.of(context)
                              .titleSmall
                              .override(
                                fontFamily: 'BT Beau Sans',
                                color:
                                    FlutterFlowTheme.of(context).secondaryText,
                                fontSize: 4.0,
                                letterSpacing: 0.0,
                                fontWeight: FontWeight.w500,
                                useGoogleFonts: false,
                              ),
                          elevation: 0.0,
                          borderSide: const BorderSide(
                            color: Color(0x0057636C),
                            width: 0.0,
                          ),
                          borderRadius: BorderRadius.circular(100.0),
                        ),
                      ),
                    ),
                  Padding(
                    padding:
                        const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 25.0),
                    child: InkWell(
                      splashColor: Colors.transparent,
                      focusColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      onTap: () async {
                        _model.updateSearchPrefStruct(
                          (e) => e..dealbreaker = !e.dealbreaker,
                        );
                        safeSetState(() {});
                      },
                      child: Container(
                        decoration: const BoxDecoration(),
                        child: Visibility(
                          visible:
                              widget.searchPrefConfig?.dealbreakerEligible ??
                                  true,
                          child: Column(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              const Divider(
                                thickness: 1.0,
                                color: Color(0xFFD4D8DE),
                              ),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 17.0, 0.0, 19.0),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      getRemoteConfigString(
                                          'generic_search_pref_dealbreaker_string'),
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            fontFamily: 'BT Beau Sans',
                                            letterSpacing: 0.0,
                                            useGoogleFonts: false,
                                          ),
                                    ),
                                    Stack(
<<<<<<< HEAD
                        alignment: Alignment.center,
                        children: [
                          Container(
                            width: 18.0,
                            height: 18.0,
                            decoration: BoxDecoration(
                              color: _model.searchPref!.dealbreaker
                                  ? const Color(0xFFBAABF1)
                                  : const Color(0xFFF1F0F0),
                              shape: BoxShape.circle,
                            ),
                          ),
                          if (_model.searchPref!.dealbreaker)
                          Align(
                            alignment: Alignment.center,
                            child: Container(
                              width: 5.5,
                              height: 5.5,
                              decoration: const BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                              ),
                            ),
                          ),
                        ],
                      ),
=======
                                      alignment: Alignment.center,
                                      children: [
                                        Container(
                                          width: 18.0,
                                          height: 18.0,
                                          decoration: BoxDecoration(
                                            color:
                                        
                                                _model.searchPref!.dealbreaker
                                                    ? const Color(0xFFBAABF1)
                                                    : const Color(0xFFF1F0F0),
                                            shape: BoxShape.circle,
                                          ),
                                        ),
                                        if (true)
                                          Align(
                                            alignment: Alignment.center,
                                            child: Container(
                                              width: 5.5,
                                              height: 5.5,
                                              decoration: const BoxDecoration(
                                                color: Colors.white,
                                                shape: BoxShape.circle,
                                              ),
                                            ),
                                          ),
                                      ],
                                    ),
>>>>>>> 7f271f2b8 (Solve roles page error, Solve for empty list data for gender preferences, Get dev alternativePackageId for gold and evolved)
                                  ].divide(const SizedBox(width: 8.0)),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  wrapWithModel(
                    model: _model.gradientButtonModel,
                    updateCallback: () => safeSetState(() {}),
                    updateOnChange: true,
                    child: GradientButtonWidget(
                      title: valueOrDefault<String>(
                        widget.config?.nextBtnTitle,
                        'Save',
                      ),
                      action: () async {
                        _model.updateSearchPrefStruct(
                          (e) => e
                            ..stringReqs = _model
                                .checkboxButtonLastDefaultModel.selectedOptions
                                .toList(),
                        );

                        if(_model
                                .checkboxButtonLastDefaultModel.selectedOptions
                                .toList().length == 1 && !_model
                                .checkboxButtonLastDefaultModel.selectedOptions
                                .any((element) => functions.getStringListFromJson(getRemoteConfigString('role_req_sufficient_choices')).contains(element)) && getRemoteConfigString('role_req_popup_enabled_genders').contains(currentUserDocument?.gender?.name ?? '')) {

                        await showDialog(
                                            context: context,
                                            builder: (dialogContext) {
                                              return Dialog(
                                                elevation: 0,
                                                insetPadding: EdgeInsets.zero,
                                                backgroundColor:
                                                    Colors.transparent,
                                                alignment: const AlignmentDirectional(
                                                        0.0, 0.0)
                                                    .resolve(Directionality.of(
                                                        context)),
                                                child: GeneralPopupNewWidget(
                                                  title: getRemoteConfigString('role_req_popup_title'),
                                                  body: getRemoteConfigString('role_req_popup_body'),
                                                  buttonTitle: getRemoteConfigString('role_req_popup_btnTitle'),
                                                  buttonAction: nextAction,
                                                ),
                                              );
                                            },
                                          );
                                          return;

                        }

                        nextAction();

                        
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (!(functions.isEntitled(
              (currentUserDocument?.gender == Gender.Female
                      ? functions.getStringListFromJson(getRemoteConfigString(
                          'role_req_subscription_eligible_entitlements_female'))
                      : functions.getStringNoLangListFromJson(getRemoteConfigString(
                          'role_req_subscription_eligible_entitlements_male')))
                  .toList(),
              revenue_cat.activeEntitlementIds.toList())))
            LayoutBuilder(builder: (context, constraints) {
              return Container(
                width: constraints.maxWidth,
                height: constraints.maxHeight,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    stops: [0, 0.8],
                    colors: [
                      const Color.fromARGB(0, 255, 255, 255),
                      Colors.white,
                    ],
                  ),
                ),
                child: Padding(
                  padding:
                      const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                  child: Column(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Row(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Icon(
                                Icons.lock_outlined,
                                color: FlutterFlowTheme.of(context).primaryText,
                                size: 24.0,
                              ),
                              Text(
                                'Subscribers Only',
                                style: FlutterFlowTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'BT Beau Sans',
                                      fontSize: 14.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.w500,
                                      useGoogleFonts: false,
                                    ),
                              ),
                            ].divide(const SizedBox(width: 5.0)),
                          ),
                        ),
                        GradientButtonWidget(
                          title: getRemoteConfigString(
                              'role_req_upgrade_btn_title'),
                          action: () async {
                            if (currentUserDocument?.gender == Gender.Male
                                ? functions
                                    .getStringNoLangListFromJson(getRemoteConfigString(
                                        'role_req_subscription_eligible_entitlements_male'))
                                    .contains('paid_standard_1w')
                                : false) {
                              context.pushNamed('ChyrpeStandardNewNew');
                            } else if (() {
                              if (currentUserDocument?.gender == Gender.Male) {
                                return functions
                                    .getStringNoLangListFromJson(getRemoteConfigString(
                                        'role_req_subscription_eligible_entitlements_male'))
                                    .contains('plus_access');
                              } else if (currentUserDocument?.gender ==
                                  Gender.Female) {
                                return functions
                                    .getStringNoLangListFromJson(getRemoteConfigString(
                                        'role_req_subscription_eligible_entitlements_female'))
                                    .contains('plus_access');
                              } else {
                                return false;
                              }
                            }()) {
                              context.pushNamed('PlusSubscriptionNew');
                            } else if (() {
                              if (currentUserDocument?.gender == Gender.Male) {
                                return functions
                                    .getStringNoLangListFromJson(getRemoteConfigString(
                                        'role_req_subscription_eligible_entitlements_male'))
                                    .contains('gold_access');
                              } else if (currentUserDocument?.gender ==
                                  Gender.Female) {
                                return functions
                                    .getStringNoLangListFromJson(getRemoteConfigString(
                                        'role_req_subscription_eligible_entitlements_female'))
                                    .contains('gold_access');
                              } else {
                                return false;
                              }
                            }()) {
                              context.pushNamed('GoldSubscriptionNew');
                            } else if (() {
                              if (currentUserDocument?.gender == Gender.Male) {
                                return functions
                                    .getStringNoLangListFromJson(getRemoteConfigString(
                                        'role_req_subscription_eligible_entitlements_male'))
                                    .contains('evolved_access');
                              } else if (currentUserDocument?.gender ==
                                  Gender.Female) {
                                return functions
                                    .getStringNoLangListFromJson(getRemoteConfigString(
                                        'role_req_subscription_eligible_entitlements_female'))
                                    .contains('evolved_access');
                              } else {
                                return false;
                              }
                            }()) {
                              context.pushNamed('EvolvedSubscriptionNew');
                            } else if (currentUserDocument?.gender ==
                                    Gender.Female
                                ? functions
                                    .getStringNoLangListFromJson(getRemoteConfigString(
                                        'role_req_subscription_eligible_entitlements_female'))
                                    .contains('divine_access')
                                : false) {
                              context.pushNamed('DivineSubscription');
                            }
                          },
                        )
                      ]),
                ),
              );
            }),
        ],
      ),
    );
  }
}
