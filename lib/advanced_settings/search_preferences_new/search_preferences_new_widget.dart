import 'package:chyrpe/flutter_flow/flutter_flow_icon_button.dart';
import 'package:chyrpe/general/info_sheet_scrollable/info_sheet_scrollable_widget.dart';
import 'package:flutter_native_splash/cli_commands.dart';

import '/advanced_settings/settings_row/settings_row_widget.dart';
import '/advanced_settings/settings_row_boolean/settings_row_boolean_widget.dart';
import '/auth/firebase_auth/auth_util.dart';
import '/backend/schema/enums/enums.dart';
import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'search_preferences_new_model.dart';
export 'search_preferences_new_model.dart';
import '/components/filter_limit_hint_widget.dart';

class SearchPreferencesNewWidget extends StatefulWidget {
  const SearchPreferencesNewWidget({
    super.key,
    bool? fromDiscovery,
    
  }) : fromDiscovery = fromDiscovery ?? true;

  final bool fromDiscovery;

  @override
  State<SearchPreferencesNewWidget> createState() =>
      _SearchPreferencesNewWidgetState();
}

String convertCmToFeetAndInches(double centimeters) {
  const double cmToInchesFactor = 0.393701;
  double totalInches = centimeters * cmToInchesFactor;
  
  int feet = totalInches ~/ 12; // Get the whole feet
  int inches = (totalInches % 12).round(); // Get the remaining inches and round it
  
  return "$feet'$inches\""; // Return the result in 5'7" format
}


SearchPrefStruct getSearchPrefFromUserDoc(UsersRecord? userDoc, SearchPrefScreenStruct searchPrefScreen) {
  try {
  final name = searchPrefScreen.name;
  String singleReq = '';
  double lowerReq = 0;
  double upperReq = 0;
  List<String> multiReq = [];
  bool dealbreaker = false;
  bool imperial = userDoc?.imperialUnits ?? false;

  if (userDoc != null) {
      final snapshotData = userDoc.snapshotData;

      if (searchPrefScreen.type == SignupScreenType.radio) {
          if (name == 'gender_preferences') {
             singleReq = snapshotData['genderReq'] as String;
          } else if (snapshotData.containsKey('${name}Req') && snapshotData['${name}Req'] is String) {
            singleReq = snapshotData['${name}Req'] as String;
          }

          if (snapshotData.containsKey('${name}Dealbreaker') && snapshotData['${name}Dealbreaker'] is bool) {
            dealbreaker = snapshotData['${name}Dealbreaker'] as bool;
          }
          return SearchPrefStruct(stringReq: singleReq, dealbreaker: dealbreaker);
        } else if (searchPrefScreen.type == SignupScreenType.checkboxes) {
          if (snapshotData.containsKey('${name}Req') && snapshotData['${name}Req'] is List<dynamic>) {
              // Parse the list and ensure all elements are strings
              multiReq = (snapshotData['${name}Req'] as List<dynamic>)
                  .whereType<String>() // Filter only string elements
                  .cast<String>() // Safely cast to List<String>
                  .toList();
            }

          if (snapshotData.containsKey('${name}Dealbreaker') && snapshotData['${name}Dealbreaker'] is bool) {
            dealbreaker = snapshotData['${name}Dealbreaker'] as bool;
          }
          return SearchPrefStruct(stringReqs: multiReq, dealbreaker: dealbreaker);
        } else if (searchPrefScreen.type == SignupScreenType.special) {
          if (name == 'matching_mode') {
             return SearchPrefStruct(stringReq: userDoc.localMatching ? 'Local' : 'Global');
          }

          if (name == 'distance') {
            String reqString = '';

            if (snapshotData.containsKey('${name}Req') && snapshotData['${name}Req'] is num) {
              upperReq = snapshotData['${name}Req'].toDouble() as double;
              double finalReq = imperial ? upperReq * 0.621371 : upperReq;
              reqString = "${finalReq.round()} ${imperial ? 'mi' : 'km'}";
            }
            if (snapshotData.containsKey('${name}Dealbreaker') && snapshotData['${name}Dealbreaker'] is bool) {
              dealbreaker = snapshotData['${name}Dealbreaker'] as bool;
            }
            return SearchPrefStruct(stringReq: reqString, dealbreaker: dealbreaker, upperReq: upperReq);
          }

          if (name == 'height') {
            String reqString = '';

            if (snapshotData.containsKey('lower${name.capitalize()}Req') && snapshotData['lower${name.capitalize()}Req'] is num) {
            lowerReq = snapshotData['lower${name.capitalize()}Req'].toDouble() as double;
          }
          if (snapshotData.containsKey('upper${name.capitalize()}Req') && snapshotData['upper${name.capitalize()}Req'] is num) {
            upperReq = snapshotData['upper${name.capitalize()}Req'].toDouble() as double;
          }

            String finalLowerReqString = imperial ? convertCmToFeetAndInches(lowerReq) : "${lowerReq.round()} ${imperial ? '' : 'cm'}";
            String finalUpperReqString = imperial ? convertCmToFeetAndInches(upperReq) : "${upperReq.round()} ${imperial ? '' : 'cm'}";
            reqString = '$finalLowerReqString - $finalUpperReqString';

            if (snapshotData.containsKey('${name}Dealbreaker') && snapshotData['${name}Dealbreaker'] is bool) {
              dealbreaker = snapshotData['${name}Dealbreaker'] as bool;
            }

            return SearchPrefStruct(stringReq: reqString, dealbreaker: dealbreaker, lowerReq: lowerReq, upperReq: upperReq);
          }

          if (name == "role" && snapshotData['${name}Req'] is List<dynamic>) {
              // Parse the list and ensure all elements are strings
              multiReq = (snapshotData['${name}Req'] as List<dynamic>)
                  .whereType<String>() // Filter only string elements
                  .cast<String>() // Safely cast to List<String>
                  .toList();

            return SearchPrefStruct(stringReqs: multiReq, dealbreaker: false);
            }

          if (snapshotData.containsKey('lower${name.capitalize()}Req') && snapshotData['lower${name.capitalize()}Req'] is num) {
            lowerReq = snapshotData['lower${name.capitalize()}Req'].toDouble() as double;
          }
          if (snapshotData.containsKey('upper${name.capitalize()}Req') && snapshotData['upper${name.capitalize()}Req'] is num) {
            upperReq = snapshotData['upper${name.capitalize()}Req'].toDouble() as double;
          }
          if (snapshotData.containsKey('${name}Dealbreaker') && snapshotData['${name}Dealbreaker'] is bool) {
            dealbreaker = snapshotData['${name}Dealbreaker'] as bool;
          }
          return SearchPrefStruct(lowerReq: lowerReq, upperReq: upperReq, dealbreaker: dealbreaker);
        }
  }

  return SearchPrefStruct();
  } catch(e) {
    return SearchPrefStruct();
  }
}

bool hasDealbreakers(UsersRecord? userDoc) {
  try {
    if (userDoc == null) {
      return false; // No dealbreakers if there's no user data
    }

    final snapshotData = userDoc.snapshotData;

    // Iterate through the snapshot keys to find dealbreaker fields
    for (final key in snapshotData.keys) {
      if (key.endsWith('Dealbreaker') && snapshotData[key] is bool) {
        if (snapshotData[key] as bool) {
          return true; // At least one dealbreaker is true
        }
      }
    }

    return false; // No dealbreakers found
  } catch (e) {
    return false; // Handle errors gracefully
  }
}


class _SearchPreferencesNewWidgetState
    extends State<SearchPreferencesNewWidget> {
  late SearchPreferencesNewModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    
    _model = createModel(context, () => SearchPreferencesNewModel());

    // On page load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {

      if (hasDealbreakers(currentUserDocument) && currentUserDocument?.matchBypassReq == true) {
        await currentUserDocument?.reference.update({"matchBypassReq": false});
        safeSetState(() {});
      }


      _model.professionalsReq =
          valueOrDefault<bool>(currentUserDocument?.professionalsReq, false);
      _model.matchBypassReq =
          valueOrDefault<bool>(currentUserDocument?.matchBypassReq, false);
      _model.roleMatching =
          valueOrDefault<bool>(currentUserDocument?.roleMatching, false);
      safeSetState(() {});
      
    });
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<UsersRecord>(
      stream: UsersRecord.getDocument(currentUserReference!),
      builder: (context, snapshot) {
        // Customize what your widget looks like when it's loading.
        if (!snapshot.hasData) {
          return Scaffold(
            backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
            body: Center(
              child: SizedBox(
                width: 1.0,
                height: 1.0,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    FlutterFlowTheme.of(context).accent2,
                  ),
                ),
              ),
            ),
          );
        }

        final searchPreferencesNewUsersRecord = snapshot.data!;
        return GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
            FocusManager.instance.primaryFocus?.unfocus();
          },
          child: Scaffold(
            key: scaffoldKey,
            backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
            appBar: AppBar(
              backgroundColor: Colors.white,
              iconTheme: IconThemeData(
                  color: FlutterFlowTheme.of(context).primaryText),
              automaticallyImplyLeading: false,
              title: Text(
                'Search Preferences',
                style: FlutterFlowTheme.of(context).headlineMedium.override(
                      fontFamily: 'BT Beau Sans',
                      color: FlutterFlowTheme.of(context).primaryText,
                      fontSize: 18.0,
                      letterSpacing: 0.0,
                      fontWeight: FontWeight.w500,
                      useGoogleFonts: false,
                    ),
              ),
              actions: [
                FFButtonWidget(
                  onPressed: () async {
                      context.safePop();
                  },
                  text: 'Done',
                  options: FFButtonOptions(
                    height: 40.0,
                    padding:
                        const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                    iconPadding:
                        const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                    color: Colors.white,
                    textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                          fontFamily: 'BT Beau Sans',
                          color: FlutterFlowTheme.of(context).primaryText,
                          fontSize: 14.0,
                          letterSpacing: 0.0,
                          useGoogleFonts: false,
                        ),
                    elevation: 0.0,
                    borderSide: const BorderSide(
                      color: Color(0x00262A36),
                    ),
                    borderRadius: BorderRadius.circular(8.0),
                  ),
<<<<<<< HEAD
                ),
              ],
              centerTitle: true,
              elevation: 0.0,
            ),
            body: SafeArea(
              top: true,
              child: Padding(
                padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Builder(
                        builder: (context) {
                          final searchPreferencesList = functions
                              .getSettingsMapFromJson(getRemoteConfigString(
                                  'settings_categories_screens_standard'))
                              .toList();
                  
                          return ListView.builder(
                            padding: EdgeInsets.zero,
                            shrinkWrap: true,
                            primary: false,
                            scrollDirection: Axis.vertical,
                            itemCount: searchPreferencesList.length,
                            itemBuilder: (context, searchPreferencesListIndex) {
                              final searchPreferencesListItem =
                                  searchPreferencesList[searchPreferencesListIndex];
                              return Column(
                                mainAxisSize: MainAxisSize.max,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    searchPreferencesListItem.category,
                                    textAlign: TextAlign.center,
                                    style: FlutterFlowTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'BT Beau Sans',
                                          letterSpacing: 0.0,
                                          fontWeight: FontWeight.w500,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        0.0, 22.0, 0.0, 0.0),
                                    child: Builder(
                                      builder: (context) {
                                        final settingsAttribute =
                                            searchPreferencesListItem.screens.toList();
                  
                                        return ListView.separated(
                                          padding: EdgeInsets.zero,
                                          primary: false,
                                          shrinkWrap: true,
                                          scrollDirection: Axis.vertical,
                                          itemCount: settingsAttribute.length,
                                          separatorBuilder: (_, __) =>
                                              const SizedBox(height: 7.0),
                                          itemBuilder:
                                              (context, settingsAttributeIndex) {
                                            final settingsAttributeItem =
                                                settingsAttribute[
                                                    settingsAttributeIndex];
                                            return Builder(
                                              builder: (context) {
=======
                ],
                centerTitle: true,
                elevation: 0.0,
              ),
              body: SafeArea(
                top: true,
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Builder(
                          builder: (context) {
                            final searchPreferencesList = functions
                                .getSettingsMapFromJson(getRemoteConfigString(
                                    'settings_categories_screens_standard'))
                                .toList();

                            return ListView.builder(
                              padding: EdgeInsets.zero,
                              shrinkWrap: true,
                              primary: false,
                              scrollDirection: Axis.vertical,
                              itemCount: searchPreferencesList.length,
                              itemBuilder:
                                  (context, searchPreferencesListIndex) {
                                final searchPreferencesListItem =
                                    searchPreferencesList[
                                        searchPreferencesListIndex];

                                return Column(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      searchPreferencesListItem.category,
                                      textAlign: TextAlign.center,
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            fontFamily: 'BT Beau Sans',
                                            letterSpacing: 0.0,
                                            fontWeight: FontWeight.w500,
                                            useGoogleFonts: false,
                                          ),
                                    ),
                                    Padding(
                                      padding:
                                          const EdgeInsetsDirectional.fromSTEB(
                                              0.0, 22.0, 0.0, 0.0),
                                      child: Builder(
                                        builder: (context) {
                                          final settingsAttribute =
                                              searchPreferencesListItem.screens
                                                  .toList();

                                          return ListView.separated(
                                            padding: EdgeInsets.zero,
                                            primary: false,
                                            shrinkWrap: true,
                                            scrollDirection: Axis.vertical,
                                            itemCount: settingsAttribute.length,
                                            separatorBuilder: (_, __) =>
                                                const SizedBox(height: 7.0),
                                            itemBuilder: (context,
                                                settingsAttributeIndex) {
                                              final settingsAttributeItem =
                                                  settingsAttribute[
                                                      settingsAttributeIndex];

                                              return Builder(
                                                  builder: (context) {
>>>>>>> 7f271f2b8 (Solve roles page error, Solve for empty list data for gender preferences, Get dev alternativePackageId for gold and evolved)
                                                if ((searchPreferencesNewUsersRecord
                                                            .gender ==
                                                        Gender.Female
                                                    ? settingsAttributeItem
                                                        .eligibleFemaleEntitlements
                                                        .contains('none')
                                                    : settingsAttributeItem
                                                        .eligibleMaleEntitlements
                                                        .contains('none')) || (settingsAttributeItem.name == "distance" && valueOrDefault(searchPreferencesNewUsersRecord.globalMatching, true))) {
                                                  return Container(
                                                    width: 1.0,
                                                    height: 1.0,
                                                    decoration: BoxDecoration(
                                                      color: FlutterFlowTheme
                                                              .of(context)
                                                          .primaryBackground,
                                                    ),
                                                  );
                                                } else
                                                if (settingsAttributeItem.type ==
                                                    SignupScreenType.boolean) {
                                                  return Container(
                                                    height: 60.0,
                                                    decoration: const BoxDecoration(),
                                                    child: AuthUserStreamWidget(
                                                      builder: (context) =>
                                                          wrapWithModel(
                                                        model: _model
                                                            .settingsRowBooleanModels
                                                            .getModel(
                                                          settingsAttributeItem
                                                              .name,
                                                          settingsAttributeIndex,
                                                        ),
                                                        updateCallback: () =>
                                                            safeSetState(() {}),
                                                        updateOnChange: true,
                                                        child:
                                                            SettingsRowBooleanWidget(
                                                          key: Key(
                                                            'Key2vd_${settingsAttributeItem.name}',
                                                          ),
                                                          initialValue: () {
                                                            if (settingsAttributeItem
                                                                    .name ==
                                                                'professionals') {
                                                              return valueOrDefault<
                                                                      bool>(
                                                                  searchPreferencesNewUsersRecord
                                                                      .professionalsReq,
                                                                  false);
                                                            } else if (settingsAttributeItem
                                                                    .name ==
                                                                'match_bypass') {
                                                              return valueOrDefault<
                                                                      bool>(
                                                                  searchPreferencesNewUsersRecord
                                                                      .matchBypassReq,
                                                                  false);
                                                            } else if (settingsAttributeItem
                                                                    .name ==
                                                                'role_matching') {
                                                              return valueOrDefault<
                                                                      bool>(
                                                                  searchPreferencesNewUsersRecord
                                                                      .roleMatching,
                                                                  false);
                                                            } else {
                                                              return false;
                                                            }
                                                          }(),
                                                          settingsScreen:
                                                              settingsAttributeItem,
                                                          updateCallback:
                                                              (newValue) async {
                                                            if (settingsAttributeItem
                                                                    .name ==
                                                                'professionals') {
                                                              await currentUserReference!
                                                                  .update(
                                                                      createUsersRecordData(
                                                                professionalsReq:
                                                                    newValue,
                                                              )..addAll({'matchingSuggestions': FieldValue.delete()}));
                                                            } else if (settingsAttributeItem
                                                                    .name ==
                                                                'match_bypass') {
                                                              await currentUserReference!
                                                                  .update(
                                                                      createUsersRecordData(
                                                                matchBypassReq:
                                                                    newValue,
                                                              )..addAll({'matchingSuggestions': FieldValue.delete()}));
                                                            } else if (settingsAttributeItem
                                                                    .name ==
                                                                'role_matching') {
                                                              await currentUserReference!
                                                                  .update(
                                                                      createUsersRecordData(
                                                                roleMatching:
                                                                    newValue,
                                                              )..addAll({'matchingSuggestions': FieldValue.delete()}));
                                                            }
                                                          },
                                                        ),
                                                      ),
                                                    ),
                                                  );
                                                } else {
                                                  return Container(
                                                    height: 60.0,
                                                    decoration: const BoxDecoration(),
                                                    child: SettingsRowWidget(
                                                      key: Key(
                                                          'Keyrrs_${settingsAttributeIndex}_of_${settingsAttribute.length}'),
                                                      settingsScreen:
                                                          settingsAttributeItem,
                                                      searchPrefs:
                                                          getSearchPrefFromUserDoc(searchPreferencesNewUsersRecord, settingsAttributeItem),
                                                      updateCallback: () async {},
                                                    ),
                                                  );
                                                }
                                              }
                                            );
                                          },
                                        );
                                      },
                                    ),
                                  ),
                                ].addToStart(Container(height: 10)).addToEnd(Container(height: 30)),
                              );
                            },
                          );
                        },
                      ),
                      Builder(
                        builder: (context) {
                          final searchPreferencesList = functions
                              .getSettingsMapFromJson(getRemoteConfigString(
                                  'settings_categories_screens_advanced'))
                              .toList();
                  
                          return ListView.builder(
                            padding: EdgeInsets.zero,
                            shrinkWrap: true,
                            primary: false,
                            scrollDirection: Axis.vertical,
                            itemCount: searchPreferencesList.length,
                            itemBuilder: (context, searchPreferencesListIndex) {
                              final searchPreferencesListItem =
                                  searchPreferencesList[searchPreferencesListIndex];
                              return Column(
                                mainAxisSize: MainAxisSize.max,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  
                                  Text(
                                    searchPreferencesListItem.category,
                                    textAlign: TextAlign.center,
                                    style: FlutterFlowTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'BT Beau Sans',
                                          letterSpacing: 0.0,
                                          fontWeight: FontWeight.w500,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                  const FilterLimitHintWidget(),
                                  Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        0.0, 22.0, 0.0, 0.0),
                                    child: Builder(
                                      builder: (context) {
                                        final settingsAttribute =
                                            searchPreferencesListItem.screens
                                                .toList();
                  
                                        return ListView.separated(
                                          padding: EdgeInsets.zero,
                                          primary: false,
                                          shrinkWrap: true,
                                          scrollDirection: Axis.vertical,
                                          itemCount: settingsAttribute.length,
                                          separatorBuilder: (_, __) =>
                                              const SizedBox(height: 7.0),
                                          itemBuilder:
                                              (context, settingsAttributeIndex) {
                                            final settingsAttributeItem =
                                                settingsAttribute[
                                                    settingsAttributeIndex];
                                            return SizedBox(
                                              height: 60.0,
                                              child: SettingsRowWidget(
                                                key: Key(
                                                    'Key5p1_${settingsAttributeIndex}_of_${settingsAttribute.length}'),
                                                settingsScreen:
                                                    settingsAttributeItem,
                                                searchPrefs: getSearchPrefFromUserDoc(searchPreferencesNewUsersRecord, settingsAttributeItem),
                                                updateCallback: () async {},
                                              ),
                                            );
                                          },
                                        );
                                      },
                                    ),
                                  ),
                                ],
                              );
                            },
                          );
                        },
                      ),
                    ]
                        .divide(const SizedBox(height: 30.0))
                        .addToStart(const SizedBox(height: 10.0))
                        .addToEnd(const SizedBox(height: 30.0)),
                  ),
                ),
              ),
            ),
          ),
        );
      }
    );
  }
}
