

import 'dart:developer';

import 'package:chyrpe/advanced_settings/height_req_screen_search_pref/height_req_screen_search_pref_widget.dart';

import '/auth/firebase_auth/auth_util.dart';
import '/backend/schema/enums/enums.dart';
import '/backend/schema/structs/index.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import '/flutter_flow/revenue_cat_util.dart' as revenue_cat;
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'settings_row_model.dart';
export 'settings_row_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';

class SettingsRowWidget extends StatefulWidget {
  const SettingsRowWidget({
    super.key,
    required this.settingsScreen,
    required this.searchPrefs,
    required this.updateCallback,
  });

  final SearchPrefScreenStruct? settingsScreen;
  final SearchPrefStruct? searchPrefs;
  final Future Function()? updateCallback;

  @override
  State<SettingsRowWidget> createState() => _SettingsRowWidgetState();
}

class _SettingsRowWidgetState extends State<SettingsRowWidget> {
  late SettingsRowModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => SettingsRowModel());

    // On component load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      safeSetState(() {});
    });
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
   
    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        InkWell(
          splashColor: Colors.transparent,
          focusColor: Colors.transparent,
          hoverColor: Colors.transparent,
          highlightColor: Colors.transparent,
          onTap: () async {
            
           
            if (functions.isEntitled(
                (currentUserDocument?.gender == Gender.Female
                        ? widget.settingsScreen!.eligibleFemaleEntitlements
                        : widget.settingsScreen!.eligibleMaleEntitlements)
                    .toList(),
                revenue_cat.activeEntitlementIds.toList())) {
                  //  log(widget.settingsScreen?.title ?? '');
                  //  log(widget.settingsScreen?.name ?? '');
                  //  log(widget.settingsScreen!.type.toString() );
                   log(widget.settingsScreen!.eligibleFemaleEntitlements.toString());
                  //  log(widget.searchPrefs!.toString());
              GoRouter.of(context).pushNamed(
                'GenericPreferencesDetailPage',
                queryParameters: {
                  'name': serializeParam(
                    widget.settingsScreen?.name,
                    ParamType.String,
                  ),
                  'editorConfig': serializeParam(
                    functions.getSearchPrefScreenConfigFromRemoteConfigString(
                        widget.settingsScreen?.name ?? ''),
                    ParamType.DataStruct,
                  ),
                  'kind': serializeParam(
                    widget.settingsScreen?.type,
                    ParamType.Enum,
                  ),
                  'title': serializeParam(
                    widget.settingsScreen?.title,
                    ParamType.String,
                  ),
                  'searchPrefScreen': serializeParam(
                    widget.settingsScreen,
                    ParamType.DataStruct,
                  ),
                  'searchPref': serializeParam(
                    widget.searchPrefs,
                    ParamType.DataStruct,
                  ),
                }.withoutNulls,
              );
            } else {
              if (!functions.isEntitled(
                  (currentUserDocument?.gender == Gender.Female
                          ? widget.settingsScreen!.eligibleFemaleEntitlements
                          : widget.settingsScreen!.eligibleMaleEntitlements)
                      .toList(),
                  revenue_cat.activeEntitlementIds.toList())) {
                if (currentUserDocument?.gender == Gender.Male
                    ? widget.settingsScreen!.eligibleMaleEntitlements
                        .contains('paid_standard_1w')
                    : false) {
                  analytics.logEvent(
                      'Navigated to Standard from Search Preferences');
                  context.pushNamed('ChyrpeStandardNewNew');
                } else if (() {
                  if (currentUserDocument?.gender == Gender.Male) {
                    return widget.settingsScreen!.eligibleMaleEntitlements
                        .contains('plus_access');
                  } else if (currentUserDocument?.gender == Gender.Female) {
                    return widget.settingsScreen!.eligibleFemaleEntitlements
                        .contains('plus_access');
                  } else {
                    return false;
                  }
                }()) {
                  analytics
                      .logEvent('Navigated to Plus from Search Preferences');
                  context.pushNamed('PlusSubscriptionNew');
                } else if (() {
                  if (currentUserDocument?.gender == Gender.Male) {
                    return widget.settingsScreen!.eligibleMaleEntitlements
                        .contains('gold_access');
                  } else if (currentUserDocument?.gender == Gender.Female) {
                    return widget.settingsScreen!.eligibleFemaleEntitlements
                        .contains('gold_access');
                  } else {
                    return false;
                  }
                }()) {
                  analytics
                      .logEvent('Navigated to Gold from Search Preferences');
                  context.pushNamed('GoldSubscriptionNew');
                } else if (() {
                  if (currentUserDocument?.gender == Gender.Male) {
                    return widget.settingsScreen!.eligibleMaleEntitlements
                        .contains('evolved_access');
                  } else if (currentUserDocument?.gender == Gender.Female) {
                    return widget.settingsScreen!.eligibleFemaleEntitlements
                        .contains('evolved_access');
                  } else {
                    return false;
                  }
                }()) {
                  if (currentUserDocument?.gender == Gender.Female &&
                      valueOrDefault(currentUserDocument?.fiveTestGroup, 0) >
                          valueOrDefault(
                              getRemoteConfigInt('divine_legacy_t'), 0)) {
                    analytics.logEvent(
                        'Navigated to Divine from Search Preferences');
                    context.pushNamed(
                      'DivineSubscription',
                      extra: <String, dynamic>{
                        kTransitionInfoKey: const TransitionInfo(
                          hasTransition: true,
                          transitionType: PageTransitionType.bottomToTop,
                          duration: Duration(milliseconds: 200),
                        ),
                      },
                    );
                  } else {
                    analytics.logEvent(
                        'Navigated to Evolved from Search Preferences');
                    context.pushNamed(
                      'EvolvedSubscriptionNew',
                    );
                  }
                } else if (currentUserDocument?.gender == Gender.Female
                    ? widget.settingsScreen!.eligibleFemaleEntitlements
                        .contains('divine_access')
                    : false) {
                  if (currentUserDocument?.gender == Gender.Female &&
                      valueOrDefault(currentUserDocument?.fiveTestGroup, 0) >
                          valueOrDefault(
                              getRemoteConfigInt('divine_legacy_t'), 0)) {
                    analytics.logEvent(
                        'Navigated to Divine from Search Preferences');
                    context.pushNamed(
                      'DivineSubscription',
                      extra: <String, dynamic>{
                        kTransitionInfoKey: const TransitionInfo(
                          hasTransition: true,
                          transitionType: PageTransitionType.bottomToTop,
                          duration: Duration(milliseconds: 200),
                        ),
                      },
                    );
                  } else {
                    analytics.logEvent(
                        'Navigated to Evolved from Search Preferences');
                    context.pushNamed(
                      'EvolvedSubscriptionNew',
                    );
                  }
                }
              }
            }
          },
          child: Container(
            width: MediaQuery.sizeOf(context).width * 1.0,
            height: 55.0,
            decoration: const BoxDecoration(
              color: Color(0x00FFFFFF),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        valueOrDefault<String>(
                          widget.settingsScreen?.title,
                          'Filter',
                        ),
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'BT Beau Sans',
                              fontSize: 13.0,
                              letterSpacing: 0.0,
                              fontWeight: FontWeight.w500,
                              useGoogleFonts: false,
                            ),
                      ),
                      Text(
                        valueOrDefault<String>(
                          () {
                            if (widget.searchPrefs!.hasLowerReq() &&
                                widget.searchPrefs!.hasUpperReq()) {
                              if (widget.searchPrefs!.lowerReq == 0 &&
                                  widget.searchPrefs!.upperReq == 0) {
                                return "Open to all";
                              } else if (widget.settingsScreen?.name ==
                                  'height') {
                                if (widget.searchPrefs!.lowerReq <= 0 &&
                                    widget.searchPrefs!.upperReq >= 250) {
                                  return "Open to all";
                                }
                                if (currentUserDocument?.imperialUnits ??
                                    false) {
                                  return '${convertCmToFeetAndInches(widget.searchPrefs!.lowerReq)} - ${convertCmToFeetAndInches(widget.searchPrefs!.upperReq)}';
                                } else {
                                  return '${(widget.searchPrefs!.lowerReq)} - ${(widget.searchPrefs!.upperReq)} cm';
                                }
                              }
                              return '${formatNumber(
                                widget.searchPrefs?.lowerReq,
                                formatType: FormatType.custom,
                                format: '##',
                                locale: '',
                              )} - ${formatNumber(
                                widget.searchPrefs?.upperReq,
                                formatType: FormatType.custom,
                                format: '##',
                                locale: '',
                              )}';
                            } else if (widget.searchPrefs!.hasLowerReq()) {
                              if (widget.searchPrefs!.hasStringReq()) {
                                return widget.searchPrefs?.stringReq ??
                                    'Open to all';
                              }
                              return '${widget.searchPrefs?.lowerReq.round().toString()}';
                            } else if (widget.searchPrefs!.hasUpperReq()) {
                              if (widget.searchPrefs!.hasStringReq()) {
                                return widget.searchPrefs?.stringReq ??
                                    'Open to all';
                              }
                              return '${widget.searchPrefs?.upperReq.round().toString()}';
                            } else if (widget.searchPrefs!.hasStringReq()) {
                              return widget.searchPrefs?.stringReq;
                            } else if (widget.searchPrefs!.hasStringReqs()) {
                              return ((List<String> inputStrings) {
                                return inputStrings.join(', ');
                              }(widget.searchPrefs!.stringReqs.toList()));
                            } else {
                              return 'Open to all';
                            }
                          }(),
                          'Open to all',
                        ).maybeHandleOverflow(
                          maxChars: 25,
                          replacement: '…',
                        ),
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'BT Beau Sans',
                              color: FlutterFlowTheme.of(context).secondaryText,
                              fontSize: 13.0,
                              letterSpacing: 0.0,
                              useGoogleFonts: false,
                            ),
                      ),
                    ].divide(const SizedBox(height: 8.0)),
                  ),
                ),
                Row(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    if (widget.searchPrefs!.dealbreaker &&
                        functions.isEntitled(
                            (currentUserDocument?.gender == Gender.Female
                                    ? widget.settingsScreen!
                                        .eligibleFemaleEntitlements
                                    : widget.settingsScreen!
                                        .eligibleMaleEntitlements)
                                .toList(),
                            revenue_cat.activeEntitlementIds.toList()))
                      AuthUserStreamWidget(
                        builder: (context) => Text(
                          'Dealbreaker',
                          style:
                              FlutterFlowTheme.of(context).bodyMedium.override(
                                    fontFamily: 'BT Beau Sans',
                                    fontSize: 13.0,
                                    letterSpacing: 0.0,
                                    useGoogleFonts: false,
                                  ),
                        ),
                      ),
                    Builder(
                      builder: (context) {
                        if (functions.isEntitled(
                            (currentUserDocument?.gender == Gender.Female
                                    ? widget.settingsScreen!
                                        .eligibleFemaleEntitlements
                                    : widget.settingsScreen!
                                        .eligibleMaleEntitlements)
                                .toList(),
                            revenue_cat.activeEntitlementIds.toList())) {
                          return Icon(
                            Icons.chevron_right,
                            color: FlutterFlowTheme.of(context).primaryText,
                            size: 24.0,
                          );
                        } else {
                          return Icon(
                            Icons.lock_outline,
                            color: FlutterFlowTheme.of(context).secondaryText,
                            size: 24.0,
                          );
                        }
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        Divider(
          height: 5.0,
          thickness: 1.0,
          color: FlutterFlowTheme.of(context).secondaryBackground,
        ),
      ],
    );
  }
}
