import 'dart:convert';
import 'dart:math' as math;

import 'package:intl/intl.dart';
import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
<<<<<<< HEAD
import '/flutter_flow/firebase_remote_config_util.dart';
=======
import 'firebase_remote_config_util.dart';
>>>>>>> 7f271f2b8 (Solve roles page error, Solve for empty list data for gender preferences, Get dev alternativePackageId for gold and evolved)

double getRandomNumber() {
  final random = math.Random();
  return random.nextDouble() * 100;
}

List<PromptAndAnswerStruct> changePromptAtNoCopy(
  List<PromptAndAnswerStruct> oldPrompts,
  String newPrompt,
  String newAnswer,
  int numberToChange,
) {
  var index = numberToChange - 1;

  oldPrompts[index].prompt = newPrompt;
  oldPrompts[index].answer = newAnswer;

  return oldPrompts;
}

LatLng getLondonLocation() {
  // The function returns the coordinates of Charing Cross
  return const LatLng(51.5074, -0.1278);
}

DateTime get2weeksAgo() {
  DateTime now = DateTime.now();

  // Subtract 24 hours
  DateTime twentyFourHoursAgo = now.subtract(const Duration(hours: 336));

  return twentyFourHoursAgo;
}

DateTime get24hAgo() {
  DateTime now = DateTime.now();

  // Subtract 24 hours
  DateTime twentyFourHoursAgo = now.subtract(const Duration(hours: 24));

  return twentyFourHoursAgo;
}

List<PromptAndAnswerStruct> deletePromptAtNo(
  List<PromptAndAnswerStruct> oldPrompts,
  int numberToChange,
) {
  var index = numberToChange - 1;

  oldPrompts.removeAt(index);

  return oldPrompts;
}

DateTime getCurrentDayFunc() {
  // Get the current date and time
  DateTime now = DateTime.now();

  // Create a new DateTime object for the current day at 12am (midnight)
  DateTime currentDayAtMidnight = DateTime(now.year, now.month, now.day);

  return currentDayAtMidnight;
}

String newCustomFunction(String cityName) {
  return cityName;
}

DateTime getFeedbackAskDuration2() {
  // returns time five days ago
  return DateTime.now().subtract(const Duration(days: 14));
}

DateTime getFeedbackAskDuration3() {
  // returns time five days ago
  return DateTime.now().subtract(const Duration(days: 122));
}

String? getLocString(LatLng latLng) {
  // convert the latLng argument into a string of format "51.534,34.424"
  return '${latLng.latitude.toStringAsFixed(3)},${latLng.longitude.toStringAsFixed(3)}';
}

int? getTimeInMS(DateTime target) {
  final DateTime now = DateTime.now(); // Get the current date and time
  final Duration difference = target
      .difference(now); // Calculate the difference between target time and now

  if (difference.isNegative) {
    return 0; // If the target time is in the past, return null
  }

  return difference.inMilliseconds; // Return the difference in milliseconds
}

int getNextNPS(DateTime lastDate) {
  DateTime fiveDaysLater = lastDate.add(const Duration(days: 5));
  return fiveDaysLater.millisecondsSinceEpoch;
}

DateTime getMonday() {
  // Get the current week's Monday at 0 o'clock
  DateTime now = DateTime.now();
  int daysSinceMonday = now.weekday - 1;
  if (daysSinceMonday < 0) {
    daysSinceMonday += 7;
  }
  DateTime monday = now.subtract(Duration(days: daysSinceMonday));
  return DateTime(monday.year, monday.month, monday.day, 0, 0, 0);
}

DateTime getGold2FreeDurationAgo() {
  // get the time of 39.5 days ago
  return DateTime.now().subtract(const Duration(days: 49));
}

List<ChyrpeMessagesStruct>? combineMessages(
  List<MessagesRecord>? directMessages,
  List<BroadcastMessagesRecord>? broadcastMessages,
) {
  List<ChyrpeMessagesStruct> combinedMessages = [];

  // Convert DirectMessages to ChyrpeMessagesStruct
  if (directMessages != null) {
    combinedMessages.addAll(directMessages.map((dm) => ChyrpeMessagesStruct(
          body: dm.body,
          sender: dm.sender,
          time: dm.time,
          sysMessageReceiver:
              dm.sysMessageReceiver ?? [], // Handling possible null
          isSysMessage: dm.isSysMessage,
        )));
  }

  // Convert BroadcastMessages to ChyrpeMessagesStruct
  if (broadcastMessages != null) {
    combinedMessages.addAll(broadcastMessages.map((bm) => ChyrpeMessagesStruct(
          body: bm.body,
          sender: bm.sender,
          time: bm.time,
          sysMessageReceiver:
              bm.sysMessageReceiver ?? [], // Handling possible null
          isSysMessage: bm.isSysMessage,
        )));
  }

  // Sort combinedMessages by time in descending order
  combinedMessages.sort((a, b) => b.time!.compareTo(a.time!));

  return combinedMessages;
}

bool isSameWeek(
  DateTime serverTime,
  DateTime ntpTime,
) {
  // Start of week is considered as Monday (you can change this to Sunday if required by setting it to 7)
  int startOfWeek = DateTime.monday;

  // Function to normalize a date to the beginning of its week
  DateTime normalizeToStartOfWeek(DateTime dateTime) {
    return DateTime(
      dateTime.year,
      dateTime.month,
      dateTime.day - (dateTime.weekday - startOfWeek) % 7,
    );
  }

  // Normalize both dates to the start of their respective weeks
  final startOfLocalWeek = normalizeToStartOfWeek(serverTime);
  final startOfNtpWeek = normalizeToStartOfWeek(ntpTime);

  // Return true if both dates fall on the same start of the week
  return startOfLocalWeek == startOfNtpWeek;
}

DateTime getUtc(DateTime inputTime) {
  return inputTime.toUtc();
}

DocumentReference getUserReference(String uid) {
  return FirebaseFirestore.instance.collection('users').doc(uid);
}

List<String> getGenderOptions(String optionsArray) {
  List<String> list =
      optionsArray.substring(1, optionsArray.length - 1).split(',').toList();

  return list;
}

FreePaidTrialTestCohorts getRandomFreePaidTestCohort(List<double> chances) {
  try {
    // Return one of the 4 freePaidTrialTestCohorts according to the four probabilities given
    if (chances.length != 4) {
      FreePaidTrialTestCohorts.list_norm;
    }

    final random = math.Random();
    final total = chances.reduce((a, b) => a + b);
    var randomValue = random.nextDouble() * total;
    for (var i = 0; i < chances.length; i++) {
      if (randomValue < chances[i]) {
        return FreePaidTrialTestCohorts.values[i];
      }
      randomValue -= chances[i];
    }
    return FreePaidTrialTestCohorts.list_norm; // default value
  } catch (e) {
    return FreePaidTrialTestCohorts.list_norm;
  }
}

DateTime getGold1FreeDurationAgo() {
  // get the time of 4.5 days prior to now and return it
  return DateTime.now().subtract(const Duration(days: 3, hours: 0));
}

DateTime getFeedbackAskDuration1() {
  // returns time five days ago
  return DateTime.now().subtract(const Duration(hours: 108));
}

DateTime getTimeForFeedbackChat() {
  // returns the time one minute ago
  return DateTime.now().subtract(const Duration(seconds: 20));
}

String getLastMonday() {
  // last monday's date as string
  final now = DateTime.now();
  final lastMonday = now.subtract(Duration(days: now.weekday - 1));
  final formatter = DateFormat('yyyyMMdd');
  return formatter.format(lastMonday);
}

List<FreeStandardTableRowStruct> getFreeStandardTableRowFromJson(
    String inputString) {
  // Cast input string as JSON and that JSON as FreeStandardTableRow object
  final List<dynamic> jsonList = json.decode(inputString);
  final List<FreeStandardTableRowStruct> resultList =
      jsonList.map((json) => FreeStandardTableRowStruct.fromMap(json)).toList();
  return resultList;
}

List<String> getStringListFromJson(String inputString) {
<<<<<<< HEAD
=======
  
  
  // Cast the input string as a JSON list and then return it as list of Strings
  List<dynamic> jsonList = json.decode(inputString)['english'];
// print("Is it this : $jsonList");
  List<String> stringList = jsonList.map((item) => item.toString()).toList();
  return stringList;
}

List<String> getStringNoLangListFromJson(String inputString) {
  
>>>>>>> 7f271f2b8 (Solve roles page error, Solve for empty list data for gender preferences, Get dev alternativePackageId for gold and evolved)
  // Cast the input string as a JSON list and then return it as list of Strings
  List<dynamic> jsonList = json.decode(inputString);
  List<String> stringList = jsonList.map((item) => item.toString()).toList();
  return stringList;
}

List<double> getNumberArrayFromJsonString(String inputString) {
  // get input string, cast as json, cast that as list of doubles and return
  List<dynamic> jsonList = json.decode(inputString);
  List<double> doubleList = jsonList.cast<double>();
  return doubleList;
}

SignUpTestingCohort getRandomSignUpTestingCohort(List<double> chances) {
  try {
    // Return one of the 4 freePaidTrialTestCohorts according to the four probabilities given
    if (chances.length != 2) {
      SignUpTestingCohort.NoConfirmation;
    }

    final random = math.Random();
    final total = chances.reduce((a, b) => a + b);
    var randomValue = random.nextDouble() * total;
    for (var i = 0; i < chances.length; i++) {
      if (randomValue < chances[i]) {
        return SignUpTestingCohort.values[i];
      }
      randomValue -= chances[i];
    }
    return SignUpTestingCohort.NoConfirmation; // default value
  } catch (e) {
    return SignUpTestingCohort.NoConfirmation;
  }
}

List<PurchaseDurationStruct> getPurchaseDurationStructFromString(
    String inputString) {

      print(inputString);
  final List<dynamic> jsonList = json.decode(inputString)['english'];

  List<PurchaseDurationStruct> purchaseDurationList = jsonList.map((jsonItem) {
    String duration = jsonItem['duration'];
    String packageId = jsonItem['packageId'];
    int durationWeeks = jsonItem['durationWeeks'] ?? 0;
    String weeklyAlternativeId = jsonItem['weeklyAlternativeId'] ?? '';

    return PurchaseDurationStruct(
        duration: duration,
        packageId: packageId,
        durationWeeks: durationWeeks,
        weeklyAlternativeId: weeklyAlternativeId);
  }).toList();

  return purchaseDurationList;
}

List<PurchaseTypesHome> getHomePackagesList(
  List<String> availablePackages,
  List<String> activeEntitlements,
  Gender gender,
  bool profileHomeDualUpgradeVisible,
  bool profileHomeLifetimeUpgradeVisible,
) {
  // Initialize the list of purchase options to return
  List<PurchaseTypesHome> purchaseOptions = [];

  // Condition for Standard Purchase Option
  bool showStandard = gender == Gender.Male &&
      !(activeEntitlements.contains('paid_standard_lifetime') ||
          activeEntitlements.contains('paid_standard_1w')) &&
      availablePackages.contains('chyrpe_standard_lifetime') &&
      availablePackages.contains('chyrpe_standard_1w') &&
      profileHomeDualUpgradeVisible;

  if (showStandard) {
    purchaseOptions.add(PurchaseTypesHome.Standard);
  }

  // Condition for Standard Lifetime Purchase Option
  bool showStandardLifetime = activeEntitlements.contains('paid_standard_1w') &&
      gender == Gender.Male &&
      availablePackages.contains('chyrpe_standard_lifetime') &&
      profileHomeLifetimeUpgradeVisible &&
      !activeEntitlements.contains('paid_standard_lifetime');

  if (showStandardLifetime) {
    purchaseOptions.add(PurchaseTypesHome.StandardLifetime);
  }

  // Condition for Plus Purchase Option
  bool showPlus = !(activeEntitlements.contains('plus_access') ||
      activeEntitlements.contains('evolved_access'));

  if (showPlus) {
    purchaseOptions.add(PurchaseTypesHome.Plus);
  }

  // Condition for Gold Purchase Option
  bool showGold = !(activeEntitlements.contains('gold_access') ||
      activeEntitlements.contains('evolved_access'));

  if (showGold) {
    purchaseOptions.add(PurchaseTypesHome.Gold);
  }

  // Condition for Evolved Purchase Option
  bool showEvolved = !activeEntitlements.contains('evolved_access');

  if (showEvolved) {
    purchaseOptions.add(PurchaseTypesHome.Evolved);
  }

  // Return the list of purchase options
  return purchaseOptions;
}

List<LikesRecord>? getLikesExcept1(
  List<LikesRecord>? fullList,
  String gender,
) {
  /// MODIFY CODE ONLY BELOW THIS LINE

  if (fullList == null || fullList.isEmpty) {
    return [];
  }

  if (gender == "Female") {
    return fullList.sublist(1);
  } else {
    return fullList;
  }
}

AnnouncementStruct? getAnnouncementFromString(
  String cString,
  String wlRegion,
  String gender,
  bool bGroup,
  List<String> previousClosedIds,
) {
  // Decode the JSON string into a list of dynamic objects
  // Decode the JSON string into a list of dynamic objects
  List<dynamic> decodedJson = jsonDecode(cString);

  // Map the decoded objects to a list of AnnouncementStruct
  List<AnnouncementStruct> announcements =
      decodedJson.map((e) => AnnouncementStruct.fromMap(e)).toList();

  // Iterate over the list of announcements to find a matching one
  for (var announcement in announcements) {
    // Check if the gender matches or if it's meant for everyone
    bool genderMatch = announcement.genders.contains('Everyone') ||
        announcement.genders.contains(gender);

    // Check if the bGroup matches
    bool bGroupMatch = announcement.bGroup == bGroup;

    // Ensure the announcement ID is not in the list of closed IDs
    bool idNotClosed = !previousClosedIds.contains(announcement.id);

    // Check if the region matches or if it's meant for everywhere
    bool regionMatch = announcement.regions.contains('Everywhere') ||
        announcement.regions.contains(wlRegion);

    // If all conditions are satisfied, return this announcement
    if (genderMatch && bGroupMatch && idNotClosed && regionMatch) {
      return announcement;
    }
  }

  // If no announcement matches, return null
  return null;
}

int get30MinFuture() {
  // Returns the unix timestamp of now + 30 minutes
  DateTime now = DateTime.now();
  DateTime future = now.add(const Duration(minutes: 30));
  return future.millisecondsSinceEpoch;
}

bool isEvening(int eligibleHour) {
  // Checks and returns if the user's local time is after 8pm
  DateTime now = DateTime.now();
  int hour = now.hour;
  return hour >= eligibleHour;
}

String getDateIn5Days() {
  DateTime getDateIn5Days() {
    final now = DateTime.now();
    final fiveDaysLater = now.add(const Duration(days: 5));
    return fiveDaysLater;
  }

  final dateIn5Days = getDateIn5Days();
  final formattedDate = DateFormat('MMMM, dd').format(dateIn5Days);
  return formattedDate;
}

bool readReceiptButtonEligibility(String body) {
  return body.contains("matched on");
}

String? getTimeLeft24h(DateTime timestamp) {
  // Take input timestamp, add 1 day, return hours left till then
  DateTime tomorrow = timestamp.add(const Duration(days: 1));
  DateTime now = DateTime.now();
  Duration timeLeft = tomorrow.difference(now);
  return '${timeLeft.inHours}h left';
}

DiscountStruct getDiscountFromString(String inputString) {
  final Map<String, dynamic> decodedJson = jsonDecode(inputString);
  return DiscountStruct.fromMap(decodedJson);
}

FlexiPopupDiscoveryStruct getFlexiPopupFromJson(String inputString) {
  final Map<String, dynamic> data = jsonDecode(inputString);
  // Create an instance of FlexiPopupDiscoveryStruct from the Map
  return FlexiPopupDiscoveryStruct.fromMap(data);
}

String? getValueForKeyFromDict(
  String key,
  String? jsonString,
) {
  // Parses a string as a JSON and retrieves the string value for a certain key
  if (jsonString == null) {
    return "Please tell us more";
  }

  try {
    Map<String, dynamic> jsonMap = json.decode(jsonString);
    return jsonMap[key];
  } catch (e) {
    return "Please tell us more";
  }
}

bool appFeedbackChatProbability(double inputProbability) {
  // Returns true with input probability
  final random = math.Random();
  return random.nextDouble() < inputProbability;
}

List<PublicProfileUIDStruct> getPublicProfileUidFromJSON(String inputString) {
  try {
    final List<dynamic> jsonList = jsonDecode(inputString) as List<dynamic>;

    return jsonList.map((jsonItem) {
      if (jsonItem is Map<String, dynamic>) {
        final publicProfilePath = jsonItem['publicProfile'] as String?;
        final DocumentReference? publicProfile = publicProfilePath != null
            ? FirebaseFirestore.instance.doc(publicProfilePath)
            : null;
        final uid = jsonItem['uid'] as String?;

        return PublicProfileUIDStruct(
          publicProfile: publicProfile,
          uid: uid,
        );
      } else {
        throw FormatException("Invalid JSON format for item: $jsonItem");
      }
    }).toList();
  } catch (e) {
    // Handle JSON decoding or other errors
    return [];
  }
}

List<SymbolicBenefitlistStruct> getSymbolicBenefitlistFromJson(
    String inputString) {
// Return SymbolicBenefitlist parsed from a string that is a JSON, if fails, returns an empty list
  try {
    List<dynamic> jsonList = jsonDecode(inputString);
    List<SymbolicBenefitlistStruct> symbolicBenefitlist =
        jsonList.map((e) => SymbolicBenefitlistStruct.fromMap(e)).toList();
    return symbolicBenefitlist;
  } catch (e) {
    return [];
  }
}

String getImageFromString(String inputString) {
  return inputString;
}

List<int> getIntListFromJson(String inputString) {
  // Decode the input string that is a json into a list of integers, in case of failure return empty list
  try {
    List<dynamic> jsonList = jsonDecode(inputString);
    List<int> intList = jsonList.map((e) => e as int).toList();
    return intList;
  } catch (e) {
    return [];
  }
}

List<SignupScreenMapStruct> getSignupMapFromJson(String inputString) {
// parses a string that is a json into a list of signupscreenmap objects and returns them
  List<dynamic> jsonList = json.decode(inputString);
  List<SignupScreenMapStruct> signupScreenMapList =
      jsonList.map((json) => SignupScreenMapStruct.fromMap(json)).toList();
  return signupScreenMapList;
}

List<SearchPrefScreenMapStruct> getSettingsMapFromJson(String inputString) {
// parses a string that is a json into a list of signupscreenmap objects and returns them
  List<dynamic> jsonList = json.decode(inputString);
  List<SearchPrefScreenMapStruct> signupScreenMapList =
      jsonList.map((json) => SearchPrefScreenMapStruct.fromMap(json)).toList();
  return signupScreenMapList;
}

SignupScreenStruct getSignupConfigFromRemoteConfigString(String screenTitle) {
  // Retrieves a remote config string with the key editor_config_{screenTitle}, parses the string as JSON and the JSON as SignupScreen, returns this. Returns an empty object in case of fail.
  // Retrieve remote config string
  String remoteConfigString = getRemoteConfigString(
      'editor_config_$screenTitle'); // Assume this is the remote config string retrieved from the server

  // Parse the string as JSON
  Map<String, dynamic> jsonMap = {};
  try {
    jsonMap = json.decode(remoteConfigString);
  } catch (e) {
    return SignupScreenStruct(); // Return empty object in case of fail
  }

  // Parse the JSON as SignupScreen
  SignupScreenStruct signupScreen = SignupScreenStruct.fromMap(jsonMap);

  return signupScreen;
}

List<EditScreenCategoryMapStruct> getEditorConfigFromRemoteConfigString(
    String inputString) {
  try {
    // Decode a string that is a json into a list of EditScreenCategoryMap objects and return them
    List<dynamic> jsonList = json.decode(inputString);
    List<EditScreenCategoryMapStruct> result = jsonList
        .map((json) => EditScreenCategoryMapStruct.fromMap(json))
        .toList();
    return result;
  } catch (e) {
    return [];
  }
}

SearchPrefSelectorScreenStruct getSearchPrefScreenConfigFromRemoteConfigString(
    String screenTitle) {
  // Retrieves a remote config string with the key editor_config_{screenTitle}, parses the string as JSON and the JSON as SignupScreen, returns this. Returns an empty object in case of fail.
  // Retrieve remote config string

  String remoteConfigString = '';
  bool exists = false;

  if (getRemoteConfigString('search_config_$screenTitle') != '') {
    remoteConfigString = getRemoteConfigString('search_config_$screenTitle');
    exists = true;
  } else {
    remoteConfigString = getRemoteConfigString('search_config_$screenTitle');
    exists = false;
  }

  // Parse the string as JSON
  Map<String, dynamic> jsonMap = {};
  try {
    jsonMap = json.decode(remoteConfigString);
  } catch (e) {
    return SearchPrefSelectorScreenStruct(); // Return empty object in case of fail
  }
  

  // Parse the JSON as SignupScreen
  SearchPrefSelectorScreenStruct signupScreen =
      SearchPrefSelectorScreenStruct.fromMap(jsonMap);

  if (!exists) {
    signupScreen.infoBtnTitle =
        getRemoteConfigString('search_generic_info_btn');
  }

  return signupScreen;
}


/// Note if you are looking for how data is populated in [RadioScreenSearchPrefWidget] or [CheckboxScreenSearchPrefWidget] this [getSearchOptions]
/// Is responsible for fetching the data to populate the page from [FirebaseRemoteConfig]
List<String> getSearchOptions(String screenTitle) {
  // Cast the input string as a JSON list and then return it as list of Strings
  try {
    String inputString = '';
    bool exists = false;

    if (getRemoteConfigString('search_options_$screenTitle') != '') {
      inputString = getRemoteConfigString('search_options_$screenTitle');
     
      exists = true;
    } else {
      inputString = getRemoteConfigString('options_$screenTitle');
      exists = false;
    }



    List<dynamic> jsonList = json.decode(inputString)['english'];

  
    List<String> stringList = jsonList
        .map((item) => item.toString())
        .where(
            (item) => item != "Prefer not to say") // Remove "Prefer not to say"
        .toList();
    if (!stringList.contains("Everybody")) {
      stringList.add(getRemoteConfigString("search_generic_default_option"));
    } // Append "Open to all"
    return stringList;
  } catch (e) {
    return [];
  }
}

bool isEntitled(
  List<String> eligibleEntitlements,
  List<String> userEntitlements,
) {
  if (eligibleEntitlements.contains("all")) {
    return true;
  }

  // Compares if the two arrays share at least one common string
  for (String eligibleEntitlement in eligibleEntitlements) {
    if (userEntitlements.contains(eligibleEntitlement)) {
      return true;
    }
  }
  return false;
}

List<ProfileDetailItemStruct> getProfileDetailOrder(String inputString) {
  try {
    // Decodes input string as a json into a list of profiel detail objects
    List<ProfileDetailItemStruct> profileDetailList = [];
    List<dynamic> jsonList = jsonDecode(inputString);
    for (var item in jsonList) {
      ProfileDetailItemStruct profileDetail =
          ProfileDetailItemStruct.fromMap(item);
      profileDetailList.add(profileDetail);
    }
    return profileDetailList;
  } catch (e) {
    return [];
  }
}

List<ProfileDetailInfoStruct> getProfileDetailInfoOrder(String inputString) {
  try {
    List<ProfileDetailInfoStruct> profileDetailInfoList = [];
    List<dynamic> jsonList = json.decode(inputString);
    for (var item in jsonList) {
      ProfileDetailInfoStruct profileDetailInfo =
          ProfileDetailInfoStruct.fromMap(item);
      profileDetailInfoList.add(profileDetailInfo);
    }
    return profileDetailInfoList;
  } catch (e) {
    return [];
  }
}

List<SearchPrefScreenStruct> getSearchPrefsFromJson(String inputString) {
  // Decodes the input string that is a json into a list of search pref screens and returns them, on fail returns empty list
  try {
    List<dynamic> jsonList = json.decode(inputString);
    List<SearchPrefScreenStruct> searchPrefScreens =
        jsonList.map((json) => SearchPrefScreenStruct.fromMap(json)).toList();
    return searchPrefScreens;
  } catch (e) {
    return [];
  }
}

KinkStruct getKinkStruct(KinksRecord kink, UsersRecord user) {
  try {
    // Create base structure from the default kink record
    KinkStruct returnStruct = KinkStruct(
      fallbackDescription: kink.fallbackDescription,
      fallbackTitle: kink.fallbackTitle,
      id: kink.id,
      active: kink.active,
      image: kink.image,
    );

    // Navigate through Firestore snapshot data dynamically
    dynamic currentData = kink.snapshotData;

    List<String?> keys = [user.gender?.toString(), user.position?.toString(), user.genderReq?.toString()];

    for (var key in keys) {
      if (key != null && currentData is Map && currentData.containsKey(key)) {
        currentData = currentData[key];
      } else {
        break; // Stop traversing if the path does not exist
      }
    }

    // If we found a valid map, update fallback fields
    if (currentData is Map) {
      returnStruct.fallbackDescription = currentData["fallbackDescription"] ?? returnStruct.fallbackDescription;
      returnStruct.fallbackTitle = currentData["fallbackTitle"] ?? returnStruct.fallbackTitle;
    }

    return returnStruct;
  } catch (e) {
    return KinkStruct(); // Return an empty struct on failure
  }
}
