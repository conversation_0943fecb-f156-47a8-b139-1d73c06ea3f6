import 'package:firebase_remote_config/firebase_remote_config.dart';

Future initializeFirebaseRemoteConfig() async {
  await FirebaseRemoteConfig.instance.setConfigSettings(RemoteConfigSettings(
    fetchTimeout: const Duration(minutes: 1),
    minimumFetchInterval: const Duration(hours: 1),
  ));
  await FirebaseRemoteConfig.instance.setDefaults(const {
    'findom_info_title': '''
{
"english": 'About findom on chyrpe',
"french": 'À propos de findom sur Chyrpe',
"german": 'Über Findom auf Chyrpe',
"spanish": 'Acerca de findom en Chyrpe',
}
''',
    'phone_blocking_info_subtitle_1': '''
{

"english": "What happens to the data if I use the blocking phone numbers feature?",
"french": "Que se passe-t-il avec les données si tu utilises la fonction de blocage des numéros de téléphone ?",
"german": "Was passiert mit den Daten, wenn ich die Funktion zum Blockieren von Telefonnummern benutze?",
"spanish": "¿Qué pasa con los datos si uso la función de bloqueo de números de teléfono?",

}
''',
    'phone_blocking_info_subtitle_2': '''
{
"english": "How reliable is the process?",
"french": "À quel point le processus est-il fiable ?",
"german": "Wie zuverlässig ist der Prozess?",
"spanish": "¿Qué tan confiable es el proceso?"
}
''',
    'phone_blocking_info_subbody_2': '''
{
"english": "We use the industry-standard, state-of-the-art process to try our best and block the contacts you select. This works in most cases but can be more prone to error if you use a highly uncommon format for storing that contact's phone number. We also cannot block them if that person uses a second phone number you haven't blocked for their chyrpe account.",
"french": "Nous utilisons un processus de pointe, conforme aux normes de l'industrie, pour faire de notre mieux et bloquer les contacts que tu sélectionnes. Cela fonctionne dans la plupart des cas, mais peut être plus sujet à des erreurs si tu utilises un format très inhabituel pour enregistrer le numéro de téléphone de ce contact. Nous ne pouvons pas non plus les bloquer si cette personne utilise un deuxième numéro de téléphone que tu n'as pas bloqué pour leur compte Chyrpe.",
"german": "Wir verwenden einen branchenüblichen, hochmodernen Prozess, um unser Bestes zu tun und die von dir ausgewählten Kontakte zu blockieren. Dies funktioniert in den meisten Fällen, kann jedoch fehleranfälliger sein, wenn du ein ungewöhnliches Format zur Speicherung der Telefonnummer dieses Kontakts verwendest. Außerdem können wir sie nicht blockieren, wenn die Person eine zweite Telefonnummer für ihr Chyrpe-Konto verwendet, die du nicht blockiert hast.",
"spanish": "Usamos un proceso de última generación y estándar de la industria para hacer nuestro mejor esfuerzo y bloquear los contactos que selecciones. Esto funciona en la mayoría de los casos, pero puede ser más propenso a errores si usas un formato poco común para almacenar el número de teléfono de ese contacto. Tampoco podemos bloquearlos si esa persona utiliza un segundo número de teléfono que no hayas bloqueado en su cuenta de Chyrpe."
}
''',
    'phone_blocking_info_subbody_1': '''
{
"english": 'When you use this feature, we will upload only those contacts that you block to our server, using a secure and encrypted connection. On the server, the contacts are stored under the same strict privacy and security rules as your own data.  We will only ever use this data to try our best to block those contacts from seeing you on chyrpe. We will not use it for marketing purposes and we will never sell this data. As soon as you stop blocking that contact or delete your account, we will delete the data from the server.',
"french": 'Lorsque tu utilises cette fonction, nous ne téléchargeons sur notre serveur que les contacts que tu bloques, via une connexion sécurisée et chiffrée. Sur le serveur, les contacts sont stockés selon les mêmes règles strictes de confidentialité et de sécurité que tes propres données. Nous utiliserons ces données uniquement pour faire de notre mieux afin de bloquer ces contacts et les empêcher de te voir sur Chyrpe. Nous ne les utiliserons pas à des fins marketing et nous ne vendrons jamais ces données. Dès que tu arrêtes de bloquer ce contact ou que tu supprimes ton compte, nous supprimerons les données du serveur.',
"german": 'Wenn du diese Funktion nutzt, laden wir nur die Kontakte, die du blockierst, über eine sichere und verschlüsselte Verbindung auf unseren Server hoch. Auf dem Server werden die Kontakte nach denselben strengen Datenschutz- und Sicherheitsregeln gespeichert wie deine eigenen Daten. Wir verwenden diese Daten ausschließlich, um unser Bestes zu tun, um diese Kontakte daran zu hindern, dich auf Chyrpe zu sehen. Wir werden sie nicht für Marketingzwecke verwenden und niemals verkaufen. Sobald du den Kontakt nicht mehr blockierst oder dein Konto löschst, löschen wir die Daten vom Server.',
"spanish": 'Cuando usas esta función, solo subiremos a nuestro servidor los contactos que bloquees, utilizando una conexión segura y cifrada. En el servidor, los contactos se almacenan bajo las mismas estrictas reglas de privacidad y seguridad que tus propios datos. Solo utilizaremos estos datos para hacer todo lo posible por bloquear a esos contactos y que no te vean en Chyrpe. No los usaremos con fines de marketing y nunca venderemos estos datos. Tan pronto como dejes de bloquear ese contacto o elimines tu cuenta, eliminaremos los datos del servidor.'
}
''',
    'signin_issue_info_subbody1': '''
{
"english": 'Yes, just go back by pressing the back button in the upper left corner of the SMS code verification screen.',
"french": 'Oui, il te suffit de revenir en appuyant sur le bouton retour dans le coin supérieur gauche de l'écran de vérification du code SMS.',
"german": 'Ja, du kannst einfach zurückgehen, indem du auf die Zurück-Schaltfläche in der oberen linken Ecke des SMS-Code-Bestätigungsbildschirms tippst.',
"spanish": 'Sí, solo tienes que regresar presionando el botón de retroceso en la esquina superior izquierda de la pantalla de verificación del código SMS.'
}
''',
    'signin_issue_info_subbody2': '''
{
"english": "It can take up to 3 minutes for the SMS to be sent and sometimes even longer until it arrives, depending on your mobile service provider and your signal strength. If after 5 minutes, you still haven\'t received an SMS, please try it again. Please do not try again before waiting for this time. You might get blocked by our automated spam prevention systems if you try too often in a short time. In that case, you would have to wait multiple hours before retrying. If after a second attempt, you still encounter problems, please write <NAME_EMAIL> We will try our best to help you!",
"french": "L'envoi du SMS peut prendre jusqu'à 3 minutes, et parfois même plus longtemps avant d'arriver, selon ton opérateur mobile et la qualité de ton signal. Si après 5 minutes tu n'as toujours pas reçu le SMS, merci de réessayer. N'essaie pas avant d'avoir attendu ce délai. Si tu essaies trop souvent en peu de temps, tu risques d'être bloqué par nos systèmes automatiques de prévention de spam. Dans ce cas, tu devras attendre plusieurs heures avant de réessayer. Si après un deuxième essai tu rencontres toujours des problèmes, écris-nous à <EMAIL> Nous ferons de notre mieux pour t'aider !",
"german": "Es kann bis zu 3 Minuten dauern, bis die SMS gesendet wird, und manchmal sogar länger, bis sie ankommt, abhängig von deinem Mobilfunkanbieter und der Signalstärke. Wenn du nach 5 Minuten noch keine SMS erhalten hast, versuche es bitte erneut. Bitte versuche es nicht früher, da du sonst von unseren automatischen Spam-Schutzsystemen blockiert werden könntest, wenn du zu oft in kurzer Zeit versuchst. In diesem Fall müsstest du mehrere Stunden warten, bevor du es erneut versuchen kannst. Wenn du auch beim zweiten Versuch Probleme hast, schreibe uns <NAME_EMAIL> Wir werden unser Bestes tun, um dir zu helfen!",
"spanish": "Puede tardar hasta 3 minutos en enviarse el SMS y, a veces, incluso más tiempo en llegar, dependiendo de tu proveedor de servicios móviles y la calidad de tu señal. Si después de 5 minutos aún no has recibido el SMS, por favor intenta de nuevo. No intentes antes de esperar este tiempo. Podrías ser bloqueado por nuestros sistemas automáticos de prevención de spam si intentas demasiadas veces en poco tiempo. En ese caso, tendrías que esperar varias horas antes de volver a intentarlo. Si después de un segundo intento sigues teniendo problemas, por favor escrí<NAME_EMAIL> ¡Haremos todo lo posible por ayudarte!"
}
''',
    'signin_issue_info_subtitle1': '''
{
"english": 'I accidentally put in the wrong phone number. Can I change that?',
"french": 'J'ai accidentellement entré le mauvais numéro de téléphone. Est-ce que je peux le changer ?',
"german": 'Ich habe versehentlich die falsche Telefonnummer eingegeben. Kann ich das ändern?',
"spanish": 'J'ai accidentellement entré le mauvais numéro de téléphone. Est-ce que je peux le changer ?'
}
''',
    'signin_issue_info_subtitle2': '''
{
  "english": "I haven't received the verification SMS",
  "french": "Je n'ai pas reçu le SMS de vérification.",
  "german": "Ich habe die Verifizierungs-SMS nicht erhalten.",
  "spanish": "No he recibido el SMS de verificación."
}
''',
    'signin_issue_info_subtitle3': '''
{
  "english": "I have another issue",
  "french": "J'ai un autre problème.",
  "german": "Ich habe ein anderes Problem.",
  "spanish": "Tengo otro problema."
}
''',
    'signin_issue_info_title': '''
{
  "english": "Trouble Signing In?",
  "french": "Problème de connexion ?",
  "german": "Probleme beim Anmelden?",
  "spanish": "¿Problemas para iniciar sesión?"
}
''',
    'signin_issue_info_subbody3': '''
{
  "english": "Please write <NAME_EMAIL>. We will get back to you as soon as possible, provide further directions, and try to fix the issue.",
  "french": "Veuillez nous écrire à <EMAIL>. Nous vous répondrons dès que possible, fournirons des instructions supplémentaires et tenterons de résoudre le problème.",
  "german": "Bitte schreiben Sie <NAME_EMAIL>. Wir werden uns so schnell wie möglich bei Ihnen melden, weitere Anweisungen geben und versuchen, das Problem zu beheben.",
  "spanish": "Por favor, escrí<NAME_EMAIL>. Le responderemos lo antes posible, proporcionaremos más instrucciones y trataremos de solucionar el problema."
}
''',
    'discovery_matching_popup_title': '''
{
  "english": "Matching on Chyrpe",
  "french": "Correspondance sur Chyrpe",
  "german": "Matching auf Chyrpe",
  "spanish": "Emparejamiento en Chyrpe"
}
''',
    'discovery_matching_popup_body': '''
{
  "english": "Chyrpe provides you with a limited number of likes because we want to create genuine connections. You too benefit from this as everyone is required to be more selective.",
  "french": "Chyrpe vous offre un nombre limité de likes car nous souhaitons créer des connexions authentiques. Vous en bénéficiez également, car chacun est amené à être plus sélectif.",
  "german": "Chyrpe bietet Ihnen eine begrenzte Anzahl von Likes, weil wir echte Verbindungen schaffen möchten. Auch Sie profitieren davon, da jeder selektiver sein muss.",
  "spanish": "Chyrpe te proporciona un número limitado de 'me gusta' porque queremos crear conexiones genuinas. Tú también te beneficias de esto, ya que todos deben ser más selectivos."
}
''',
    'showLikeMatchChatHintWomen': true,
    'showLikeMatchChatHintMen': true,
    'dominant_term_info_body':
        'The partner in a relationship or dynamic who likes to set the tone and direction',
    'submissive_term_info_body':
        'The partner who likes to follow their dominant partner and their wishes.',
    'switch_term_info_body':
        'Someone who feels comfortable in either role or likes to alternate roles within a relationship or dynamic.',
    'chyrpe_chat_active': true,
    'mandatory_subscription_check1':
        'First week for free, so you can assure yourself of the quality and legitimacy of the app.',
    'mandatory_subscription_check2':
        'Chyrpe requires a little of your support thereafter to provide you with highest-quality matches.',
    'mandatory_subscription_check3':
        'Your support also helps us keep the app safe and makes sure that it can survive without selling data.',
    'mandatory_subscription_entry_headline': 'Help our launch',
    'bio_tips_info_title': 'Our tips for writing a great bio',
    'bio_tips_info_subbody1':
        'We know that many of you are passionate and eager to share or find what you desire, however, even in a kink-positive space, these things have to be talked about proportionately and carefully. People can be overwhelmed or even bothered if certain things are shared without being asked for. Share your preferences in the allocated spaces and use the bio to talk about the person that you are.',
    'bio_tips_info_subbody2':
        'Talking about yourself without bragging or being too humble can be difficult. Beyond that, even knowing what may be worth sharing can be difficult. A simple solution to this can be asking a friend, how they would describe you to someone convincingly, without being dishonest. This can include simple things from hobbies to little quirks to deeper personality traits and beliefs.',
    'bio_tips_info_subbody3':
        'The community that you are a part of is often one in which people tread carefully. We completely understand and respect this, wherefore features such as private + and anonymity modes exist, however, you should be aware that this is a two-sided blade. If people don\'t know who you are, they will have a hard time finding out whether they could like you. This is both true for visual features and more personal traits. Only by sharing will people be able to understand how desirable you are. Stay safe and do what you think is best, yet be aware that profiles with personal bios and facial images perform over 300% better than those which do not.',
    'bio_tips_info_subtitle1': 'You are more than your sexuality',
    'bio_tips_info_subtitle2': 'How to talk about yourself',
    'bio_tips_info_subtitle3': 'Anonymity is not your friend',
    'messages_forbiddenWords':
        '["Mommy","Mummy","Momy","Mamy","Mami","Mistress","Mistres","Misstress","Misstres","Mistrress","Dommy","Domy","Dommie","Dommymommy","Dominatrix","Domina","Cock","Penis","Pussy","Balls"]',
    'message_noshort_requirement_char_length': 20,
    'chyrpe_message_daily_limit': 3,
    'empty_list_view_title': 'Almost there',
    'empty_list_view_body':
        'You will be able to start matching soon. Chyrpe aims to be an excellent application which means that time can be necessary to implement improvements.',
    'verification_time': '72',
    'waitinglist_p1':
        'We maintain a waiting list to ensure a balanced community and to allow everyone to meet others in an engaging atmosphere.',
    'welcome_screen_t1': {
      'english': 'This app is focused on dating with female-led power dynamics',
      'french':
          'Cette app est dédiée aux rencontres avec des dynamiques de pouvoir menées par des femmes',
      'german':
          'Diese App dreht sich um Dating mit weiblich geführten Machtverhältnissen',
      'spanish':
          'Esta app se centra en las citas con dinámicas de poder lideradas por mujeres'
    },
    'welcome_screen_t2': {
      'english': 'Primarily built for the enjoyment & comfort of women',
      'french': 'Conçue avant tout pour le confort et le plaisir des femmes',
      'german': 'Entwickelt für das Wohlbefinden und den Komfort von Frauen',
      'spanish':
          'Diseñada principalmente para el bienestar y la comodidad de las mujeres'
    },
    'welcome_screen_t3': {
      'english': 'Very strict on prohibited behaviour',
      'french': 'Très stricte sur les comportements interdits',
      'german': 'Sehr streng bei verbotenem Verhalten',
      'spanish': 'Muy estricta con el comportamiento prohibido'
    },
    'welcome_screen2_heading': {
      'english': 'For Queens',
      'french': 'Pour les Reines',
      'german': 'Für Queens',
      'spanish': 'Para Reinas'
    },
    'showTbdScreen': false,
    'tt_screen_profiles':
        '\n\nUser profiles\n\nGetting a lot of matches and meeting the right person can depend on a lot of different variables. Some of these you can influence, others you cannot. We are here to help you regarding anything that you can do to impact and ensure that you have the highest possible likelihood of finding what you are looking for.\n\nA proven way of increasing the amount of positive attention a profile gets by a substantial amount is by writing good profile bios. While you may not deem it to be the most important metric, others may care a lot. While looks and preferences can be nice, many of our users care most about who you are as a person. You are the only one who can tell them! Statistically, meaningful and genuine profile bios are most important to our female users, so don\'t underestimate them if this is who you would like to impress.\n\nEveryone has to write a bio on chyrpe and we encourage you to put effort into it. Men additionally have to select between 1 and 3 prompts to answer.\n\nAnother important part of your profile are your images. Make sure to select images that create an accurate impression of you and what you enjoy. Use face pictures to build trust and express emotions.\n\nLastly, make use of the additional options that chyrpe offers. You can select your hobbies and “More Details” about you in the “Edit Profile” section. These can help you better convey what you enjoy and what you value in life.\n\nAlways remember, your profile is about painting an accurate picture of you. Make sure you get it right.',
    'tt_screen_discovery':
        'On chyrpe, users always need to both be interested in each other before they can match.\n\nYou can see profiles of people who you might be interested in on the first screen in the app, which you can always reach by tapping the left-most symbol in the tab bar. \n\nYou can view more details about a person by tapping on their picture.\n\nTo like someone, swipe right or tap on the check.\nTo give somebody a Rose (old name: evolved like), swipe up or tap on the stars. A Rose makes sure the other person knows you like them when they see your card.\nTo pass on a profile, swipe left or tap on the cross.\nTo save a profile, swipe down or tap on the arrow.',
    'tt_screen_chat':
        'The chat allows you to meet a person you are interested in more closely and get to know them better. Here, you might agree on a date but it can also happen that you notice you are actually not a great fit.\n\nWhether you talk about hobbies, preferences or just life, always make sure to stay respectful and obey the rules.\n\nSome rules are set by chyrpe. Sexual language, insults and dangerous content are always forbidden. \n\nIf you are in a heterosexual match, the female gets further options to steer the chat in the direction she desires. She can set this in the so-called power board and her rules have to be obeyed.\n\nHave fun, be friendly and get to know each other. The chat is a great place to meet your match more closely, so make sure to be proactive.',
    'tt_screen_privacy':
        'Chyrpe offers features that help increase your privacy while making sure you and others can still get to know each other effectively.\n\nYou decide whether others see your selected role and you can choose to hide your name. You can also selectively hide pictures.\n\nOur stringent data security measures ensure that your information remains confidential. Beyond that, we guarantee that we will never sell your information to third parties. Every user on chyrpe undergoes a mandatory verification process, providing you with the assurance that you are interacting with real individuals.\n\nApart from these measures, you can contribute to your privacy yourself. Don\'t share too much with people you don\'t know yet and only talk about yourself when you feel comfortable doing so.',
    'tt_screen_rules':
        'At chyrpe, our users\' safety is at the very core of our attention. We are deeply committed to ensuring the safety of all our users, regardless of gender.\n\nPlease refer to our Community Rules, which are included in our Terms of Use to see the full set of rules that govern chyrpe.\n\nAlways make sure to report individuals that you believe do not comply with these rules. On the bottom of every profile, you can find blocking, unmatching and reporting options at any time.\n\nWe maintain a proactive approach to user safety by closely monitoring reports and addressing them with the utmost seriousness. Reprehensible and prohibited behaviour will be addressed with firm immediacy. Rest assured that at chyrpe, your safety is paramount, and we continuously strive to create a secure and trustworthy environment for all our users.\n',
    'showKinks': true,
    'waitinglist_prelaunch_p1':
        'You will have access to chyrpe very soon! Not all regions are fully launched simultaneously.',
    'waitinglist_prelaunch_p2':
        'Regions are launched when enough users have signed up within them to allow for a positive matching experience.',
    'waitinglist_prelaunch_p3':
        'Your wait will significantly improve the likelihood of you finding what you are looking for. We promise, we work hard for your pleasure and future.',
    'hideRoleForAll': false,
    'hideXpForAll': false,
    'waitinglist_currentPosition_heading': 'Your position is between',
    'waitinglist_showInterval': true,
    'waitinglist_currentPosition_fallbackText': 'Wait for matching',
    'waitingList_localTitle': 'You are on the waiting list',
    'waitingList_notAvailable_caption': 'We will launch in your area soon',
    'waitinglist_moreInfo_subtitle': 'More information',
    'speedup_screen_150sub_description': 'Move with 1.5x speed',
    'speedup_screen_150sub_title': '150% speed',
    'speedup_screen_200sub_description': 'Finish the waiting in half the time',
    'speedup_screen_200sub_title': '200% speed',
    'speedup_screen_300sub_description':
        'Finish the waiting in a third of the time',
    'speedup_screen_300sub_title': '300% speed',
    'speedup_screen_additional_description': '',
    'speedup_screen_additional_description_shown': false,
    'speedup_screen_description':
        'Move faster and accelerate the list for all at the same time:',
    'speedup_screen_title': 'Speed it up.',
    'speedup_boolean_name': 'Speed Up',
    'global_matching_info_countdown_cta': 'Start global matching ',
    'global_matching_info_cta': 'Start global matching',
    'global_matching_info_description':
        'Your region has not started matching yet. Knowing how excited some of you are, we added this mode.\n\nGlobal mode allows you to match with others whose regions have not been opened yet. The biggest disadvantage is that you might not be very close.\n\nGlobal mode, like regional mode, has a waiting list to ensure a balanced community.\n\nDon‘t worry, when your region goes live, you keep your matches and your position in the regional waiting list.\n\nYour region has not started matching yet. Knowing how excited some of you are, we added this mode.\n\nGlobal mode allows you to match with others whose regions have not been opened yet. The biggest disadvantage is that you might not be very close.\n\nGlobal mode, like regional mode, has a waiting list to ensure a balanced community.\n\nDon‘t worry, when your region goes live, you keep your matches and your position in the regional waiting list.',
    'global_matching_info_title': 'Global Matching\n',
    'waitinglist_goto_global_cta': 'Start now!',
    'waitinglist_p2':
        'Like that, the waiting list helps every user. We do not distinguish based on your profile and even if the position looks high, it tends to move very quickly.',
    'waitinglist_p3':
        'Unlike other dating apps, we want to be fully transparent.  You can move faster if you contribute to making the wait quicker for everyone else too.',
    'phone_update_info_body':
        'You can change your phone number at anytime in the "Settings" of the app. Just like for signing up, we will verify your phone number by sending you a one-time code. We will also delete your old phone number promptly.',
    'phone_update_info_title': 'What happens if my phone number changes?',
    'findom_info_body':
        'Findom is a topic that is a topic that comes up sooner or later when one is active in the realm of Femdom. Therefore, here we will share our outlook on it and how it will be treated on Chyrpe.  The platform Chyrpe exists to facilitate the finding of a connection or even love between two people who share an appreciation for dominant women in one way or another. It is not a service that in any way intends to mislead its users into dynamics that rely on financial means or services that require purchasing. As a dating app, our goal is that the only reason for mutual interest in people is their shared interest in one another. You can trust chyrpe and its team that this mission and objective will always be at the forefront of what we stand for and seek to facilitate.  If Findommes desire to use Chyrpe they will not be prohibited from doing so, as long as they declare themselves as such during the sign-in process. This will allow users who desire to avoid them to do so effectively or even filter them out. Alternatively, those interested in the proposition of Findommes may interact with them at their own discretion.  Findommes or people intending to financially profit off of the users of Chyrpe who do not declare themselves as such will be banned from the application with immediacy.',
    'phone_blocking_info_title': 'Blocking phone numbers',
    'waitinglist_ppaywlink':
        '100% of proceeds from speed-ups are invested into accelerating the queue for all users.  Want to have a look?',
    'waitinglist_ppaywlink_cursub':
        '100% of proceeds from speed-ups are invested into accelerating the queue for all users.',
    'waitinglist_global_accepted_title':
        'You\'ve been admitted to global matching',
    'waitinglist_local_accepted_title':
        'You\'ve been admitted to matching in your region',
    'global_waitinglist_currentPosition_fallbackText': 'Wait for matching',
    'global_waitinglist_prelaunch_p3':
        'Your wait will significantly improve the likelihood of you finding what you are looking for. We promise, we work hard for your pleasure and future.',
    'waitingList_globalTitle': 'You are on the global waiting list',
    'global_waitinglist_prelaunch_p2':
        'Global matching has a waiting list just like regions, to ensure a balanced user base also in this mode.',
    'global_waitinglist_ppaywlink_cursub':
        '100% of proceeds from speed-ups are invested into accelerating the queue for all users.',
    'global_waitinglist_moreInfo_subtitle': 'More information',
    'global_waitinglist_showInterval': true,
    'global_waitinglist_p3':
        'Unlike other dating apps, we want to be fully transparent. You can move faster if you contribute to making the wait quicker for everyone else too.',
    'global_waitinglist_p1':
        'We maintain a waiting list to ensure a balanced community and to allow everyone to meet others in an engaging atmosphere.',
    'global_waitinglist_ppaywlink':
        '100% of proceeds from speed-ups are invested into accelerating the queue for all users.  Want to have a look?',
    'global_waitinglist_p2':
        'Like that, the waiting list helps every user. We do not distinguish based on your profile and even if the position looks high, it tends to move very quickly.',
    'global_waitinglist_currentPosition_heading': 'Your position is between',
    'global_waitinglist_prelaunch_p1':
        'As announced, not all regions start matching simultaneously. We promise this is for your benefit. While you wait for yours to launch, you can use global matching to match with others whose regions haven\'t launched yet.',
    'global_matching_short_explanation':
        'If your region is released before you enter global matching, you will be returned to it. You will always have the fastest of the two solutions.',
    'global_waitinglist_showSpeedups': true,
    'speedup_cta_name': 'Speed-Ups',
    'global_speedup_cta_name': 'Speed-Ups',
    'global_speedup_boolean_name': 'Speed Up',
    'global_speedup_screen_description':
        'Move faster and accelerate the list for all at the same time:',
    'global_speedup_screen_additional_description_shown': false,
    'global_speedup_screen_300sub_title': '300% speed',
    'global_speedup_screen_title': 'Speed it up.',
    'global_speedup_screen_300sub_description':
        'Finish the waiting in a third of the time',
    'global_speedup_screen_200sub_description':
        'Finish the waiting in half the time',
    'global_speedup_screen_additional_description': '',
    'global_speedup_screen_150sub_title': '150% speed',
    'global_speedup_screen_150sub_description': 'Move with 1.5x speed',
    'global_speedup_screen_200sub_title': '200% speed',
    'gmatchers_speedup_screen_additional_description_shown': true,
    'gmatchers_speedup_screen_300sub_title': '300% speed',
    'gmatchers_speedup_screen_title': 'Speed it up.',
    'gmatchers_speedup_screen_300sub_description':
        'Finish the waiting in a third of the time',
    'gmatchers_speedup_screen_200sub_description':
        'Finish the waiting in half the time',
    'gmatchers_waitinglist_showInterval': true,
    'gmatchers_speedup_screen_additional_description': '',
    'gmatchers_speedup_screen_150sub_title': '150% speed',
    'gmatchers_speedup_screen_200sub_title': '200% speed',
    'gmatchers_speedup_screen_150sub_description': 'Move with 1.5x speed',
    'gmatchers_waitinglist_currentPosition_fallbackText': 'Wait for matching',
    'gmatchers_speedup_screen_description':
        'Move faster and accelerate the list for all at the same time:',
    'gmatchers_currentPosition_heading': 'Your position is between',
    'gmatchers_speedup_cta_name': 'Speed-Ups',
    'gmatchers_waitinglist_p2':
        'Like that, the waiting list helps every user. We do not distinguish based on your profile and even if the position looks high, it tends to move very quickly.',
    'gmatchers_waitinglist_prelaunch_p3':
        'Your wait will significantly improve the likelihood of you finding what you are looking for. We promise, we work hard for your pleasure and future.',
    'gmatchers_waitinglist_prelaunch_p1':
        'As announced, not all regions start matching simultaneously. We promise this is for your benefit. If you have any questions, contact us via mail or instagram immediately!',
    'gmatchers_waitinglist_prelaunch_p2':
        'Regions are launched when funds are available to support them actively. We might introduce global matching over the next 2 weeks if you are excited to start!',
    'gmatchers_localTitle': 'You are on the waiting list',
    'gmatchers_waitinglist_ppaywlink_cursub':
        '100% of proceeds from speed-ups are invested into accelerating the queue for all users.',
    'gmatchers_waitingList_notAvailable_caption':
        'We will launch in your area soon',
    'gmatchers_waitinglist_p3':
        'Unlike other dating apps, we want to be fully transparent. You can move faster if you contribute to making the wait quicker for everyone else too.',
    'gmatchers_waitinglist_ppaywlink':
        '100% of proceeds from speed-ups are invested into accelerating the queue for all users.  Want to have a look?',
    'gmatchers_waitinglist_moreInfo_subtitle': 'More information',
    'gmatchers_waitinglist_p1':
        'We maintain a waiting list to ensure a balanced community and to allow everyone to meet others in an engaging atmosphere.',
    'gmatchers_speedup_boolean_name': 'Speed Up',
    'profile_home_local_wl_text': 'Local matching waiting list',
    'discovery_global_matching_explanation_m':
        'Not all regions open for matching at the same time. While yours is not yet open, global matching allows you to meet people whose regions are also not yet matching. You will get access to matching with people from your region when it opens and you have made it through the local waiting list. If you do not want to participate in global matching anymore, you can always pause your profile in the settings.',
    'discovery_global_matching_explanation_w':
        'Not all regions open for matching at the same time. While yours is not yet open, global matching allows you to meet people whose regions are also not yet matching. You will get access to matching with people from your region as soon as it opens. If you do not want to participate in global matching anymore, you can always pause your profile in the settings.',
    'discovery_global_matching_explanation_title': 'You are in global matching',
    'gmatchers_noloc_home': true,
    'gmatchers_no_loc_detail': true,
    'showHobbies': true,
    'showEdu': true,
    'dominant_term_info_title': 'Dominant',
    'signup_birthday_info':
        'Your profile will display your age, not your date of birth. Please make sure that you are over the age of 18 and that you provide your date of birth in the correct order, day before month!',
    'signup_email_info':
        'Please only use lowercase letters.This helps us keep your account safe. We will not use it for marketing purposes and we will never sell it.',
    'signup_genderPref_info': ' ',
    'signup_name_info':
        'Ensure this is the name that others should call you. You can\'t change it later.',
    'signup_ownGender_info':
        'Chyrpe is for everyone and we understand that this might not describe you perfectly, however, it is important for your experience. You can specify it in more detail later.',
    'signup_phone_info':
        'We will send a text with a verification code. Message and data rates may apply.',
    'signup_xp_info_1':
        'Whether it is female agency, lead or femdom itself, all levels of experience are welcome.',
    'signup_xp_info_2':
        'Every profile will show this information. When communicating with someone, make sure to respect their experience level.',
    'switch_term_info_title': 'Switch',
    'submissive_term_info_title': 'submissive',
    'signup_verificationCode_info': ' ',
    'empty_list_title': 'Something went wrong',
    'empty_list_description':
        'Either you\'re offline or you\'ve seen everybody who fulfills your search criteria for now. To see new people, go online or change your preferences.',
    'gender_options': '[Male, Female, Alternative]',
    'daily_nlikes_w': 35,
    'weekly_elikes_gold': 5,
    'daily_nlikes_m': 10,
    'weekly_elikes_evolved': 7,
    'daily_nlikes_m_gold': 15,
    'daily_nlikes_w_gold': 50,
    'discovery_matching_popup_body_w': 'You are ready to get started!',
    'empty_list_view_almost_there': 'Almost there',
    'show_globalWaitingListAscent_button': false,
    'noGlobalMatchingForLocalMatchersM': true,
    'noGlobalMatchingForLocalMatchersW': true,
    'noGlobalForLocalTitle': 'Not available',
    'noGlobalForLocalBody':
        'Global matching is primarily for those whose regional matching is not open yet. To make sure they get a chance of using chyrpe, we currently restrict access to global matching for others.',
    'local_waitingIntro_showInfo': false,
    'local_waitingIntro_sheetInfo_body':
        'The waiting list is necessary to make sure that everybody can have a good matching experience on the app',
    'local_waitingIntro_sheetInfo_title':
        'More information about the waiting list',
    'global_waitingIntro_showInfo': false,
    'global_waitingIntro_sheetInfo_title':
        'More information about the waiting list',
    'gmatchers_waitingIntro_showInfo': false,
    'gmatchers_waitingIntro_sheetInfo_body':
        'The waiting list is necessary to make sure that everybody can have a good matching experience on the app',
    'gmatchers_waitingIntro_sheetInfo_title':
        'More information about the waiting list',
    'global_waitingIntro_sheetInfo_body':
        'The waiting list is necessary to make sure that everybody can have a good matching experience on the app.',
    'dominant_term': 'Dominant',
    'submissive_term': 'submissive',
    'switch_term': 'Switch',
    'showNpsStyleFeedback': false,
    'men_additional_rules_1_description': 'This can take up to 72 hours',
    'men_additional_rules_1_title': 'Chyrpe has mandatory verification',
    'men_additional_rules_2_description':
        'This is for you, when admitted, it\'s a balanced community',
    'men_additional_rules_2_title': 'You will be in a waiting list',
    'men_additional_rules_3_description':
        'You get a free week. This is necessary to run the app.',
    'men_additional_rules_3a_title': 'Chyrpe costs',
    'men_additional_rules_3b_title': 'per week',
    'men_additional_rules_page_subtitle': 'Check the boxes to agree.',
    'men_additional_rules_page_title':
        'What you need to know before getting started',
    'free_gold_explanation_text':
        'This is a gift to you, from us, we appreciate your feedback and effort to help chyrpe out!',
    'free_gold_subs_explanation_text':
        'You are already one of chyrpe\'s biggest supporters. Write <NAME_EMAIL> so we can return your kindness.',
    'daily_nlikes_m_gold_legacy': 15,
    'daily_nlikes_w_evolved': 70,
    'daily_nlikes_m_evolved': 20,
    'feedback_popup_own_negative_description':
        'Write below what\'s not great yet. Your feedback is the only way for us to learn and improve.',
    'feedback_popup_own_positive_cta1': 'Quick rating!',
    'feedback_popup_own_positive_cta2': 'Write a review',
    'feedback_popup_own_positive_description':
        'We are glad that you had great experiences on chyrpe!  Share your opinion about it. It makes a difference!',
    'own_feedback_alert_style': true,
    'feedback_popup_own_question':
        'Have you had interesting experiences or success on chyrpe so far?',
    'noLongTermSubscriptions': true,
    'chyrpe_standard_new_headline': 'Make chyrpe possible',
    'chyrpe_standard_new_body1': 'Shape chyrpe with us, we listen to our users',
    'chyrpe_standard_new_body2':
        'Support an independent startup you want to succeed',
    'chyrpe_standard_new_body3': 'We don\'t have to or will sell your data',
    'chyrpe_standard_new_weekly_title': 'Weekly Subscription',
    'chyrpe_standard_new_lifetime_title': 'Lifetime Chyrpe',
    'chyrpe_standard_new_weekly_subtitle': '',
    'chyrpe_standard_new_lifetime_subtitle': '',
    'show_chyrpe_standard_new_lifetime_subtitle': false,
    'show_chyrpe_standard_new_weekly_subtitle': false,
    'show_chyrpe_standard_new_weekly': true,
    'show_chyrpe_standard_new_lifetime': true,
    'chyrpe_standard_new_lifetime_disclaimer':
        'By tapping “Continue”, you will make a one-time purchase that permanently entitles you to the benefits attached to the Chyrpe standard weekly subscription. It does not include any additional benefits or rights to premium subscription benefits. Our and Google Play Terms apply.',
    'chyrpe_standard_new_weekly_disclaimer':
        'By tapping “Continue”, you will be charged and your subscription will auto-renew for the same price and length until you cancel via Play Store settings. Our Terms and Google Play Terms apply.',
    'enjoy_free_trial_body': 'Enjoy your free trial',
    'enjoy_free_trial_duration': 4,
    'show_new_additional_rules_3_text': true,
    'new_additional_rules_3_text':
        'Afterwards, you get a few options for supporting chyrpe',
    'new_additional_rules_3_title': 'You receive one free week',
    'show_additional_male_rules': true,
    'show_referral_signup': false,
    'in_discovery_waitinglist_regional_heading':
        'You are on the waiting list for your region',
    'in_discovery_waitinglist_regional_notopen_position_title': 'Status',
    'in_discovery_waitinglist_regional_open_position_title':
        'Your position is between',
    'in_discovery_waitinglist_regional_notopen_explanation1':
        'Regional matching has not yet started in your area. You will see your position when it does.',
    'in_discovery_waitinglist_regional_notopen_globalmatching_link_title':
        'Check out global matching',
    'in_discovery_waitinglist_regional_faq_link_title':
        'Learn more in our waiting list FAQs',
    'in_discovery_waitinglist_regional_notopen_position_fallback':
        'Wait for opening',
    'in_discovery_waitinglist_regional_open_explanation1':
        'Speed-ups allow you get ahead faster. All proceeds are used to accelerate the queue for all:',
    'in_discovery_waitinglist_regional_open_speedup_link_title':
        'View Speed-Ups',
    'in_discovery_waitinglist_global_info_cta': 'Join',
    'in_discovery_waitinglist_global_info_description':
        'Global mode is a feature we added based on community feedback and allows you to match with others from around the world while you are waiting to join regional matching. The biggest disadvantage is that you might not be very close.Global mode, like regional mode, has a waiting list to ensure a balanced community.Don‘t worry, when your region goes live, you keep your matches and your position in the regional waiting list.',
    'in_discovery_waitinglist_global_info_heading': 'Join global matching',
    'in_discovery_waitinglist_global_heading':
        'You are on the global matching waiting list',
    'in_discovery_waitinglist_global_open_explanation1':
        'The global waiting list tends to move quickly. To accelerate your progression, consider a speed-up.Speed-ups allow you get ahead faster. All proceeds are used to accelerate the queue for all:',
    'global_waitlist_active': true,
    'in_discovery_faq_heading': 'Questions & Answers about the waiting list',
    'discovery_standard_view_legal_information_sheet_title':
        'Legal Information',
    'discovery_standard_view_legal_information_button_title':
        'View Legal Information\n',
    'discovery_standard_view_lifetime_title': 'Lifetime Chyrpe',
    'discovery_standard_view_weekly_title': 'Weekly Subscription',
    'lifetime_upgrade_screen_benefits_box_caption':
        'Benefits of chyrpe lifetime',
    'lifetime_upgrade_screen_check1_text':
        'No more weekly standard subscription',
    'lifetime_upgrade_screen_check2_text': 'View profiles, like & match',
    'lifetime_upgrade_screen_check3_text':
        'Unlimited messaging with your matches',
    'lifetime_upgrade_screen_duration_name': 'Forever',
    'lifetime_upgrade_screen_forgetnot_caption':
        'Don\'t forget to cancel standard weekly',
    'lifetime_upgrade_screen_forgetnot_closebuttontitle': 'Close',
    'lifetime_upgrade_screen_forgetnot_descriptionlifetime_upgrade_screen_forgetnot_description':
        'We cannot do this for you. Please cancel it now.',
    'lifetime_upgrade_screen_forgetnot_managesubsbuttontitle':
        'Manage subscription',
    'lifetime_upgrade_screen_title': 'Get chyrpe standard, forever.',
    'lifetime_upgrade_screen_duration_select_caption': 'Select',
    'profile_home_lifetime_upgrade_description':
        'Invest into an idea you believe in. No more weekly standard subscription.',
    'profile_home_lifetime_upgrade_visible': true,
    'like_stage2_men': true,
    'like_stage2_women': false,
    'chat_screen_placeholder_description':
        'You can match with others and send and receive messages once you have finished creating your account.',
    'chat_screen_placeholder_title': 'Keep going\n',
    'location_screen_explainer_description':
        'In order to find matches in your region, you need to grant us access to your current location. We will not routinely access your current location and will never share it.You can change your location and search radius later in the settings at anytime. ',
    'ambassador_button_title': 'Ambassador Programme',
    'ambassador_button_visible': false,
    'ambassador_button_url': 'https://chyrpe.com',
    'welcome_screen2_login_hint':
        'If you already have an account and want to log in, select the same method you signed up with.',
    'welcome_screen2_issues_hint': 'Issues? Email:\<EMAIL>',
    'welcome_screen2_show_google': true,
    'welcome_rules_new_account_hint': {
      'english':
          'You are creating a new account.\nAlready have an account? Tap on "x" and login with your original credentials.',
      'french':
          'Tu es en train de créer un nouveau compte.\nTu as déjà un compte ? Appuie sur “x” et connecte-toi avec tes identifiants.',
      'german':
          'Du erstellst gerade ein neues Konto.\nHast du schon eins? Tippe auf „x“ und melde dich an.',
      'spanish':
          ' Estás creando una cuenta nueva.\n¿Ya tienes cuenta? Toca “x” e inicia sesión con tus datos.'
    },
    'welcome_rules_show_new_account_hint': true,
    'freepaid_surecomp_title': 'Are you sure?',
    'freepaid_surecomp_mainButtonTitle_ryan': 'I agree',
    'freepaid_surecomp_secondaryButtonTitle_ryan': 'I want to support Chyrpe',
    'freepaid_surecomp_mainButtonTitle_norm': 'I want to support Chyrpe',
    'freepaid_surecomp_secondaryButtonTitle_norm': 'I understand, continue',
    'freepaid_tablecomp_title': 'What you get:',
    'freepaid_tablecomp_mainButtonTitle_ryan': 'I want it for free',
    'freepaid_tablecomp_secondaryButtonTitle_ryan': 'I want more',
    'freepaid_tablecomp_mainButtonTitle_norm': 'I want Standard',
    'freepaid_tablecomp_secondaryButtonTitle_norm': 'Continue for free',
    'freepaid_tablecomp_freeStandardCompTable':
        '[{"benefit": "All standard app utility", "number": false, "free_binary_true": true, "paid_binary_true": true, "free_number": 1, "paid_number": 1}, {"benefit": "Chyrpe assistance", "number": false, "free_binary_true": true, "paid_binary_true": true, "free_number": 1, "paid_number": 1}, {"benefit": "Likes per day", "number": true, "free_binary_true": true, "paid_binary_true": true, "free_number": 3, "paid_number": 10}, {"benefit": "Unlimited dislikes", "number": false, "free_binary_true": false, "paid_binary_true": true, "free_number": 0, "paid_number": 1}, {"benefit": "Support an independent startup", "number": false, "free_binary_true": false, "paid_binary_true": true, "free_number": 0, "paid_number": 1}, {"benefit": "Shape Chyrpe\'s future, we listen to our users", "number": false, "free_binary_true": false, "paid_binary_true": true, "free_number": 0, "paid_number": 1}]',
    'freepaid_benefitlist_free_title': 'What you get in Chyrpe Free:',
    'freepaid_benefitlist_free_mainButtonTitle_ryan': 'Next',
    'freepaid_benefitlist_free_secondaryButtonTitle_ryan':
        'I want better options',
    'freepaid_benefitlist_free_mainButtonTitle_norm': 'Improve Chyrpe',
    'freepaid_benefitlist_free_secondaryButtonTitle_norm': 'Continue',
    'freepaid_benefitlist_standard_title':
        'What you would get in Chyrpe Standard:',
    'freepaid_benefitlist_standard_mainButtonTitle_ryan': 'Next',
    'freepaid_benefitlist_standard_secondaryButtonTitle_ryan':
        'I want Standard',
    'freepaid_benefitlist_standard_mainButtonTitle_norm': 'I want Standard',
    'freepaid_benefitlist_standard_secondaryButtonTitle_norm':
        'Continue for free instead',
    'freepaid_finaliseChoice_title': 'Finalise your choice',
    'freepaid_finaliseChoice_mainBodyText': 'We appreciate you no matter what',
    'freepaid_finaliseChoice_mainButtonTitle_ryan': 'Improve Chyrpe',
    'freepaid_finaliseChoice_secondaryButtonTitle_ryan': 'Continue for free',
    'freepaid_finaliseChoice_mainButtonTitle_norm': 'Improve Chyrpe',
    'freepaid_finaliseChoice_secondaryButtonTitle_norm': 'Continue for free',
    'freepaid_standardPurchaseDiscovery_buttonTitle': 'Chyrpe for free',
    'freepaid_cohort_chances_selection': '[0.3,0.2,0.3,0.2]',
    'freepaid_cohort_profilehomewidg_desc':
        'Support an idea you believe in. Get Chyrpe Standard.',
    'freepaid_cohort_profilehomewidg_bttn': 'Upgrade',
    'profile_home_dual_upgrade_visible': true,
    'daily_nlikes_m_free': 3,
    'free_m_outofdislikes_title': 'You\'re out of likes',
    'free_m_outofdislikes_body':
        'Feel free to continue swiping when all of your likes have been refreshed.\n\nLifetime Chyrpe increases like quantity and swipe limits. It also makes this possible.',
    'free_m_outofdislikes_buttontitle': 'Get Standard',
    'settings_freeswitch_title': 'Make sure to cancel your subscription',
    'settings_freeswitch_body':
        'You will be able to use a free version of chyrpe. We can, however, not cancel your paid subscription for you. Please do so at the end of the process in your app store.',
    'settings_freeswitch_buttontitle': 'Next',
    'settings_freeswitch_visible': true,
    'settings_freeswitch_entrybuttontitle': 'Use Chyrpe for free',
    'freepaid_benefitlist_free_benefitList':
        '["All standard app utility", "Chyrpe assistance", "3 likes per day"]',
    'freepaid_benefitlist_standard_benefitList':
        '["All standard app utility", "Chyrpe assistance", "10 likes per day", "Unlimited dislikes", "Hide pictures selectively", "Get Incognito Mode", "Support an independent startup", "Shape Chyrpe’s future, we listen to our users"]',
    'freepaid_benefitlist_free_benefitList_placeholder':
        '["All standard app utility", "Chyrpe assistance", "{dailyMaleFreeLikeLimit} likes per day"]',
    'freepaid_benefitlist_standard_benefitList_placeholder':
        '["All standard app utility", "Chyrpe assistance", "{dailyMenLikeLimit} likes per day", "Unlimited dislikes", "Hide pictures selectively", "Get Incognito Mode", "Support an independent startup", "Shape Chyrpe’s future, we listen to our users"]',
    'freepaid_surecomp_boldBodyText':
        '\nAre you certain you do not want to help this idea succeed?',
    'freepaid_surecomp_mainBodyText2': '\nChyrpe is not a mega corporation.',
    'freepaid_surecomp_mainBodyText':
        'Chyrpe has been created to help a small niche community find the love they want.\n\nThis is expensive and difficult. Without your support Chyrpe may be shutdown at some point.',
    'profile_supporter_star_active': true,
    'settings_standard_support_badge_title':
        'Show Chyrpe Supporter Badge on Profile',
    'forums_chats_forbiddenWords':
        '["bastard", "bitch", "crap", "shit", "motherfucker", "cunt", "dick", "pussy","cock", "prick", "asshole", "douchebag", "bollocks", "bugger", "wanker", "twat", "slut", "whore"]',
    'forums_chats_forbiddenMessage_alert_title': 'Mind your language',
    'forums_chats_forbiddenMessage_alert_body':
        'Please make sure your message is respectful and follows all applicable rules before you send it.',
    'allowForumMessaging': true,
    'forums_report_reasons':
        '["Spam", "Scam", "Offensive", "Bad Energy", "Against Forum Rules", "Other"]',
    'forums_active': false,
    'forums_intro_title': 'What are Chyrpe Forums?',
    'forums_intro_text':
        'Welcome to Chyrpe Forums. This is a social space for you to meet and chat with like-minded women on a friendly basis. Everyone can write a message and like others\' messages by double tapping them.\n\nEach channel has a topic, make sure to review it by tapping on the name within the forum.\n\nForums are accessible to women only and are monitored by us. Please do not use them for dating or to talk about other users. Report messages by long holding them.',
    'forums_tab_name': 'Forums',
    'welcome_screen2_show_login_hint': false,
    'welcome_screen2_login_button_title': 'Log In',
    'welcome_screen2_phone_button_title': 'Continue with Phone',
    'welcome_screen2_google_title': 'Continue with Google',
    'welcome_screen2_show_phone_button': false,
    'welcome_screen2_show_login_button': true,
    'welcome_screen2_show_phone_login_android': false,
    'extra_rules_headline': 'What you should know before getting started:',
    'extra_rules_title1': 'Verification is mandatory on Chyrpe',
    'extra_rules_title2': 'You may have to wait a bit',
    'extra_rules_title3': 'Chyrpe is an independent startup',
    'extra_rules_title4': 'We never sell your data',
    'extra_rules_body1':
        'To protect you from fakes.\nIt can take up to 72 hours.',
    'extra_rules_body2':
        'So you to have a real shot when matching.\nA waiting list ensures a good balance.',
    'extra_rules_body3':
        'Still growing and active in a small niche. Free options may be more limited than elsewhere.',
    'extra_rules_body4': 'It’s simple as that.',
    'extra_rules_page1_confirm_button_title': 'I understand, let’s continue',
    'extra_rules_page1_doubt_button_title': 'I am not happy with this',
    'extra_rules_page2_title': 'We are sorry!',
    'extra_rules_page2_explanation':
        'We want you to be happy. Please tell us what you don’t like so we can improve.',
    'extra_rules_page2_options_title': 'Here are your options:',
    'extra_rules_page2_options_confirm_button_title':
        'I’ll give it a try nonetheless',
    'extra_rules_page2_options_notify_button_title':
        'Notify me when it’s different',
    'extra_rules_page2_options_delete_button_title': 'Delete my account',
    'extra_rules_unhappiness_reasons':
        '["Verification is mandatory on Chyrpe", "You may have to wait a bit", "Limited free options", "We never sell your data"]',
    'signup_confirmation_after_location':
        'Now, just one last step and you’re in ☺️',
    'signup_confirmation_after_images':
        '🎉\nWell done.\nYou’re all set to impress.',
    'signup_confirmation_after_basics1': 'Well done with the basics.',
    'signup_confirmation_after_basics2':
        'Let’s add a bit more about yourself and some pictures.',
    'signup_confirmation_after_basics3': 'This will really make you stand out.',
    'signup_confirmation_after_name2': 'Let’s make your profile!',
    'signup_confirmation_after_name1b': ', nice to meet you 👋',
    'signup_confirmation_after_name1a': 'Hey ',
    'bio_prompts_headline': 'Tell Others What Makes You, You!',
    'bio_prompts_description': 'Pick and answer up to three prompts.',
    'bio_prompts_tip':
        'Profiles with a well-crafted bio or prompts are up to 124% more successful than those without.',
    'bio_prompts_do_later_button': 'I’ll do that later',
    'bio_prompts_bio_instead': 'I want to write a bio instead',
    'bio_bio_description': 'Write your own bio.',
    'bio_bio_prompts_instead': 'I want to answer prompts instead',
    'location_allowing_headline': 'Where are you based?',
    'location_allowing_explanation':
        'Location access is necessary for us to show you people nearby. We never share anyone’s exact location.',
    'location_allowing_button_title': 'Allow location access',
    'verification_entry_headline': 'Get verified now',
    'verification_entry_description':
        'We verify every profile so you can feel safe on the platform.',
    'verification_entry_step1_title': 'Upload a selfie',
    'verification_entry_step1_description':
        'Take a selfie while you do the below hand gesture. It’s stored safely.',
    'verification_entry_step2_title': 'We verify it',
    'verification_entry_step2_description':
        'This can take up to 72 hours.\nThe verification picture will never be shown on your profile.',
    'verification_entry_selfie_button_title': 'Take photo',
    'verification_inreview_headline': 'We’re reviewing\nyour verification.',
    'verification_inreview_description':
        'Tap below to get notified\nwhen it’s done!',
    'verification_inreview_button_title': 'Get notifications',
    'verification_in_review_allowancegranted_button_title': 'We\'ll notify you',
    'verification_success_headline': 'Congrats, you’re all set and verified.',
    'verification_success_description': 'A warm welcome to Chyrpe!',
    'verification_success_getstarted_button_title': 'Get started now!',
    'verification_fail_headline':
        'We couldn’t verify you yet but you can try again.',
    'verification_fail_tryagain_button_title': 'Continue',
    'verification_genderreclass_description':
        'An independent review board could verify you are a real person but believes your gender selection might not reflect\nwhat you wanted to select.',
    'gender_reclass_evidence_button_title': 'Evidence your gender',
    'notification_sheet_headline': 'Get notified as soon as things happen?',
    'notification_sheet_description': '',
    'notification_sheet_allow_button_title': 'Yes, allow notifications',
    'notification_sheet_disallow_button_title': 'No, not now',
    'images_headline': 'Share moments from your life',
    'images_description': 'Pick at least 3 photos.',
    'signup_testing_cohorts_chances': '[0.5,0.5]',
    'images_tips': 'Having at least one face picture increases success by 91%.',
    'pn_allow_sl_question':
        'Grant Chyrpe permission to send push notifications?',
    'pn_allow_sl_description': 'You can turn it off anytime.',
    'gender_reclass_male_button_title': 'Proceed as Male',
    'gender_reclass_alternative_button_title':
        'Proceed as Alternative (Female)',
    'gender_reclass_headline': 'You can proceed',
    'signup_ownGender_info_button_title': {
      'english': 'Information on gender on Chyrpe',
      'french': 'Informations sur le genre sur Chyrpe',
      'german': 'Informationen zu Geschlechtsidentität bei Chyrpe',
      'spanish': 'Información sobre género en Chyrpe'
    },
    'signup_ownGender_sheet_title': 'Gender on Chyrpe',
    'signup_kink_ibutton_title': 'What does this mean?',
    'signup_birthday_ibutton_title': {
      'english': 'We only accept users aged 18+',
      'french': 'Uniquement réservé aux 18 ans et plus',
      'german': 'Nur für Nutzer ab 18 Jahren',
      'spanish': 'Solo aceptamos personas mayores de 18 años'
    },
    'signup_birthday_sheet_title': 'Age on Chyrpe',
    'settings_pause_explanation':
        'When you pause your profile, you won’t be able to see other profiles and others will not be able to see you. You can still talk to your matches.\n\nUse this to keep your profile while you wait for local matching, if you don\'t want to be shown in global matching anymore.',
    'settings_contact_blocking_explanation':
        'Review who you blocked, unblock or block contacts.',
    'settings_pushn_explanation':
        'Select for which events you want to receive push notifications. You will only receive them if you gave Chyrpe permission inyour settings app.',
    'purchases_gold_offer_string':
        '[{"packageId": "chyrpe_gold_1w", "duration": "1 week", "durationWeeks": 1, "weeklyAlternativeId": "chyrpe_gold_1w"}, {"packageId": "chyrpe_gold_1m", "duration": "1 month", "durationWeeks": 4, "weeklyAlternativeId": "chyrpe_gold_1w"}, {"packageId": "chyrpe_gold_6m", "duration": "3 months", "durationWeeks": 12, "weeklyAlternativeId": "chyrpe_gold_1w"}]',
    'purchases_plus_offer_string':
        '[{"packageId": "chyrpe_plus_1w", "duration": "1 week", "durationWeeks": 1, "weeklyAlternativeId": "chyrpe_plus_1w"}, {"packageId": "chyrpe_plus_1m", "duration": "1 month", "durationWeeks": 4, "weeklyAlternativeId": "chyrpe_plus_1w"}, {"packageId": "chyrpe_plus_6m", "duration": "3 months", "durationWeeks": 12, "weeklyAlternativeId": "chyrpe_plus_1w"}]',
    'purchases_evolved_offer_string':
        '[{"packageId": "chyrpe_evolved_1w", "duration": "1 week", "durationWeeks": 1, "weeklyAlternativeId": "chyrpe_evolved_1w"}, {"packageId": "chyrpe_evolved_1m", "duration": "1 month", "durationWeeks": 4, "weeklyAlternativeId": "chyrpe_evolved_1w"}, {"packageId": "chyrpe_evolved_6m", "duration": "3 months", "durationWeeks": 12, "weeklyAlternativeId": "chyrpe_evolved_1w"}]',
    'purchases_standard_new_tagline': 'Match & message, get unlimited dislikes',
    'purchases_standard_ltu_new_tagline':
        'Purchase once, get Chyrpe Standard benefits forever.',
    'purchases_plus_new_tagline': 'See feedback & get more likes',
    'purchases_gold_new_tagline':
        'Match immediately with people who liked you, stand out & get more likes',
    'purchases_evolved_new_tagline':
        'Use all of Chyrpe’s features & get double likes',
    'purchases_standard_new_btn': 'Upgrade Now',
    'purchases_standard_new_ltu_btn': 'Upgrade Now',
    'purchases_plus_btn': 'Upgrade Now',
    'purchases_gold_btn': 'Upgrade Now',
    'purchases_evolved_btn': 'Upgrade Now',
    'signup_age_ibutton_title': 'We only accept users aged 18+',
    'signup_age_sheet_title': 'Age on Chyrpe',
    'extra_rules_women_headline': 'Welcome to the launch edition of Chyrpe',
    'extra_rules_women_title1': 'Chyrpe is still young',
    'extra_rules_women_body1':
        'Launched a few weeks ago. Keep this in mind when you assess our features & size.',
    'extra_rules_women_title2': 'Tell us what you think',
    'extra_rules_women_body2':
        'We want to hear your feedback & provide personal support in the app or via email.',
    'extra_rules_women_page2_title1':
        'We have received your feedback.\n\nThank you!',
    'extra_rules_women_page2_title2': 'Here are your options for now:',
    'show_additional_female_rules': true,
    'signup_experienceLevel_info_button_title': 'What does this mean?',
    'signup_experienceLevel_info':
        'Chyrpe is open to everyone. Knowing your level of experience in relationships with a strong female partner makes it easier for others to communicate with you in the right way.',
    'freestandard_availability_group_a': '[]',
    'freestandard_availability_group_b': '[]',
    'evolved_screen_whos_next_title': 'Who\'s next',
    'evolved_screen_whos_next_body':
        'With Chyrpe Evolved, you can view all your likes at once',
    'evolved_screen_empty_title': 'See who liked you & match directly',
    'evolved_screen_empty_description':
        'You have not yet received any likes. Once you have, you can match directly with users on this screen.',
    'announcements': '[]',
    'settings_area_info':
        'Chyrpe has been designed for people that share a specific love interest and is still a young company. Because of this, our user base is more spread out than on other dating apps. Hence, we currently only support a slightly wider search radius.',
    'settings_area_info_direct':
        'We will prioritise showing people in this radius around your location to you.',
    'settings_min_area': 80,
    'settings_max_area': 500.1,
    'm_dlimit_likdep_enforced_free': true,
    'm_dlimit_likdep_enforced_all': true,
    'm_dlimit_indep_enforced_all': true,
    'm_dlimit_indep_warn_free': 30,
    'm_dlimit_indep_warn_standard': 60,
    'm_dlimit_indep_warn_premium': 75,
    'm_dlimit_indep_lim_free': 45,
    'm_dlimit_indep_lim_standard': 75,
    'm_dlimit_indep_lim_premium': 90,
    'dlimit_likdep_title_free': 'You’re out of likes',
    'dlimit_likdep_title_standard': 'You’re out of likes',
    'dlimit_likdep_title_evolved': 'You’re out of likes',
    'dlimit_likdep_body_free':
        'Continue swiping when your likes have been refreshed. Lifetime Chyrpe increases swipe limits.',
    'dlimit_likdep_body_standard':
        'Continue swiping when your likes have been refreshed. You can double your likes with Chyrpe Evolved.',
    'dlimit_likdep_body_standard_m':
        'Continue swiping when your likes have been refreshed. You can get 20 daily likes with Chyrpe Evolved.',
    'dlimit_likdep_body_standard_m_placeholder':
        'Continue swiping when your likes have been refreshed. You can get {dailyEvolvedMenLikeLimit} daily likes with Chyrpe Evolved.',
    'dlimit_likdep_body_standard_w':
        'Continue swiping when your likes have been refreshed. You can get 70 daily likes with Chyrpe Evolved.',
    'dlimit_likdep_body_standard_w_placeholder':
        'Continue swiping when your likes have been refreshed. You can get {dailyEvolvedWomenLikeLimit} daily likes with Chyrpe Evolved.',
    'dlimit_likdep_body_plus_m':
        'Continue swiping when your likes have been refreshed. Get 15 likes per day with Chyrpe Plus.',
    'dlimit_likdep_body_plus_w':
        'Continue swiping when your likes have been refreshed. Get 50 likes per day with Chyrpe Plus.',
    'dlimit_likdep_body_plus_m_placeholder':
        'Continue swiping when your likes have been refreshed. Get {dailyPlusMenLikeLimit} likes per day with Chyrpe Plus.',
    'dlimit_likdep_body_plus_w_placeholder':
        'Continue swiping when your likes have been refreshed. Get {dailyPlusWomenLikeLimit} likes per day with Chyrpe Plus.',
    'dlimit_likdep_btn_plus': 'Get Plus',
    'dlimit_likdep_btn_free': 'Get Standard',
    'dlimit_likdep_btn_standard': 'Get Evolved',
    'dlimit_likdep_btn_evolved': 'Continue',
    'dlimit_bot_warn_title': 'System Warning',
    'dlimit_bot_warn_body':
        'You are disliking >90% more than our average user. This is associated with bot behaviour. We may limit you.',
    'dlimit_bot_limit_title': 'System Warning',
    'dlimit_bot_limit_body':
        'Your dislikes are briefly limited to ensure the safety of the community. You can continue tomorrow.',
    'dlimit_bot_warn_btn': 'I understand',
    'dlimit_bot_limit_btn': 'I understand',
    'plus_benefitlist_m':
        '["Get 15 daily likes", "See profile feedback", "Hide pictures selectively", "Get Incognito Mode"]',
    'plus_benefitlist_m_placeholder':
        '["Get {dailyPlusMenLikeLimit} daily likes", "See profile feedback", "Hide pictures selectively", "Get Incognito Mode"]',
    'plus_benefitlist_w':
        '["Get 50 daily likes", "Hide pictures selectively", "Get Incognito Mode"]',
    'plus_benefitlist_w_placeholder':
        '["Get {dailyPlusWomenLikeLimit} daily likes", "Hide pictures selectively", "Get Incognito Mode"]',
    'gold_benefitlist_m':
        '["Get Chyrpe Standard", "See who liked you", "Get 15 daily likes", "Get 5 weekly Roses", "Get advanced search preferences"]',
    'gold_benefitlist_w':
        '["See who liked you", "Get 50 daily likes", "Get 2 weekly Roses"]',
    'gold_benefitlist_m_placeholder':
        '["Get Chyrpe Standard", "See who liked you", "Get {dailyGoldMenLikeLimit} daily likes", "Get {weeklyGoldLikeLimit} weekly Roses", "Get advanced search preferences"]',
    'gold_benefitlist_w_placeholder':
        '["See who liked you", "Get {dailyGoldWomenLikeLimit} daily likes", "Get {weeklyGoldLikeLimit} weekly Roses"]',
    'evolved_benefitlist_m':
        '["Get Chyrpe Standard", "Get 20 daily likes", "See who liked you", "Get 7 weekly Roses", "Get advanced search preferences", "Save profiles", "See profile feedback", "Hide pictures selectively", "Hide your name", "Get Incognito Mode"]',
    'evolved_benefitlist_m_placeholder':
        '["Get Chyrpe Standard", "Get {dailyEvolvedMenLikeLimit} daily likes", "See who liked you", "Get {weeklyEvolvedLikeLimit} weekly Roses", "Get advanced search preferences", "Save profiles", "See profile feedback", "Hide pictures selectively", "Hide your name", "Get Incognito Mode"]',
    'evolved_benefitlist_w':
        '["Get 70 daily likes", "See who liked you", "Get 5 weekly Roses", "Hide pictures", "Hide your name selectively", "Get Incognito Mode"]',
    'evolved_benefitlist_w_placeholder':
        '["Get {dailyEvolvedWomenLikeLimit} daily likes", "See who liked you", "Get {weeklyEvolvedLikeLimit} weekly Roses", "Hide pictures", "Hide your name selectively", "Get Incognito Mode"]',
    'lifetime_up_benefitlist':
        '["No more monthly standard subscription", "View profiles, like & match", "Unlimited messaging with your matches"]',
    'evolved_screen_global_in_reg_w': true,
    'evolved_screen_new_availability_ab': '["bGroup"]',
    'evolved_screen_new_availability_time': 1737319573000,
    'evolved_screen_new_availability_time_secondary': 1742552791000,
    'signup_images_ibutton_title': 'No NSFW pictures allowed',
    'signup_images_info':
        'This application does not allow NSFW pictures on anyone\'s profile or elsewhere.',
    'bio_prompts_min_prompts_info': '3 answers required',
    'bio_prompts_min_prompts_m': 3,
    'signup_biobio_ibutton_title': 'Write at least 100 characters',
    'signup_biobio_info':
        'A good bio doesn’t have to be long. A few words about yourself as a person can be enough to make a great first impression. Write at least 100 characters (less than a tweet!), or answer prompts if you would prefer a more guided style.',
    'bio_bio_min_chars_m': 100,
    'welcome_screen2_show_apple': true,
    'welcome_screen2_apple_button_title': 'Continue with Apple',
    'welcome_screen2_apple_login_button_title': 'Log In with Apple',
    'lifetime_upgrade_screen_current_for_gold':
        'Included temporarily in Chyrpe Gold',
    'lifetime_upgrade_screen_current_for_evolved':
        'Included temporarily in Chyrpe Evolved',
    'waitinglist_admitted_comp_btn_title':
        '["Continue", "Start free 7-day trial"]',
    'gold_offer50_headline': 'Get **50% off** your first month of Chyrpe Gold',
    'gold_offer_available_regions':
        '["US-Northeast", "US-South", "US-West", "US-Midwest"]',
    'gold_offer50_profile_home_headline': '50% OFF CHYRPE GOLD',
    'gold_offer50_tagline_visible': '["bGroup"]',
    'gold_offer50_evening_definition': 19,
    'signup_show_bio_m': '["aGroup", "bGroup"]',
    'signup_show_bio_w': '["aGroup", "bGroup"]',
    'premium_includes_standard_badge_title': 'Get Chyrpe Standard',
    'premium_standard_badge_visibility': '[""]',
    'premium_standard_badge_title': 'Includes Chyrpe Standard',
    'countdown_new_title': 'Launch in:',
    'countdown_new_description':
        'You joined just in time for the launch of your region 🎉',
    'countdown_new_button_title': 'Learn More',
    'countdown_new_sheet': '',
    'countdown_new_sheet_title': 'About the countdown',
    'discovery_liked_you_text': 'Liked You',
    'discovery_screen_new_availability_ab': '["aGroup"]',
    'settings_role_matching_title': 'Role-based matching',
    'countdown_new_pn_text':
        'Turn on push notifications to be notified when it starts',
    'discovery_elike_confirm_title': 'Send a Rose?',
    'discovery_elike_confirm_d1':
        'A Rose makes your profile visually stand out to ',
    'discovery_elike_confirm_d2': '. This makes a match 8x more likely.',
    'feedback_own_q_m':
        '["Have you had interesting experiences on Chyrpe so far?", "Are you happy that a femdom dating app was created?"]',
    'feedback_own_q_w':
        '["Have you had interesting experiences on Chyrpe so far?"]',
    'feedback_own_pm_m':
        '["We are glad that you had great experiences on chyrpe!  Share your opinion about it. It makes a difference!", "We are glad to hear that!  Share your opinion about it. It makes a difference!"]',
    'feedback_own_pm_w':
        '["We are glad that you had great experiences on chyrpe!  Share your opinion about it. It makes a difference!"]',
    'discovery_elike_confirm_btn': 'Yes',
    'chat_custom_rules_visible': true,
    'litePaid_tablecomp_liteStandardCompTable':
        '[{"benefit":"Like, Match & Message","number":false,"free_binary_true":true,"paid_binary_true":true,"free_number":1,"paid_number":1},{"benefit":"Chyrpe assistance","number":false,"free_binary_true":true,"paid_binary_true":true,"free_number":1,"paid_number":1},{"benefit":"Likes per day","number":true,"free_binary_true":true,"paid_binary_true":true,"free_number":3,"paid_number":10},{"benefit":"Hide pictures selectively","number":false,"free_binary_true":false,"paid_binary_true":true,"free_number":0,"paid_number":1},{"benefit":"Get Incognito Mode","number":false,"free_binary_true":false,"paid_binary_true":true,"free_number":0,"paid_number":1},{"benefit":"Support an independent startup","number":false,"free_binary_true":false,"paid_binary_true":true,"free_number":0,"paid_number":1},{"benefit":"Shape Chyrpe\'s future, we listen to our users","number":false,"free_binary_true":false,"paid_binary_true":true,"free_number":0,"paid_number":1}]',
    'litePaid_tablecomp_liteStandardCompTable_placeholder':
        '[{"benefit":"Like, Match & Message","number":false,"free_binary_true":true,"paid_binary_true":true,"free_number":1,"paid_number":1},{"benefit":"Chyrpe assistance","number":false,"free_binary_true":true,"paid_binary_true":true,"free_number":1,"paid_number":1},{"benefit":"Likes per day","number":true,"free_binary_true":true,"paid_binary_true":true,"free_number":"{dailyMaleFreeLikeLimit}","paid_number":"{dailyMenLikeLimit}"},{"benefit":"Hide pictures selectively","number":false,"free_binary_true":false,"paid_binary_true":true,"free_number":0,"paid_number":1},{"benefit":"Get Incognito Mode","number":false,"free_binary_true":false,"paid_binary_true":true,"free_number":0,"paid_number":1},{"benefit":"Support an independent startup","number":false,"free_binary_true":false,"paid_binary_true":true,"free_number":0,"paid_number":1},{"benefit":"Shape Chyrpe\'s future, we listen to our users","number":false,"free_binary_true":false,"paid_binary_true":true,"free_number":0,"paid_number":1}]',
    'litePaid_tablecomp_tagline': 'Find who you are looking for, faster',
    'litePaid_pnReminder_btnTitle': 'Start my 1 free week',
    'litePaid_liteName': 'Lite',
    'litePaid_tablecomp_btnTitle_standard': 'Try Standard for',
    'litePaid_tablecomp_btnTitle_lite': 'Continue with Lite',
    'discovery_regional_switch_info_title': 'You’re now in regional matching',
    'discovery_regional_switch_info_body':
        'Regional communities are still small. Give us time to grow, density will increase with time.',
    'discovery_regional_switch_info_btn': 'Continue',
    'popup_ool_upsell_t': 'You’re out of likes for today',
    'popup_ool_upsell_b':
        'By becoming an evolved subscriber, you can like more people every single day!',
    'popup_ool_def_t': 'You’re out of likes for today',
    'popup_ool_def_b': 'Check back tomorrow to like more profiles on Chyrpe!',
    'popup_ool_upsell_btn': 'Get evolved',
    'popup_ool_def_btn': 'I understand',
    'popup_ooel_def_t': 'You’re out of Roses for this week',
    'popup_ooel_def_b':
        'You can still give a normal like to this person, however!',
    'popup_save_men_t': 'Save profiles for later',
    'popup_save_men_b':
        'With Chyrpe evolved, you can save profiles for later if you don\'t want to make a decision right now.',
    'popup_save_men_btn': 'Get evolved',
    'popup_save_women_btn': 'Save',
    'popup_save_women_b':
        'If you are not sure whether to like right now, you can save this profile for later. This is completely free for you as a woman.',
    'popup_save_women_t': 'Save profiles for later',
    'popup_chat_men_b1': 'Respect ',
    'popup_chat_men_b2':
        '\'s boundaries when chatting with them. Disregarding this can result in a ban from Chyrpe.',
    'popup_chat_men_t': 'Show your best behavior',
    'popup_chat_women_t': 'Show your best behavior',
    'popup_chat_women_b1': 'When you chat with ',
    'popup_chat_women_b3':
        'Choose whether they can send explicit messages or silence them entirely. Settings are available in the power board (upper-right corner).',
    'popup_chat_women_b2':
        ', make sure you respect their boundaries. You are in control, use your powers with responsibility.',
    'popup_chat_women_t2': 'It\'s your choice',
    'popup_evolved_t': 'For the extra special...',
    'popup_evolved_b':
        'Give a Rose to those that caught your special attention. It’ll make you stand out from the rest and is 8x as likely to result in a match.',
    'popup_evolved_btn': 'Get Evolved',
    'popup_lineart_purchase_eligibility': '["bGroup", "aGroup"]',
    'verification_auto_photosensitivity_warning':
        'Photosensitivity Warning:\nIncludes flashing colours. If you are photosensitive, proceed with caution or contact us for manual verification.',
    'verification_auto_get_ready_title': 'Center your face',
    'verification_auto_get_ready_btn': 'Start Video Check',
    'verification_auto_get_ready_subtitle': '',
    'verification_auto_processing_title': 'Verification processing',
    'verification_auto_processing_description':
        'This usually takes less than 2 minutes',
    'verification_auto_rejection_headline':
        'We couldn\'t verify you yet. Please try again.',
    'verification_auto_rejection_explanation':
        'In order for your verification to go smoothly:\n\n- Ensure there is at least one clear photo of your face on your profile.\n- Ensure you are in a well-lit setting.\n\nYou can also request a manual review from us (takes up to 48 hours).',
    'verification_auto_fail_try_again_btn': 'Continue',
    'verification_auto_fail_req_review_btn': 'Request review',
    'verification_auto_fail_bio_headline':
        'There may be a problem with your bio',
    'verification_auto_fail_bio_explanation':
        'Bios and Prompts on Chyrpe must not contain sexual or fetish content. \n\nEspecially female users have told us they want to decide when to speak about this.\n\nPlease ensure your bio and prompts follow our rules and try again or request a manual review (takes up to 48 hours).',
    'auto_verification_manual_reclass_heading': 'Manual review',
    'auto_verification_manual_reclass_subheading':
        'A team member is reviewing your verification now',
    'auto_verification_manual_reclass_explanation':
        'You have been put into an additional human review. Don\'t worry, this does not mean your profile is problematic. It can take up to 48 hours.',
    'iap_boost_home_tagline': 'Get seen up to 10x more',
    'iap_roses_home_tagline': 'Special likes that make you stand out',
    'iap_boost_home_active_title': 'Boosting now',
    'iap_rose_purchase_title': 'Send a Rose to show your like is special',
    'iap_rose_purchase_description':
        'Roses make your like stand out and are 8x more likely to lead to a match.',
    'iap_purchase_eupgrade_tagline':
        'Get 7 free Roses per week & many more benefits with Chyrpe Evolved',
    'iap_purchase_eupgrade_tagline_placeholder':
        'Get {weeklyEvolvedLikeLimit} free Roses per week & many more benefits with Chyrpe Evolved',
    'iap_boost_purchase_title': 'Boost your profile for maximum visibility',
    'iap_boost_purchase_description':
        'Each boost increases your visibility up to 10x for 2 hours.',
    'iap_rreceipt_title': 'See if they have read your messages',
    'iap_rreceipt_description':
        'Read receipts indicate whether your messages have been seen or not. You can add this feature to each match you get.',
    'iap_feedback_title': 'See the feedback you get for your profile',
    'iap_feedback_description':
        'Get access to real feedback for your profile and make improvements that matter',
    'iap_feedback_pupgrade':
        'See all your feedback & get many more benefits with Chyrpe Plus',
    'iap_boost_startnow_description':
        'Tap below to start boosting your profile for the next hour',
    'iap_boost_start_now_title': 'Boost now?',
    'iap_boost_started_now_title': 'Boosting now!',
    'iap_boost_started_now_description':
        'Relax and enjoy extra attention in the next hour. We keep our fingers crossed that it will lead to extra likes and matches!',
    'discovery_everyseen_title': 'You’ve seen everybody for now',
    'discovery_everyseen_description':
        'Adjust your filters to see more people, reset your skipped profiles or come back later',
    'discovery_everyseen_btn1': 'Review filters',
    'discovery_everyseen_btn2': 'Review skipped profiles',
    'evolved_screen_age_title': 'Age Range',
    'evolved_screen_age_description': 'Select the age range you want to see',
    'evolved_tab_select_age_range_title': 'Select age range',
    'iap_diamond_purchase_title': 'Send a direct message before you match',
    'iap_diamond_purchase_description':
        'A diamond like allows you to land directly in their inbox. Use it for those chances you really don’t want to miss.',
    'iap_diamond_inchat_title': 'This is a diamond like',
    'iap_diamond_inchat_description':
        ' wanted to send you a special token of appreciation. You can match and chat or reject it.',
    'iap_boost_finished_title': 'Boost complete!',
    'iap_boost_finished_description':
        'Sometimes timing makes a huge difference and this time, you didn’t get any new likes. Here are some tips to improve your profile for the best chance of success.\n',
    'iap_boost_finished_tips': 'Our Profile Tips',
    'iap_boost_finished_url': 'https://www.chyrpe.com/how-to-write-a-great-bio',
    'discovery_everyseen_btn_2a': 'Reload this page',
    'discovery_everyseen_email_btn': 'email support: <EMAIL>',
    'discovery_everyseen_email_visible': true,
    'discovery_empty_offline_title': 'It seems you are offline right now',
    'discovery_empty_offline_description':
        'Go back online to see new suggestions',
    'searchpref_light_hint_caption1': 'You currently have Chyrpe Light',
    'searchpref_light_hint_caption2': 'Get all filters with Standard',
    'searchpref_light_hint_btn': 'Get a free trial today',
    'standard_signup_la_benefitlist':
        '["More filters for matching", "10 likes per day", "Unlimited dislikes", "Get Incognito Mode", "Hide images selectively", "Priority support"]',
    'standard_signup_la_benefitlist_placeholder':
        '["More filters for matching", "{dailyMenLikeLimit} likes per day", "Unlimited dislikes", "Get Incognito Mode", "Hide images selectively", "Priority support"]',
    'standard_signup_la_title': 'Chyrpe Standard',
    'standard_signup_la_subtitle':
        'Get all filters & find the right one faster',
    'standard_signup_la_purchasetitle':
        'Buy once for life or try the subscription for free',
    'standard_signup_la_trial_caption': 'Try for free',
    'standard_signup_la_btn_title_trial': 'Start 1 week free trial',
    'standard_signup_la_btn_title_notrial': 'Continue',
    'standard_signup_btn_liteswitch': 'Use Chyrpe Lite for free',
    'iap_diamond_confirmation_title': 'Diamond like sent',
    'iap_diamond_confirmation_subtitle': 'Good luck!',
    'iap_rose_confirmation_title': 'Like upgraded to a Rose',
    'iap_rose_confirmation_subtitle': 'Good luck!',
    'default_standard_package': 'chyrpe_standard_lifetime_e',
    'evolved_hint_title': 'For the extra special...',
    'evolved_hint_description':
        'Give a rose to those that caught your special attention. It’ll make you stand out from the rest.',
    'evolved_hint_btn': 'Get Evolved',
    'editprofile_select_dlocation_sheet_title': 'Select display location',
    'editprofile_select_dlocation_sheet_label': 'Enter a city or state...',
    'editprofile_select_dlocation_sheet_furtherhint':
        'If you can’t find what you’re looking for, try another search term or let us know. We can help!',
    'editprofile_select_dlocation_sheet_errorhint':
        'No results found. \n\nPlease try with a different search term or contact us!',
    'editprofile_location_sheet_title': 'Location on Chyrpe',
    'editprofile_location_sheet_explanation':
        'You can decide yourself which location is displayed on your profile or if it is shown at all. Your suggestions will be based on your physical (GPS) location. Go to the settings to review your device’s location settings and make sure you grant Chyrpe sufficient access. We do not use your location for anything but matching and never share it.\nIn rare cases, GPS location determination may not be correct. In these cases, reach out to us so we can correct it for you.',
    'verification_auto_gender_confirm_headline': 'You\'re verified',
    'verification_auto_gender_confirm_subtitle':
        'You can now type and confirm your gender identity and get started',
    'verification_auto_gender_confirm_btn': 'Confirm',
    'discount_evolved_monthly':
        '{"baseProduct": "evolvedMonthly", "tagline1": "Get ", "tagline2": " your first month of Evolved", "imageUrl": "https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FLineArtGirl2.webp?alt=media&token=3945efbc-f1ec-4540-917b-2d66c4b269b8", "dualBenefitlist": {"product1": "Standard", "product2": "Evolved", "benefitlist": [{"benefit": "Matching", "product1": true, "product2": true}, {"benefit": "See all your likes immediately", "product1": false, "product2": true}, {"benefit": "Double likes", "product1": false, "product2": true}, {"benefit": "7 weekly roses", "product1": false, "product2": true}]}\n}',
    'discount_evolved_weekly':
        '{"baseProduct": "evolvedWeekly", "tagline1": "Get ", "tagline2": " your first week of Evolved", "imageUrl": "https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FLineArtGirl2.webp?alt=media&token=3945efbc-f1ec-4540-917b-2d66c4b269b8", "dualBenefitlist": {"product1": "Standard", "product2": "Evolved", "benefitlist": [{"benefit": "Matching", "product1": true, "product2": true}, {"benefit": "See all your likes immediately", "product1": false, "product2": true}, {"benefit": "Double likes", "product1": false, "product2": true}, {"benefit": "7 weekly roses", "product1": false, "product2": true}]}\n}',
    'discount_gold_monthly':
        '{"baseProduct": "goldMonthly", "tagline1": "Get ", "tagline2": " your first month of Gold", "imageUrl": "https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FLineArtGirl2.webp?alt=media&token=3945efbc-f1ec-4540-917b-2d66c4b269b8", "dualBenefitlist": {"product1": "Standard", "product2": "Gold", "benefitlist": [{"benefit": "Matching", "product1": true, "product2": true}, {"benefit": "See all your likes immediately", "product1": false, "product2": true}, {"benefit": "More likes", "product1": false, "product2": true}, {"benefit": "5 weekly roses", "product1": false, "product2": true}]}\n}',
    'discount_gold_weekly':
        '{"baseProduct": "goldWeekly", "tagline1": "Get ", "tagline2": " your first week of Gold", "imageUrl": "https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FLineArtGirl2.webp?alt=media&token=3945efbc-f1ec-4540-917b-2d66c4b269b8", "dualBenefitlist": {"product1": "Standard", "product2": "Gold", "benefitlist": [{"benefit": "Matching", "product1": true, "product2": true}, {"benefit": "See all your likes immediately", "product1": false, "product2": true}, {"benefit": "More likes", "product1": false, "product2": true}, {"benefit": "5 weekly roses", "product1": false, "product2": true}]}\n}\n',
    'discount_standard_monthly':
        '{"baseProduct": "standardMonthly", "tagline1": "Get ", "tagline2": " your first month of Standard", "imageUrl": "https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FLineArtGirl2.webp?alt=media&token=3945efbc-f1ec-4540-917b-2d66c4b269b8", "dualBenefitlist": {"product1": "Free", "product2": "Standard", "benefitlist": [{"benefit": "Matching", "product1": true, "product2": true}, {"benefit": "More likes", "product1": false, "product2": true}, {"benefit": "Incognito mode", "product1": false, "product2": true}, {"benefit": "Hide pictures selectively", "product1": false, "product2": true}]}\n}',
    'discount_purchase_terms':
        'By tapping “Continue”, you will purchase an auto-renewing subscription for chyrpe. If you do not cancel up to 24 hours before the subscription renews, your subscription will auto-renew for its regular price (without any discounts) until you cancel via your app store settings. You also agree to our Terms.',
    'chat_preview_tagline': 'Match now and chat!',
    'matching_save_visible_women': true,
    'delete_p1_q': 'Why are you leaving Chyrpe?',
    'delete_reason_list':
        '["I met someone", "I need a break from dating apps", "Different than expected", "Community is too small", "Quality of profiles is too low", "Paid options are too expensive", "Missing features", "Prefer not to say"]',
    'delete_p2_1': 'What would you like to do?',
    'delete_p1_pause_title': 'Pause your account',
    'delete_p1_pause_explanation':
        'You will no longer be shown to others but keep your matches and your account.',
    'delete_p1_pause_btn': 'Pause',
    'delete_p2_delete_title': 'Delete account',
    'delete_p2_delete_explanation':
        'Erase your account permanently, including all matches and conversations.',
    'delete_p2_delete_btn': 'Delete',
    'delete_p1_img':
        'https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FLineArtWoman2.webp?alt=media&token=51f3e1e5-8eb4-42bd-bc1d-b35a3708dfa4',
    'delete_reasons_nav_pause': '["I need a break from dating apps"]',
    'delete_reasons_nav_newchapter':
        '["Community is too small", "Quality of profiles is too low", "Prefer not to say"]',
    'delete_thanks_pause_title': 'Thank you!',
    'delete_thanks_pause_explanation':
        'We take your feedback seriously and will act on it. To see the improvements we make, consider pausing your account. You will not be shown to new people but keep your profile and matches.',
    'delete_thanks_pause_img':
        'https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FLineArtGirl2.webp?alt=media&token=3945efbc-f1ec-4540-917b-2d66c4b269b8',
    'delete_thanks_pause_btn': 'Try it',
    'delete_thanks_pause_no': 'No thanks',
    'delete_pause_title': 'How about a pause instead?',
    'delete_pause_explanation':
        'You will no longer be shown to others but keep your matches and your account, so you can come back anytime with minimum effort.',
    'delete_pause_img':
        'https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FMysteryLadyWEBP.webp?alt=media&token=b789f255-871a-4b69-ad0d-83f3613a045f',
    'delete_pause_btn': 'Try it',
    'delete_pause_no': 'No thanks',
    'delete_newchapter_title': 'How about a fresh chapter?',
    'delete_newchapter_explanation':
        'Dating isn’t an exact science.Use Fresh Chapter to refresh your feed while keeping your profile and matches intact.',
    'delete_newchapter_img':
        'https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FLineArtGirl2.webp?alt=media&token=3945efbc-f1ec-4540-917b-2d66c4b269b8',
    'delete_newchapter_btn': 'Try it',
    'delete_newchapter_no': 'No thanks',
    'delete_reasons_textfield_titles':
        '{"Different than expected": "What is different?", "Missing features": "What is missing?"}',
    'delete_reasons_nav_thanks_pause': '["Paid options are too expensive"]',
    'delete_reasons_nav_textfield':
        '["Different than expected", "Missing features"]',
    'delete_textfield_next': 'Next',
    'delete_textfield_skip': 'Skip',
    'delete_textfield_preview': 'Your feedback...',
    'profile_location_private': 'Location set to private',
    'chat_ask_app_feedback_chance': 0.5,
    'chat_ask_feedback_men': true,
    'chat_ask_feedback_women': true,
    'intro_test_data':
        '[{"publicProfile": "/users/tSx0KH5R5uoIm8MAG0sP/publicProfile/eUdnh81KGBkEoVmsL7bQ", "uid": "tSx0KH5R5uoIm8MAG0sP"},\n  {"publicProfile": "/users/fHtED2TMvD5MSknMV6tr/publicProfile/36lLBYUBYS9zCifjLpMw", "uid": "fHtED2TMvD5MSknMV6tr"},\n{"publicProfile": "/users/wryd4OU124DjDWuvCDsR/publicProfile/v4XWoEiHk8NNqfUbClcB", "uid": "wryd4OU124DjDWuvCDsR"}]',
    'evolved_screen_test_data':
        '[{"publicProfile": "/users/DHpsPYp3AihGoqs6fMsj64dcTM63/publicProfile/kpHTXpCDdNcWE4kygRQX", "uid": "DHpsPYp3AihGoqs6fMsj64dcTM63"},\n  {"publicProfile": "/users/RBQXtn3MO5ci6IdqT5RchcSSGWw2/publicProfile/DidNTW75s3GVAW9c3Ych", "uid": "RBQXtn3MO5ci6IdqT5RchcSSGWw2"},\n{"publicProfile": "/users/XI9mSrdQ85UkDmlIMZBI85LJHx03/publicProfile/20Q1mXhpmAskBjbvVk95", "uid": "XI9mSrdQ85UkDmlIMZBI85LJHx03"},\n{"publicProfile": "/users/b2vf7Oie9WUFsofRl95P8Zd2cvh1/publicProfile/Wvoh3XqmTXfPLNkWnp2A", "uid": "b2vf7Oie9WUFsofRl95P8Zd2cvh1"},\n{"publicProfile": "/users/fBABQZjioCXhnl3RGgWHWdOlgpq1/publicProfile/Zlq1DLHmcGSplniFSugl", "uid": "fBABQZjioCXhnl3RGgWHWdOlgpq1"}]',
    'discovery_welcome_intro_title': {
      'english': 'Welcome to Chyrpe!',
      'french': 'Bienvenue sur Chyrpe',
      'german': 'Willkommen bei Chyrpe',
      'spanish': 'Bienvenida a Chyrpe'
    },
    'discovery_welcome_intro_subtitle':
        'To get started, allow us to familiarise you with the app’s look & features.',
    'discovery_welcome_intro_start_btn': 'Start Intro',
    'discovery_welcome_intro_skip_btn': 'Skip',
    'discovery_welcome_intro_time_est': '3 minutes',
    'discovery_show_women_new_intro': true,
    'tutorial_chat_t1': 'Here you have an overview of all ',
    'tutorial_chat_t2': 'your matches and chats!',
    'tutorial_chat_t3': 'Click on a profile, ',
    'tutorial_chat_t4': 'to ',
    'tutorial_chat_t5': 'open a new chat.',
    'tutorial_chat_t6': 'All ongoing conversations will appear below.',
    'tutorial_chat_t6a': 'All ongoing conversations will appear below.',
    'tutorial_chat_window_t1':
        "Now here's what makes the chat experience on Chyrpe truly different: \n\n",
    'tutorial_chat_window_t2': 'The Powerbard.',
    'tutorial_chat_window_t3': 'Here, ',
    'tutorial_chat_window_t4': 'only you ',
    'tutorial_chat_window_t5': 'get to ',
    'tutorial_chat_window_t6': 'set boundaries and rules ',
    'tutorial_chat_window_t7': 'for the conversation.',
    'tutorial_chat_window_t8': 'Chyrpe is all about making you ',
    'tutorial_chat_window_t9': 'feel comfortable, ',
    'tutorial_chat_window_t10': 'even in chats!',
    'tutorial_discovery1_t1': 'Hello!\n',
    'tutorial_discovery1_t2': 'Welcome to Chyrpe, the app designed for women.',
    'tutorial_discovery1_t3': 'This is a ',
    'tutorial_discovery1_t4': 'safe space for women,\n',
    'tutorial_discovery1_t5':
        'created to enhance the female online dating experience.',
    'tutorial_discovery1_t6':
        'To finetune your profile and other in-app features, click here to view your current personal profile.',
    'tutorial_discovery2_t1': "Now let's get down to business!\n",
    'tutorial_discovery2_t2': 'We are now in the ',
    'tutorial_discovery2_t3': 'Matching Screen',
    'tutorial_discovery2_t4': ' and are seeing the first profile',
    'tutorial_discovery2_t5': "Let's have a closer look\n",
    'tutorial_discovery2_t6': 'Just ',
    'tutorial_discovery2_t7': 'click on the Matchcard ',
    'tutorial_discovery2_t8': 'to open their profile with more information',
    'tutorial_discovery3_t1': "Hmmm...\n",
    'tutorial_discovery3_t2': 'Not really our cup of tea, right?',
    'tutorial_discovery3_t3':
        "If you'e nore sure and want to decide later, you can ",
    'tutorial_discovery3_t4': 'save their profile',
    'tutorial_discovery3_t5': "To ",
    'tutorial_discovery3_t6': 'dislike ',
    'tutorial_discovery3_t7': "someone, you can ",
    'tutorial_discovery3_t8': 'swipe left ',
    'tutorial_discovery3_t9': "or",
    'tutorial_discovery3_t10': ' click on the X.',
    'tutorial_discovery3_t11': "Bye-bye toxic masculinity!",
    'tutorial_discovery3_t12': "He seems so sweet, let's ",
    'tutorial_discovery3_t13': 'give him a like!\n',
    'tutorial_discovery3_t14': 'Do so by ',
    'tutorial_discovery3_t15': 'swiping right ',
    'tutorial_discovery3_t16': 'or ',
    'tutorial_discovery3_t17': 'clicking the tick button.',
    'tutorial_discovery4_t1':
        "Before you start swiping, let's check out where you can ",
    'tutorial_discovery4_t2': 'find your matches.',
    'tutorial_editprofile_t1': 'Want to check what your changes look like?\n',
    'tutorial_editprofile_t2': 'You can easily do so by clicking the ',
    'tutorial_editprofile_t3': 'preview button!',
    'tutorial_evolvedtab_t1': "Lastly, for an ",
    'tutorial_evolvedtab_t2': 'overview of all likes ',
    'tutorial_evolvedtab_t3': "click here!",
    'tutorial_evolvedtab_t4': "On this page, you can see all ",
    'tutorial_evolvedtab_t5': 'likes you have received and sent, ',
    'tutorial_evolvedtab_t6': "as well as the ",
    'tutorial_evolvedtab_t7': 'profiles you have saved ',
    'tutorial_evolvedtab_t8': "for later.",
    'tutorial_evolvedtab_t9': "Now that you're all set and ready, ",
    'tutorial_evolvedtab_t10': 'have fun chyrp-ing!',
    'tutorial_matchstage2_t1': "How lucky, ",
    'tutorial_matchstage2_t2': "it's a match!\n",
    'tutorial_matchstage2_t3': "You even got a message!",
    'tutorial_matchstage2_m_p1': 'Hey ',
    'tutorial_matchstage2_m_p2': ', you look gorgeous!',
    'tutorial_ownprofile_t1':
        'Feel free to scroll through your profile! When you are done, tap anywhere to get to the next step.',
    'tutorial_profiledetail_t1': "If you want, you can",
    'tutorial_profiledetail_t2': ' rate this profile.\n',
    'tutorial_profiledetail_t3':
        'This is a feature that only exists for the women on Chyrpe',
    'tutorial_profiledetail_t4':
        "The ratings are anonymous and help their receivers to enhance their profile, ensuring a positive experience, where ",
    'tutorial_profiledetail_t5': "womens' voices are heard and valued.",
    'tutorial_profilehome_t1': 'Wow!\nLook at this beauty!',
    'tutorial_profilehome_t2':
        'If you feel like adjusting your stunning profile, you can do so by clicking here.',
    'settings_professionals_info_title': 'More information on this',
    'settings_professionals_info':
        'Here you can filter out users who may be on Chyrpe for professional purposes. \n\nIf you encounter someone who is dishonest about their intentions, please report them so transparency is maintained on Chyrpe. \n\nPlease note, while you will no longer see professionals, they may still see and like you.',
    'settings_professionals_title': 'Hide Findoms',
    'settings_professionals_forall': false,
    'evolved_tab_info_title': 'Likes never expire',
    'evolved_tab_info':
        'The 24 hour timers on this screen are not an expiry time but the time you have to upgrade a like you made to a rose.',
    'divine_title_bg':
        'https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FChyrpe_Divine_Bg.webp?alt=media&token=92627f50-9cf5-4dba-aaa8-e12f2edf4131',
    'divine_logo':
        'https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FChype_Divine_Logo.webp?alt=media&token=0507c5f9-a214-455e-bed6-9cb4a02abd4a',
    'divine_title_slogan': 'Get all Chyrpe features & unlimited likes',
    'purchases_divine_offer_string':
        '[{"packageId": "chyrpe_divine_3m", "duration": "3 months", "durationWeeks": 12, "weeklyAlternativeId": "chyrpe_divine_1w"}, {"packageId": "chyrpe_divine_1m", "duration": "1 month", "durationWeeks": 4, "weeklyAlternativeId": "chyrpe_divine_1w"}, {"packageId": "chyrpe_divine_1w", "duration": "1 week", "durationWeeks": 1, "weeklyAlternativeId": "chyrpe_divine_1w"}]',
    'divine_symbolicBenefitlist':
        '[{"benefit": "Get unlimited likes", "symbolLink": "https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FDivine%2FInfinite_Symbol.webp?alt=media&token=42e16461-2c1a-4d29-ad1e-7482c805e84e"}, {"benefit": "See everybody who likes you", "symbolLink": "https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FDivine%2FPeople_Symbol.webp?alt=media&token=2c037471-6afa-4d97-81b0-808b8f7092a9"}, {"benefit": "Send 5 roses per week", "symbolLink": "https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FDivine%2FRose_Symbol.webp?alt=media&token=bb3b667e-2368-4535-b58b-a8217d61eea1"}, {"benefit": "Boost your profile 2 times per week", "symbolLink": "https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FDivine%2FBolt_Symbol.webp?alt=media&token=599707e1-5ce6-4a39-9524-68873fcb88dc"}, {"benefit": "Unlimited read receipts", "symbolLink": "https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FDivine%2FRead_Receipt_Symbol.webp?alt=media&token=5b5e69ee-178e-42ec-aded-915653f42457"}, {"benefit": "Incognito Mode", "symbolLink": "https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FDivine%2FIncognito_Symbol.webp?alt=media&token=1b50863b-b643-4ec1-b50c-87e7f5b0f086"}, {"benefit": "Hide pictures & your name", "symbolLink": "https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FDivine%2FEye_Symbol.webp?alt=media&token=734e30d8-0f11-440c-b12a-9a6b918466ec"}]',
    'divine_symbolicBenefitlist_placeholder':
        '[{"benefit": "Get unlimited likes", "symbolLink": "https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FDivine%2FInfinite_Symbol.webp?alt=media&token=42e16461-2c1a-4d29-ad1e-7482c805e84e"}, {"benefit": "See everybody who likes you", "symbolLink": "https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FDivine%2FPeople_Symbol.webp?alt=media&token=2c037471-6afa-4d97-81b0-808b8f7092a9"}, {"benefit": "Send {weeklyDivineLikeLimit} roses per week", "symbolLink": "https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FDivine%2FRose_Symbol.webp?alt=media&token=bb3b667e-2368-4535-b58b-a8217d61eea1"}, {"benefit": "Boost your profile {weeklyDivineBoostLimit} times per week", "symbolLink": "https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FDivine%2FBolt_Symbol.webp?alt=media&token=599707e1-5ce6-4a39-9524-68873fcb88dc"}, {"benefit": "Unlimited read receipts", "symbolLink": "https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FDivine%2FRead_Receipt_Symbol.webp?alt=media&token=5b5e69ee-178e-42ec-aded-915653f42457"}, {"benefit": "Incognito Mode", "symbolLink": "https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FDivine%2FIncognito_Symbol.webp?alt=media&token=1b50863b-b643-4ec1-b50c-87e7f5b0f086"}, {"benefit": "Hide pictures & your name", "symbolLink": "https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FDivine%2FEye_Symbol.webp?alt=media&token=734e30d8-0f11-440c-b12a-9a6b918466ec"}]',
    'divine_p2_slogan1': 'Get all Chyrpe features',
    'divine_p2_slogan2': 'See everybody who likes you \n& get unlimited likes',
    'divine_p2_more_btn': 'Show all benefits',
    'divine_p2_benefitstitle': 'Choose your plan:',
    'divine_logo_dark':
        'https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FChype_Divine_Logo_Dark.png?alt=media&token=b100a5b3-ba1f-4d2e-a3ff-be1472dd39fc',
    'divine_legacy_t': 1,
    'divine_onepage_t': 3,
    'divine_show_days': '[2, 4]',
    'divine_day_replace':
        '{"1 week": "7 days", "1 month": "30 days", "3 months": "90 days"}',
    'popup_ool_upsell_b_divine':
        'By becoming a Chyrpe Divine subscriber, you can like more people every single day!',
    'popup_ool_upsell_btn_divine': 'Get Chyrpe Divine',
    'iap_purchase_eupgrade_tagline_divine':
        'Get 5 free Roses per week & many more benefits with Chyrpe Divine',
    'iap_purchase_eupgrade_tagline_divine_placeholder':
        'Get {weeklyDivineLikeLimit} free Roses per week & many more benefits with Chyrpe Divine',
    'evolved_hint_btn_divine': 'Get Chyrpe Divine',
    'dlimit_likdep_body_standard_divine':
        'Continue swiping when your likes have been refreshed. You can get unlimited likes with Chyrpe Divine.',
    'weekly_boosts_divine': 2,
    'weekly_elikes_divine': 5,
    'daily_nlikes_w_divine': 10000,
    'daily_nlikes_m_plus': 15,
    'daily_nlikes_w_plus': 50,
    'generic_signup_hide_string': 'Hide in my profile',
    "sign_up_order":
        '["gender", "name", "age", "gender_preferences", "femdom_role", "femdom_experience", "dating_intentions", "height", "ethnicity", "children", "family_plans", "job_title", "employer", "university", "education_level", "religion", "politics", "drinking", "smoking", "kinks", "images", "prompts", "location"]',
    "sign_up_var_kind":
        '[{ "screen": "kinks", "kind": "special" }, { "screen": "gender", "kind": "special" },{ "screen": "name", "kind": "special" },{ "screen": "age", "kind": "special" },{ "screen": "gender_preferences", "kind": "special" },{ "screen": "femdom_role", "kind": "special" },{ "screen": "femdom_experience", "kind": "radio" },{ "screen": "dating_intentions", "kind": "checkboxes" },{ "screen": "height", "kind": "special" },{ "screen": "ethnicity", "kind": "checkboxes" },{ "screen": "children", "kind": "radio" },{ "screen": "family_plans", "kind": "radio" },{ "screen": "job_title", "kind": "textfield" },{ "screen": "employer", "kind": "textfield" },{ "screen": "university", "kind": "textfield" },{ "screen": "education_level", "kind": "radio" },{ "screen": "religion", "kind": "checkboxes" },{ "screen": "politics", "kind": "radio" },{ "screen": "drinking", "kind": "radio" },{ "screen": "smoking", "kind": "radio" },{ "screen": "images", "kind": "special" },{ "screen": "prompts", "kind": "special" },{ "screen": "location", "kind": "special" }]',
    "options_femdom_experience":
        '["I\'m new to this", "I\'ve tried it before", "I have experience"]',
    "options_dating_intentions":
        '["Long-term relationships","Short-term relationships","One night stand","Play sessions","Platonic relationship","Virtual relationship"]',
    "options_ethnicity":
        '["Black/African Descent","East Asian","Hispanic/Latino","Middle Eastern","Native American","Pacific Islander","South Asian","Southeast Asian","White/Caucasian","Other","Prefer not to say"]',
    "options_children":
        '["Don’t have children","Have children","Prefer not to say"]',
    "options_family_plans":
        '["Don’t want children","Want children","Open to children","Not sure","Prefer not to say"]',
    "options_education_level":
        '[ "High School","Bachelor","Master or Doctorate","Prefer not to say"]',
    "options_religion":
        '["Agnostic","Atheist","Buddhist","Catholic","Christian","Hindu","Jewish","Muslim","Sikh","Spiritual","Other","Prefer not to say"]',
    "options_politics":
        '["Left","Center","Right","Not Political","Other","Prefer not to say"]',
    "options_drinking": '["Yes","Occasionally","No","Prefer not to say"]',
    "options_smoking": '["Yes","Occasionally","No","Prefer not to say"]',
    "editor_config_gender_preferences":
        '''{"english" : {"title": "Who would you like to see?","subheading": "Select all the people you\u2019re open to meeting","hideable": true,"dbKey": "genderReq"}, 
        "french": {"title": "Qui veux-tu voir ?","subheading": "Select all the people you\u2019re open to meeting","hideable": true,"dbKey": "genderReq"}, 
        "german" : {"title": "Wen möchtest du sehen?","subheading": "Select all the people you\u2019re open to meeting","hideable": true,"dbKey": "genderReq"}, 
        "spanish": {"title": "¿A quién te gustaría ver?","subheading": "Select all the people you\u2019re open to meeting","hideable": true,"dbKey": "genderReq"}}''',
    "editor_config_femdom_role": '''{
"english": {"title": "What are you?","subheading": "Chyrpe will help you find the relationship dynamic that suits you best.","info_btn_title": "What does this mean?","info_btn": "A dominant partner in a relationship or dynamic likes to set the tone and direction. A submissive partner likes to follow and be led by their dominant partner. Switches feel comfortable in either role or like to alternate roles within a relationship or dynamic.","hideable": true,"dbKey": "publicRole"}, 
   "french": {"title": "Quel est ton rôle ?","subheading": "Chyrpe t’aide à trouver la dynamique relationnelle qui te correspond.","info_btn_title": "Qu’est-ce que ça veut dire?","info_btn": "A dominant partner in a relationship or dynamic likes to set the tone and direction. A submissive partner likes to follow and be led by their dominant partner. Switches feel comfortable in either role or like to alternate roles within a relationship or dynamic.","hideable": true,"dbKey": "publicRole"},      
   "german": {"title": "Was bist du?","subheading": "Chyrpe hilft dir, die Beziehungsdynamik zu finden, die zu dir passt.","info_btn_title": "Was bedeutet das?","info_btn": "A dominant partner in a relationship or dynamic likes to set the tone and direction. A submissive partner likes to follow and be led by their dominant partner. Switches feel comfortable in either role or like to alternate roles within a relationship or dynamic.","hideable": true,"dbKey": "publicRole"}, 
    "spanish": {"title": "¿Qué eres?","subheading": "Chyrpe te ayudará a encontrar la dinámica de relación que más encaje contigo.","info_btn_title": "¿Qué significa esto?","info_btn": "A dominant partner in a relationship or dynamic likes to set the tone and direction. A submissive partner likes to follow and be led by their dominant partner. Switches feel comfortable in either role or like to alternate roles within a relationship or dynamic.","hideable": true,"dbKey": "publicRole"}
        }''',
    "editor_config_height": '''{
"english": {"title": "What’s your height?","hideable": true,"info_btn_title": "Why this matters?","info_btn": "We ask about height because it’s an important preference for some users when building attraction. At Chyrpe, we believe in supporting all kinds of connections, based on what matters most to you.","dbKey": "height"},
"french": {"title": "Quelle est ta taille?","hideable": true,"info_btn_title": "Pourquoi c’est important?","info_btn": "We ask about height because it’s an important preference for some users when building attraction. At Chyrpe, we believe in supporting all kinds of connections, based on what matters most to you.","dbKey": "height"},
"german": {"title": "Wie groß bist du?","hideable": true,"info_btn_title": "Warum das wichtig ist","info_btn": "We ask about height because it’s an important preference for some users when building attraction. At Chyrpe, we believe in supporting all kinds of connections, based on what matters most to you.","dbKey": "height"},
"spanish": {"title": "¿Cuál es tu estatura?","hideable": true,"info_btn_title": "¿Por qué importa esto?","info_btn": "We ask about height because it’s an important preference for some users when building attraction. At Chyrpe, we believe in supporting all kinds of connections, based on what matters most to you.","dbKey": "height"}
}''',
    "editor_config_femdom_experience": '''
{
"english": {"title": "What\u2019s your level of experience?","subheading": "Chyrpe is open to all people and supports you in finding the right partner.","info_btn_title": "What does this mean?","info_btn": "Chyrpe is open to everyone. Knowing your level of experience in relationships with a strong female partner makes it easier for others to communicate with you in the right way.","hideable": true,"dbKey": "experienceLevel"},
"french": {"title": "Quel est ton niveau d’expérience?","subheading": "Chyrpe est ouvert à tous·tes et t’aide à trouver le·la bon·ne partenaire.","info_btn_title": "Qu’est-ce que ça veut dire?","info_btn": "Chyrpe is open to everyone. Knowing your level of experience in relationships with a strong female partner makes it easier for others to communicate with you in the right way.","hideable": true,"dbKey": "experienceLevel"},
"german": {"title": "Wie viel Erfahrung hast du?","subheading": "Chyrpe ist für alle da – wir helfen dir, die richtige Person zu finden.","info_btn_title": "Was bedeutet das?","info_btn": "Chyrpe is open to everyone. Knowing your level of experience in relationships with a strong female partner makes it easier for others to communicate with you in the right way.","hideable": true,"dbKey": "experienceLevel"},
"spanish": {"title": "¿Cuál es tu nivel de experiencia?","subheading": "Chyrpe está abierta a todas las personas y te ayuda a encontrar la pareja adecuada.","info_btn_title": "¿Qué significa esto?","info_btn": "Chyrpe is open to everyone. Knowing your level of experience in relationships with a strong female partner makes it easier for others to communicate with you in the right way.","hideable": true,"dbKey": "experienceLevel"}
}
''',
    "editor_config_dating_intentions": '''
{
"english": {"title": "What relationship do you look for?","hideable": false,"dbKey": "relationPreferences"},
"french": {"title": "Quel type de relation cherches-tu?","hideable": false,"dbKey": "relationPreferences"},
"german": {"title": "Welche Art von Beziehung suchst du?","hideable": false,"dbKey": "relationPreferences"},
"spanish": {"title": "¿Qué tipo de relación buscas?","hideable": false,"dbKey": "relationPreferences"}
}
''',
    "editor_config_ethnicity": '''
{
"english": {"title": "What\u2019s your ethnicity?","info_btn_title": "Why do we ask this?","info_btn": "Chyrpe created the ethnicity preference to support users seeking partners with shared cultural backgrounds and values. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "ethnicity"},
"french": {"title": "Quelle est ton origine ethnique?","info_btn_title": "Pourquoi on te demande ça?","info_btn": "Chyrpe created the ethnicity preference to support users seeking partners with shared cultural backgrounds and values. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "ethnicity"},
"german": {"title": "Was ist deine ethnische Zugehörigkeit?","info_btn_title": "Warum wir das fragen","info_btn": "Chyrpe created the ethnicity preference to support users seeking partners with shared cultural backgrounds and values. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "ethnicity"},
"spanish": {"title": "¿Cuál es tu origen étnico?","info_btn_title": "¿Por qué preguntamos esto?","info_btn": "Chyrpe created the ethnicity preference to support users seeking partners with shared cultural backgrounds and values. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "ethnicity"}
}
''',
    "editor_config_children": '''
{
"english": {"title": "Do you have children?","info_btn_title": "Why do we ask this?","info_btn": "We ask about children to help users find matches whose lifestyles and family goals align with their own. This makes it easier to connect with someone who shares your values and priorities. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "children"},
"french": {"title": "As-tu des enfants ?","info_btn_title": "Pourquoi on te demande ça ?","info_btn": "We ask about children to help users find matches whose lifestyles and family goals align with their own. This makes it easier to connect with someone who shares your values and priorities. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "children"},
"german": {"title": "Hast du Kinder?","info_btn_title": "Warum wir das fragen","info_btn": "We ask about children to help users find matches whose lifestyles and family goals align with their own. This makes it easier to connect with someone who shares your values and priorities. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "children"},
"spanish": {"title": "¿Tienes hijos?","info_btn_title": "¿Por qué preguntamos esto?","info_btn": "We ask about children to help users find matches whose lifestyles and family goals align with their own. This makes it easier to connect with someone who shares your values and priorities. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "children"}
}
''',
    "editor_config_family_plans": '''
{
"english" : {"title": "What are your family plans?","info_btn_title": "Why do we ask this?","info_btn": "We ask about family plans to help users find matches whose lifestyles and family goals align with their own. This makes it easier to connect with someone who shares your values and priorities. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "familyPlans"},
"french" : {"title": "Quels sont tes projets familiaux?","info_btn_title": "Pourquoi on te demande ça?","info_btn": "We ask about family plans to help users find matches whose lifestyles and family goals align with their own. This makes it easier to connect with someone who shares your values and priorities. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "familyPlans"},
"german" : {"title": "Welche Familienpläne hast du?","info_btn_title": "Warum wir das fragen","info_btn": "We ask about family plans to help users find matches whose lifestyles and family goals align with their own. This makes it easier to connect with someone who shares your values and priorities. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "familyPlans"},
"spanish" : {"title": "¿Cuáles son tus planes familiares?","info_btn_title": "¿Por qué preguntamos esto?","info_btn": "We ask about family plans to help users find matches whose lifestyles and family goals align with their own. This makes it easier to connect with someone who shares your values and priorities. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "familyPlans"}
}
''',
    "editor_config_job_title": '''
{
"english": {"title": "What\u2019s your job title?", "subheading": "Skippable", "textfield_descriptor": "Job title","hideable": true,"optional_gender": ["Male", "Female"],"dbKey": "job"},
"french": {"title": "Quel est ton métier?", "subheading": "Facultatif", "textfield_descriptor": "Poste","hideable": true,"optional_gender": ["Male", "Female"],"dbKey": "job"},
"german": {"title": "Was ist dein Jobtitel?", "subheading": "Optional", "textfield_descriptor": "Berufsbezeichnung","hideable": true,"optional_gender": ["Male", "Female"],"dbKey": "job"},
"french": {"title": "¿Cuál es tu profesión?", "subheading": "Opcional", "textfield_descriptor": "Título del puesto","hideable": true,"optional_gender": ["Male", "Female"],"dbKey": "job"}
}
''',
    "editor_config_employer": '''
{
"english": {"title": "Where do you work?", "subheading": "Skippable", "textfield_descriptor": "Workplace","hideable": true,"optional_gender": ["Male", "Female"],"dbKey": "employer"},
"french": {"title": "Où travailles-tu ?", "subheading": "Facultatif", "textfield_descriptor": "Nom de l’entreprise","hideable": true,"optional_gender": ["Male", "Female"],"dbKey": "employer"},
"german": {"title": "Wo arbeitest du?", "subheading": "optional", "textfield_descriptor": "Arbeitsplatz","hideable": true,"optional_gender": ["Male", "Female"],"dbKey": "employer"},
"spanish": {"title": "¿Dónde trabajas?", "subheading": "Opcional", "textfield_descriptor": "Nombre del lugar de trabajo","hideable": true,"optional_gender": ["Male", "Female"],"dbKey": "employer"}
}
''',
    "editor_config_university": '''
{
"english": {"title": "Where did you study?", "subheading": "Skippable", "textfield_descriptor": "University, College, School, etc.","hideable": true,"optional_gender": ["Male", "Female"],"dbKey": "education"},
"french": {"title": "Où as-tu étudié ?", "subheading": "Facultatif", "textfield_descriptor": "Université, école, etc.","hideable": true,"optional_gender": ["Male", "Female"],"dbKey": "education"},
"german": {"title": "Wo hast du studiert?", "subheading": "optional", "textfield_descriptor": "Universität, Hochschule, Schule usw.","hideable": true,"optional_gender": ["Male", "Female"],"dbKey": "education"},
"spanish": {"title": "¿Dónde estudiaste?", "subheading": "Opcional", "textfield_descriptor": "Universidad, colegio, escuela, etc.","hideable": true,"optional_gender": ["Male", "Female"],"dbKey": "education"}
}
''',
    "editor_config_education_level": '''
{
"english": {"title": "What\u2019s your level of education?","info_btn_title": "Why do we ask this?","info_btn": "Our educational journeys shape us. At Chyrpe, we support and celebrate relationships across all educational and other backgrounds. This question is just one way we aim to help foster meaningful connections.","hideable": true,"dbKey": "eduLevel"},
"french": {"title": "Quel est ton niveau d’études?","info_btn_title": "Pourquoi on te demande ça?","info_btn": "Our educational journeys shape us. At Chyrpe, we support and celebrate relationships across all educational and other backgrounds. This question is just one way we aim to help foster meaningful connections.","hideable": true,"dbKey": "eduLevel"},
"german": {"title": "Was ist dein Bildungsniveau?","info_btn_title": "Warum wir das fragen","info_btn": "Our educational journeys shape us. At Chyrpe, we support and celebrate relationships across all educational and other backgrounds. This question is just one way we aim to help foster meaningful connections.","hideable": true,"dbKey": "eduLevel"},
"spanish": {"title": "¿Cuál es tu nivel de estudios?","info_btn_title": "¿Por qué preguntamos esto?","info_btn": "Our educational journeys shape us. At Chyrpe, we support and celebrate relationships across all educational and other backgrounds. This question is just one way we aim to help foster meaningful connections.","hideable": true,"dbKey": "eduLevel"}
}
''',
    "editor_config_religion": '''
{
"english": {"title": "What is your faith?","info_btn_title": "Why do we ask this?","info_btn": "Chyrpe offers the religious preference to help users find partners who share their beliefs and values. This feature supports meaningful connections by empowering individuals to match with those who align with their spiritual or cultural identity. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "religion"},
"french": {"title": "Quelle est ta foi ?","info_btn_title": "Pourquoi on te demande ça?","info_btn": "Chyrpe offers the religious preference to help users find partners who share their beliefs and values. This feature supports meaningful connections by empowering individuals to match with those who align with their spiritual or cultural identity. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "religion"},
"german": {"title": "Was ist dein Glaube?","info_btn_title": "Warum wir das fragen","info_btn": "Chyrpe offers the religious preference to help users find partners who share their beliefs and values. This feature supports meaningful connections by empowering individuals to match with those who align with their spiritual or cultural identity. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "religion"},
"spanish": {"title": "¿Cuál es tu religión?","info_btn_title": "¿Por qué preguntamos esto?","info_btn": "Chyrpe offers the religious preference to help users find partners who share their beliefs and values. This feature supports meaningful connections by empowering individuals to match with those who align with their spiritual or cultural identity. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "religion"}
}
''',
    "editor_config_politics": '''
{
"english": {"title": "What is your political stance?","info_btn_title": "Why do we ask this?","info_btn": "We ask about political stance to help users connect with matches who share similar values and perspectives. This can foster deeper understanding and compatibility in meaningful relationships. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "politics"},
"french": {"title": "Quelle est ta position politique?","info_btn_title": "Pourquoi on te demande ça?","info_btn": "We ask about political stance to help users connect with matches who share similar values and perspectives. This can foster deeper understanding and compatibility in meaningful relationships. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "politics"},
"german": {"title": "Wie ist deine politische Einstellung?","info_btn_title": "Warum wir das fragen","info_btn": "We ask about political stance to help users connect with matches who share similar values and perspectives. This can foster deeper understanding and compatibility in meaningful relationships. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "politics"},
"spanish": {"title": "¿Cuál es tu postura política?","info_btn_title": "¿Por qué preguntamos esto?","info_btn": "We ask about political stance to help users connect with matches who share similar values and perspectives. This can foster deeper understanding and compatibility in meaningful relationships. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "politics"}
}
''',
    "editor_config_drinking": '''
{
"english": {"title": "Do you drink?","info_btn_title": "Why do we ask this?","info_btn": "We ask about drinking habits to help users connect with matches whose lifestyles align with their own. This ensures compatibility on everyday choices that can matter in a relationship. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "drinking"},
"french": {"title": "Est-ce que tu bois de l’alcool ?","info_btn_title": "Pourquoi on te demande ça ?","info_btn": "We ask about drinking habits to help users connect with matches whose lifestyles align with their own. This ensures compatibility on everyday choices that can matter in a relationship. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "drinking"},
"german": {"title": "Trinkst du Alkohol?","info_btn_title": "Warum wir das fragen","info_btn": "We ask about drinking habits to help users connect with matches whose lifestyles align with their own. This ensures compatibility on everyday choices that can matter in a relationship. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "drinking"},
"spanish": {"title": "¿Bebes alcohol?","info_btn_title": "¿Por qué preguntamos esto?","info_btn": "We ask about drinking habits to help users connect with matches whose lifestyles align with their own. This ensures compatibility on everyday choices that can matter in a relationship. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "drinking"}
}
''',
    "editor_config_smoking": '''
{
"english": {"title": "Do you smoke?","info_btn_title": "Why do we ask this?","info_btn": "We ask about smoking habits to help users connect with matches whose lifestyles align with their own. This ensures compatibility on everyday choices that can matter in a relationship. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "smoking"},
"french": {"title": "Est-ce que tu fumes ?","info_btn_title": "Pourquoi on te demande ça ?","info_btn": "We ask about smoking habits to help users connect with matches whose lifestyles align with their own. This ensures compatibility on everyday choices that can matter in a relationship. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "smoking"},
"german": {"title": "Rauchst du?","info_btn_title": "Warum wir das fragen","info_btn": "We ask about smoking habits to help users connect with matches whose lifestyles align with their own. This ensures compatibility on everyday choices that can matter in a relationship. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "smoking"},
"spanish": {"title": "¿Fumas?","info_btn_title": "¿Por qué preguntamos esto?","info_btn": "We ask about smoking habits to help users connect with matches whose lifestyles align with their own. This ensures compatibility on everyday choices that can matter in a relationship. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "smoking"}
}
''',
    "editor_config_workout": '''
{

"english": {"title": "How often do you work out?","info_btn_title": "Why do we ask this?","info_btn": "We ask about workout habits to help users connect with matches whose lifestyles align with their own. This ensures compatibility on everyday choices that can matter in a relationship. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "workout"},
"french": {"title": "How often do you work out?","info_btn_title": "Why do we ask this?","info_btn": "We ask about workout habits to help users connect with matches whose lifestyles align with their own. This ensures compatibility on everyday choices that can matter in a relationship. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "workout"},
"english": {"title": "How often do you work out?","info_btn_title": "Why do we ask this?","info_btn": "We ask about workout habits to help users connect with matches whose lifestyles align with their own. This ensures compatibility on everyday choices that can matter in a relationship. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "workout"},
"english": {"title": "How often do you work out?","info_btn_title": "Why do we ask this?","info_btn": "We ask about workout habits to help users connect with matches whose lifestyles align with their own. This ensures compatibility on everyday choices that can matter in a relationship. You can hide this info on your profile if you wish.","hideable": true,"dbKey": "workout"}
}
''',
    "editor_config_specific_role":
        '{"title": "Tell others more about your specific role", "textfield_descriptor": "Your specific role", "hideable": true,"dbKey": "specificRole", "optional_gender": ["Male", "Female"]}',
    "editor_config_specific_gender":
        '{"title": "Tell others more about your specific gender", "textfield_descriptor": "Your specific gender identity", "hideable": true,"dbKey": "specificGender", "optional_gender": ["Male", "Female"]}',
    "search_config_role":
        '{"title": "Which roles would you like to see?", "subheading": "Select all roles you are interested in seeing.", "hideable": true,"dbKey": "specificRole", "optional_gender": ["Male", "Female"]}',
    "editor_categories_screens":
        '[{ "category": "BASICS", "screens": [{"name": "dating_intentions", "display_title": "Relationship Type", "kind": "checkboxes"}, {"name": "femdom_role", "display_title": "Role", "kind": "special"}, {"name": "femdom_experience", "display_title": "Experience Level", "kind": "radio"}, {"name": "kinks", "display_title": "Kinks", "kind": "special"}, {"name": "height", "display_title": "Height", "kind": "special"}, {"name": "religion", "display_title": "Religious Beliefs", "kind": "checkboxes"}, {"name": "ethnicity", "display_title": "Ethnicity", "kind": "checkboxes"}, {"name": "education_level", "display_title": "Education", "kind": "radio"}]}, { "category": "MORE ABOUT ME", "screens": [{"name": "hobbies", "display_title": "Hobbies", "kind": "special"}, {"name": "specific_role", "display_title": "Specific Role", "kind": "textfield"}, {"name": "specific_gender", "display_title": "Specific Gender", "kind": "textfield"}, {"name": "politics", "display_title": "Political Views", "kind": "radio"}, {"name": "personality_type", "display_title": "Personality Type", "kind": "radio"}, {"name": "zodiac", "display_title": "Zodiac", "kind": "radio"}, {"name": "languages", "display_title": "Languages", "kind": "checkboxes"}]}, { "category": "LIFESTYLE", "screens": [{"name": "children", "display_title": "Children", "kind": "radio"}, {"name": "family_plans", "display_title": "Family Plans", "kind": "radio"}, {"name": "relationship_type", "display_title": "Romantic Dynamic", "kind": "radio"}, {"name": "job_title", "display_title": "Job", "kind": "textfield"}, {"name": "employer", "display_title": "Workplace", "kind": "textfield"}, {"name": "university", "display_title": "School", "kind": "textfield"}, {"name": "pets", "display_title": "Pets", "kind": "radio"}, {"name": "workout", "display_title": "Workout", "kind": "radio"}, {"name": "diet", "display_title": "Diet", "kind": "radio"}, {"name": "drinking", "display_title": "Alcohol", "kind": "radio"}, {"name": "smoking", "display_title": "Smoking", "kind": "radio"}]}]',
    "options_workout": '''
{
"english": ["I don\'t workout", "I workout occasionally", "I workout regularly", "I\'m a fitness enthusiast"],
"french": [
  "Tu ne fais pas de sport",
  "Tu fais du sport occasionnellement",
  "Tu fais du sport régulièrement",
  "Tu es un passionné de fitness"
],

"german": [
  "Ich mache keinen Sport",
  "Ich mache gelegentlich Sport",
  "Ich mache regelmäßig Sport",
  "Ich bin ein Fitness-Enthusiast"
],

"spanish": [
  "No hago ejercicio",
  "Hago ejercicio ocasionalmente",
  "Hago ejercicio regularmente",
  "Soy un entusiasta del fitness"
]
}
''',
    "options_gender_preferences": '''
{
"english": ["I\'m new to this", "I\'ve tried it before", "I have experience"],
"french": [
  "Tu es nouveau dans ce domaine",
  "Tu l'as déjà essayé",
  "Tu as de l'expérience"
],
"german": [
  "Ich bin neu darin",
  "Ich habe es schon einmal ausprobiert",
  "Ich habe Erfahrung"
],

"spanish": [
  "Soy nuevo en esto",
  "Lo he probado antes",
  "Tengo experiencia"
]

}
''',
    "options_personality_type":
        '["INTJ","INTP","ENTJ","ENTP","INFJ","INFP","ENFJ","ENFP","ISTJ","ISFJ","ESTJ","ESFJ","ISTP","ISFP","ESTP","ESFP"  ]',
    "options_relationship_type": '''
{
"english": ["Monogamy","Non-monogamy","Figuring out my relationship type"  ],
"french":[
  "Monogamie",
  "Non-monogamie",
  "Tu es en train de découvrir ton type de relation"
],

"german":[
  "Monogamie",
  "Nicht-Monogamie",
  "Ich finde heraus, welcher Beziehungstyp ich bin"
],

"spanish":[
  "Monogamia",
  "No monogamia",
  "Estoy descubriendo mi tipo de relación"
]


}
''',
    "options_diet": '''
{
"english": ["Omnivore","Vegetarian","Vegan","Pescatarian","Keto","Paleo"  ],
"french": [
  "Omnivore",
  "Végétarien",
  "Végétalien",
  "Pescétarien",
  "Kéto",
  "Paléo"
],

"german": [
  "Allesesser",
  "Vegetarier",
  "Veganer",
  "Pescetarier",
  "Keto",
  "Paleo"
],

"spanish": [
  "Omnívoro",
  "Vegetariano",
  "Vegano",
  "Pescetariano",
  "Keto",
  "Paleo"
]



}
''',
    "options_zodiac": '''
{
"english": ["Aries","Taurus","Gemini","Cancer","Leo","Virgo","Libra","Scorpio","Sagittarius","Capricorn","Aquarius","Pisces"  ],
"french": [
  "Bélier",
  "Taureau",
  "Gémeaux",
  "Cancer",
  "Lion",
  "Vierge",
  "Balance",
  "Scorpion",
  "Sagittaire",
  "Capricorne",
  "Verseau",
  "Poissons"
],
"german": [
  "Widder",
  "Stier",
  "Zwillinge",
  "Krebs",
  "Löwe",
  "Jungfrau",
  "Waage",
  "Skorpion",
  "Schütze",
  "Steinbock",
  "Wassermann",
  "Fische"
],

"spanish": [
  "Aries",
  "Tauro",
  "Géminis",
  "Cáncer",
  "Leo",
  "Virgo",
  "Libra",
  "Escorpio",
  "Sagitario",
  "Capricornio",
  "Acuario",
  "Piscis"
]



}
''',
    "options_languages": '''
{
"english": ["Afrikaans", "Albanian", "Amharic", "Arabic", "Azerbaijani", "Basque", "Belarusian", "Bengali", "Bosnian", "Breton", "Bulgarian", "Burmese", "Catalan", "Cebuano", "Chichewa", "Corsican", "Croatian", "Czech", "Danish", "Dutch", "Emilian-Romagnol", "English", "Estonian", "Finnish", "French", "Frisian", "Friulian", "Fula", "Galician", "German", "Greek", "Gujarati", "Haitian Creole", "Hausa", "Hebrew", "Hindi", "Hungarian", "Icelandic", "Igbo", "Indonesian", "Irish", "Italian", "Japanese", "Javanese", "Kannada", "Kazakh", "Kinyarwanda", "Korean", "Kurdish", "Ladin", "Latvian", "Ligurian", "Lithuanian", "Lombard", "Luxembourgish", "Macedonian", "Malay", "Malayalam", "Maltese", "Mandarin Chinese", "Marathi", "Neapolitan", "Norwegian", "Odia", "Pashto", "Persian", "Piedmontese", "Polish", "Portuguese", "Romanian", "Romansh", "Russian", "Sardinian", "Scottish Gaelic", "Serbo-Croatian", "Shona", "Sicilian", "Sinhala", "Slovak", "Slovenian", "Somali", "Spanish", "Swahili", "Swedish", "Tagalog", "Tamil", "Tatar", "Telugu", "Thai", "Tigrinya", "Turkish", "Turkmen", "Ukrainian", "Urdu", "Uzbek", "Venetian", "Vietnamese", "Welsh", "Western Punjabi", "Wu Chinese", "Xhosa", "Yue Chinese (Cantonese)", "Zulu"  ],
"spanish": [
  "Afrikáans",
  "Albanés",
  "Amárico",
  "Árabe",
  "Azerí",
  "Vasco",
  "Bielorruso",
  "Bengalí",
  "Bosnio",
  "Bretón",
  "Búlgaro",
  "Birmano",
  "Catalán",
  "Cebuano",
  "Chichewa",
  "Corso",
  "Croata",
  "Checo",
  "Danés",
  "Neerlandés",
  "Emiliano-Romañol",
  "Inglés",
  "Estonio",
  "Finlandés",
  "Francés",
  "Frisón",
  "Friulano",
  "Fula",
  "Gallego",
  "Alemán",
  "Griego",
  "Gujarati",
  "Criollo haitiano",
  "Hausa",
  "Hebreo",
  "Hindi",
  "Húngaro",
  "Islandés",
  "Igbo",
  "Indonesio",
  "Irlandés",
  "Italiano",
  "Japonés",
  "Javanés",
  "Canarés",
  "Kazajo",
  "Kinyarwanda",
  "Coreano",
  "Kurdo",
  "Ladino",
  "Letón",
  "Ligurio",
  "Lituano",
  "Lombardo",
  "Luxemburgués",
  "Macedonio",
  "Malayo",
  "Malayalam",
  "Maltés",
  "Chino mandarín",
  "Maratí",
  "Neapolitano",
  "Noruego",
  "Odia",
  "Pastún",
  "Persa",
  "Piamontés",
  "Polaco",
  "Portugués",
  "Rumano",
  "Romanche",
  "Ruso",
  "Sardo",
  "Gaélico escocés",
  "Serbocroata",
  "Shona",
  "Siciliano",
  "Cingalés",
  "Eslovaco",
  "Esloveno",
  "Somalí",
  "Español",
  "Suajili",
  "Sueco",
  "Tagalo",
  "Tamil",
  "Tártaro",
  "Télugu",
  "Tailandés",
  "Tigriña",
  "Turco",
  "Turcomano",
  "Ucraniano",
  "Urdu",
  "Uzbeko",
  "Veneciano",
  "Vietnamita",
  "Galés",
  "Panyabí occidental",
  "Chino wu",
  "Xhosa",
  "Chino cantonés (Yue)",
  "Zulú"
]
,
"german": [
  "Afrikaans",
  "Albanisch",
  "Amharisch",
  "Arabisch",
  "Aserbaidschanisch",
  "Baskisch",
  "Weißrussisch",
  "Bengalisch",
  "Bosnisch",
  "Bretonisch",
  "Bulgarisch",
  "Birmanisch",
  "Katalanisch",
  "Cebuano",
  "Chichewa",
  "Korsisch",
  "Kroatisch",
  "Tschechisch",
  "Dänisch",
  "Niederländisch",
  "Emilianisch-Romagnolisch",
  "Englisch",
  "Estnisch",
  "Finnisch",
  "Französisch",
  "Friesisch",
  "Furlanisch",
  "Fula",
  "Galicisch",
  "Deutsch",
  "Griechisch",
  "Gujarati",
  "Haitianisches Kreol",
  "Hausa",
  "Hebräisch",
  "Hindi",
  "Ungarisch",
  "Isländisch",
  "Igbo",
  "Indonesisch",
  "Irisch",
  "Italienisch",
  "Japanisch",
  "Javanisch",
  "Kannada",
  "Kasachisch",
  "Kinyarwanda",
  "Koreanisch",
  "Kurdisch",
  "Ladinisch",
  "Lettisch",
  "Ligurisch",
  "Litauisch",
  "Lombardisch",
  "Luxemburgisch",
  "Mazedonisch",
  "Malaiisch",
  "Malayalam",
  "Maltesisch",
  "Mandarin-Chinesisch",
  "Marathi",
  "Neapolitanisch",
  "Norwegisch",
  "Odia",
  "Paschtunisch",
  "Persisch",
  "Piemontesisch",
  "Polnisch",
  "Portugiesisch",
  "Rumänisch",
  "Rätoromanisch",
  "Russisch",
  "Sardisch",
  "Schottisches Gälisch",
  "Serbokroatisch",
  "Shona",
  "Sizilianisch",
  "Singhalesisch",
  "Slowakisch",
  "Slowenisch",
  "Somali",
  "Spanisch",
  "Swahili",
  "Schwedisch",
  "Tagalog",
  "Tamil",
  "Tatarisch",
  "Telugu",
  "Thailändisch",
  "Tigrinya",
  "Türkisch",
  "Turkmenisch",
  "Ukrainisch",
  "Urdu",
  "Usbekisch",
  "Venezianisch",
  "Vietnamesisch",
  "Walisisch",
  "West-Pandschabi",
  "Wu-Chinesisch",
  "Xhosa",
  "Kantonesisch (Yue)",
  "Zulu"
],
"french": [
  "Afrikaans",
  "Albanais",
  "Amharique",
  "Arabe",
  "Azerbaïdjanais",
  "Basque",
  "Biélorusse",
  "Bengali",
  "Bosniaque",
  "Breton",
  "Bulgare",
  "Birman",
  "Catalan",
  "Cebuano",
  "Chichewa",
  "Corse",
  "Croate",
  "Tchèque",
  "Danois",
  "Néerlandais",
  "Émilien-Romagnol",
  "Anglais",
  "Estonien",
  "Finnois",
  "Français",
  "Frison",
  "Frioulan",
  "Peul",
  "Galicien",
  "Allemand",
  "Grec",
  "Gujarati",
  "Créole haïtien",
  "Haoussa",
  "Hébreu",
  "Hindi",
  "Hongrois",
  "Islandais",
  "Igbo",
  "Indonésien",
  "Irlandais",
  "Italien",
  "Japonais",
  "Javanais",
  "Kannada",
  "Kazakh",
  "Kinyarwanda",
  "Coréen",
  "Kurde",
  "Ladin",
  "Letton",
  "Ligure",
  "Lituanien",
  "Lombard",
  "Luxembourgeois",
  "Macédonien",
  "Malais",
  "Malayalam",
  "Maltais",
  "Chinois mandarin",
  "Marathi",
  "Napolitain",
  "Norvégien",
  "Odia",
  "Pachtou",
  "Persan",
  "Piémontais",
  "Polonais",
  "Portugais",
  "Roumain",
  "Romanche",
  "Russe",
  "Sarde",
  "Gaélique écossais",
  "Serbo-croate",
  "Shona",
  "Sicilien",
  "Singhalais",
  "Slovaque",
  "Slovène",
  "Somali",
  "Espagnol",
  "Swahili",
  "Suédois",
  "Tagalog",
  "Tamoul",
  "Tatar",
  "Télougou",
  "Thaï",
  "Tigrigna",
  "Turc",
  "Turkmène",
  "Ukrainien",
  "Ourdou",
  "Ouzbek",
  "Vénitien",
  "Vietnamien",
  "Gallois",
  "Pendjabi occidental",
  "Wu chinois",
  "Xhosa",
  "Cantonais (Yue)",
  "Zoulou"
]

}
''',
    "options_pets": '''
{
"english": ["I don't have pets","I have one pet","I have two pets","I have three pets","I have 4+ pets"],
"french": [
  "Tu n'as pas d'animaux",
  "Tu as un animal de compagnie",
  "Tu as deux animaux de compagnie",
  "Tu as trois animaux de compagnie",
  "Tu as 4 animaux ou plus"
],
"german": [
  "Ich habe keine Haustiere",
  "Ich habe ein Haustier",
  "Ich habe zwei Haustiere",
  "Ich habe drei Haustiere",
  "Ich habe 4 oder mehr Haustiere"
],

"spanish": [
  "No tengo mascotas",
  "Tengo una mascota",
  "Tengo dos mascotas",
  "Tengo tres mascotas",
  "Tengo 4 o más mascotas"
]



}
''',
    "options_femdom_role": '''
{
"english": ["Dominant", "Switch", "submissive"],
"french": [
  "Dominant",
  "Switch",
  "Soumis"
],
"german": [
  "Dominant",
  "Switch",
  "Devot"
],
"spanish": [
  "Dominante",
  "Switch",
  "Sumiso"
]


}
''',
    "options_femdom_role_req": '''
{
"english": ["Dominant", "Switch", "Submissive", "Open to all"],
"french": [
  "Dominant",
  "Switch",
  "Soumis",
  "Ouvert à tout"
],
"german": [
  "Dominant",
  "Switch",
  "Devot",
  "Für alles offen"
],
"spanish": [
  "Dominante",
  "Switch",
  "Sumiso",
  "Abierto a todo"
]

}
''',
    "search_options_gender_preferences": '''
{
"english": ["Men", "Women", "Everybody"],
"french": [
  "Hommes",
  "Femmes",
  "Tout le monde"
],

"german":[
  "Männer",
  "Frauen",
  "Alle"
],

"spanish": [
  "Hombres",
  "Mujeres",
  "Todos"
]
}
''',
    "editor_config_personality_type": '''
{
"english": {"title": "What’s your personality type?","hideable": true, "optional_gender": ["Male", "Female"], "dbKey": "personality"},
"french": {"title": "Quel est ton type de personnalité ?","hideable": true, "optional_gender": ["Male", "Female"], "dbKey": "personality"},
"spanish": {"title": "¿Cuál es tu tipo de personalidad?","hideable": true, "optional_gender": ["Male", "Female"], "dbKey": "personality"},
"german": {"title":  "Was ist dein Persönlichkeitstyp?","hideable": true, "optional_gender": ["Male", "Female"], "dbKey": "personality"}
}
''',
    "editor_config_relationship_type": '''
{
"english":{"title": "What kind of relationship do you seek?", "optional_gender": ["Male", "Female"], "hideable": true, "dbKey": "monoPolyRelationship"},
"french":{"title": "Quel type de relation recherches-tu ?", "optional_gender": ["Male", "Female"], "hideable": true, "dbKey": "monoPolyRelationship"},
"german":{"title": "Welche Art von Beziehung suchst du?", "optional_gender": ["Male", "Female"], "hideable": true, "dbKey": "monoPolyRelationship"},
"spanish":{"title": "¿Qué tipo de relación buscas?", "optional_gender": ["Male", "Female"], "hideable": true, "dbKey": "monoPolyRelationship"}
}
''',
    "editor_config_diet": '''
{
"english": {"title": "What's your diet?", "optional_gender": ["Male", "Female"], "hideable": true, "dbKey": "diet"},
"french": {"title": "Quel est ton régime alimentaire ?", "optional_gender": ["Male", "Female"], "hideable": true, "dbKey": "diet"},
"german": {"title": "Was ist deine Ernährung?", "optional_gender": ["Male", "Female"], "hideable": true, "dbKey": "diet"},
"spanish": {"title": "¿Cuál es tu dieta?", "optional_gender": ["Male", "Female"], "hideable": true, "dbKey": "diet"}
}
''',
    "editor_config_zodiac": '''
{
"english": {"title": "What's your zodic sign?", "optional_gender": ["Male", "Female"], "hideable": true, "dbKey": "zodiac"},
"french": {"title": "Quel est ton signe du zodiaque ?", "optional_gender": ["Male", "Female"], "hideable": true, "dbKey": "zodiac"},
"german": {"title": "Was ist dein Sternzeichen?", "optional_gender": ["Male", "Female"], "hideable": true, "dbKey": "zodiac"},
"spanish": {"title": "¿Cuál es tu signo del zodiaco?", "optional_gender": ["Male", "Female"], "hideable": true, "dbKey": "zodiac"}
}
''',
    "editor_config_languages": '''
{
"english": {"title": "Which languages do you speak?", "optional_gender": ["Male", "Female"], "hideable": true, "dbKey": "languages"},
"french": {"title": "Quelles langues parles-tu ?", "optional_gender": ["Male", "Female"], "hideable": true, "dbKey": "languages"},
"german": {"title": "Welche Sprachen sprichst du?", "optional_gender": ["Male", "Female"], "hideable": true, "dbKey": "languages"},
"spanish": {"title": "¿Qué idiomas hablas?", "optional_gender": ["Male", "Female"], "hideable": true, "dbKey": "languages"}
}
''',
    "editor_config_pets": '''
{
"english": {"title": "How many pets do you have?", "optional_gender": ["Male", "Female"], "hideable": true, "dbKey": "pets"},
"french": {"title": "Combien d'animaux as-tu ?", "optional_gender": ["Male", "Female"], "hideable": true, "dbKey": "pets"},
"german": {"title": "Wie viele Haustiere hast du?", "optional_gender": ["Male", "Female"], "hideable": true, "dbKey": "pets"},
"spanish": {"title": "¿Cuántas mascotas tienes?", "optional_gender": ["Male", "Female"], "hideable": true, "dbKey": "pets"}
}
''',
    "editor_config_hobbies": '''
{
"english": {"title": "What are your hobbies?", "subheading": "Select your hobbies", "optional_gender": ["Male", "Female"], "hideable": true, "dbKey": "hobbies"},
"french": {"title": "Quels sont tes passe-temps ?", "subheading": "Select your hobbies", "optional_gender": ["Male", "Female"], "hideable": true, "dbKey": "hobbies"},
"german": {"title": "Was sind deine Hobbys?", "subheading": "Select your hobbies", "optional_gender": ["Male", "Female"], "hideable": true, "dbKey": "hobbies"},
"spanish": {"title": "¿Cuáles son tus pasatiempos?", "subheading": "Select your hobbies", "optional_gender": ["Male", "Female"], "hideable": true, "dbKey": "hobbies"}
}
''',
    "editor_bio_screens": '''
{
"english": [{ "category": "BIO & PROMPTS", "screens": [{"name": "prompts", "display_title": "Prompts", "kind": "special"}, {"name": "bio", "display_title": "Bio", "kind": "special"}]}],
"french": [{ "category": "Biographie et Suggestions", "screens": [{"name": "prompts", "display_title": "Prompts", "kind": "special"}, {"name": "bio", "display_title": "Bio", "kind": "special"}]}],
"german": [{ "category": "Biografie und Prompts", "screens": [{"name": "prompts", "display_title": "Prompts", "kind": "special"}, {"name": "bio", "display_title": "Bio", "kind": "special"}]}],
"spanish": [{ "category": "Biografía y Prompts", "screens": [{"name": "prompts", "display_title": "Prompts", "kind": "special"}, {"name": "bio", "display_title": "Bio", "kind": "special"}]}]
}
''',
    "editor_config_bio": '''
{
"english": {"title": "Write a bio", "subheading": "Write at least 50 characters.", "min_chars_textfield": 50, "max_chars_textfield": 1000, "info_btn_title": "Our tips for a great bio","info_btn": "https://www.chyrpe.com/how-to-write-a-great-bio", "textfield_descriptor": "Tell others more about yourself. No sexual content allowed.", "hideable": false, "dbKey": "bio"},
"french": {"title": "Écris une biographie", "subheading": "Écris au moins 50 caractères.", "min_chars_textfield": 50, "max_chars_textfield": 1000, "info_btn_title": "Our tips for a great bio","info_btn": "https://www.chyrpe.com/how-to-write-a-great-bio", "textfield_descriptor": "Parle un peu plus de toi. Le contenu sexuel n'est pas autorisé.", "hideable": false, "dbKey": "bio"},
"german": {"title": "Schreibe eine Biografie", "subheading": "Schreibe mindestens 50 Zeichen.", "min_chars_textfield": 50, "max_chars_textfield": 1000, "info_btn_title": "Our tips for a great bio","info_btn": "https://www.chyrpe.com/how-to-write-a-great-bio", "textfield_descriptor": "Erzähle anderen mehr über dich. Sexuelle Inhalte sind nicht erlaubt.", "hideable": false, "dbKey": "bio"},
"spanish": {"title": "Escribe una biografía", "subheading": "Escribe al menos 50 caracteres.", "min_chars_textfield": 50, "max_chars_textfield": 1000, "info_btn_title": "Our tips for a great bio","info_btn": "https://www.chyrpe.com/how-to-write-a-great-bio", "textfield_descriptor": "Cuéntales más sobre ti. No se permite contenido sexual.", "hideable": false, "dbKey": "bio"}
}
''',
    "editor_config_prompts": '''
{
"english": {"title": "Write a prompt", "subheading": "Write at least 5 characters.", "min_chars_textfield": 5, "max_chars_textfield": 200, "info_btn_title": "Our tips for great prompts","info_btn": "https://www.chyrpe.com/how-to-write-a-great-bio", "textfield_descriptor": "Tell others more about yourself. No sexual content allowed.", "hideable": false, "dbKey": "prompts"},
"french": {"title": "Écris une invite", "subheading": "Écris au moins 5 caractères.", "min_chars_textfield": 5, "max_chars_textfield": 200, "info_btn_title": "Our tips for great prompts","info_btn": "https://www.chyrpe.com/how-to-write-a-great-bio", "textfield_descriptor": "Parle un peu plus de toi. Le contenu sexuel n'est pas autorisé", "hideable": false, "dbKey": "prompts"},
"german": {"title": "Schreibe eine Eingabeaufforderung", "subheading": "Schreibe mindestens 5 Zeichen.", "min_chars_textfield": 5, "max_chars_textfield": 200, "info_btn_title": "Our tips for great prompts","info_btn": "https://www.chyrpe.com/how-to-write-a-great-bio", "textfield_descriptor": "Erzähle anderen mehr über dich. Sexuelle Inhalte sind nicht erlaubt.", "hideable": false, "dbKey": "prompts"},
"spanish": {"title": "Escribe una indicación", "subheading": "Escribe al menos 5 caracteres.", "min_chars_textfield": 5, "max_chars_textfield": 200, "info_btn_title": "Our tips for great prompts","info_btn": "https://www.chyrpe.com/how-to-write-a-great-bio", "textfield_descriptor": "Cuéntales más sobre ti. No se permite contenido sexual.", "hideable": false, "dbKey": "prompts"}
}
''',
    "editor_config_kinks": '''
{
"english": {"title": "What are your preferences?","subheading": "Select all that apply. Tap on the icon to select it. Tap long for more info.","dbKey": "kinks", "hideable": true},
"french": {"title": "Quelles sont tes préférences ?","subheading": "Sélectionne toutes les options qui s'appliquent. Appuie sur l'icône pour la sélectionner. Appuie longuement pour plus d'informations.","dbKey": "kinks", "hideable": true},
"german": {"title": "Was sind deine Vorlieben?","subheading": "Wähle alle zutreffenden Optionen aus. Tippe auf das Symbol, um es auszuwählen. Langes Tippen für weitere Informationen.","dbKey": "kinks", "hideable": true},
"spanish": {"title": "¿Cuáles son tus preferencias?" ,"subheading": "Selecciona todas las opciones que correspondan. Toca el ícono para seleccionarlo. Mantén pulsado para más información.","dbKey": "kinks", "hideable": true}
}
''',
    "signup_bio_bio_textfield_descriptior":
        "Tell others more about yourself. No sexual content allowed.",
    'prompt_default_title': 'Choose a prompt',
    'prompt_default_answer': 'And show others what makes you unique!',
    'prompt_editor_title': 'Edit Prompt',
    'editor_show_location_change': true,
    'editor_show_name_hiding': true,
    'generic_search_pref_dealbreaker_string': 'Dealbreaker',
    'search_generic_info_btn': "More information on this",
    'search_generic_default_option': "Open to all",
    'settings_categories_screens_standard':
        '[{"category": "STANDARD PREFERENCES", "screens": [{"name": "gender_preferences", "title": "I\'m interested in", "type": "radio", "dealbreakerEligible": false, "eligibleMaleEntitlements": ["all"], "eligibleFemaleEntitlements": ["all"]}, {"name": "age", "title": "Age range", "type": "special", "dealbreakerEligible": true, "eligibleMaleEntitlements": ["all"], "eligibleFemaleEntitlements": ["all"]},{"name": "dating_intentions", "title": "Relationship type", "type": "checkboxes", "dealbreakerEligible": true, "eligibleMaleEntitlements": ["paid_standard_1w", "paid_standard_lifetime", "gold_access", "evolved_access"], "eligibleFemaleEntitlements": ["all"]},{"name": "matching_mode", "title": "Matching mode", "type": "special", "dealbreakerEligible": false, "eligibleMaleEntitlements": ["paid_standard_1w", "paid_standard_lifetime", "gold_access", "evolved_access"], "eligibleFemaleEntitlements": ["all"]},{"name": "distance", "title": "Maximum distance", "type": "special", "dealbreakerEligible": true, "eligibleMaleEntitlements": ["paid_standard_1w", "paid_standard_lifetime", "gold_access", "evolved_access"], "eligibleFemaleEntitlements": ["all"]}, {"name": "role", "title": "Roles", "type": "special", "dealbreakerEligible": false, "eligibleMaleEntitlements": ["all"], "eligibleFemaleEntitlements": ["all"]}, {"name": "professionals", "title": "Hide findoms", "type": "boolean", "dealbreakerEligible": true, "eligibleMaleEntitlements": ["all"], "eligibleFemaleEntitlements": ["none"]}, {"name": "match_bypass", "title": "Likes bypass filters", "type": "boolean", "dealbreakerEligible": true, "eligibleMaleEntitlements": ["paid_standard_1w", "paid_standard_lifetime", "gold_access", "evolved_access"], "eligibleFemaleEntitlements": ["all"]}]}]',
    'settings_categories_screens_advanced':
        '[{"category": "ADVANCED PREFERENCES", "screens": [{"name": "height", "title": "Height", "type": "special", "dealbreakerEligible": true, "eligibleMaleEntitlements": ["gold_access", "evolved_access"], "eligibleFemaleEntitlements": ["all"]}, {"name": "ethnicity", "title": "Ethnicity", "type": "checkboxes", "dealbreakerEligible": true, "eligibleMaleEntitlements": ["gold_access", "evolved_access"], "eligibleFemaleEntitlements": ["all"]}, {"name": "religion", "title": "Religious beliefs", "type": "checkboxes", "dealbreakerEligible": true, "eligibleMaleEntitlements": ["gold_access", "evolved_access"], "eligibleFemaleEntitlements": ["all"]}, {"name": "education_level", "title": "Education level", "type": "checkboxes", "dealbreakerEligible": true, "eligibleMaleEntitlements": ["gold_access", "evolved_access"], "eligibleFemaleEntitlements": ["all"]}, {"name": "children", "title": "Children", "type": "checkboxes", "dealbreakerEligible": true, "eligibleMaleEntitlements": ["gold_access", "evolved_access"], "eligibleFemaleEntitlements": ["all"]}, {"name": "family_plans", "title": "Family Plans", "type": "checkboxes", "dealbreakerEligible": true, "eligibleMaleEntitlements": ["gold_access", "evolved_access"], "eligibleFemaleEntitlements": ["all"]}, {"name": "politics", "title": "Political views", "type": "checkboxes", "dealbreakerEligible": true, "eligibleMaleEntitlements": ["gold_access", "evolved_access"], "eligibleFemaleEntitlements": ["all"]}, {"name": "drinking", "title": "Alcohol", "type": "checkboxes", "dealbreakerEligible": true, "eligibleMaleEntitlements": ["gold_access", "evolved_access"], "eligibleFemaleEntitlements": ["all"]}, {"name": "smoking", "title": "Smoking", "type": "checkboxes", "dealbreakerEligible": true, "eligibleMaleEntitlements": ["gold_access", "evolved_access"], "eligibleFemaleEntitlements": ["all"]}]}]',
    'profile_detail_info_horizontal':
        '[{"name": "age", "symbol": "birthdayCake"}, {"name": "height", "symbol": "ruler"}, {"name": "city", "symbol": "locationPin"}, {"name": "ethnicity", "symbol": "globe"}, {"name": "eduLevel", "symbol": "academicHat"}, {"name": "children", "symbol": "buggy"}, {"name": "familyPlans", "symbol": "buggy"}, {"name": "personality", "symbol": "brain"}, {"name": "zodiac", "symbol": "moon"}, {"name": "pets", "symbol": "paw"}, {"name": "publicRole", "symbol": "heartLock"}, {"name": "diet", "symbol": "cutlery"}, {"name": "workout", "symbol": "muscle"}, {"name": "drinking", "symbol": "wine"}, {"name": "smoking", "symbol": "cigarette"}]',
    'profile_detail_info_vertical':
        '[{"name": "displayG", "symbol": "genders"}, {"name": "publicRole", "symbol": "heartLock"}, {"name": "relationPreferences", "symbol": "rose"}, {"name": "monoPolyRelationship", "symbol": "rings"}, {"name": "experienceLevel", "symbol": "puzzlePiece"}, {"name": "jobCombo", "symbol": "work"}, {"name": "education", "symbol": "academicHat"}, {"name": "languages", "symbol": "language"}, {"name": "hobbies", "symbol": "palette"}, {"name": "religion", "symbol": "candle"}, {"name": "politics", "symbol": "institution"}]',
    'profile_detail_order':
        '[{"type": "bio", "number": 0}, {"type": "prompt", "number": 0}, {"type": "info", "number": 0}, {"type": "findom", "number": 0}, {"type": "image", "number": 1}, {"type": "prompt", "number": 1}, {"type": "image", "number": 2}, {"type": "image", "number": 3}, {"type": "prompt", "number": 2}, {"type": "image", "number": 4}, {"type": "image", "number": 5}, {"type": "kinks", "number": 0}]',
    'profile_detail_info_role_horizontal': '["I\'m new to this"]',
    'profile_card_role_hidden': '["I\'m new to this"]',
    'profile_card_show_vitals': '["aGroup"]',
    'chats_evolved_name_blur': true,
    'chats_free_feedback': true,
    'discovery_top_filters':
        '[{"name": "age", "title": "Age", "type": "special", "dealbreakerEligible": true, "eligibleMaleEntitlements": ["all"], "eligibleFemaleEntitlements": ["all"]}, {"name": "dating_intentions", "title": "Relationship Type", "type": "checkboxes", "dealbreakerEligible": true, "eligibleMaleEntitlements": ["paid_standard_1w", "paid_standard_lifetime", "gold_access", "evolved_access"], "eligibleFemaleEntitlements": ["all"]}]',
    'discovery_info_card_height': 500,
    'checkboxes_exclusionary_items': '["Prefer not to say"]',
    'location_explainer': "How is my location used?",
    'location_explainer_detail':
        "Your location is only used to find potential matches in your area.",
    'discovery_show_intros': "[]",
    'searchpref_filter_limit_text':
        'This is a new feature. Most profiles can’t be filtered yet, dealbreakers may lead to few suggestions.',
    'searchpref_filter_limit_show': true,
    'chat_understood_btn_title': 'Understood',
    'chat_understood_description': 'Tap to hide chat for now',
    'evolved_tab_rose_btn': 'Upgrade like to rose',
    'dealbreaker_confirm_body':
        'If you turn on this dealbreaker, "Likes Bypass Filters" will be switched off.',
    'dealbreaker_confirm_title': 'Make it a dealbreaker?',
    'likesbypass_confirm_title': 'Let likes bypass filters?',
    'likesbypass_confirm_body':
        'Turning this on will disable any dealbreakers you’ve set.',
    'dealbreaker_confirm_btn': 'Confirm',
    'likesbypass_confirm_btn': 'Confirm',
    'use_brach_analytics': false,
    'use_fb_events': true,
    'chat_show_notification_phasein_deadline': 1738624985,
    'chat_notification_phasein_title': 'Message indicators get reworked',
    'chat_notification_phasein_body':
        'The red dot in the navigation bar now shows unread instead of unanswered messages. For the next 3 days, this may not be 100% reliable yet. Thank you for your patience!',
    'evolved_tab_info_title_p1': 'More information',
    'evolved_tab_info_p1':
        'Likes marked as FD have a chance of being from users that may identify as findommes. As this is not always declared, both false negatives and positives may occur.',
    'evolved_tab_fd_tag_p1': 'FD',
    'mpe_request_review_discovery_p1': 'You can also ',
    'mpe_request_review_discovery_p2': 'request a manual review.',
    'mpe_request_review_popup_title': 'Request a manual review?',
    'mpe_request_review_popup_body':
        'Your profile will be reviewed by a human team member. This can take up to 72 hours. \n\nYou can add any comments below:',
    'mpe_request_review_popup_textfield_label': 'Add comment here...',
    'mpe_request_review_popup_textfield_length': 150,
    'mpe_request_review_popup_btn': 'Request review',
    'mpe_edit_profile_header':
        'Change the highlighted section(s) to continue matching',
    'mpe_info_sheet_title':
        'Chyrpe rules are designed to maximise your chances',
    'mpe_info_sheet_btn_learn_more': 'Learn More',
    'mpe_info_sheet_btn_understand': 'I understand',
    'mpe_info_sheet_btn_request_review': 'Request review',
    'mpe_learn_more_url': '',
    'mpe_discovery_edit_profile_btn': 'Edit Profile',
    'mpe_info_sheet_body': '',
    'mpe_info_sheet_btn_in_review': 'Manual review in progress',
    'mpe_edit_profile_highlight_color_bg': '#F4EBFA',
    'mpe_edit_profile_highlight_color_fg': '#C79CE4',
    'discovery_welcome_widget_content':
        '[{  "title": "Welcome to Chyrpe,",  "description": "\\nWhat is a dating app for female led relationships?",  "listItem": {},  "buttonLabel": "Let us tell you!",  "isCircleButton": true},{  "title": "Where safety is a given...",  "titleUnderlined": "",  "description": "",  "listItem": {    "Every single profile is verified": "https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FWelcome_Widget_250213%2Fverification.webp?alt=media&token=97b13a04-dd72-4aae-bedd-2a9697977d0a",    "Potential partners who appreciate you for you!": "https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FWelcome_Widget_250213%2Fcheckmark_white_placeholder.webp?alt=media&token=5a2cfcb2-c059-4811-bb06-cd0ba0ee6bfe",    "“I want to make her my priority”": null  },  "buttonLabel": "",  "isCircleButton": true},{  "title": "And where men are educated!",  "titleUnderlined": "",  "description": "",  "listItem": {    "Give anonymous feedback on profiles": "https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FWelcome_Widget_250213%2Frating.webp?alt=media&token=2d30dc83-b19c-410a-b820-9c74b9365bd8",    "You control your chats with the power board": "https://firebasestorage.googleapis.com/v0/b/greenrocks-9abc3.appspot.com/o/selectionsForApp%2FWelcome_Widget_250213%2Fpowerboard.webp?alt=media&token=11884ff0-8ed2-4750-9458-38709335be53",    "Enjoy setting the rules of your dating experience!": null  },  "buttonLabel": "",  "isCircleButton": true},{  "title": "Chyrpe...\\nmade for your gaze\\n\\nThe female gaze",  "titleUnderlined": "your",  "description": "Discover what it means to have the power you deserve!",  "listItem": {},  "buttonLabel": "Begin",  "isCircleButton": false}]',
    'discovery_welcome_replaceable': 'Welcome to Chyrpe,',
    'discovery_welcome_max_age': 1738396527,
    'discovery_welcome_active': true,
    'discovery_welcome_placement': 'after_verification',
    'welcome_rule_page_title': {
      "english": "Welcome to Chyrpe",
      "french": "Bienvenue sur Chyrpe",
      "german": "Willkommen bei Chyrpe",
      "spanish": "Bienvenida a Chyrpe"
    },
    'welcome_rule_page_subtitle': {
      "english": 'The most important guidelines.\nCheck the boxes to agree.',
      "french":
          'Les règles les plus importantes.\nCoche les cases pour accepter.',
      "german": 'Die wichtigsten Regeln.\nBitte alle Kästchen ankreuzen.',
      "spanish": 'Las reglas más importantes.\nMarca las casillas para aceptar.'
    },
    'welcome_rule_title1': {
      'english': 'Be respectful.',
      'french': 'Sois respectueux·se',
      'german': 'Sei respektvoll.',
      'spanish': 'Sé respetuoso.'
    },
    'welcome_rule_title2': {
      'english': "Be yourself",
      'french': 'Sois toi-même',
      'german': 'Sei du selbst',
      'spanish': 'Sé tú mismo'
    },
    'welcome_rule_title3': {
      'english': "Take it slow.",
      'french': 'Prends ton temps',
      'german': 'Lass dir Zeit',
      'spanish': 'Ve con calma'
    },
    'welcome_rule_title4': {
      'english': 'Engage!',
      'french': 'Participe!',
      'german': 'Sei aktiv!',
      'spanish': '¡Participa!'
    },
    'welcome_rule_description1': {
      'english':
          'We bring interesting people together. Treat everyone you meet with respect.',
      'french':
          'On rassemble des personnes intéressantes. Traite chacun·e avec respect.',
      'german':
          'Wir bringen spannende Menschen zusammen. Begegne allen mit Respekt.',
      'spanish':
          'Reunimos a personas interesantes. Trata con respeto a quien conozcas.'
    },
    'welcome_rule_description2': {
      'english':
          'This platform has trust and legitimacy at its core. Be yourself, everyone is.',
      'french':
          'Cette plateforme repose sur la confiance et l’authenticité. Sois toi-même, comme tout le monde ici.',
      'german':
          'Vertrauen und Echtheit stehen im Mittelpunkt. Sei du selbst – hier sind es alle.',
      'spanish': 'Esta plataforma se basa en la confianza y la autenticidad.'
    },
    'welcome_rule_description3': {
      'english':
          'Sexual messages have no place in this application unless mutually agreed upon.',
      'french':
          'Les messages sexuels n’ont pas leur place ici sans accord mutuel.',
      'german':
          'Sexuelle Nachrichten sind nur erlaubt, wenn beide ausdrücklich zustimmen.',
      'spanish':
          'Los mensajes sexuales no tienen lugar aquí, salvo si hay un acuerdo mutuo.'
    },
    'welcome_rule_description4': {
      'english': 'Talk to others, this is what the app is here for.',
      'french': 'Discute avec les autres, c’est l’objectif de l’app.',
      'german': 'Sprich mit anderen – dafür ist die App da.',
      'spanish': 'Habla con otros, para eso está esta app.'
    },
    'kink_selector_filter_title': 'Get shown to more people who share this',
    'settings_kink_title': 'Kink interest',
    'settings_kink_explanation':
        'Chyrpe is a kink-positive space. In order to keep it enjoyable for all, our rules on the topic have to be followed. Kink is only allowed in the kink area, which you can activate/deactivate here in the settings. You can choose both whether you want to see others\' kink areas and whether you want to fill in yours. Outside of the kink area, women decide when it is okay to bring up the topic.',
    'settings_kink_interested_title': "I’m interested in kink",
    'settings_kinks_others_visible_title': 'Show kink areas on profiles',
    'settings_kinks_own_visible_title': 'Enable kink area for my profile',
    'kink_shown': true,
    'kink_selector_filter_shown': '["Male"]',
    'kink_selector_filter_places': '["signup, edit_profile"]',
    'profile_detail_kink_name': 'interests',
    'profile_detail_kink_hide_titles': true,
    'profile_detail_kink_subtitle': 'Tap long on each to learn more about it.',
    'kink_rules':
        '[ {"titles": [{"title": "Kink on Chyrpe: The rules","underlinedText": "Kink"}],"descriptions": ["Chyrpe is a kink-positive space.",  "Our rules ensure this is enjoyable for all."],"agreeFieldTitle": null,"confirmText": null,"buttonLabel": "Next","isCircleButton": true  },  {"titles": [  {"title": "Kink is only allowed in the kink area. \\n Not in your bio, images or prompts.","underlinedText": "Kink"  }],"descriptions": [],"agreeFieldTitle": "Type “I understand” to agree:","confirmText": "i understand","buttonLabel": "Next","isCircleButton": true  },  {"titles": [  {"title": "Women decide when it’s okay to discuss kink in chats. \\n \\n This status will be displayed.","underlinedText": "Women decide"  }],"descriptions": [],"agreeFieldTitle": "Type “I understand” to agree:","confirmText": "i understand","buttonLabel": "Next","isCircleButton": true  },  {"titles": [  {"title": "Thank you for keeping Chyrpe welcoming for all!"  }],"descriptions": [  "You can now fill in your kink area if you would like.",  "You can see others’ kink area when they enable it."],"agreeFieldTitle": null,"confirmText": null,"buttonLabel": "Begin","isCircleButton": false  }]',
    'signup_kink_rules_shown': '["Male"]',
    'kinks_max': 5,
    'standard_disclaimer_freetrial_android':
        'You are eligible for a free 1-week trial. After that, you will be charged the price displayed every month until you cancel via Play Store settings. Our Terms and Google Play Terms apply.',
    'subscription_disclaimer_android':
        'By tapping "Upgrade", you will be charged and your subscription will auto-renew for the same price and length until you cancel via Play Store settings. Our Terms and Google Play Terms apply.',
    'subscription_disclaimer_android_show': true,
    'prompt_selector_new_selections_w':
        '["Bio Prompts incl. Categories", "Bio Prompts incl. Categories", "Bio Prompts incl. Categories"]',
    'prompt_selector_new_selections_m':
        '["Prompts incl. Appreciation", "Prompts incl. Appreciation", "Bio Prompts incl. Categories"]',
    'chat_new_sending_enabled': false,
    'editor_roles_all_available': false,
    'verification_auto_session_failed_title': 'Something went wrong',
    'verification_auto_session_failed_text':
        'Please try again. If the issue persists, you can use manual verification instead.',
    'verification_auto_session_failed_btn': 'Start manual verification',
    'verification_auto_session_old_btn': false,
    'standard_lite_remove': false,
    'role_req_popup_title': "You might be missing out",
    'role_req_popup_body':
        "Roles are broad. Most users, across all genders, identify as Switch. Consider keeping things more open for more matches.",
    'role_req_popup_btnTitle': "Continue anyway",
    'role_req_popup_enabled_genders': '["Male"]',
    'role_req_sufficient_choices': '["Open to all", "Switch"]',
    'role_req_upgrade_btn_title': 'Upgrade to use filters',
    'role_req_subscription_eligible_entitlements_male':
        '["paid_standard_1w", "paid_standard_lifetime", "gold_access", "evolved_access"]',
    'role_req_subscription_eligible_entitlements_female': '["all"]',
    'signup_name_title': 'What\'s your name?',
    'signup_name_hint': 'Enter preferred name',
    'image_verification_start_map':
        '[{"title": "Bio & prompts language","description": "No kinky/intimate content on profiles.\\nWomen decide about the right time.","hintUrl": null,"hintLabel": null,"isCompleted": false,"leadingImageUrl": null},{"title": "Image content & face image","description": "Every profile needs a face picture.\\nExplicit images are not allowed.","hintUrl": null,"hintLabel": null,"isCompleted": false,"leadingImageUrl": null},{"title": "3D face verification","description": "Only real users are allowed on Chyrpe.","hintUrl": null,"hintLabel": null,"isCompleted": false,"leadingImageUrl": null}]',
    'image_verification_start_map_2':
        '[{"title": "Always private","description": "We never share your verification selfie.","hintUrl": null,"hintLabel": null,"isCompleted": false,"leadingImageUrl": "https://firebasestorage.googleapis.com/v0/b/greenrocks-dev.appspot.com/o/lock.png?alt=media&token=f630d2a1-d08f-46fc-b95b-1af249ec6dd5"},{"title": "Come as you are","description": "No need to worry about hair or makeup.","hintUrl": null,"hintLabel": null,"isCompleted": false,"leadingImageUrl": "https://firebasestorage.googleapis.com/v0/b/greenrocks-dev.appspot.com/o/sparkles.png?alt=media&token=81f86bb7-f8eb-4e14-9303-85d4ed5cda09"},{"title": "Good lighting helps","description": "A clearer photo makes it easier to get verified.","hintUrl": null,"hintLabel": null,"isCompleted": false,"leadingImageUrl": "https://firebasestorage.googleapis.com/v0/b/greenrocks-dev.appspot.com/o/sun.png?alt=media&token=9fec5568-3cfa-4004-97bd-46e43a30db06"}]',
    'verifications_request_manual_confirmation_title': 'Request manual review?',
    'verifications_request_manual_confirmation_body':
        'A human team member will check your profile and give feedback. This may take up to 48 hours.',
    'verifications_request_manual_confirmation_btn': 'Request manual review',
    'image_verification_fail_face_crtiterion':
        "At least one image with your face",
    'image_verification_fail_explicit_crtiterion': "No explicit/NSFW images",
    'image_verification_fail_edit_btn': "Edit images",
    'image_verification_fail_make_changes_btn': "Make changes, then try again",
    'image_verification_fail_recheck_btn': "Re-check now",
    'image_verification_in_progress_manual_finish_now': "Reviewing your images",
    'image_verification_in_progress_pn_activated':
        "Push notifications activated",
    'image_verification_in_progress_pn_activate':
        'Activate push notitifications',
    'image_verification_failed_manual_finish_now':
        "Finish your verification now",
    'image_verification_failed_must_change_note':
        "You cannot request another human review until you make changes.\\nTechnical issues? Contact <EMAIL>",
    'image_verification_failed_reattempt_now': "Re-attempt manual verification",
    'verification_entry_2_auto_new_title': "Take a video selfie",
    'verification_entry_2_auto_new_subtitle':
        "Take a quick video selfie to show you are really you. Here’s what you need to know:",
    'verification_entry_auto_title': "Finish your verification now",
    'verification_entry_auto_subtitle': "Average Completion Time: <2 minutes",
    'images_verification_fail_title': 'Finish your verification now',
    'verification_id_request_title':
        'Please upload an image of your photo ID (e.g., passport) below.',
    'settings_full_pause_explanation':
        'This will pause your account for further matching and messaging. That means, you will not be able to see others, incl. your matches, and others, incl. your matches, will not be able to see you until you unpause.',
    'welcome_screen_show_google': true,
    'welcome_screen_show_apple': true,
    'welcome_screen_show_phone_login_android': true,
    'welcome_screen_show_phone_button': true,
    'introduction_cards':
        '[{"title": "Female-led dating", "description": "Chyrpe is made for dynamics where women take control.", "image_url": "https://firebasestorage.googleapis.com/v0/b/greenrocks-dev.appspot.com/o/intro_card_1.png?alt=media&token=fa10aa21-7c00-4daa-b4dd-c8f2d20bf8f7"}, {"title": "Women set the rules", "description": "The app features advanced controls for women and strict moderation.", "image_url": "https://firebasestorage.googleapis.com/v0/b/greenrocks-dev.appspot.com/o/intro_card_2.png?alt=media&token=8650b918-2baf-450a-a091-4b5816c66538"}, {"title": "100% verified", "description": "All users undergo mandatory verification.\\n", "image_url": "https://firebasestorage.googleapis.com/v0/b/greenrocks-dev.appspot.com/o/intro_card_3.png?alt=media&token=823e8c0d-81e8-46c9-bc13-ee9698600c28"}, {"title": "Safe & consensual", "description": "Explore all forms of female-led dynamics in a place that puts safety & consent first.", "image_url": "https://firebasestorage.googleapis.com/v0/b/greenrocks-dev.appspot.com/o/intro_card_4.png?alt=media&token=57ff8263-e872-4ded-b35a-bd96a1b6f77f"}]',
    'chat_processing_text': 'Processing...',
    'chat_new_new_sending_enabled': true,
    'pause_screen_title': 'You\'ve paused your profile',
    'pause_screen_description':
        'Your profile is currently paused. Unpause it to use this screen.',
    'evolved_screen_findom_section_title': 'Potential findom interest',
    'evolved_screen_findom_explainer_title': 'Why this section exists',
    'evolved_screen_findom_explainer_body':
        'We believe in offering transparency and letting everyone make their own choices when it comes to findom dynamics.\n\nIf it’s not your thing, feel free to ignore the likes in this section. If findoms like you without disclosing their interest, please report them. It is against the rules.',
    'search_config_professionals':
        '{"title":"Search Preferences","subheading":"Customize your search options","info_btn_title":"About Findom on Chyrpe","info_btn":"We want to put an end to matches with hidden intentions.\\n\\nAll Findoms on Chyrpe have to disclose, so you can filter them out.\\n\\nPlease report any undisclosed Findoms you see. It’s against rules.","hideable":true,"min_options_checkbox":1,"max_options_checkbox":5,"min_chars_textfield":10,"max_chars_textfield":100,"textfield_descriptor":"Describe your preferences in detail","optional_gender":["male","female","non_binary"],"back_eligible":false,"next_btn_title":"Continue","dbKey":"search_preferences_config_v1"}',
    'chat_premium_initial_screen': 'Gold',
    'chat_premium_preview_visible': true
  });
  await FirebaseRemoteConfig.instance.fetchAndActivate();
}

String getRemoteConfigString(String key) =>
    FirebaseRemoteConfig.instance.getString(key);

bool getRemoteConfigBool(String key) =>
    FirebaseRemoteConfig.instance.getBool(key);

int getRemoteConfigInt(String key) => FirebaseRemoteConfig.instance.getInt(key);

double getRemoteConfigDouble(String key) =>
    FirebaseRemoteConfig.instance.getDouble(key);
