import 'dart:convert';

import 'package:equatable/equatable.dart';

class LanguageModel extends Equatable {
  final String? english;
  final String? french;
  final String? german;
  final String? spanish;

  const LanguageModel({
    this.english,
    this.french,
    this.german,
    this.spanish,
  });

  factory LanguageModel.fromMap(Map<String, dynamic> data) {

    return LanguageModel(
      english: data['english'] as String?,
      french: data['french'] as String?,
      german: data['german'] as String?,
      spanish: data['spanish'] as String?,
    );
  }

  Map<String, dynamic> toMap() => {
        'english': english,
        'french': french,
        'german': german,
        'spanish': spanish,
      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [LanguageModel].
  factory LanguageModel.fromJson(String data) {
    
    return LanguageModel.fromMap(json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [LanguageModel] to a JSON string.
  String toJson() => json.encode(toMap());

  LanguageModel copyWith({
    String? english,
    String? french,
    String? german,
    String? spanish,
  }) {
    return LanguageModel(
      english: english ?? this.english,
      french: french ?? this.french,
      german: german ?? this.german,
      spanish: spanish ?? this.spanish,
    );
  }

  @override
  bool get stringify => true;

  @override
  List<Object?> get props => [english, french, german, spanish];
}
