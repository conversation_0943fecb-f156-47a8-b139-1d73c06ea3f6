import 'dart:async';

import 'package:chyrpe/introduction_auth/welcome_screen/welcome_screen_widget.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';

import '/auth/base_auth_user_provider.dart';

import '/backend/push_notifications/push_notifications_handler.dart'
    show PushNotificationsHandler;
import '/index.dart';
import '/main.dart';
import '/flutter_flow/flutter_flow_util.dart';

export 'package:go_router/go_router.dart';
export 'serialization_util.dart';

const kTransitionInfoKey = '__transition_info__';

class AppStateNotifier extends ChangeNotifier {
  AppStateNotifier._();

  static AppStateNotifier? _instance;
  static AppStateNotifier get instance => _instance ??= AppStateNotifier._();

  BaseAuthUser? initialUser;
  BaseAuthUser? user;
  bool showSplashImage = true;
  String? _redirectLocation;

  /// Determines whether the app will refresh and build again when a sign
  /// in or sign out happens. This is useful when the app is launched or
  /// on an unexpected logout. However, this must be turned off when we
  /// intend to sign in/out and then navigate or perform any actions after.
  /// Otherwise, this will trigger a refresh and interrupt the action(s).
  bool notifyOnAuthChange = true;

  bool get loading => user == null || showSplashImage;
  bool get loggedIn => user?.loggedIn ?? false;
  bool get initiallyLoggedIn => initialUser?.loggedIn ?? false;
  bool get shouldRedirect => loggedIn && _redirectLocation != null;

  String getRedirectLocation() => _redirectLocation!;
  bool hasRedirect() => _redirectLocation != null;
  void setRedirectLocationIfUnset(String loc) => _redirectLocation ??= loc;
  void clearRedirectLocation() => _redirectLocation = null;

  /// Mark as not needing to notify on a sign in / out when we intend
  /// to perform subsequent actions (such as navigation) afterwards.
  void updateNotifyOnAuthChange(bool notify) => notifyOnAuthChange = notify;

  void update(BaseAuthUser newUser) {
    final shouldUpdate =
        user?.uid == null || newUser.uid == null || user?.uid != newUser.uid;
    initialUser ??= newUser;
    user = newUser;
    // Refresh the app on auth change unless explicitly marked otherwise.
    // No need to update unless the user has changed.
    if (notifyOnAuthChange && shouldUpdate) {
      notifyListeners();
    }
    // Once again mark the notifier as needing to update on auth change
    // (in order to catch sign in / out events).
    updateNotifyOnAuthChange(true);
  }

  void stopShowingSplashImage() {
    showSplashImage = false;
    notifyListeners();
  }
}

GoRouter createRouter(AppStateNotifier appStateNotifier) => GoRouter(
      initialLocation: '/',
      debugLogDiagnostics: true,
      refreshListenable: appStateNotifier,
      errorBuilder: (context, state) => RootPageContext.wrap(
        appStateNotifier.loggedIn ? const NavBarPage() : const WelcomeScreenWidget(),
        errorRoute: state.uri.toString(),
      ),
      routes: [
        FFRoute(
          name: '_initialize',
          path: '/',
          builder: (context, _) => RootPageContext.wrap(
            appStateNotifier.loggedIn ? const NavBarPage() : const WelcomeScreenWidget(),
          ),
        ),
        FFRoute(
          name: 'Discovery',
          path: '/discovery',
          requireAuth: true,
          builder: (context, params) => params.isEmpty
              ? const NavBarPage(initialPage: 'Discovery')
              : const DiscoveryWidget(),
        ),
        FFRoute(
          name: 'ProfileHome',
          path: '/profileHome',
          requireAuth: true,
          builder: (context, params) => params.isEmpty
              ? const NavBarPage(initialPage: 'ProfileHome')
              : const ProfileHomeWidget(),
        ),
        FFRoute(
          name: 'ProfileDetail',
          path: '/profileDetail',
          requireAuth: true,
          asyncParams: {
            'profile': getDoc(
                ['users', 'publicProfile'], PublicProfileRecord.fromSnapshot),
          },
          builder: (context, params) => ProfileDetailWidget(
            profile: params.getParam('profile', ParamType.Document),
            showLikeEtcFields:
                params.getParam('showLikeEtcFields', ParamType.bool),
            isMatch: params.getParam('isMatch', ParamType.bool),
            message: params.getParam('message', ParamType.String),
            like: params.getParam(
              'like',
              ParamType.DocumentReference,
            ),
            showEverything: params.getParam(
              'showEverything',
              ParamType.bool,
            ),
            fromLikesScreen: params.getParam(
              'fromLikesScreen',
              ParamType.bool,
            ),
            evolvedUpgradeDetail: params.getParam(
              'evolvedUpgradeDetail',
              ParamType.bool,
            ),
            rose: params.getParam(
              'rose',
              ParamType.bool,
            ),
            match: params.getParam(
              'match',
              ParamType.bool,
            ),
            fromDiscovery: params.getParam(
              'fromDiscovery',
              ParamType.bool,
            ),
          ),
        ),
        FFRoute(
          name: 'EditProfile',
          path: '/editProfile',
          requireAuth: true,
          builder: (context, params) => const EditProfileWidget(),
        ),
        FFRoute(
          name: 'MoreAboutYou',
          path: '/moreAboutYou',
          requireAuth: true,
          builder: (context, params) => MoreAboutYouWidget(
            hideNameVisible: params.getParam(
              'hideNameVisible',
              ParamType.bool,
            ),
          ),
        ),
        FFRoute(
          name: 'SearchPreferences',
          path: '/searchPreferences',
          requireAuth: true,
          builder: (context, params) => const SearchPreferencesWidget(),
        ),
        FFRoute(
          name: 'Settings',
          path: '/settings',
          requireAuth: true,
          builder: (context, params) => const SettingsWidget(),
        ),
        FFRoute(
          name: 'LocationAllowingEntry',
          path: '/locationAllowingEntry',
          requireAuth: true,
          builder: (context, params) => const LocationAllowingEntryWidget(),
        ),
        FFRoute(
          name: 'LocationAllowingUsageNotice',
          path: '/locationAllowingUsageNotice',
          requireAuth: true,
          builder: (context, params) => const LocationAllowingUsageNoticeWidget(),
        ),
        FFRoute(
          name: 'LocationAllowingMap',
          path: '/locationAllowingMap',
          requireAuth: true,
          builder: (context, params) => const LocationAllowingMapWidget(),
        ),
        FFRoute(
          name: 'ContactBlockingEntry',
          path: '/contactBlockingEntry',
          requireAuth: true,
          builder: (context, params) => const ContactBlockingEntryWidget(),
        ),
        FFRoute(
          name: 'WelcomeScreen',
          path: '/welcomeScreen',
          builder: (context, params) => const WelcomeScreenWidget(),
        ),
        FFRoute(
          name: 'WelcomeScreen1',
          path: '/welcomeScreen1',
          builder: (context, params) => const WelcomeScreen1Widget(),
        ),
        FFRoute(
          name: 'WelcomeScreen2',
          path: '/welcomeScreen2',
          builder: (context, params) => const WelcomeScreen2Widget(),
        ),
        FFRoute(
          name: 'PhoneNumber',
          path: '/phoneNumber',
          builder: (context, params) => const PhoneNumberWidget(),
        ),
        FFRoute(
          name: 'PhoneVerificationCode',
          path: '/phoneVerificationCode',
          builder: (context, params) => const PhoneVerificationCodeWidget(),
        ),
        FFRoute(
          name: 'WelcomeRules',
          path: '/welcomeRules',
          requireAuth: true,
          builder: (context, params) => const WelcomeRulesWidget(),
        ),
        FFRoute(
          name: 'Name',
          path: '/name',
          requireAuth: true,
          builder: (context, params) => const NameWidget(),
        ),
        FFRoute(
          name: 'Birthday',
          path: '/birthday',
          requireAuth: true,
          builder: (context, params) => const BirthdayWidget(),
        ),
        FFRoute(
          name: 'OwnGender',
          path: '/ownGender',
          requireAuth: true,
          builder: (context, params) => const OwnGenderWidget(),
        ),
        FFRoute(
          name: 'RelationshipAims',
          path: '/relationshipAims',
          requireAuth: true,
          builder: (context, params) => const RelationshipAimsWidget(),
        ),
        FFRoute(
          name: 'Hobbies',
          path: '/hobbies',
          requireAuth: true,
          builder: (context, params) => const HobbiesWidget(),
        ),
        FFRoute(
          name: 'Education',
          path: '/education',
          requireAuth: true,
          builder: (context, params) => const EducationWidget(),
        ),
        FFRoute(
          name: 'BioWomen',
          path: '/bioWomen',
          requireAuth: true,
          builder: (context, params) => const BioWomenWidget(),
        ),
        FFRoute(
          name: 'Images',
          path: '/images',
          requireAuth: true,
          builder: (context, params) => const ImagesWidget(),
        ),
        FFRoute(
          name: 'VerificationPrompt',
          path: '/verificationPrompt',
          requireAuth: true,
          builder: (context, params) => const VerificationPromptWidget(),
        ),
        FFRoute(
          name: 'Chat',
          path: '/chat',
          requireAuth: true,
          builder: (context, params) =>
              params.isEmpty ? const NavBarPage(initialPage: 'Chat') : const ChatWidget(),
        ),
        FFRoute(
          name: 'ChatWindow',
          path: '/chatWindow',
          requireAuth: true,
          builder: (context, params) => ChatWindowWidget(
            chat: params.getParam(
                'chat', ParamType.DocumentReference, isList: false, collectionNamePath: ['likes']),
            partnerName: params.getParam('partnerName', ParamType.String),
          ),
        ),
        FFRoute(
          name: 'Report1',
          path: '/report1',
          requireAuth: true,
          asyncParams: {
            'reportedUser': getDoc(
                ['users', 'publicProfile'], PublicProfileRecord.fromSnapshot),
          },
          builder: (context, params) => Report1Widget(
            reportedUser: params.getParam('reportedUser', ParamType.Document),
            reportedMatch: params.getParam(
                'reportedMatch', ParamType.DocumentReference, isList: false, collectionNamePath: ['likes']),
          ),
        ),
        FFRoute(
          name: 'Report2',
          path: '/report2',
          requireAuth: true,
          asyncParams: {
            'userToReport': getDoc(
                ['users', 'publicProfile'], PublicProfileRecord.fromSnapshot),
          },
          builder: (context, params) => Report2Widget(
            userToReport: params.getParam('userToReport', ParamType.Document),
            matchToReport: params.getParam(
                'matchToReport', ParamType.DocumentReference, isList: false, collectionNamePath: ['likes']),
            reason: params.getParam('reason', ParamType.String),
          ),
        ),
        FFRoute(
          name: 'NotificationCenter',
          path: '/notificationCenter',
          requireAuth: true,
          builder: (context, params) => const NotificationCenterWidget(),
        ),
        FFRoute(
          name: 'EvolvedTab',
          path: '/evolvedTab',
          requireAuth: true,
          builder: (context, params) => params.isEmpty
              ? const NavBarPage(initialPage: 'EvolvedTab')
              : const EvolvedTabWidget(),
        ),
        FFRoute(
          name: 'EvolvedSubscription',
          path: '/evolvedSubscription',
          requireAuth: true,
          builder: (context, params) => const EvolvedSubscriptionWidget(),
        ),
        FFRoute(
          name: 'PlusSubscription',
          path: '/plusSubscription',
          requireAuth: true,
          builder: (context, params) => const PlusSubscriptionWidget(),
        ),
        FFRoute(
          name: 'PreferredGender',
          path: '/preferredGender',
          requireAuth: true,
          builder: (context, params) => const PreferredGenderWidget(),
        ),
        FFRoute(
          name: 'Kink',
          path: '/kink',
          requireAuth: true,
          builder: (context, params) => const KinkWidget(),
        ),
        FFRoute(
          name: 'OwnProfilePreview',
          path: '/ownProfilePreview',
          requireAuth: true,
          asyncParams: {
            'profile': getDoc(
                ['users', 'publicProfile'], PublicProfileRecord.fromSnapshot),
          },
          builder: (context, params) => OwnProfilePreviewWidget(
            profile: params.getParam('profile', ParamType.Document),
          ),
        ),
        FFRoute(
          name: 'EditPrompts',
          path: '/editPrompts',
          requireAuth: true,
          builder: (context, params) => const EditPromptsWidget(),
        ),
        FFRoute(
          name: 'BioMen',
          path: '/bioMen',
          requireAuth: true,
          builder: (context, params) => const BioMenWidget(),
        ),
        FFRoute(
          name: 'LikeStage2',
          path: '/likeStage2',
          requireAuth: true,
          asyncParams: {
            'publicProfile': getDoc(
                ['users', 'publicProfile'], PublicProfileRecord.fromSnapshot),
          },
          builder: (context, params) => LikeStage2Widget(
            publicProfile: params.getParam('publicProfile', ParamType.Document),
            like: params.getParam(
                'like', ParamType.DocumentReference, isList: false, collectionNamePath: ['likes']),
            evolvedLike: params.getParam('evolvedLike', ParamType.bool),
          ),
        ),
        FFRoute(
          name: 'MatchStage2',
          path: '/matchStage2',
          requireAuth: true,
          asyncParams: {
            'publicProfile': getDoc(
                ['users', 'publicProfile'], PublicProfileRecord.fromSnapshot),
          },
          builder: (context, params) => MatchStage2Widget(
            publicProfile: params.getParam('publicProfile', ParamType.Document),
            like: params.getParam(
                'like', ParamType.DocumentReference, isList: false, collectionNamePath: ['likes']),
            evolvedLike: params.getParam('evolvedLike', ParamType.bool),
            fromLikesScreen: params.getParam(
               'fromLikesScreen',
               ParamType.bool,
             ),
          ),
        ),
        FFRoute(
          name: 'LocationAllowingMapSettings',
          path: '/locationAllowingMapSettings',
          requireAuth: true,
          builder: (context, params) => const LocationAllowingMapSettingsWidget(),
        ),
        FFRoute(
          name: 'UpdatePhoneNumber',
          path: '/updatePhoneNumber',
          requireAuth: true,
          builder: (context, params) => const UpdatePhoneNumberWidget(),
        ),
        FFRoute(
          name: 'UpdatePhoneVerificationCode',
          path: '/updatePhoneVerificationCode',
          requireAuth: true,
          builder: (context, params) => const UpdatePhoneVerificationCodeWidget(),
        ),
        FFRoute(
          name: 'TBDScreen',
          path: '/tBDScreen',
          requireAuth: true,
          builder: (context, params) => const TBDScreenWidget(),
        ),
        FFRoute(
          name: 'ExperienceLevel',
          path: '/experienceLevel',
          requireAuth: true,
          builder: (context, params) => const ExperienceLevelWidget(),
        ),
        FFRoute(
          name: 'WaitingListSpeedUpPurchase',
          path: '/waitingListSpeedUpPurchase',
          requireAuth: true,
          builder: (context, params) => const WaitingListSpeedUpPurchaseWidget(),
        ),
        FFRoute(
          name: 'ChatWindowFeedback',
          path: '/chatWindowFeedback',
          requireAuth: true,
          builder: (context, params) => ChatWindowFeedbackWidget(
            chat: params.getParam(
                'chat', ParamType.DocumentReference, isList: false, collectionNamePath: ['likes']),
            partnerName: params.getParam('partnerName', ParamType.String),
          ),
        ),
        FFRoute(
          name: 'EmailAddress',
          path: '/emailAddress',
          builder: (context, params) => const EmailAddressWidget(),
        ),
        FFRoute(
          name: 'EmailAddressChange',
          path: '/emailAddressChange',
          requireAuth: true,
          builder: (context, params) => const EmailAddressChangeWidget(),
        ),
        FFRoute(
          name: 'GoldSubscription',
          path: '/goldSubscription',
          requireAuth: true,
          builder: (context, params) => const GoldSubscriptionWidget(),
        ),
        FFRoute(
          name: 'GoldEvolvedSubscription',
          path: '/goldEvolvedSubscription',
          requireAuth: true,
          builder: (context, params) => GoldEvolvedSubscriptionWidget(
            initialSubscription: params.getParam(
              'initialSubscription',
              ParamType.String,
            ),
          ),
        ),
        FFRoute(
          name: 'PlusEvolvedSubscription',
          path: '/plusEvolvedSubscription',
          requireAuth: true,
          builder: (context, params) => PlusEvolvedSubscriptionWidget(
            initialSubscription: params.getParam(
              'initialSubscription',
              ParamType.String,
            ),
          ),
        ),
        FFRoute(
          name: 'WaitingListIntro',
          path: '/waitingListIntro',
          requireAuth: true,
          builder: (context, params) => const WaitingListIntroWidget(),
        ),
        FFRoute(
          name: 'Licences',
          path: '/licences',
          requireAuth: true,
          builder: (context, params) => const LicencesWidget(),
        ),
        FFRoute(
          name: 'ChatWindowChyrpe',
          path: '/chatWindowChyrpe',
          requireAuth: true,
          builder: (context, params) => ChatWindowChyrpeWidget(
            chat: params.getParam(
              'chat',
              ParamType.DocumentReference,
              isList: false,
              collectionNamePath: ['likes'],
            ),
            partnerName: params.getParam(
              'partnerName',
              ParamType.String,
            ),
          ),
        ),
        FFRoute(
          name: 'ChyrpeStandardSubscription',
          path: '/chyrpeStandardSubscription',
          requireAuth: true,
          builder: (context, params) => const ChyrpeStandardSubscriptionWidget(),
        ),
        FFRoute(
          name: 'ChyrpeStandardSubscriptionNonFTU',
          path: '/chyrpeStandardSubscriptionNonFTU',
          requireAuth: true,
          builder: (context, params) =>
              const ChyrpeStandardSubscriptionNonFTUWidget(),
        ),
        FFRoute(
          name: 'GlobalMatchingInfo',
          path: '/globalMatchingInfo',
          requireAuth: true,
          builder: (context, params) => const GlobalMatchingInfoWidget(),
        ),
        FFRoute(
          name: 'GlobalWaitingListIntro',
          path: '/globalWaitingListIntro',
          requireAuth: true,
          builder: (context, params) => const GlobalWaitingListIntroWidget(),
        ),
        FFRoute(
          name: 'GlobalWaitingListSpeedUpPurchase',
          path: '/globalWaitingListSpeedUpPurchase',
          requireAuth: true,
          builder: (context, params) =>
              const GlobalWaitingListSpeedUpPurchaseWidget(),
        ),
        FFRoute(
          name: 'LocalWaitingListForGlobalMatchers',
          path: '/localWaitingListForGlobalMatchers',
          requireAuth: true,
          builder: (context, params) =>
              const LocalWaitingListForGlobalMatchersWidget(),
        ),
        FFRoute(
          name: 'GlobalMatchersSpeedUpPurchase',
          path: '/globalMatchersSpeedUpPurchase',
          requireAuth: true,
          builder: (context, params) => const GlobalMatchersSpeedUpPurchaseWidget(),
        ),
        FFRoute(
          name: 'EditProfileSignUp',
          path: '/editProfileSignUp',
          requireAuth: true,
          builder: (context, params) => const EditProfileSignUpWidget(),
        ),
        FFRoute(
          name: 'FreePlusPeriod',
          path: '/freePlusPeriod',
          builder: (context, params) => const FreePlusPeriodWidget(),
        ),
        FFRoute(
          name: 'FreeGoldPeriod',
          path: '/freeGoldPeriod',
          builder: (context, params) => FreeGoldPeriodWidget(
             freeGold2: params.getParam(
               'freeGold2',
               ParamType.bool,
             ),
           ),
        ),
        FFRoute(
          name: 'MenAdditionalRules',
          path: '/menAdditionalRules',
          builder: (context, params) => const MenAdditionalRulesWidget(),
        ),
        FFRoute(
          name: 'FreeGiftSubs',
          path: '/freeGiftSubs',
          builder: (context, params) => FreeGiftSubsWidget(
            freeGold2: params.getParam(
              'freeGold2',
              ParamType.bool,
            ),
          ),
        ),
        FFRoute(
          name: 'ChyrpeStandardNew',
          path: '/chyrpeStandardNew',
          requireAuth: true,
          builder: (context, params) => const ChyrpeStandardNewWidget(),
        ),
        FFRoute(
          name: 'FreeTrialEnjoy',
          path: '/freeTrialEnjoy',
          requireAuth: true,
          builder: (context, params) => const FreeTrialEnjoyWidget(),
        ),
        FFRoute(
          name: 'SpeedUpPurcahsesWaiting',
          path: '/speedUpPurcahsesWaiting',
          requireAuth: true,
          builder: (context, params) => const SpeedUpPurcahsesWaitingWidget(),
        ),
        FFRoute(
          name: 'StandardLifetimeUpgrade',
          path: '/standardLifetimeUpgrade',
          requireAuth: true,
          builder: (context, params) => const StandardLifetimeUpgradeWidget(),
        ),
        FFRoute(
          name: 'TempLoaderScreen',
          path: '/tempLoaderScreen',
          builder: (context, params) => const TempLoaderScreenWidget(),
        ),
        FFRoute(
          name: 'FreeStandardTable',
          path: '/freeStandardTable',
          requireAuth: true,
          builder: (context, params) => const FreeStandardTableWidget(),
        ),
        FFRoute(
          name: 'FreeStandardList',
          path: '/freeStandardList',
          requireAuth: true,
          builder: (context, params) => const FreeStandardListWidget(),
        ),
        FFRoute(
          name: 'ForumChat',
          path: '/forumChat',
          builder: (context, params) => ForumChatWidget(
            forumRef: params.getParam(
              'forumRef',
              ParamType.DocumentReference, isList: false, collectionNamePath: ['forums']
            ),
          ),
        ),
        FFRoute(
          name: 'ForumDetails',
          path: '/forumDetails',
          asyncParams: {
            'forumDoc': getDoc(['forums'], ForumsRecord.fromSnapshot),
          },
          builder: (context, params) => ForumDetailsWidget(
            forumDoc: params.getParam(
              'forumDoc',
              ParamType.Document,
            ),
          ),
        ),
        FFRoute(
          name: 'MiscScreen',
          path: '/miscScreen',
          requireAuth: true,
          builder: (context, params) => params.isEmpty
              ? const NavBarPage(initialPage: 'MiscScreen')
              : const MiscScreenWidget(),
        ),
        FFRoute(
          name: 'NameConfirmationPage',
          path: '/nameConfirmationPage',
          requireAuth: true,
          builder: (context, params) => const NameConfirmationPageWidget(),
        ),
        FFRoute(
          name: 'BasicsConfirmationPage',
          path: '/basicsConfirmationPage',
          requireAuth: true,
          builder: (context, params) => const BasicsConfirmationPageWidget(),
        ),
        FFRoute(
          name: 'BioConfirmationPage',
          path: '/bioConfirmationPage',
          requireAuth: true,
          builder: (context, params) => const BioConfirmationPageWidget(),
        ),
        FFRoute(
          name: 'LastStepConfirmationPage',
          path: '/lastStepConfirmationPage',
          requireAuth: true,
          builder: (context, params) => const LastStepConfirmationPageWidget(),
        ),
        FFRoute(
          name: 'BioPrompts',
          path: '/bioPrompts',
          requireAuth: true,
          builder: (context, params) => const BioPromptsWidget(),
        ),
        FFRoute(
          name: 'BioBio',
          path: '/bioBio',
          requireAuth: true,
          builder: (context, params) => const BioBioWidget(),
        ),
        FFRoute(
          name: 'LocationAllowingEntryLegacy',
          path: '/locationAllowingEntryLegacy',
          requireAuth: true,
          builder: (context, params) => const LocationAllowingEntryLegacyWidget(),
        ),
        FFRoute(
          name: 'GoldSubscriptionNew',
          path: '/goldSubscriptionNew',
          requireAuth: true,
          builder: (context, params) => const GoldSubscriptionNewWidget(),
        ),
        FFRoute(
          name: 'PlusSubscriptionNew',
          path: '/plusSubscriptionNew',
          requireAuth: true,
          builder: (context, params) => const PlusSubscriptionNewWidget(),
        ),
        FFRoute(
          name: 'EvolvedSubscriptionNew',
          path: '/evolvedSubscriptionNew',
          requireAuth: true,
          builder: (context, params) => EvolvedSubscriptionNewWidget(
            navigateBack: params.getParam(
              'navigateBack',
              ParamType.bool,
            ),
          ),
        ),
        FFRoute(
          name: 'GoldEvolvedSubscriptionNew',
          path: '/goldEvolvedSubscriptionNew',
          requireAuth: true,
          builder: (context, params) => GoldEvolvedSubscriptionNewWidget(
            initialSubscription: params.getParam(
              'initialSubscription',
              ParamType.String,
            ),
          ),
        ),
        FFRoute(
          name: 'PlusEvolvedSubscriptionNew',
          path: '/plusEvolvedSubscriptionNew',
          requireAuth: true,
          builder: (context, params) => PlusEvolvedSubscriptionNewWidget(
            initialSubscription: params.getParam(
              'initialSubscription',
              ParamType.String,
            ),
          ),
        ),
        FFRoute(
          name: 'ChyrpeStandardNewNew',
          path: '/chyrpeStandardNewNew',
          requireAuth: true,
          builder: (context, params) => const ChyrpeStandardNewNewWidget(),
        ),
        FFRoute(
          name: 'StandardLifetimeUpgradeNew',
          path: '/standardLifetimeUpgradeNew',
          requireAuth: true,
          builder: (context, params) => const StandardLifetimeUpgradeNewWidget(),
        ),
        FFRoute(
          name: 'GoldSubscriptionOffer',
          path: '/goldSubscriptionOffer',
          requireAuth: true,
          builder: (context, params) => const GoldSubscriptionOfferWidget(),
        ),
        FFRoute(
          name: 'SignUpLA',
          path: '/signUpLA',
          builder: (context, params) => const SignUpLAWidget(),
        ),
        FFRoute(
          name: 'LiteStandard',
          path: '/liteStandard',
          requireAuth: true,
          builder: (context, params) => const LiteStandardWidget(),
        ),
        FFRoute(
          name: 'DiscountEvolved',
          path: '/discountEvolved',
          builder: (context, params) => DiscountEvolvedWidget(
            discount: params.getParam(
              'discount',
              ParamType.DataStruct,
              isList: false,
              structBuilder: DiscountStruct.fromSerializableMap,
              ),
          ),
        ),
        FFRoute(
          name: 'DiscountGold',
          path: '/discountGold',
          builder: (context, params) => DiscountGoldWidget(
            discount: params.getParam(
              'discount',
              ParamType.DataStruct,
              isList: false,
              structBuilder: DiscountStruct.fromSerializableMap,
              ),
          ),
        ),
        FFRoute(
          name: 'DiscountStandard',
          path: '/discountStandard',
          builder: (context, params) => DiscountStandardWidget(
            discount: params.getParam(
              'discount',
              ParamType.DataStruct,
              isList: false,
              structBuilder: DiscountStruct.fromSerializableMap,
              ),
          ),
        ),
        FFRoute(
          name: 'DiscoveryIntro',
          path: '/discoveryIntro',
          requireAuth: true,
          builder: (context, params) => const DiscoveryIntroWidget(),
        ),
        FFRoute(
          name: 'ProfileHomeIntro',
          path: '/profileHomeIntro',
          requireAuth: true,
          builder: (context, params) => const ProfileHomeIntroWidget(),
        ),
        FFRoute(
          name: 'EditProfileIntro',
          path: '/editProfileIntro',
          requireAuth: true,
          builder: (context, params) => const EditProfileIntroWidget(),
        ),
        FFRoute(
          name: 'OwnProfileIntro',
          path: '/ownProfileIntro',
          requireAuth: true,
          asyncParams: {
            'profile': getDoc(
                ['users', 'publicProfile'], PublicProfileRecord.fromSnapshot),
          },
          builder: (context, params) => OwnProfileIntroWidget(
            profile: params.getParam(
              'profile',
              ParamType.Document,
            ),
          ),
        ),
        FFRoute(
          name: 'DiscoveryIntro2',
          path: '/discoveryIntro2',
          requireAuth: true,
          builder: (context, params) => const DiscoveryIntro2Widget(),
        ),
        FFRoute(
          name: 'DiscoveryIntro3',
          path: '/discoveryIntro3',
          requireAuth: true,
          builder: (context, params) => const DiscoveryIntro3Widget(),
        ),
        FFRoute(
          name: 'ProfileDetailIntro',
          path: '/profileDetailIntro',
          requireAuth: true,
          asyncParams: {
            'profile': getDoc(
                ['users', 'publicProfile'], PublicProfileRecord.fromSnapshot),
          },
          builder: (context, params) => ProfileDetailIntroWidget(
            profile: params.getParam(
              'profile',
              ParamType.Document,
            ),
            showLikeEtcFields: params.getParam(
              'showLikeEtcFields',
              ParamType.bool,
            ),
            isMatch: params.getParam(
              'isMatch',
              ParamType.bool,
            ),
            like: params.getParam(
              'like',
              ParamType.DocumentReference,
              isList: false,
              collectionNamePath: ['likes'],
            ),
            showEverything: params.getParam(
              'showEverything',
              ParamType.bool,
            ),
            fromLikesScreen: params.getParam(
              'fromLikesScreen',
              ParamType.bool,
            ),
            evolvedUpgradeDetail: params.getParam(
              'evolvedUpgradeDetail',
              ParamType.bool,
            ),
          ),
        ),
        FFRoute(
          name: 'MatchStage2Intro',
          path: '/matchStage2Intro',
          requireAuth: true,
          asyncParams: {
            'publicProfile': getDoc(
                ['users', 'publicProfile'], PublicProfileRecord.fromSnapshot),
          },
          builder: (context, params) => MatchStage2IntroWidget(
            publicProfile: params.getParam(
              'publicProfile',
              ParamType.Document,
            ),
          ),
        ),
        FFRoute(
          name: 'DiscoveryIntro4',
          path: '/discoveryIntro4',
          requireAuth: true,
          builder: (context, params) => const DiscoveryIntro4Widget(),
        ),
        FFRoute(
          name: 'ChatIntro',
          path: '/chatIntro',
          requireAuth: true,
          builder: (context, params) => const ChatIntroWidget(),
        ),
        FFRoute(
          name: 'ChatWindowIntro',
          path: '/chatWindowIntro',
          requireAuth: true,
          builder: (context, params) => const ChatWindowIntroWidget(),
        ),
        FFRoute(
          name: 'EvolvedTabIntro',
          path: '/evolvedTabIntro',
          requireAuth: true,
          builder: (context, params) => const EvolvedTabIntroWidget(),
        ),
        FFRoute(
          name: 'DivineSubscription',
          path: '/divineSubscription',
          builder: (context, params) => DivineSubscriptionWidget(
            navigateBack: params.getParam(
              'navigateBack',
              ParamType.bool,
            ),
          ),
        ),
        FFRoute(
          name: 'SignUpModular',
          path: '/signUpModular',
          builder: (context, params) => const SignUpModularWidget(),
        ),
        FFRoute(
          name: 'GenericEditProfileDetailPage',
          path: '/genericEditProfileDetailPage',
          builder: (context, params) => GenericEditProfileDetailPageWidget(
            name: params.getParam(
              'name',
              ParamType.String,
            ),
            editorConfig: params.getParam(
              'editorConfig',
              ParamType.DataStruct,
              isList: false,
              structBuilder: SignupScreenStruct.fromSerializableMap,
            ),
            initialSelectedOption: params.getParam(
              'initialSelectedOption',
              ParamType.String,
            ),
            initialSelectedOptions: params.getParam<String>(
              'initialSelectedOptions',
              ParamType.String,
              isList: true,
            ),
            kind: params.getParam<SignupScreenType>(
              'kind',
              ParamType.Enum,
            ),
            title: params.getParam(
              'title',
              ParamType.String,
            ),
          ),
        ),
       FFRoute(
          name: 'EditProfileNew',
          path: '/editProfileNew',
          builder: (context, params) => const EditProfileNewWidget(),
        ),
        FFRoute(
          name: 'SearchPreferencesNew',
          path: '/searchPreferencesNew',
          builder: (context, params) => SearchPreferencesNewWidget(
            fromDiscovery: params.getParam(
              'fromDiscovery',
              ParamType.bool,
            ),
          ),
        ),
        FFRoute(
          name: 'GenericPreferencesDetailPage',
          path: '/genericPreferencesDetailPage',
<<<<<<< HEAD
          builder: (context, params) => GenericPreferencesDetailPageWidget(
            name: params.getParam(
              'name',
              ParamType.String,
            ),
            editorConfig: params.getParam(
              'editorConfig',
              ParamType.DataStruct,
              isList: false,
              structBuilder: SearchPrefSelectorScreenStruct.fromSerializableMap,
            ),
            kind: params.getParam<SignupScreenType>(
              'kind',
              ParamType.Enum,
            ),
            title: params.getParam(
              'title',
              ParamType.String,
            ),
            searchPrefScreen: params.getParam(
              'searchPrefScreen',
              ParamType.DataStruct,
              isList: false,
              structBuilder: SearchPrefScreenStruct.fromSerializableMap,
            ),
            searchPref: params.getParam(
              'searchPref',
              ParamType.DataStruct,
              isList: false,
              structBuilder: SearchPrefStruct.fromSerializableMap,
            ),
          ),
=======
          builder: (context, params) {
            return GenericPreferencesDetailPageWidget(
              name: params.getParam(
                'name',
                ParamType.String,
              ),
              editorConfig: params.getParam(
                'editorConfig',
                ParamType.DataStruct,
                isList: false,
                structBuilder:
                    SearchPrefSelectorScreenStruct.fromSerializableMap,
              ),
              kind: params.getParam<SignupScreenType>(
                'kind',
                ParamType.Enum,
              ),
              title: params.getParam(
                'title',
                ParamType.String,
              ),
              searchPrefScreen: params.getParam(
                'searchPrefScreen',
                ParamType.DataStruct,
                isList: false,
                structBuilder: SearchPrefScreenStruct.fromSerializableMap,
              ),
              searchPref: params.getParam(
                'searchPref',
                ParamType.DataStruct,
                isList: false,
                structBuilder: SearchPrefStruct.fromSerializableMap,
              ),
            );
          },
>>>>>>> 7f271f2b8 (Solve roles page error, Solve for empty list data for gender preferences, Get dev alternativePackageId for gold and evolved)
        ),
        FFRoute(
          name: 'ProfileDetailNew',
          path: '/profileDetailNew',
          builder: (context, params) => ProfileDetailNewWidget(
            publicProfile: params.getParam(
              'publicProfile',
              ParamType.DocumentReference,
              isList: false,
              collectionNamePath: ['users', 'publicProfile'],
            ),
          ),
        ),
        FFRoute(
          name: 'ChatNew',
          path: '/chatNew',
          requireAuth: true,
          builder: (context, params) => const ChatNewWidget(),
        )
      ].map((r) => r.toRoute(appStateNotifier)).toList(),
    );

extension NavParamExtensions on Map<String, String?> {
  Map<String, String> get withoutNulls => Map.fromEntries(
        entries
            .where((e) => e.value != null)
            .map((e) => MapEntry(e.key, e.value!)),
      );
}

extension NavigationExtensions on BuildContext {
  void goNamedAuth(
    String name,
    bool mounted, {
    Map<String, String> pathParameters = const <String, String>{},
    Map<String, String> queryParameters = const <String, String>{},
    Object? extra,
    bool ignoreRedirect = false,
  }) =>
      !mounted || GoRouter.of(this).shouldRedirect(ignoreRedirect)
          ? null
          : goNamed(
              name,
              pathParameters: pathParameters,
              queryParameters: queryParameters,
              extra: extra,
            );

  void pushNamedAuth(
    String name,
    bool mounted, {
    Map<String, String> pathParameters = const <String, String>{},
    Map<String, String> queryParameters = const <String, String>{},
    Object? extra,
    bool ignoreRedirect = false,
  }) =>
      !mounted || GoRouter.of(this).shouldRedirect(ignoreRedirect)
          ? null
          : pushNamed(
              name,
              pathParameters: pathParameters,
              queryParameters: queryParameters,
              extra: extra,
            );

  void safePop() {
    // If there is only one route on the stack, navigate to the initial
    // page instead of popping.
    if (canPop()) {
      pop();
    } else {
      go('/');
    }
  }
}

extension GoRouterExtensions on GoRouter {
  AppStateNotifier get appState => AppStateNotifier.instance;
  void prepareAuthEvent([bool ignoreRedirect = false]) =>
      appState.hasRedirect() && !ignoreRedirect
          ? null
          : appState.updateNotifyOnAuthChange(false);
  bool shouldRedirect(bool ignoreRedirect) =>
      !ignoreRedirect && appState.hasRedirect();
  void clearRedirectLocation() => appState.clearRedirectLocation();
  void setRedirectLocationIfUnset(String location) =>
      appState.updateNotifyOnAuthChange(false);
}

extension _GoRouterStateExtensions on GoRouterState {
  Map<String, dynamic> get extraMap =>
      extra != null ? extra as Map<String, dynamic> : {};
  Map<String, dynamic> get allParams => <String, dynamic>{}
    ..addAll(pathParameters)
    ..addAll(uri.queryParameters)
    ..addAll(extraMap);
  TransitionInfo get transitionInfo => extraMap.containsKey(kTransitionInfoKey)
      ? extraMap[kTransitionInfoKey] as TransitionInfo
      : TransitionInfo.appDefault();
}

class FFParameters {
  FFParameters(this.state, [this.asyncParams = const {}]);

  final GoRouterState state;
  final Map<String, Future<dynamic> Function(String)> asyncParams;

  Map<String, dynamic> futureParamValues = {};

  // Parameters are empty if the params map is empty or if the only parameter
  // present is the special extra parameter reserved for the transition info.
  bool get isEmpty =>
      state.allParams.isEmpty ||
      (state.extraMap.length == 1 &&
          state.extraMap.containsKey(kTransitionInfoKey));
  bool isAsyncParam(MapEntry<String, dynamic> param) =>
      asyncParams.containsKey(param.key) && param.value is String;
  bool get hasFutures => state.allParams.entries.any(isAsyncParam);
  Future<bool> completeFutures() => Future.wait(
        state.allParams.entries.where(isAsyncParam).map(
          (param) async {
            final doc = await asyncParams[param.key]!(param.value)
                .onError((_, __) => null);
            if (doc != null) {
              futureParamValues[param.key] = doc;
              return true;
            }
            return false;
          },
        ),
      ).onError((_, __) => [false]).then((v) => v.every((e) => e));

  dynamic getParam<T>(
    String paramName,
    ParamType type, {
    bool isList = false,
    List<String>? collectionNamePath,
    StructBuilder<T>? structBuilder,
  }) {
    if (futureParamValues.containsKey(paramName)) {
      return futureParamValues[paramName];
    }
    if (!state.allParams.containsKey(paramName)) {
      return null;
    }
    final param = state.allParams[paramName];
    // Got parameter from `extras`, so just directly return it.
    if (param is! String) {
      return param;
    }
    // Return serialized value.
    return deserializeParam<T>(
      param,
      type,
      isList,
      collectionNamePath: collectionNamePath,
      structBuilder: structBuilder,
    );
  }
}

class FFRoute {
  const FFRoute({
    required this.name,
    required this.path,
    required this.builder,
    this.requireAuth = false,
    this.asyncParams = const {},
    this.routes = const [],
  });

  final String name;
  final String path;
  final bool requireAuth;
  final Map<String, Future<dynamic> Function(String)> asyncParams;
  final Widget Function(BuildContext, FFParameters) builder;
  final List<GoRoute> routes;

  GoRoute toRoute(AppStateNotifier appStateNotifier) => GoRoute(
        name: name,
        path: path,
        redirect: (context, state) {
          if (appStateNotifier.shouldRedirect) {
            final redirectLocation = appStateNotifier.getRedirectLocation();
            appStateNotifier.clearRedirectLocation();
            return redirectLocation;
          }

          if (requireAuth && !appStateNotifier.loggedIn) {
            appStateNotifier.setRedirectLocationIfUnset(state.uri.toString());
            return '/welcomeScreen';
          }
          return null;
        },
        pageBuilder: (context, state) {
          fixStatusBarOniOS16AndBelow(context);
          final ffParams = FFParameters(state, asyncParams);
          final page = ffParams.hasFutures
              ? FutureBuilder(
                  future: ffParams.completeFutures(),
                  builder: (context, _) => builder(context, ffParams),
                )
              : builder(context, ffParams);
          final child = appStateNotifier.loading
              ? Container(
                  color: Colors.white,
                  child: Center(
                    child: Image.asset(
                      'assets/images/Artboard_1.svg',
                      width: 250.0,
                      fit: BoxFit.cover,
                    ),
                  ),
                )
              : PushNotificationsHandler(child: page);

          final transitionInfo = state.transitionInfo;
          return transitionInfo.hasTransition
              ? CustomTransitionPage(
                  key: state.pageKey,
                  child: child,
                  transitionDuration: transitionInfo.duration,
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) =>
                          PageTransition(
                    type: transitionInfo.transitionType,
                    duration: transitionInfo.duration,
                    reverseDuration: transitionInfo.duration,
                    alignment: transitionInfo.alignment,
                    child: child,
                  ).buildTransitions(
                    context,
                    animation,
                    secondaryAnimation,
                    child,
                  ),
                )
              : MaterialPage(key: state.pageKey, child: child);
        },
        routes: routes,
      );
}

class TransitionInfo {
  const TransitionInfo({
    required this.hasTransition,
    this.transitionType = PageTransitionType.fade,
    this.duration = const Duration(milliseconds: 300),
    this.alignment,
  });

  final bool hasTransition;
  final PageTransitionType transitionType;
  final Duration duration;
  final Alignment? alignment;

  static TransitionInfo appDefault() => const TransitionInfo(hasTransition: false);
}

class RootPageContext {
  const RootPageContext(this.isRootPage, [this.errorRoute]);
  final bool isRootPage;
  final String? errorRoute;

  static bool isInactiveRootPage(BuildContext context) {
    final rootPageContext = context.read<RootPageContext?>();
    final isRootPage = rootPageContext?.isRootPage ?? false;
    final location = GoRouterState.of(context).uri.toString();
    return isRootPage &&
        location != '/' &&
        location != rootPageContext?.errorRoute;
  }

  static Widget wrap(Widget child, {String? errorRoute}) => Provider.value(
        value: RootPageContext(true, errorRoute),
        child: child,
      );
}
