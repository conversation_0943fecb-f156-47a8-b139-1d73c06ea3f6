import 'package:chyrpe/auth/firebase_auth/auth_util.dart';
import 'package:chyrpe/backend/schema/enums/enums.dart';
import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'forum_selector_component_model.dart';
export 'forum_selector_component_model.dart';

class ForumSelectorComponentWidget extends StatefulWidget {
  const ForumSelectorComponentWidget({super.key});

  @override
  State<ForumSelectorComponentWidget> createState() =>
      _ForumSelectorComponentWidgetState();
}

class _ForumSelectorComponentWidgetState
    extends State<ForumSelectorComponentWidget> {
  late ForumSelectorComponentModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => ForumSelectorComponentModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(10.0, 10.0, 10.0, 10.0),
      child: AuthUserStreamWidget(
        builder: (context) => StreamBuilder<List<ForumsRecord>>(
          stream: queryForumsRecord(
            queryBuilder: (forumsRecord) => forumsRecord
                .where(
                  'gAvailability',
                  arrayContains: currentUserDocument?.gender?.serialize(),
                )
                .orderBy('latestMessageSentTime', descending: true),
          ),
          builder: (context, snapshot) {
            // Customize what your widget looks like when it's loading.
            if (!snapshot.hasData) {
              return Center(
                child: SizedBox(
                  width: 50.0,
                  height: 50.0,
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      FlutterFlowTheme.of(context).accent2,
                    ),
                  ),
                ),
              );
            }
            List<ForumsRecord> listViewForumsRecordList = snapshot.data!;

            return ListView.separated(
              padding: EdgeInsets.zero,
              primary: false,
              shrinkWrap: true,
              scrollDirection: Axis.vertical,
              itemCount: listViewForumsRecordList.length,
              separatorBuilder: (_, __) => const SizedBox(height: 20.0),
              itemBuilder: (context, listViewIndex) {
                final listViewForumsRecord =
                    listViewForumsRecordList[listViewIndex];
                return Container(
                  width: MediaQuery.sizeOf(context).width * 1.0,
                  constraints: const BoxConstraints(
                    minHeight: 110.0,
                  ),
                  decoration: BoxDecoration(
                    color: FlutterFlowTheme.of(context).primaryBackground,
                    borderRadius: BorderRadius.circular(9.0),
                    border: Border.all(
                      color: const Color(0xFFBFC1C5),
                      width: 1.0,
                    ),
                  ),
                  child: InkWell(
                    splashColor: Colors.transparent,
                    focusColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    onTap: () async {
                      context.pushNamed(
                        'ForumChat',
                        queryParameters: {
                          'forumRef': serializeParam(
                            listViewForumsRecord.reference,
                            ParamType.DocumentReference,
                          ),
                        }.withoutNulls,
                      );
                    },
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Align(
                          alignment: const AlignmentDirectional(1.0, -1.0),
                          child: Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                9.0, 7.0, 0.0, 5.0),
                            child: Stack(
                              alignment: const AlignmentDirectional(1.0, -1.0),
                              children: [
                                Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      10.0, 10.0, 10.0, 10.0),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(5.0),
                                    child: Image.network(
                                      listViewForumsRecord.titlePicture,
                                      width: 90.0,
                                      height: 90.0,
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                                Align(
                                 alignment: const AlignmentDirectional(1.0, -1.0),
                                 child: Visibility(visible: ((currentUserDocument?.lastReadForumMessageTime[listViewForumsRecord.reference.id] ?? DateTime(1990,1,1)).isBefore((listViewForumsRecord.latestMessageSentTime ?? DateTime(2020,1,1))) ? true : false), 
                                 child: Container(
                                   width: 21.0,
                                   height: 21.0,
                                   decoration: BoxDecoration(
                                     color: const Color(0xFFFF2551),
                                     shape: BoxShape.circle,
                                     border: Border.all(
                                       color: FlutterFlowTheme.of(context).info,
                                       width: 2.0,
                                       ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        Flexible(
                          child: Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                15.0, 0.0, 10.0, 0.0),
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  listViewForumsRecord.name,
                                  textAlign: TextAlign.start,
                                  style: FlutterFlowTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        fontSize: 18.0,
                                        letterSpacing: 0.0,
                                        fontWeight: FontWeight.bold,
                                        useGoogleFonts: false,
                                      ),
                                ),
                                Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      0.0, 2.0, 0.0, 0.0),
                                  child: Text(
                                    listViewForumsRecord
                                        .latestMessageSentPreview
                                        .maybeHandleOverflow(
                                      maxChars: 25,
                                      replacement: '…',
                                    ),
                                    textAlign: TextAlign.start,
                                    maxLines: 1,
                                    style: FlutterFlowTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'BT Beau Sans',
                                          fontSize: 16.0,
                                          letterSpacing: 0.0,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }
}
