import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'button_color_async_model.dart';
export 'button_color_async_model.dart';

class ButtonColorAsyncWidget extends StatefulWidget {
  const ButtonColorAsyncWidget({
    super.key,
    required this.buttonColor,
    required this.callback,
    required this.title,
  });

  final Color? buttonColor;
  final Future Function()? callback;
  final String? title;

  @override
  State<ButtonColorAsyncWidget> createState() => _ButtonColorAsyncWidgetState();
}

class _ButtonColorAsyncWidgetState extends State<ButtonColorAsyncWidget> {
  late ButtonColorAsyncModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => ButtonColorAsyncModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return InkWell(
      splashColor: Colors.transparent,
      focusColor: Colors.transparent,
      hoverColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: () async {
        setState(() {
          _model.actionCompleted = false;
        });
        await widget.callback?.call();
        setState(() {
          _model.actionCompleted = true;
        });
      },
      child: Container(
        width: MediaQuery.sizeOf(context).width * 1.0,
        height: 50.0,
        decoration: BoxDecoration(
          color: widget.buttonColor,
          borderRadius: BorderRadius.circular(100.0),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Align(
              alignment: const AlignmentDirectional(0.0, 0.0),
              child: Text(
                valueOrDefault<String>(
                  widget.title,
                  'Next',
                ),
                style: FlutterFlowTheme.of(context).bodyMedium.override(
                      fontFamily: 'BT Beau Sans',
                      color: FlutterFlowTheme.of(context).info,
                      fontSize: 18.0,
                      fontWeight: FontWeight.bold,
                      useGoogleFonts: false,
                    ),
              ),
            ),
            if (!_model.actionCompleted)
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(10.0, 0.0, 0.0, 0.0),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8.0),
                  child: Image.asset(
                    'assets/images/loading.gif',
                    width: 30.0,
                    height: 30.0,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
