import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import 'package:flutter/material.dart';
import 'button_no_outline_model.dart';
export 'button_no_outline_model.dart';

class ButtonNoOutlineWidget extends StatefulWidget {
  const ButtonNoOutlineWidget({
    super.key,
    required this.title,
    required this.action,
  });

  final String? title;
  final Future Function()? action;

  @override
  State<ButtonNoOutlineWidget> createState() => _ButtonNoOutlineWidgetState();
}

class _ButtonNoOutlineWidgetState extends State<ButtonNoOutlineWidget> with SingleTickerProviderStateMixin{
  late ButtonNoOutlineModel _model;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => ButtonNoOutlineModel());
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.9).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) {
        setState(() => _isPressed = true);
        _animationController.forward();
      },
      onTapUp: (_) {
        setState(() => _isPressed = false);
        _animationController.reverse();
      },
      onTapCancel: () {
        setState(() => _isPressed = false);
        _animationController.reverse();
      },
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: child,
          );
        },
        child: FFButtonWidget(
      onPressed: () async {
        await widget.action?.call();
      },
      text: widget.title!,
      options: FFButtonOptions(
        width: double.infinity,
        height: 50.0,
        padding: const EdgeInsetsDirectional.fromSTEB(24.0, 0.0, 24.0, 0.0),
        iconPadding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
        color: Colors.white,
        textStyle: FlutterFlowTheme.of(context).titleSmall.override(
              fontFamily: 'BT Beau Sans',
              color: FlutterFlowTheme.of(context).primaryText,
              fontSize: 18.0,
              letterSpacing: 0.0,
              fontWeight: FontWeight.bold,
              useGoogleFonts: false,
            ),
        elevation: 0.0,
        borderSide: const BorderSide(
          color: Colors.transparent,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(100.0),
      ),
        ),
      ),
    );
  }
}
