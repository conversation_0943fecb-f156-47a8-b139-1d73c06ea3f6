import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'done_button_blue_model.dart';
export 'done_button_blue_model.dart';

class DoneButtonBlueWidget extends StatefulWidget {
  const DoneButtonBlueWidget({
    super.key,
    required this.action,
  });

  final Future Function()? action;

  @override
  State<DoneButtonBlueWidget> createState() => _DoneButtonBlueWidgetState();
}

class _DoneButtonBlueWidgetState extends State<DoneButtonBlueWidget> {
  late DoneButtonBlueModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => DoneButtonBlueModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return InkWell(
      splashColor: Colors.transparent,
      focusColor: Colors.transparent,
      hoverColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: () async {
        setState(() {
          _model.actionCompleted = false;
        });
        await widget.action?.call();
        setState(() {
          _model.actionCompleted = true;
        });
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            child: Text(
              'Done',
              style: FlutterFlowTheme.of(context).bodyMedium.override(
                    fontFamily: 'BT Beau Sans',
                    color: const Color(0xFF006EDA),
                    fontSize: 16.0,
                    fontWeight: FontWeight.bold,
                    useGoogleFonts: false,
                  ),
            ),
          ),
          if (_model.actionCompleted == false)
            Flexible(
              child: Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(5.0, 0.0, 5.0, 0.0),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8.0),
                  child: Image.asset(
                    'assets/images/loading.gif',
                    width: 16.0,
                    height: 16.0,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
