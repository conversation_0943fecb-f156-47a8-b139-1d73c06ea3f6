import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'gradient_button_flex_model.dart';
export 'gradient_button_flex_model.dart';

class GradientButtonFlexWidget extends StatefulWidget {
  const GradientButtonFlexWidget({
    super.key,
    required this.title,
    required this.action,
    double? height,
  }) : height = height ?? 50.0;

  final String? title;
  final Future Function()? action;
  final double height;

  @override
  State<GradientButtonFlexWidget> createState() =>
      _GradientButtonFlexWidgetState();
}

class _GradientButtonFlexWidgetState extends State<GradientButtonFlexWidget> with SingleTickerProviderStateMixin{
  late GradientButtonFlexModel _model;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => GradientButtonFlexModel());
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.9).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) {
        setState(() => _isPressed = true);
        _animationController.forward();
      },
      onTapUp: (_) {
        setState(() => _isPressed = false);
        _animationController.reverse();
      },
      onTapCancel: () {
        setState(() => _isPressed = false);
        _animationController.reverse();
      },
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: child,
          );
        },
        child: InkWell(
      splashColor: Colors.transparent,
      focusColor: Colors.transparent,
      hoverColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: () async {
        _model.actionCompleted = false;
        safeSetState(() {});
        await widget.action?.call();
        _model.actionCompleted = true;
        safeSetState(() {});
      },
      child: AnimatedContainer(
            duration: const Duration(milliseconds: 150),
        width: double.infinity,
        height: widget.height,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
                  _isPressed
                      ? const Color(0xFFE38BBD)
                      : const Color(0xFFF49BD1), // Darker when pressed
                  _isPressed ? const Color(0xFF5C9FCD) : const Color(0xFF67B0E5),
                ],
            stops: const [0.0, 1.0],
            begin: const AlignmentDirectional(-1.0, 0.0),
            end: const AlignmentDirectional(1.0, 0),
          ),
          borderRadius: BorderRadius.circular(100.0),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Align(
              alignment: const AlignmentDirectional(0.0, 0.0),
              child: Text(
                valueOrDefault<String>(
                  widget.title,
                  'Next',
                ),
                textAlign: TextAlign.center,
                style: FlutterFlowTheme.of(context).bodyMedium.override(
                      fontFamily: 'BT Beau Sans',
                      color: FlutterFlowTheme.of(context).info,
                      fontSize: 16.0,
                      letterSpacing: 0.0,
                      fontWeight: FontWeight.bold,
                      useGoogleFonts: false,
                    ),
              ),
            ),
            if (_model.actionCompleted == false)
              const Padding(
                    padding: EdgeInsetsDirectional.fromSTEB(10.0, 0.0, 0.0, 0.0),
                    child: SizedBox(
                      width: 30.0,
                      height: 30.0,
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        strokeWidth: 2.0,
                      ),
                    ),
                  ),
          ],
        ),
      ),
        ),
      ),
    );
  }
}
