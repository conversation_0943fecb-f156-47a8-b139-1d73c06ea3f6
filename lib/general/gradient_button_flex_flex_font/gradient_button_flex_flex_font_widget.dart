import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'gradient_button_flex_flex_font_model.dart';
export 'gradient_button_flex_flex_font_model.dart';

class GradientButtonFlexFlexFontWidget extends StatefulWidget {
  const GradientButtonFlexFlexFontWidget({
    super.key,
    required this.title,
    required this.action,
    double? height,
    required this.fontSize,
  }) : height = height ?? 50.0;

  final String? title;
  final Future Function()? action;
  final double height;
  final double? fontSize;

  @override
  State<GradientButtonFlexFlexFontWidget> createState() =>
      _GradientButtonFlexFlexFontWidgetState();
}

class _GradientButtonFlexFlexFontWidgetState
    extends State<GradientButtonFlexFlexFontWidget> {
  late GradientButtonFlexFlexFontModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => GradientButtonFlexFlexFontModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      splashColor: Colors.transparent,
      focusColor: Colors.transparent,
      hoverColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: () async {
        _model.actionCompleted = false;
        safeSetState(() {});
        await widget.action?.call();
        _model.actionCompleted = true;
        safeSetState(() {});
      },
      child: Container(
        width: double.infinity,
        height: widget.height,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [Color(0xFFF49BD1), Color(0xFF67B0E5)],
            stops: [0.0, 1.0],
            begin: AlignmentDirectional(-1.0, 0.0),
            end: AlignmentDirectional(1.0, 0),
          ),
          borderRadius: BorderRadius.circular(100.0),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Align(
              alignment: const AlignmentDirectional(0.0, 0.0),
              child: Text(
                valueOrDefault<String>(
                  widget.title,
                  'Next',
                ),
                textAlign: TextAlign.center,
                style: FlutterFlowTheme.of(context).bodyMedium.override(
                      fontFamily: 'BT Beau Sans',
                      color: Colors.white,
                      fontSize: widget.fontSize,
                      letterSpacing: 0.0,
                      fontWeight: FontWeight.bold,
                      useGoogleFonts: false,
                    ),
              ),
            ),
            if (_model.actionCompleted == false)
              const Padding(
                    padding: EdgeInsetsDirectional.fromSTEB(10.0, 0.0, 0.0, 0.0),
                    child: SizedBox(
                      width: 30.0,
                      height: 30.0,
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        strokeWidth: 2.0,
                      ),
                    ),
                  ),
          ],
        ),
      ),
    );
  }
}
