import '/flutter_flow/flutter_flow_util.dart';
import 'gradient_button_inverse_plus_widget.dart' show GradientButtonInversePlusWidget;
import 'package:flutter/material.dart';

class GradientButtonInversePlusModel
    extends FlutterFlowModel<GradientButtonInversePlusWidget> {
  ///  Local state fields for this component.

  bool actionCompleted = true;

  /// Initialization and disposal methods.

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {}

  /// Action blocks are added here.

  /// Additional helper methods are added here.
}
