import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';
import 'package:gradient_borders/gradient_borders.dart';

import 'gradient_button_inverse_plus_model.dart';
export 'gradient_button_inverse_plus_model.dart';

class GradientButtonInversePlusWidget extends StatefulWidget {
  const GradientButtonInversePlusWidget({
    super.key,
    required this.title,
    required this.action,
  });

  final String? title;
  final Future Function()? action;

  @override
  State<GradientButtonInversePlusWidget> createState() =>
      _GradientButtonInversePlusWidgetState();
}

class _GradientButtonInversePlusWidgetState
    extends State<GradientButtonInversePlusWidget> {
  late GradientButtonInversePlusModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => GradientButtonInversePlusModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      splashColor: Colors.transparent,
      focusColor: Colors.transparent,
      hoverColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: () async {
        _model.actionCompleted = false;
        safeSetState(() {});
        await widget.action?.call();
        _model.actionCompleted = true;
        safeSetState(() {});
      },
      child: Container(
        width: double.infinity,
        height: 50,
        decoration: BoxDecoration(
          color: FlutterFlowTheme.of(context).primaryBackground,
          borderRadius: BorderRadius.circular(100),
          border: const GradientBoxBorder(
            gradient: LinearGradient(colors: [Color(0xFF67B0E5), Color(0xFFF49BD1)]),
            width: 2,
          ),          
        ),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Align(
              alignment: const AlignmentDirectional(0, 0),
              child: GradientText(
                valueOrDefault<String>(
                  widget.title,
                  'Next',
                ),
                textAlign: TextAlign.center,
                style: FlutterFlowTheme.of(context).bodyMedium.override(
                      fontFamily: 'BT Beau Sans',
                      color: FlutterFlowTheme.of(context).info,
                      fontSize: 18,
                      letterSpacing: 0.0,
                      fontWeight: FontWeight.w600,
                      useGoogleFonts: false,
                    ),
                colors: const [Color(0xFF67B0E5), Color(0xFFF49BD1)],
                gradientDirection: GradientDirection.ltr,
                gradientType: GradientType.linear,
              ),
            ),
            if (_model.actionCompleted == false)
              Padding(
                    padding: const EdgeInsetsDirectional.fromSTEB(10.0, 0.0, 0.0, 0.0),
                    child: SizedBox(
                      width: 30.0,
                      height: 30.0,
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(FlutterFlowTheme.of(context).accent2),
                        strokeWidth: 2.0,
                      ),
                    ),
                  ),
          ],
        ),
      ),
    );
  }
}
