import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'gradient_button_outline_dark_disabled_model.dart';
export 'gradient_button_outline_dark_disabled_model.dart';

class GradientButtonOutlineDarkDisabledWidget extends StatefulWidget {
  const GradientButtonOutlineDarkDisabledWidget({
    super.key,
    required this.title,
    required this.action,
  });

  final String? title;
  final Future Function()? action;

  @override
  State<GradientButtonOutlineDarkDisabledWidget> createState() =>
      _GradientButtonOutlineDarkDisabledWidgetState();
}

class _GradientButtonOutlineDarkDisabledWidgetState
    extends State<GradientButtonOutlineDarkDisabledWidget> {
  late GradientButtonOutlineDarkDisabledModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model =
        createModel(context, () => GradientButtonOutlineDarkDisabledModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      splashColor: Colors.transparent,
      focusColor: Colors.transparent,
      hoverColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: () async {
        setState(() {
          _model.actionCompleted = false;
        });
        await widget.action?.call();
        setState(() {
          _model.actionCompleted = true;
        });
      },
      child: Container(
        width: double.infinity,
        height: 50.0,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(100.0),
          border: Border.all(
            color: FlutterFlowTheme.of(context).secondaryText,
            width: 1.0,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Align(
              alignment: const AlignmentDirectional(0.0, 0.0),
              child: Text(
                valueOrDefault<String>(
                  widget.title,
                  'Next',
                ),
                textAlign: TextAlign.center,
                style: FlutterFlowTheme.of(context).bodyMedium.override(
                      fontFamily: 'BT Beau Sans',
                      color: FlutterFlowTheme.of(context).secondaryText,
                      fontSize: 15.0,
                      letterSpacing: 0.0,
                      fontWeight: FontWeight.w600,
                      useGoogleFonts: false,
                    ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
