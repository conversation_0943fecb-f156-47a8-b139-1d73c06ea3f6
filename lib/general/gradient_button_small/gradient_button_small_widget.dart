import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'gradient_button_small_model.dart';
export 'gradient_button_small_model.dart';

class GradientButtonSmallWidget extends StatefulWidget {
  const GradientButtonSmallWidget({
    super.key,
    required this.title,
    required this.action,
  });

  final String? title;
  final Future Function()? action;

  @override
  State<GradientButtonSmallWidget> createState() =>
      _GradientButtonSmallWidgetState();
}

class _GradientButtonSmallWidgetState extends State<GradientButtonSmallWidget> {
  late GradientButtonSmallModel _model;
  bool _isPressed = false;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => GradientButtonSmallModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return GestureDetector(
      onTapDown: (_) {
        setState(() => _isPressed = true);
      },
      onTapUp: (_) {
        setState(() => _isPressed = false);
      },
      onTapCancel: () {
        setState(() => _isPressed = false);
      },
      child: InkWell(
      splashColor: Colors.transparent,
      focusColor: Colors.transparent,
      hoverColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: () async {
        setState(() {
          _model.actionCompleted = false;
        });
        await widget.action?.call();
        setState(() {
          _model.actionCompleted = true;
        });
      },
      child: AnimatedContainer(
        width: double.infinity,
        duration: const Duration(milliseconds: 150),
        height: 42.0,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [_isPressed
                      ? const Color(0xFFE38BBD)
                      : const Color(0xFFF49BD1), // Darker when pressed
                  _isPressed ? const Color(0xFF5C9FCD) : const Color(0xFF67B0E5),
                ],
            stops: const [0.0, 1.0],
            begin: const AlignmentDirectional(-1.0, 0.0),
            end: const AlignmentDirectional(1.0, 0),
          ),
          borderRadius: BorderRadius.circular(100.0),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Align(
              alignment: const AlignmentDirectional(0.0, 0.0),
              child: Text(
                valueOrDefault<String>(
                  widget.title,
                  'Next',
                ),
                textAlign: TextAlign.center,
                style: FlutterFlowTheme.of(context).bodyMedium.override(
                      fontFamily: 'BT Beau Sans',
                      color: FlutterFlowTheme.of(context).info,
                      fontSize: 18.0,
                      fontWeight: FontWeight.bold,
                      useGoogleFonts: false,
                    ),
              ),
            ),
            if (_model.actionCompleted == false)
              const Padding(
                    padding: EdgeInsetsDirectional.fromSTEB(10.0, 0.0, 0.0, 0.0),
                    child: SizedBox(
                      width: 30.0,
                      height: 30.0,
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        strokeWidth: 2.0,
                      ),
                    ),
                  ),
          ],
        ),
      ),
      ),
    );
  }
}
