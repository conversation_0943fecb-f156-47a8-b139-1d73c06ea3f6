import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'gradient_button_small_disabled_async_model.dart';
export 'gradient_button_small_disabled_async_model.dart';

class GradientButtonSmallDisabledAsyncWidget extends StatefulWidget {
  const GradientButtonSmallDisabledAsyncWidget({
    super.key,
    required this.title,
    required this.action,
  });

  final String? title;
  final Future Function()? action;

  @override
  State<GradientButtonSmallDisabledAsyncWidget> createState() =>
      _GradientButtonSmallDisabledAsyncWidgetState();
}

class _GradientButtonSmallDisabledAsyncWidgetState
    extends State<GradientButtonSmallDisabledAsyncWidget> {
  late GradientButtonSmallDisabledAsyncModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model =
        createModel(context, () => GradientButtonSmallDisabledAsyncModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      splashColor: Colors.transparent,
      focusColor: Colors.transparent,
      hoverColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: () async {
        _model.actionCompleted = false;
        setState(() {});
        await widget.action?.call();
        _model.actionCompleted = true;
        setState(() {});
      },
      child: Container(
        width: double.infinity,
        height: 42.0,
        decoration: BoxDecoration(
          color: const Color(0xFFD8DBDF),
          borderRadius: BorderRadius.circular(100.0),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Align(
              alignment: const AlignmentDirectional(0.0, 0.0),
              child: Text(
                valueOrDefault<String>(
                  widget.title,
                  'Next',
                ),
                textAlign: TextAlign.center,
                style: FlutterFlowTheme.of(context).bodyMedium.override(
                      fontFamily: 'BT Beau Sans',
                      color: const Color(0xFF747E90),
                      fontSize: 18.0,
                      letterSpacing: 0.0,
                      fontWeight: FontWeight.bold,
                      useGoogleFonts: false,
                    ),
              ),
            ),
            if (_model.actionCompleted == false)
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(10.0, 0.0, 0.0, 0.0),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8.0),
                  child: Image.asset(
                    'assets/images/loading.gif',
                    width: 30.0,
                    height: 30.0,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
