import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'list_multiselect_model.dart';
export 'list_multiselect_model.dart';

class ListMultiselectWidget extends StatefulWidget {
  const ListMultiselectWidget({
    super.key,
    required this.choices,
    required this.previouslyMadeChoices,
  });

  final List<String>? choices;
  final List<String>? previouslyMadeChoices;

  @override
  State<ListMultiselectWidget> createState() => _ListMultiselectWidgetState();
}

class _ListMultiselectWidgetState extends State<ListMultiselectWidget> {
  late ListMultiselectModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => ListMultiselectModel());

    // On component load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      setState(() {
        _model.choicesMade =
            widget.previouslyMadeChoices!.toList().cast<String>();
      });
    });
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return Builder(
      builder: (context) {
        final choices = widget.choices!.toList();
        return ListView.builder(
          padding: EdgeInsets.zero,
          shrinkWrap: true,
          scrollDirection: Axis.vertical,
          itemCount: choices.length,
          itemBuilder: (context, choicesIndex) {
            final choicesItem = choices[choicesIndex];
            return Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 27.0),
              child: InkWell(
                splashColor: Colors.transparent,
                focusColor: Colors.transparent,
                hoverColor: Colors.transparent,
                highlightColor: Colors.transparent,
                onTap: () async {
                  if (_model.choicesMade.contains(choicesItem)) {
                    _model.updatePage(() {
                      _model.removeFromChoicesMade(choicesItem);
                    });
                  } else {
                    _model.updatePage(() {
                      _model.addToChoicesMade(choicesItem);
                    });
                  }
                },
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    if (_model.choicesMade.contains(choicesItem))
                      Text(
                        choicesItem,
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'BT Beau Sans',
                              fontSize: 16.0,
                              fontWeight: FontWeight.bold,
                              useGoogleFonts: false,
                            ),
                      ),
                    if (!_model.choicesMade.contains(choicesItem))
                      Text(
                        choicesItem,
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'BT Beau Sans',
                              fontSize: 16.0,
                              useGoogleFonts: false,
                            ),
                      ),
                    if (_model.choicesMade.contains(choicesItem))
                      FaIcon(
                        FontAwesomeIcons.check,
                        color: FlutterFlowTheme.of(context).accent2,
                        size: 16.0,
                      ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}
