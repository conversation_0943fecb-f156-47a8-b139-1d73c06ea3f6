import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/gradient_button_inverse/gradient_button_inverse_widget.dart';
import 'picture_action_sheet_widget.dart' show PictureActionSheetWidget;
import 'package:flutter/material.dart';
import '/general/gradient_button_inverse_plus/gradient_button_inverse_plus_widget.dart';


class PictureActionSheetModel
    extends FlutterFlowModel<PictureActionSheetWidget> {
  ///  State fields for stateful widgets in this component.

  // Model for GradientButton component.
  late GradientButtonModel gradientButtonModel;
  bool isDataUploading = false;
  FFUploadedFile uploadedLocalFile =
      FFUploadedFile(bytes: Uint8List.fromList([]));
  String uploadedFileUrl = '';

  // Stores action output result for [Backend Call - Read Document] action in GradientButton widget.
  PublicProfileRecord? publicProfileAfterChanges;
  // Stores action output result for [Backend Call - Read Document] action in GradientButton widget.
  ImagesRecord? firstPic;
  // Model for GradientButtonInverse component.
  late GradientButtonInverseModel gradientButtonInverseModel;
  // Stores action output result for [Backend Call - Read Document] action in GradientButtonInverse widget.
  PublicProfileRecord? publicProfileToChange;
  // Stores action output result for [Backend Call - Read Document] action in GradientButtonInverse widget.
  PublicProfileRecord? publicProfileAfterDeletion;
  // Stores action output result for [Backend Call - Read Document] action in GradientButtonInverse widget.
  ImagesRecord? firstPicDelete;

  late GradientButtonInversePlusModel gradientButtonInversePlusModel;

  /// Initialization and disposal methods.

  @override
  void initState(BuildContext context) {
    gradientButtonModel = createModel(context, () => GradientButtonModel());
    gradientButtonInverseModel =
        createModel(context, () => GradientButtonInverseModel());
    gradientButtonInversePlusModel = createModel(context, () => GradientButtonInversePlusModel());
  }

  @override
  void dispose() {
    gradientButtonModel.dispose();
    gradientButtonInverseModel.dispose();
    gradientButtonInversePlusModel.dispose();
  }

  /// Action blocks are added here.

  /// Additional helper methods are added here.
}
