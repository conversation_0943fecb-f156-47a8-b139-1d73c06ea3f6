import '/auth/firebase_auth/auth_util.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '/backend/backend.dart';
import '/backend/firebase_storage/storage.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/flutter_flow/upload_data.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/gradient_button_inverse/gradient_button_inverse_widget.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:path_provider/path_provider.dart';
import 'picture_action_sheet_model.dart';
export 'picture_action_sheet_model.dart';
import 'package:dio/dio.dart';
import 'dart:io';
import '/general/gradient_button_inverse_plus/gradient_button_inverse_plus_widget.dart';
import 'package:flutter/scheduler.dart';



class PictureActionSheetWidget extends StatefulWidget {
  const PictureActionSheetWidget({
    super.key,
    required this.photoToChange,
    required this.publicProfile,
    required this.photoIndex,
  });

  final DocumentReference? photoToChange;
  final DocumentReference? publicProfile;
  final int photoIndex;

  @override
  State<PictureActionSheetWidget> createState() =>
      _PictureActionSheetWidgetState();
}

class _PictureActionSheetWidgetState extends State<PictureActionSheetWidget> {
  late PictureActionSheetModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => PictureActionSheetModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<ImagesRecord>(
      stream: ImagesRecord.getDocument(widget.photoToChange!),
      builder: (context, snapshot) {
        // Customize what your widget looks like when it's loading.
        if (!snapshot.hasData) {
          return Center(
            child: SizedBox(
              width: 40,
              height: 40,
              child: SpinKitCircle(
                color: FlutterFlowTheme.of(context).accent4,
                size: 40,
              ),
            ),
          );
        }

        final bottomSheetEditImagesRecord = snapshot.data!;

        return Material(
          color: Colors.transparent,
          elevation: 1,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(0),
              bottomRight: Radius.circular(0),
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
            ),
          ),
          child: Container(
            width: MediaQuery.of(context).size.width,
            height: 700,
            decoration: BoxDecoration(
              color: FlutterFlowTheme.of(context).primaryBackground,
              boxShadow: const [
                BoxShadow(
                  blurRadius: 5,
                  color: Color(0x3B1D2429),
                  offset: Offset(
                    0.0,
                    -3,
                  ),
                )
              ],
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(0),
                bottomRight: Radius.circular(0),
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: SafeArea(
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    wrapWithModel(
                      model: _model.gradientButtonModel,
                      updateCallback: () => safeSetState(() {}),
                      child: GradientButtonWidget(
                        title: 'Update',
                        action: () async {
                          var shouldSetState = false;

                          var oldImageId = widget.photoToChange?.id;
                          // Upload new photo to storage
                          final selectedMedia =
                              await selectMediaWithSourceBottomSheet(
                            context: context,
                            maxWidth: 800.00,
                            imageQuality: 70,
                            allowPhoto: true,
                            pickerFontFamily: 'Inter Tight',
                          );
                          if (selectedMedia != null &&
                              selectedMedia.every((m) =>
                                  validateFileFormat(m.storagePath, context))) {
                            safeSetState(() => _model.isDataUploading = true);
                            var selectedUploadedFiles = <FFUploadedFile>[];
                
                            var downloadUrls = <String>[];
                            try {
                              selectedUploadedFiles = selectedMedia
                                  .map((m) => FFUploadedFile(
                                        name: m.storagePath.split('/').last,
                                        bytes: m.bytes,
                                        height: m.dimensions?.height,
                                        width: m.dimensions?.width,
                                        blurHash: m.blurHash,
                                      ))
                                  .toList();
                
                              downloadUrls = (await Future.wait(
                                selectedMedia.map(
                                  (m) async =>
                                      await uploadData(m.storagePath, m.bytes),
                                ),
                              ))
                                  .where((u) => u != null)
                                  .map((u) => u!)
                                  .toList();
                            } finally {
                              _model.isDataUploading = false;
                            }
                            if (selectedUploadedFiles.length ==
                                    selectedMedia.length &&
                                downloadUrls.length == selectedMedia.length) {
                              safeSetState(() {
                                _model.uploadedLocalFile =
                                    selectedUploadedFiles.first;
                                _model.uploadedFileUrl = downloadUrls.first;
                              });
                            } else {
                              safeSetState(() {});
                              return;
                            }
                          }
                
                          if (_model.uploadedFileUrl != '') {
                            await FirebaseStorage.instance
                                .refFromURL(bottomSheetEditImagesRecord.url)
                                .delete();
                            // Create new images document for upload

                            
                
                            var imagesRecordReference = ImagesRecord.createDoc(currentUserReference!);
                          await imagesRecordReference.set(createImagesRecordData(
                            url: _model.uploadedFileUrl, // Use the URL from downloadUrls
                            availableToAll: true,
                            v2: true
                          ));

                            _model.publicProfileAfterChanges =
                                await PublicProfileRecord.getDocumentOnce(
                                    widget.publicProfile!);
                            shouldSetState = true;
                            _model.firstPic = await ImagesRecord.getDocumentOnce(
                                _model
                                    .publicProfileAfterChanges!.nPictures.first);
                            shouldSetState = true;
                            var newNPictures = _model.publicProfileAfterChanges!.nPictures;
                            newNPictures[widget.photoIndex] = imagesRecordReference;
                
                            await currentUserDocument!.publicProfile!
                                .update({...mapToFirestore(
                                    {
                                      'nPictures': newNPictures,
                                      'profilePhoto': _model.firstPic!.url
                                    },
                                  ),});
                          } else {
                            if (shouldSetState) safeSetState(() {});
                            return;
                          }

                          FFAppState().imageToDelete = oldImageId;
                
                          if (!mounted) return;
                            Navigator.of(context).pop();

                        },
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(0, 15, 0, 0),
                      child: wrapWithModel(
                        model: _model.gradientButtonInversePlusModel,
                        updateCallback: () => safeSetState(() {}),
                        child: GradientButtonInversePlusWidget(
                          title: 'Edit Picture',
                          action: () async {
                            var oldImageId = widget.photoToChange?.id;
                            try {
                              String documentPath = widget.photoToChange!.path; 
                              DocumentReference documentRef = FirebaseFirestore.instance.doc(documentPath);
                              DocumentSnapshot documentSnapshot = await documentRef.get();
                              String imageUrl = documentSnapshot.get('url');
                              
                              // Get temporary directory
                              Directory tempDir = await getTemporaryDirectory();
                              String tempPath = tempDir.path;
                              String localPath = '$tempPath/tempImage.jpg';
                              
                              // Download image
                              Response response = await Dio().download(imageUrl, localPath);
                              
                              if (response.statusCode == 200) {
                                CroppedFile? croppedFile = await ImageCropper().cropImage(
                                  sourcePath: localPath,
                                  uiSettings: [
                                    AndroidUiSettings(
                                      toolbarTitle: 'Edit Picture',
                                      toolbarColor: const Color(0xFFEDEBEB),
                                      toolbarWidgetColor: const Color.fromARGB(255, 78, 73, 73),
                                      initAspectRatio: CropAspectRatioPreset.original,
                                      lockAspectRatio: false,
                                    ),
                                    IOSUiSettings(
                                      title: 'Edit Picture',
                                    ),
                                  ],
                                );
                      
                                if (croppedFile != null) {
                                  setState(() => _model.isDataUploading = true);
                                  String? croppedFileDownloadUrl;
                                  var croppedFileBytes = await croppedFile.readAsBytes();
                                  var imageDimensions = await decodeImageFromList(croppedFileBytes);
                      
                                  FFUploadedFile croppedUploadedFile = FFUploadedFile(
                                    name: croppedFile.path.split('/').last,
                                    bytes: croppedFileBytes,
                                    height: imageDimensions.height.toDouble(),
                                    width: imageDimensions.width.toDouble(),
                                  );
                                
                      
                                  try {
                                    // Upload the cropped image to Firebase Storage
                                    final user = FirebaseAuth.instance.currentUser;
                                    if (user == null) throw Exception('User not authenticated');
                      
                                    final storageRef = FirebaseStorage.instance.ref().child(
                                      'users/${user.uid}/uploads/${DateTime.now().millisecondsSinceEpoch}.jpg'
                                    );
                                    
                                    await storageRef.putData(croppedFileBytes);
                                    croppedFileDownloadUrl = await storageRef.getDownloadURL();
                      
                                  } catch (uploadError) {
                                    print('Upload error: $uploadError');
                                    // Handle upload error (show message to user, etc.)
                                  } finally {
                                    setState(() => _model.isDataUploading = false);
                                  }
                      
                                  if (croppedFileDownloadUrl != null) {
                                    setState(() {
                                      _model.uploadedLocalFile = croppedUploadedFile;
                                      _model.uploadedFileUrl = croppedFileDownloadUrl!;
                                    });
                      
                                    // Delete the old image from Firebase Storage
                                    try {
                                      await FirebaseStorage.instance.refFromURL(imageUrl).delete();
                                    } catch (deleteError) {
                                      print('Error deleting old image: $deleteError');
                                      // Handle delete error (you might want to proceed anyway)
                                    }
                                  
                      
                                    // Update the document with the new image URL
                                    var imagesRecordReference = ImagesRecord.createDoc(currentUserReference!);
                          await imagesRecordReference.set(createImagesRecordData(
                            url: _model.uploadedFileUrl, // Use the URL from downloadUrls
                            availableToAll: true,
                            v2: true
                          ));

                            _model.publicProfileAfterChanges =
                                await PublicProfileRecord.getDocumentOnce(
                                    widget.publicProfile!);
                            _model.firstPic = await ImagesRecord.getDocumentOnce(
                                _model
                                    .publicProfileAfterChanges!.nPictures.first);
                            var newNPictures = _model.publicProfileAfterChanges!.nPictures;
                            newNPictures[widget.photoIndex] = imagesRecordReference;
                
                            await currentUserDocument!.publicProfile!
                                .update({...mapToFirestore(
                                    {
                                      'nPictures': newNPictures,
                                      'profilePhoto': _model.firstPic!.url
                                    },
                                  ),});
                          } else {
                            return;
                          }

                          FFAppState().imageToDelete = oldImageId;
                
                          if (!mounted) return;
                            Navigator.of(context).pop();
                                }
                              }
                            } catch(e) {
                              return;
                            }
                            },
                        ),
                      ),
                    ),
                  
                    Builder(
                      builder: (context) => Padding(
                        padding: const EdgeInsetsDirectional.fromSTEB(0, 15, 0, 0),
                        child: wrapWithModel(
                          model: _model.gradientButtonInverseModel,
                          updateCallback: () => safeSetState(() {}),
                          updateOnChange: true,
                          child: GradientButtonInverseWidget(
                            title: 'Delete',
                            action: () async {
                              var shouldSetState = false;
                              var oldImageId = widget.photoToChange?.id;
                              _model.publicProfileToChange =
                                  await PublicProfileRecord.getDocumentOnce(
                                      widget.publicProfile!);
                              shouldSetState = true;
                              if (_model.publicProfileToChange!.nPictures.length >
                                  3) {
                                await widget.publicProfile!.update({
                                  ...mapToFirestore(
                                    {
                                      'nPictures': FieldValue.arrayRemove(
                                          [widget.photoToChange]),
                                    },
                                  ),
                                });
                                // Delete pic from Firebase
                                await FirebaseStorage.instance
                                    .refFromURL(bottomSheetEditImagesRecord.url)
                                    .delete();

                              
                              } else {
                                await showDialog(
                                  context: context,
                                  builder: (dialogContext) {
                                    return Dialog(
                                      elevation: 0,
                                      insetPadding: EdgeInsets.zero,
                                      backgroundColor: Colors.transparent,
                                      alignment: const AlignmentDirectional(0, 0)
                                          .resolve(Directionality.of(context)),
                                      child: const GeneralPopupWidget(
                                        alertTitle: 'You need at least 3 images',
                                        alertText:
                                            'You cannot delete this image without uploading another one. You can change it, however.',
                                      ),
                                    );
                                  },
                                );
                
                                if (shouldSetState) safeSetState(() {});
                                return;
                              }                            
                              _model.publicProfileAfterDeletion =
                                  await PublicProfileRecord.getDocumentOnce(
                                      widget.publicProfile!);
                              shouldSetState = true;
                              _model.firstPicDelete =
                                  await ImagesRecord.getDocumentOnce(_model
                                      .publicProfileAfterDeletion!
                                      .nPictures
                                      .first);
                              shouldSetState = true;
                
                              await currentUserDocument!.publicProfile!
                                  .update(createPublicProfileRecordData(
                                profilePhoto: _model.firstPicDelete?.url,
                              ));

                              FFAppState().imageToDelete = oldImageId;
                              
                              if (!context.mounted) return;
                              Navigator.of(context).pop();

                            },
                          ),
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(0, 16, 0, 0),
                      child: FFButtonWidget(
                        onPressed: () async {
                          Navigator.pop(context);
                        },
                        text: 'Cancel',
                        options: FFButtonOptions(
                          width: double.infinity,
                          height: 60,
                          padding: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 0),
                          iconPadding: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 0),
                          color: FlutterFlowTheme.of(context).primaryBackground,
                          textStyle: FlutterFlowTheme.of(context)
                              .titleSmall
                              .override(
                                fontFamily: 'Lexend Deca',
                                color: FlutterFlowTheme.of(context).secondaryText,
                                fontSize: 16,
                                letterSpacing: 0.0,
                                fontWeight: FontWeight.normal,
                              ),
                          elevation: 0,
                          borderSide: const BorderSide(
                            color: Colors.transparent,
                            width: 0,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
