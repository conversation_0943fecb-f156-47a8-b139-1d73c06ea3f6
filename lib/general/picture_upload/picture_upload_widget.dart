import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
import '/backend/firebase_storage/storage.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/upload_data.dart';
import '/general/picture_action_sheet/picture_action_sheet_widget.dart';
import '/profile_settings/settings_selector/picture_permission_sheet1/picture_permission_sheet1_widget.dart';
import '/flutter_flow/revenue_cat_util.dart' as revenue_cat;
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'picture_upload_model.dart';
export 'picture_upload_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';

class PictureUploadWidget extends StatefulWidget {
  const PictureUploadWidget({
    super.key,
    required this.signUp,
    required this.publicProfile,
    required this.picturePosition,
  });

  final bool? signUp;
  final PublicProfileRecord? publicProfile;
  final int? picturePosition;

  @override
  State<PictureUploadWidget> createState() => _PictureUploadWidgetState();
}

class _PictureUploadWidgetState extends State<PictureUploadWidget> {
  late PictureUploadModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => PictureUploadModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 5.0, 0.0),
      child: SizedBox(
        width: min(MediaQuery.sizeOf(context).width * 0.3, 130),
        child: Stack(
          alignment: const AlignmentDirectional(-1.0, 0.0),
          children: [
            StreamBuilder<ImagesRecord>(
              stream: ImagesRecord.getDocument(
                  widget.publicProfile!.nPictures[(widget.picturePosition!) - 1]),
              builder: (context, snapshot) {
                // Customize what your widget looks like when it's loading.
                if (!snapshot.hasData) {
                  return Center(
                    child: SizedBox(
                      width: 50.0,
                      height: 50.0,
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                          FlutterFlowTheme.of(context).accent2,
                        ),
                      ),
                    ),
                  );
                }
                final containerImagesRecord = snapshot.data!;
                return Container(
                  width: min(MediaQuery.sizeOf(context).width * 0.3, 130),
                  height: min(MediaQuery.sizeOf(context).width * 0.3, 130),
                  decoration: const BoxDecoration(),
                  child: Stack(
                    children: [
                      InkWell(
                        splashColor: Colors.transparent,
                        focusColor: Colors.transparent,
                        hoverColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        onTap: () async {
                          await showModalBottomSheet(
                            isScrollControlled: true,
                            backgroundColor: Colors.transparent,
                            enableDrag: false,
                            useSafeArea: true,
                            context: context,
                            builder: (context) {
                              return Padding(
                                padding: MediaQuery.viewInsetsOf(context),
                                child: SizedBox(
                                  height:
                                      MediaQuery.sizeOf(context).height * 0.5,
                                  child: PictureActionSheetWidget(
                                    photoToChange:
                                        containerImagesRecord.reference,
                                    publicProfile:
                                        currentUserDocument!.publicProfile!,
                                    photoIndex: widget.picturePosition! - 1,
                                  ),
                                ),
                              );
                            },
                          ).then((value) => safeSetState(() {}));
                            if (FFAppState().imageToDelete != null) {
                            await currentUserReference?.collection('images').doc(FFAppState().imageToDelete).delete();
                            FFAppState().imageToDelete = null;
                            }
                          },
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(11.0),
                          child: Container(
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(11.0),
                                border: Border.all(
                                   color: shouldShowImage(currentUserDocument: currentUserDocument, containerImagesRecord: containerImagesRecord) ? (getSchemaColor(getRemoteConfigString('mpe_edit_profile_highlight_color_fg')) ?? FlutterFlowTheme.of(context).accent1) : const Color(0xFFBFC1C5),
                                   width: shouldShowImage(currentUserDocument: currentUserDocument, containerImagesRecord: containerImagesRecord) ? 2.0 : 1.0,
                                ),
                              ),
                            child: Container(
                              width: MediaQuery.sizeOf(context).width * 0.925,
                              height: MediaQuery.sizeOf(context).height * 0.95,
                              decoration: BoxDecoration(
                                color: const Color(0xFFD8DBDF),
                                image: DecorationImage(
                                  fit: BoxFit.cover,
                                  image: Image.network(
                                    containerImagesRecord.url,
                                  ).image,
                                ),
                                borderRadius: BorderRadius.circular(9.0),
                                border: Border.all(
                                   color: shouldShowImage(currentUserDocument: currentUserDocument, containerImagesRecord: containerImagesRecord) ? Colors.white : const Color(0xFFBFC1C5),
                                   width: shouldShowImage(currentUserDocument: currentUserDocument, containerImagesRecord: containerImagesRecord) ? 2.0 : 0.0,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      Align(
                        alignment: const AlignmentDirectional(1.0, -1.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            if (widget.publicProfile!.nPictures.length <
                                widget.picturePosition!)
                              Align(
                                alignment: const AlignmentDirectional(1.1, 0.0),
                                child: InkWell(
                                  splashColor: Colors.transparent,
                                  focusColor: Colors.transparent,
                                  hoverColor: Colors.transparent,
                                  highlightColor: Colors.transparent,
                                  onTap: () async {
                                    var shouldSetState = false;
                                    final firestoreBatch =
                                        FirebaseFirestore.instance.batch();
                                    try {
                                      // Upload new image to storage
                                      final selectedMedia =
                                          await selectMediaWithSourceBottomSheet(
                                        context: context,
                                        maxWidth: 800.00,
                                        imageQuality: 70,
                                        allowPhoto: true,
                                        pickerFontFamily: 'Inter',
                                      );
                                      if (selectedMedia != null &&
                                          selectedMedia.every((m) =>
                                              validateFileFormat(
                                                  m.storagePath, context))) {
                                        setState(
                                            () => _model.isDataUploading = true);
                                        var selectedUploadedFiles =
                                            <FFUploadedFile>[];
      
                                        var downloadUrls = <String>[];
                                        try {
                                          showUploadMessage(
                                            context,
                                            'Uploading file...',
                                            showLoading: true,
                                          );
                                          selectedUploadedFiles = selectedMedia
                                              .map((m) => FFUploadedFile(
                                                    name: m.storagePath
                                                        .split('/')
                                                        .last,
                                                    bytes: m.bytes,
                                                    height: m.dimensions?.height,
                                                    width: m.dimensions?.width,
                                                    blurHash: m.blurHash,
                                                  ))
                                              .toList();
      
                                          downloadUrls = (await Future.wait(
                                            selectedMedia.map(
                                              (m) async => await uploadData(
                                                  m.storagePath, m.bytes),
                                            ),
                                          ))
                                              .where((u) => u != null)
                                              .map((u) => u!)
                                              .toList();
                                        } finally {
                                          ScaffoldMessenger.of(context)
                                              .hideCurrentSnackBar();
                                          _model.isDataUploading = false;
                                        }
                                        if (selectedUploadedFiles.length ==
                                                selectedMedia.length &&
                                            downloadUrls.length ==
                                                selectedMedia.length) {
                                          setState(() {
                                            _model.uploadedLocalFile =
                                                selectedUploadedFiles.first;
                                            _model.uploadedFileUrl =
                                                downloadUrls.first;
                                          });
                                          showUploadMessage(context, 'Success!');
                                        } else {
                                          setState(() {});
                                          showUploadMessage(
                                              context, 'Failed to upload data');
                                          return;
                                        }
                                      }
      
                                      if (_model.uploadedFileUrl != '') {
                                        // Create image document
      
                                        var imagesRecordReference1 =
                                            ImagesRecord.createDoc(
                                                currentUserReference!);
                                        firestoreBatch.set(
                                            imagesRecordReference1,
                                            createImagesRecordData(
                                              url: _model.uploadedFileUrl,
                                              availableToAll: true,
                                              v2: true
                                            ));
                                        _model.photoDoc =
                                            ImagesRecord.getDocumentFromData(
                                                createImagesRecordData(
                                                  url: _model.uploadedFileUrl,
                                                  availableToAll: true,
                                                  v2: true
                                                ),
                                                imagesRecordReference1);
                                        shouldSetState = true;
      
                                        firestoreBatch.update(
                                            currentUserDocument!.publicProfile!, {
                                          ...mapToFirestore(
                                            {
                                              'nPictures': FieldValue.arrayUnion(
                                                  [_model.photoDoc?.reference]),
                                            },
                                          ),
                                        });
                                        _model.firstPic =
                                            await ImagesRecord.getDocumentOnce(
                                                widget.publicProfile!.nPictures
                                                    .first);
                                        shouldSetState = true;
      
                                        firestoreBatch.update(
                                            currentUserDocument!.publicProfile!,
                                            createPublicProfileRecordData(
                                              profilePhoto: _model.firstPic?.url,
                                            ));
                                      } else {
                                        if (shouldSetState) setState(() {});
                                        return;
                                      }
                                    } finally {
                                      await firestoreBatch.commit();
                                    }
      
                                    if (shouldSetState) setState(() {});
                                  },
                                  child: Container(
                                    width: 34.0,
                                    height: 34.0,
                                    decoration: BoxDecoration(
                                      gradient: const LinearGradient(
                                        colors: [
                                          Color(0xFF67B0E5),
                                          Color(0xFFF49BD1)
                                        ],
                                        stops: [0.0, 1.0],
                                        begin: AlignmentDirectional(0.0, -1.0),
                                        end: AlignmentDirectional(0, 1.0),
                                      ),
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: FlutterFlowTheme.of(context).info,
                                        width: 1.0,
                                      ),
                                    ),
                                    child: Icon(
                                      Icons.add,
                                      color: FlutterFlowTheme.of(context).info,
                                      size: 24.0,
                                    ),
                                  ),
                                ),
                              ),
                            if (widget.publicProfile!.nPictures.length >=
                                widget.picturePosition!)
                              Align(
                                alignment: const AlignmentDirectional(1.1, 0.0),
                                child: InkWell(
                                  splashColor: Colors.transparent,
                                  focusColor: Colors.transparent,
                                  hoverColor: Colors.transparent,
                                  highlightColor: Colors.transparent,
                                  onTap: () async {
                                    await showModalBottomSheet(
                                      isScrollControlled: true,
                                      backgroundColor: Colors.transparent,
                                      enableDrag: false,
                                      useSafeArea: true,
                                      context: context,
                                      builder: (context) {
                                        return Padding(
                                          padding:
                                              MediaQuery.viewInsetsOf(context),
                                          child: SizedBox(
                                            height: MediaQuery.sizeOf(context)
                                                    .height *
                                                0.5,
                                            child: PictureActionSheetWidget(
                                              photoToChange:
                                                  containerImagesRecord
                                                      .reference,
                                              publicProfile:
                                                  currentUserDocument!
                                                      .publicProfile!,
                                              photoIndex: widget.picturePosition! - 1,
                                            ),
                                          ),
                                        );
                                      },
                                    ).then((value) => safeSetState(() {}));

                            if (FFAppState().imageToDelete != null) {
                            await currentUserReference?.collection('images').doc(FFAppState().imageToDelete).delete();
                            FFAppState().imageToDelete = null;
                            }
                                                                    },
                                  child: Container(
                                    width: 34.0,
                                    height: 34.0,
                                    decoration: BoxDecoration(
                                      color: FlutterFlowTheme.of(context)
                                          .primaryBackground,
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: shouldShowImage(currentUserDocument: currentUserDocument, containerImagesRecord: containerImagesRecord) ? (getSchemaColor(getRemoteConfigString('mpe_edit_profile_highlight_color_fg')) ?? FlutterFlowTheme.of(context).accent1) : const Color(0xFF747E90),
                                  width: shouldShowImage(currentUserDocument: currentUserDocument, containerImagesRecord: containerImagesRecord) ? 2.0 : 1.0,
                                      ),
                                    ),
                                    child: const Align(
                                      alignment: AlignmentDirectional(0.0, 0.0),
                                      child: FaIcon(
                                        FontAwesomeIcons.pencilAlt,
                                        color: Color(0xFF747E90),
                                        size: 16.0,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            if (!widget.signUp! && (widget.picturePosition! > 3))
                              Align(
                                alignment: const AlignmentDirectional(1.1, 0.0),
                                child: InkWell(
                                  splashColor: Colors.transparent,
                                  focusColor: Colors.transparent,
                                  hoverColor: Colors.transparent,
                                  highlightColor: Colors.transparent,
                                  onTap: () async {
                                    if (revenue_cat.activeEntitlementIds
                                            .contains('evolved_access') ||
                                        revenue_cat.activeEntitlementIds
                                            .contains('plus_access') ||
                                        revenue_cat.activeEntitlementIds
                                            .contains('paid_standard_1w') ||
                                        revenue_cat.activeEntitlementIds
                                            .contains('paid_standard_lifetime') ||
                                        (revenue_cat
                                                            .activeEntitlementIds
                                                            .contains(
                                                                'evolved_access') || (currentUserDocument
                                                            ?.gender ==
                                                        Gender.Female) &&
                                                    revenue_cat
                                                        .activeEntitlementIds
                                                        .contains(
                                                            'divine_access'))) {
                                      await showModalBottomSheet(
                                        isScrollControlled: true,
                                        backgroundColor: Colors.transparent,
                                        enableDrag: false,
                                        useSafeArea: true,
                                        context: context,
                                        builder: (context) {
                                          return Padding(
                                            padding:
                                                MediaQuery.viewInsetsOf(context),
                                            child: PicturePermissionSheet1Widget(
                                              pictureDoc: containerImagesRecord,
                                            ),
                                          );
                                        },
                                      ).then((value) => safeSetState(() {}));
                                    } else if (currentUserDocument?.gender == Gender.Female && 
                                                                                      valueOrDefault(currentUserDocument?.fiveTestGroup, 0) > valueOrDefault(getRemoteConfigInt('divine_legacy_t'), 0)) {
                                                                                      analytics.logEvent('Navigated to Divine from Picture Upload Hiding');
                                                                                      context.pushNamed(
                                                                                        'DivineSubscription',
                                                                                        extra: <String, dynamic>{
                                                                                          kTransitionInfoKey: const TransitionInfo(
                                                                                            hasTransition: true,
                                                                                            transitionType: PageTransitionType.bottomToTop,
                                                                                            duration: Duration(milliseconds: 200),
                                                                                          ),
                                                                                        },
                                                                                      );
                                                                                    } else {
                                      try {
                         analytics.logEvent('Navigated to Plus Evolved from Picture Upload Hiding');
                         } catch(e) {}
                                      context.pushNamed(
                                        'PlusEvolvedSubscriptionNew',
                                        queryParameters: {
                                          'initialSubscription': serializeParam(
                                            'Plus',
                                            ParamType.String,
                                          ),
                                        }.withoutNulls,
                                      );
                                    }
                                  },
                                  child: Container(
                                    width: 34.0,
                                    height: 34.0,
                                    decoration: BoxDecoration(
                                      color: containerImagesRecord
                                              .hasAvailableToAll()
                                          ? FlutterFlowTheme.of(context)
                                              .primaryBackground
                                          : const Color(0xFF747E90),
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: containerImagesRecord
                                                .hasAvailableToAll()
                                            ? const Color(0xFF747E90)
                                            : FlutterFlowTheme.of(context)
                                                .primaryBackground,
                                        width: 1.0,
                                      ),
                                    ),
                                    child: Align(
                                      alignment: const AlignmentDirectional(0.0, 0.0),
                                      child: Icon(
                                        FFIcons.keyeSimple2,
                                        color: containerImagesRecord
                                                .hasAvailableToAll()
                                            ? const Color(0xFF747E90)
                                            : FlutterFlowTheme.of(context)
                                                .primaryBackground,
                                        size: 26.0,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                          ].divide(const SizedBox(height: 5.0)),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

bool shouldShowImage({
  required dynamic currentUserDocument,
  required dynamic containerImagesRecord,
}) {
  return (((currentUserDocument?.mpeImprovements.images.contains(containerImagesRecord.reference.id) ?? false) &&
  (currentUserDocument?.mpeBlock ?? false)) ||
 ((currentUserDocument?.imageVeriImprovementImages.contains(containerImagesRecord.reference.id) ?? false) &&
  (currentUserDocument?.imageVeriFailed ?? false)) &&
 !(currentUserDocument?.verified ?? false));

}

