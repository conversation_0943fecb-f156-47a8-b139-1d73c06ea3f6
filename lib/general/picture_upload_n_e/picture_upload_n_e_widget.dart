import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/firebase_storage/storage.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/upload_data.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'picture_upload_n_e_model.dart';
export 'picture_upload_n_e_model.dart';
import 'package:chyrpe/components/general_popup_widget.dart';

class PictureUploadNEWidget extends StatefulWidget {
  const PictureUploadNEWidget({
    super.key,
    required this.signUp,
    required this.publicProfile,
    required this.picturePosition,
  });

  final bool? signUp;
  final PublicProfileRecord? publicProfile;
  final int? picturePosition;

  @override
  State<PictureUploadNEWidget> createState() => _PictureUploadNEWidgetState();
}

class _PictureUploadNEWidgetState extends State<PictureUploadNEWidget> {
  late PictureUploadNEModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => PictureUploadNEModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return Stack(
      children: [
        Padding(
          padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 5.0, 0.0),
          child: Container(
            width: min(MediaQuery.sizeOf(context).width * 0.3, 130),
            height: min(MediaQuery.sizeOf(context).width * 0.3, 130),
            decoration: const BoxDecoration(),
            child: Stack(
              children: [
                InkWell(
                  splashColor: Colors.transparent,
                  focusColor: Colors.transparent,
                  hoverColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  onTap: () async {
                    
                    var shouldSetState = false;
                    final selectedMedia =
                        await selectMediaWithSourceBottomSheet(
                      context: context,
                      maxWidth: 800.00,
                      imageQuality: 70,
                      allowPhoto: true,
                      backgroundColor:
                          FlutterFlowTheme.of(context).primaryBackground,
                      textColor: FlutterFlowTheme.of(context).primaryText,
                      pickerFontFamily: 'Inter',
                      multiImage: true,
                    );
                    
                    if (selectedMedia != null &&
                        selectedMedia.every((m) =>
                            validateFileFormat(m.storagePath, context))) {
                    try {
                    showUploadMessage(
                                    context,
                                    'Picking & uploading photos...',
                                    showLoading: true,
                                  );
                    } catch(error) {}
                      setState(() => _model.isDataUploading1 = true);
                      var selectedUploadedFiles = <FFUploadedFile>[];

                      var downloadUrls = <String>[];
                      try {
                        
                        selectedUploadedFiles = selectedMedia
                            .map((m) => FFUploadedFile(
                                  name: m.storagePath.split('/').last,
                                  bytes: m.bytes,
                                  height: m.dimensions?.height,
                                  width: m.dimensions?.width,
                                  blurHash: m.blurHash,
                                ))
                            .toList();

                        var queryPuPro = await currentUserDocument!.publicProfile!.get();
                        var npictures;
                        try{
                          npictures = queryPuPro['nPictures'];
                        }catch (e) {
                          npictures = null; // Assign null if the field doesn't exist or there's an error
                        }
                        
                        if (npictures != null){
                          if(selectedUploadedFiles.length + npictures.length > 6) {
                            // Show a dialogue to indicate too many files are being uploaded
                            showDialog(
                              context: context, // Make sure to pass the BuildContext here
                              builder: (BuildContext context){
                                return const GeneralPopupWidget(
                                  alertTitle: "Too many pictures",
                                  alertText: "You cannot upload more than six pictures to your profile.",
                                );
                              }
                            );
                            try {
                              ScaffoldMessenger.of(context).hideCurrentSnackBar();
                              } catch (error) {}
                            return;
                        }
                      }else{
                        if(selectedUploadedFiles.length > 6) {
                            // Show a dialogue to indicate too many files are being uploaded
                            showDialog(
                              context: context, // Make sure to pass the BuildContext here
                              builder: (BuildContext context){
                                return const GeneralPopupWidget(
                                  alertTitle: "Too many pictures",
                                  alertText: "You cannot upload more than six pictures to your profile.",
                                );
                              }
                            );
                            try {
                              ScaffoldMessenger.of(context).hideCurrentSnackBar();
                              } catch (error) {}
                            return;
                        }
                      }

                        downloadUrls = (await Future.wait(
                          selectedMedia.map(
                            (m) async =>
                                await uploadData(m.storagePath, m.bytes),
                          ),
                        ))
                            .where((u) => u != null)
                            .map((u) => u!)
                            .toList();

                        //print(downloadUrls.length.toString() + " " + selectedUploadedFiles.length.toString());
                      } finally {
                        ScaffoldMessenger.of(context).hideCurrentSnackBar();
                        _model.isDataUploading1 = false;
                      }
                      if (selectedUploadedFiles.length ==
                              selectedMedia.length &&
                          downloadUrls.length == selectedMedia.length) {
                        setState(() {
                          _model.uploadedLocalFile1 =
                              selectedUploadedFiles.first;
                          _model.uploadedFileUrl1 = downloadUrls.first;
                          _model.selectedUploadedFiles = selectedUploadedFiles;
                          _model.downloadUrls = downloadUrls;
                        });
                        showUploadMessage(context, 'Success!');
                      } else {
                        setState(() {});
                        showUploadMessage(context, 'Failed to upload data');
                        return;
                      }
                    }else if(selectedMedia == null){
                      return;
                    }

                    if (_model.selectedUploadedFiles != null &&
                        _model.selectedUploadedFiles.isNotEmpty &&
                        _model.downloadUrls != null &&
                        _model.downloadUrls.length == _model.selectedUploadedFiles.length) {
                      
                      // List to store references to the created image documents
                      List<DocumentReference> imageDocReferences = [];

                      for (int i = 0; i < _model.selectedUploadedFiles.length; i++) {
                        String uploadedFileUrl = _model.downloadUrls[i]; // Access URL from downloadUrls

                        if (uploadedFileUrl.isNotEmpty) {
                          // Create image document for each uploaded file
                          var imagesRecordReference = ImagesRecord.createDoc(currentUserReference!);
                          await imagesRecordReference.set(createImagesRecordData(
                            url: uploadedFileUrl, // Use the URL from downloadUrls
                            availableToAll: true,
                            v2: true
                          ));

                          var photoDocCopy = ImagesRecord.getDocumentFromData(
                              createImagesRecordData(
                                url: uploadedFileUrl, // Use the URL from downloadUrls
                                availableToAll: true,
                                v2: true
                              ),
                              imagesRecordReference);

                          imageDocReferences.add(photoDocCopy.reference);
                        }
                      }

                      // After all documents are created, update the user's profile
                      if (imageDocReferences.isNotEmpty) {
                        
                        FirebaseFirestore.instance.runTransaction((transaction) async {
                          // Fetch the public profile document
                          var queryPuPro = await transaction.get(currentUserDocument!.publicProfile!); 

                          List<dynamic>? npictures;
                          
                          // Safely attempt to retrieve nPictures field
                          try {
                            npictures = List.from(queryPuPro['nPictures'] ?? []); // If null, default to an empty list
                          } catch (e) {
                            npictures = []; // If any error occurs, initialize as an empty list
                          }
                          
                          // Adding new image references to the list
                          var newnPictures = npictures..addAll(imageDocReferences);

                          _model.firstPicCopy = await ImagesRecord.getDocumentOnce(
                                newnPictures.first);

                          // Update the document with the new list
                          transaction.update(currentUserDocument!.publicProfile!, {"nPictures": newnPictures, "profilePhoto": _model.firstPicCopy?.url});
                        });

                        // Set the profile photo to the first uploaded image, if needed
                        
                      }
                      try {
                    ScaffoldMessenger.of(context).hideCurrentSnackBar();
                    } catch (error) {}
                    }

                    
                    
                    else {
                      if (shouldSetState) setState(() {});
                      return;
                    }
                    try {
                    ScaffoldMessenger.of(context).hideCurrentSnackBar();
                    } catch (error) {}

                    if (shouldSetState) setState(() {});
                  },
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(11.0),
                    child: Container(
                      width: MediaQuery.sizeOf(context).width * 0.925,
                      height: MediaQuery.sizeOf(context).height * 0.95,
                      decoration: BoxDecoration(
                        color: const Color(0xFFD8DBDF),
                        borderRadius: BorderRadius.circular(11.0),
                        border: Border.all(
                          color: const Color(0xFFBFC1C5),
                        ),
                      ),
                    ),
                  ),
                ),
                Align(
                  alignment: const AlignmentDirectional(1.0, -1.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      if (widget.publicProfile!.nPictures.length <
                          widget.picturePosition!)
                        Align(
                          alignment: const AlignmentDirectional(1.1, 0.0),
                          child: InkWell(
                            splashColor: Colors.transparent,
                            focusColor: Colors.transparent,
                            hoverColor: Colors.transparent,
                            highlightColor: Colors.transparent,
                            onTap: () async {
                              var shouldSetState = false;
                              final selectedMedia =
                                  await selectMediaWithSourceBottomSheet(
                                context: context,
                                maxWidth: 800.00,
                                imageQuality: 70,
                                allowPhoto: true,
                                backgroundColor:
                                    FlutterFlowTheme.of(context).primaryBackground,
                                textColor: FlutterFlowTheme.of(context).primaryText,
                                pickerFontFamily: 'Inter',
                                multiImage: true,
                              );
                              
                              if (selectedMedia != null &&
                                  selectedMedia.every((m) =>
                                      validateFileFormat(m.storagePath, context))) {
                                setState(() => _model.isDataUploading1 = true);
                                var selectedUploadedFiles = <FFUploadedFile>[];

                                var downloadUrls = <String>[];
                                try {

                                  try {
                              showUploadMessage(
                                    context,
                                    'Picking & uploading photos...',
                                    showLoading: true,
                                  );
                              } catch(error) {}
                                  
                                  selectedUploadedFiles = selectedMedia
                                      .map((m) => FFUploadedFile(
                                            name: m.storagePath.split('/').last,
                                            bytes: m.bytes,
                                            height: m.dimensions?.height,
                                            width: m.dimensions?.width,
                                            blurHash: m.blurHash,
                                          ))
                                      .toList();

                                  var queryPuPro = await currentUserDocument!.publicProfile!.get();
                                  var npictures;
                                  try{
                                    npictures = queryPuPro['nPictures'];
                                  }catch (e) {
                                    npictures = null; // Assign null if the field doesn't exist or there's an error
                                  }
                                  
                                  if (npictures != null){
                                    if(selectedUploadedFiles.length + npictures.length > 6) {
                                      // Show a dialogue to indicate too many files are being uploaded
                                      showDialog(
                              context: context, // Make sure to pass the BuildContext here
                              builder: (BuildContext context){
                                return const GeneralPopupWidget(
                                  alertTitle: "Too many pictures",
                                  alertText: "You cannot upload more than six pictures to your profile.",
                                );
                              }
                            );
                            try {
                              ScaffoldMessenger.of(context).hideCurrentSnackBar();
                              } catch (error) {}
                            return;
                                  }
                                }else{
                                  if(selectedUploadedFiles.length > 6) {
                                      // Show a dialogue to indicate too many files are being uploaded
                                      showDialog(
                              context: context, // Make sure to pass the BuildContext here
                              builder: (BuildContext context){
                                return const GeneralPopupWidget(
                                  alertTitle: "Too many pictures",
                                  alertText: "You cannot upload more than six pictures to your profile.",
                                );
                              }
                            );
                            try {
                              ScaffoldMessenger.of(context).hideCurrentSnackBar();
                              } catch (error) {}
                            return;
                                  }
                                }

                                  downloadUrls = (await Future.wait(
                                    selectedMedia.map(
                                      (m) async =>
                                          await uploadData(m.storagePath, m.bytes),
                                    ),
                                  ))
                                      .where((u) => u != null)
                                      .map((u) => u!)
                                      .toList();

                                  //print(downloadUrls.length.toString() + " " + selectedUploadedFiles.length.toString());
                                } finally {
                                  ScaffoldMessenger.of(context).hideCurrentSnackBar();
                                  _model.isDataUploading1 = false;
                                }
                                if (selectedUploadedFiles.length ==
                                        selectedMedia.length &&
                                    downloadUrls.length == selectedMedia.length) {
                                  setState(() {
                                    _model.uploadedLocalFile1 =
                                        selectedUploadedFiles.first;
                                    _model.uploadedFileUrl1 = downloadUrls.first;
                                    _model.selectedUploadedFiles = selectedUploadedFiles;
                                    _model.downloadUrls = downloadUrls;
                                  });
                                  showUploadMessage(context, 'Success!');
                                } else {
                                  setState(() {});
                                  showUploadMessage(context, 'Failed to upload data');
                                  return;
                                }
                              }else if(selectedMedia == null){
                                return;
                              }

                              if (_model.selectedUploadedFiles != null &&
                                  _model.selectedUploadedFiles.isNotEmpty &&
                                  _model.downloadUrls != null &&
                                  _model.downloadUrls.length == _model.selectedUploadedFiles.length) {
                                
                                // List to store references to the created image documents
                                List<DocumentReference> imageDocReferences = [];

                                for (int i = 0; i < _model.selectedUploadedFiles.length; i++) {
                                  String uploadedFileUrl = _model.downloadUrls[i]; // Access URL from downloadUrls

                                  if (uploadedFileUrl.isNotEmpty) {
                                    // Create image document for each uploaded file
                                    var imagesRecordReference = ImagesRecord.createDoc(currentUserReference!);
                                    await imagesRecordReference.set(createImagesRecordData(
                                      url: uploadedFileUrl, // Use the URL from downloadUrls
                                      availableToAll: true,
                                      v2: true
                                    ));

                                    var photoDocCopy = ImagesRecord.getDocumentFromData(
                                        createImagesRecordData(
                                          url: uploadedFileUrl, // Use the URL from downloadUrls
                                          availableToAll: true,
                                          v2: true
                                        ),
                                        imagesRecordReference);

                                    imageDocReferences.add(photoDocCopy.reference);
                                  }
                                }

                                // After all documents are created, update the user's profile
                                if (imageDocReferences.isNotEmpty) {
                        
                        FirebaseFirestore.instance.runTransaction((transaction) async {
                          // Fetch the public profile document
                          var queryPuPro = await transaction.get(currentUserDocument!.publicProfile!); 

                          List<dynamic>? npictures;
                          
                          // Safely attempt to retrieve nPictures field
                          try {
                            npictures = List.from(queryPuPro['nPictures'] ?? []); // If null, default to an empty list
                          } catch (e) {
                            npictures = []; // If any error occurs, initialize as an empty list
                          }
                          
                          // Adding new image references to the list
                          var newnPictures = npictures..addAll(imageDocReferences);

                          _model.firstPicCopy = await ImagesRecord.getDocumentOnce(
                                newnPictures.first);

                          // Update the document with the new list
                          transaction.update(currentUserDocument!.publicProfile!, {"nPictures": newnPictures, "profilePhoto": _model.firstPicCopy?.url});
                        });
                        try {
                    ScaffoldMessenger.of(context).hideCurrentSnackBar();
                    } catch (error) {}

                        // Set the profile photo to the first uploaded image, if needed
                        
                      }
                                

                              }
                              else {
                                if (shouldSetState) setState(() {});
                                return;
                              }
                              try {
                              ScaffoldMessenger.of(context).hideCurrentSnackBar();
                              } catch (error) {}
                              
                              if (shouldSetState) setState(() {});
                            },
                            child: Container(
                              width: 34.0,
                              height: 34.0,
                              decoration: BoxDecoration(
                                gradient: const LinearGradient(
                                  colors: [
                                    Color(0xFF67B0E5),
                                    Color(0xFFF49BD1)
                                  ],
                                  stops: [0.0, 1.0],
                                  begin: AlignmentDirectional(0.0, -1.0),
                                  end: AlignmentDirectional(0, 1.0),
                                ),
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: FlutterFlowTheme.of(context).info,
                                  width: 1.0,
                                ),
                              ),
                              child: Icon(
                                Icons.add,
                                color: FlutterFlowTheme.of(context).info,
                                size: 24.0,
                              ),
                            ),
                          ),
                        ),
                    ].divide(const SizedBox(height: 5.0)),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
