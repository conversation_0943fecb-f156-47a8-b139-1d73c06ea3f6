
import '/backend/backend.dart';
import '/chat/match_normal_round/match_normal_round_widget.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/chat/match_premium_preview_round/match_premium_preview_round_widget.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import 'package:flutter/rendering.dart';
import 'package:rive/rive.dart' hide LinearGradient, Image;
import '/flutter_flow/custom_functions.dart' as functions;
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'chat_intro_model.dart';
import 'package:chyrpe/intro/intro_amplitude_commons.dart';
export 'chat_intro_model.dart';

class ChatIntroWidget extends StatefulWidget {
  const ChatIntroWidget({super.key});

  @override
  State<ChatIntroWidget> createState() => _ChatIntroWidgetState();
}

class _ChatIntroWidgetState extends State<ChatIntroWidget> {
  late ChatIntroModel _model;
  final GlobalKey chatIntroRoundImage = GlobalKey(); 
  List<RRect> _cutoutRects = [];

  final _pageController = PageController();
  double opacityText1 = 0.0;
  double opacityText2 = 0.0;

  bool _ignore = false;


  late StateMachineController _riveController1;
  late StateMachineController _riveController2;
  late StateMachineController _riveController3;
  late StateMachineController _riveController4;
  late StateMachineController _riveController5;

  void _onInit1(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    art.addController(ctrl);
    _riveController1 = ctrl;
      _riveController1.isActive = true;
      }
     // Keep it inactive initially
  }

  void _onInit2(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    art.addController(ctrl);
    _riveController2 = ctrl;
      _riveController2.isActive = false;
      }
     // Keep it inactive initially
  }

  void _onInit3(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    art.addController(ctrl);
    _riveController3 = ctrl;
      _riveController3.isActive = false;
      }
     // Keep it inactive initially
  }

  void _onInit4(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    Future.delayed(const Duration(milliseconds: 800), () {
    art.addController(ctrl);
    _riveController4 = ctrl;
      _riveController4.isActive = true;
      });
      }
     // Keep it inactive initially
  }

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => ChatIntroModel());

    logPageVisitIntro(runtimeType.toString());

    WidgetsBinding.instance.addPostFrameCallback((_) {

      Future.delayed(const Duration(milliseconds: 100), () {
      _calculateCutouts();
      calculatePositionImage();
      });

      Future.delayed(const Duration(milliseconds: 1000), () {
      setState(() => opacityText1 = 1);
      setState(() {
        _riveController2.isActive = true;
      });
      });

      //  Future.delayed(Duration(milliseconds: 3000), () {
      // _pageController.nextPage(duration: Duration(milliseconds: 1000), curve: Curves.easeInCubic);
      // _advanceCutouts();
      // calculatePositionImage();
      // });

  });

  
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  int _currentCutoutStep = 0;
  var roundProfileImage = 0.0;

  void _advanceCutouts() {
  setState(() {
    _currentCutoutStep++;
    _calculateCutouts();
  });
}

void calculatePositionImage() {
    final RenderBox? renderBox = chatIntroRoundImage.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      final Offset offset = renderBox.localToGlobal(Offset.zero);
      final double elementBottom = offset.dy;
      setState(() {
        roundProfileImage = elementBottom + 120;
      });
    }
  }

  void _calculateCutouts() {
  List<RRect> rects = [];
  
  if (_currentCutoutStep > 0 && chatIntroRoundImage.currentContext != null) {
    // Only add profile picture cutout when step is 1
    final RenderBox profileBox = chatIntroRoundImage.currentContext!.findRenderObject() as RenderBox;
    final Size profileSize = profileBox.size;
    final Offset profilePosition = profileBox.localToGlobal(Offset.zero);
    rects.add(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(
          profilePosition.dx,
          profilePosition.dy,
          profileSize.width,
          profileSize.height,
        ),
        const Radius.circular(100),
      ),
    );
    
  setState(() {
    _cutoutRects = rects;
  });
  }
}

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Stack(
        children: [
          Scaffold(
            key: scaffoldKey,
            backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
            appBar: AppBar(
              backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
              automaticallyImplyLeading: false,
              title: Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8.0),
                    child: SvgPicture.asset(
                      'assets/images/ColorfulLogoA.svg',
                      width: 150.0,
                      height: 28.0,
                      fit: BoxFit.contain,
                    ),
                  ),
                  Container(
                    width: 1.0,
                    height: 1.0,
                    decoration: const BoxDecoration(),
                  ),
                ],
              ),
              actions: const [],
              centerTitle: false,
              elevation: 0.0,
            ),
            body: SafeArea(
              top: true,
              child: Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Expanded(
                    child: Container(
                      width: MediaQuery.sizeOf(context).width * 1.0,
                      decoration: const BoxDecoration(),
                      child: Stack(
                        alignment: const AlignmentDirectional(0.0, -1.0),
                        children: [
                          Padding(
                            padding:
                                const EdgeInsetsDirectional.fromSTEB(8.0, 26.0, 8.0, 0.0),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                Container(
                                  decoration: const BoxDecoration(),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    children: [
                                      Align(
                                        alignment:
                                            const AlignmentDirectional(-1.0, 0.0),
                                        child: Padding(
                                          padding: const EdgeInsetsDirectional.fromSTEB(
                                              20.0, 0.0, 0.0, 14.0),
                                          child: Text(
                                            'Matches',
                                            style: FlutterFlowTheme.of(context)
                                                .bodyMedium
                                                .override(
                                                  fontFamily: 'BT Beau Sans',
                                                  fontSize: 16.0,
                                                  letterSpacing: 0.0,
                                                  fontWeight: FontWeight.bold,
                                                  useGoogleFonts: false,
                                                ),
                                          ),
                                        ),
                                      ),
                                      Padding(
                                        padding: const EdgeInsetsDirectional.fromSTEB(
                                            15.0, 0.0, 15.0, 0.0),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.max,
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceAround,
                                          children: [
                                            Container(
                                              decoration: const BoxDecoration(),
                                              child: Container(
                                                decoration: const BoxDecoration(),
                                                child: StreamBuilder<
                                                    PublicProfileRecord>(
                                                  stream: PublicProfileRecord
                                                      .getDocument(functions
                                                          .getPublicProfileUidFromJSON(
                                                              getRemoteConfigString(
                                                                  'intro_test_data'))[2]
                                                          .publicProfile!),
                                                  builder: (context, snapshot) {
                                                    // Customize what your widget looks like when it's loading.
                                                    if (!snapshot.hasData) {
                                                      return Center(
                                                        child: SizedBox(
                                                          width: 50.0,
                                                          height: 50.0,
                                                          child:
                                                              CircularProgressIndicator(
                                                            valueColor:
                                                                AlwaysStoppedAnimation<
                                                                    Color>(
                                                              FlutterFlowTheme.of(
                                                                      context)
                                                                  .accent2,
                                                            ),
                                                          ),
                                                        ),
                                                      );
                                                    }
                                      
                                                    final matchPremiumPreviewRoundPublicProfileRecord =
                                                        snapshot.data!;
                                      
                                                    return wrapWithModel(
                                                      model: _model
                                                          .matchPremiumPreviewRoundModel,
                                                      updateCallback: () =>
                                                          safeSetState(() {}),
                                                      child:
                                                          MatchPremiumPreviewRoundWidget(
                                                        likes: 99,
                                                        likePicture:
                                                            matchPremiumPreviewRoundPublicProfileRecord
                                                                .profilePhoto,
                                                      ),
                                                    );
                                                  },
                                                ),
                                              ),
                                            ),
                                            Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Container(
                                                  width:
                                                      MediaQuery.sizeOf(context)
                                                              .width *
                                                          0.22,
                                                  height:
                                                      MediaQuery.sizeOf(context)
                                                              .width *
                                                          0.22,
                                                  decoration: const BoxDecoration(
                                                    shape: BoxShape.circle,
                                                  ),
                                                  child: StreamBuilder<
                                                      PublicProfileRecord>(
                                                    stream: PublicProfileRecord
                                                        .getDocument(functions
                                                            .getPublicProfileUidFromJSON(
                                                                getRemoteConfigString(
                                                                    'intro_test_data'))[1]
                                                            .publicProfile!),
                                                    builder: (context, snapshot) {
                                                      // Customize what your widget looks like when it's loading.
                                                      if (!snapshot.hasData) {
                                                        return Container();
                                                      }
                                      
                                                      final matchNormalRoundPublicProfileRecord =
                                                          snapshot.data!;
                                      
                                                      return InkWell(
                                                        splashColor:
                                                            Colors.transparent,
                                                        focusColor:
                                                            Colors.transparent,
                                                        hoverColor:
                                                            Colors.transparent,
                                                        highlightColor:
                                                            Colors.transparent,
                                                        onTap: () async {
                                                              context.pushReplacementNamed(
                                                              'ChatWindowIntro');
                                                          
                                                        },
                                                        child: wrapWithModel(
                                                          model: _model
                                                              .matchNormalRoundModel1,
                                                          updateCallback: () =>
                                                              safeSetState(() {}),
                                                          child:
                                                              MatchNormalRoundWidget(
                                                            key: chatIntroRoundImage,
                                                            image:
                                                                matchNormalRoundPublicProfileRecord
                                                                    .profilePhoto,
                                                            matchViewed: false,
                                                            goTo: () async {
                                                              
                                                            },
                                                          ),
                                                        ),
                                                      );
                                                    },
                                                  ),
                                                ),
                                              ],
                                            ),
                                            
                                            Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Container(
                                                  width:
                                                      MediaQuery.sizeOf(context)
                                                              .width *
                                                          0.22,
                                                  height:
                                                      MediaQuery.sizeOf(context)
                                                              .width *
                                                          0.22,
                                                  decoration: const BoxDecoration(
                                                    shape: BoxShape.circle,
                                                  ),
                                                  child: StreamBuilder<
                                                      PublicProfileRecord>(
                                                    stream: PublicProfileRecord
                                                        .getDocument(functions
                                                            .getPublicProfileUidFromJSON(
                                                                getRemoteConfigString(
                                                                    'intro_test_data'))[1]
                                                            .publicProfile!),
                                                    builder: (context, snapshot) {
                                                      // Customize what your widget looks like when it's loading.
                                                      if (!snapshot.hasData) {
                                                        return Container();
                                                      }
                                      
                                                      final matchNormalRoundPublicProfileRecord =
                                                          snapshot.data!;
                                      
                                                      return InkWell(
                                                        splashColor:
                                                            Colors.transparent,
                                                        focusColor:
                                                            Colors.transparent,
                                                        hoverColor:
                                                            Colors.transparent,
                                                        highlightColor:
                                                            Colors.transparent,
                                                        onTap: () async {
                                                              context.pushReplacementNamed(
                                                              'ChatWindowIntro');
                                                          
                                                        },
                                                        child: wrapWithModel(
                                                          model: _model
                                                              .matchNormalRoundModel1,
                                                          updateCallback: () =>
                                                              safeSetState(() {}),
                                                          child:
                                                              Container()
                                                            
                                                          
                                                        ),
                                                      );
                                                    },
                                                  ),
                                                ),
                                              ],
                                            ),
                                            
                                            
                                          ].divide(const SizedBox(width: 10.0)),
                                        ),
                                      ),
                                      ],
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      0.0, 26.0, 0.0, 0.0),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Align(
                                        alignment:
                                            const AlignmentDirectional(-1.0, 0.0),
                                        child: Padding(
                                          padding: const EdgeInsetsDirectional.fromSTEB(
                                              20.0, 0.0, 0.0, 14.0),
                                          child: Text(
                                            'Messages',
                                            style: FlutterFlowTheme.of(context)
                                                .bodyMedium
                                                .override(
                                                  fontFamily: 'BT Beau Sans',
                                                  fontSize: 16.0,
                                                  letterSpacing: 0.0,
                                                  fontWeight: FontWeight.bold,
                                                  useGoogleFonts: false,
                                                ),
                                          ),
                                        ),
                                      ),
                                      Builder(
                                        builder: (context) {
                                          final publicProfiles = functions
                                                            .getPublicProfileUidFromJSON(
                                                                getRemoteConfigString(
                                                                    'intro_test_data'));
                                      
                                          return ListView.builder(
                                            padding: EdgeInsets.zero,
                                            primary: false,
                                            shrinkWrap: true,
                                            scrollDirection: Axis.vertical,
                                            itemCount: publicProfiles.length,
                                            itemBuilder:
                                                (context, publicProfilesIndex) {
                                              final publicProfilesItem =
                                                  publicProfiles[
                                                      publicProfilesIndex];
                                              return Padding(
                                                padding: const EdgeInsetsDirectional
                                                    .fromSTEB(
                                                        0.0, 0.0, 0.0, 25.0),
                                                child: StreamBuilder<
                                                    PublicProfileRecord>(
                                                  stream: PublicProfileRecord
                                                      .getDocument(
                                                          publicProfilesItem
                                                              .publicProfile!),
                                                  builder: (context, snapshot) {
                                                    // Customize what your widget looks like when it's loading.
                                                    if (!snapshot.hasData) {
                                                      Container();
                                                    }
                                      
                                                    final rowPublicProfileRecord =
                                                        snapshot.data!;
                                      
                                                    return Opacity(
                                                      opacity: 0,
                                                      child: Row(
                                                        mainAxisSize:
                                                            MainAxisSize.max,
                                                        children: [
                                                          Align(
                                                            alignment:
                                                                const AlignmentDirectional(
                                                                    1.0, -1.0),
                                                            child: Stack(
                                                              alignment:
                                                                  const AlignmentDirectional(
                                                                      1.0, -1.0),
                                                              children: [
                                                                Container(
                                                                  width: MediaQuery
                                                                              .sizeOf(
                                                                                  context)
                                                                          .width *
                                                                      0.21,
                                                                  height: MediaQuery
                                                                              .sizeOf(
                                                                                  context)
                                                                          .width *
                                                                      0.21,
                                                                  decoration:
                                                                      BoxDecoration(
                                                                    color: FlutterFlowTheme.of(
                                                                            context)
                                                                        .secondaryBackground,
                                                                    image:
                                                                        DecorationImage(
                                                                      fit: BoxFit
                                                                          .cover,
                                                                      image: Image
                                                                          .network(
                                                                        rowPublicProfileRecord
                                                                            .profilePhoto,
                                                                      ).image,
                                                                    ),
                                                                    shape: BoxShape
                                                                        .circle,
                                                                  ),
                                                                ),
                                                                Align(
                                                                  alignment:
                                                                      const AlignmentDirectional(
                                                                          1.0,
                                                                          -1.0),
                                                                  child: Container(
                                                                    width: 21.0,
                                                                    height: 21.0,
                                                                    decoration:
                                                                        BoxDecoration(
                                                                      color: const Color(
                                                                          0xFFFF2551),
                                                                      shape: BoxShape
                                                                          .circle,
                                                                      border: Border
                                                                          .all(
                                                                        color: FlutterFlowTheme.of(
                                                                                context)
                                                                            .info,
                                                                        width: 2.0,
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                          Padding(
                                                            padding:
                                                                const EdgeInsetsDirectional
                                                                    .fromSTEB(
                                                                        10.0,
                                                                        0.0,
                                                                        0.0,
                                                                        0.0),
                                                            child: Column(
                                                              mainAxisSize:
                                                                  MainAxisSize.max,
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .start,
                                                              children: [
                                                                Row(
                                                                  mainAxisSize:
                                                                      MainAxisSize
                                                                          .max,
                                                                  children: [
                                                                    Text(
                                                                      rowPublicProfileRecord
                                                                          .publicName,
                                                                      style: FlutterFlowTheme.of(
                                                                              context)
                                                                          .bodyMedium
                                                                          .override(
                                                                            fontFamily:
                                                                                'BT Beau Sans',
                                                                            fontSize:
                                                                                18.0,
                                                                            letterSpacing:
                                                                                0.0,
                                                                            fontWeight:
                                                                                FontWeight.bold,
                                                                            useGoogleFonts:
                                                                                false,
                                                                          ),
                                                                    ),
                                                                  ],
                                                                ),
                                                                Padding(
                                                                  padding:
                                                                      const EdgeInsetsDirectional
                                                                          .fromSTEB(
                                                                              0.0,
                                                                              3.0,
                                                                              0.0,
                                                                              0.0),
                                                                  child: Container(
                                                                    width: MediaQuery.sizeOf(
                                                                                context)
                                                                            .width *
                                                                        0.6,
                                                                    height: 22.0,
                                                                    decoration:
                                                                        const BoxDecoration(),
                                                                    child: Text(
                                                                      publicProfilesItem
                                                                          .uid
                                                                          .maybeHandleOverflow(
                                                                        maxChars:
                                                                            50,
                                                                        replacement:
                                                                            '…',
                                                                      ),
                                                                      textAlign:
                                                                          TextAlign
                                                                              .start,
                                                                      style: FlutterFlowTheme.of(
                                                                              context)
                                                                          .bodyMedium
                                                                          .override(
                                                                            fontFamily:
                                                                                'BT Beau Sans',
                                                                            fontSize:
                                                                                16.0,
                                                                            letterSpacing:
                                                                                0.0,
                                                                            fontWeight:
                                                                                FontWeight.normal,
                                                                            useGoogleFonts:
                                                                                false,
                                                                          ),
                                                                    ),
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    );
                                                  },
                                                ),
                                              
                                              );
                                            },
                                          );
                                        
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Align(
                    alignment: Alignment.bottomCenter,
                    child: Container(
                                    width: MediaQuery.sizeOf(context).width * 1.0,
                                    height: 70.0,
                                    decoration: const BoxDecoration(
                    color: Colors.white,
                                    ),
                                    child: Padding(
                    padding: const EdgeInsetsDirectional.fromSTEB(20.0, 0.0, 20.0, 0.0),
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Align(
                          alignment: const AlignmentDirectional(0.0, 0.0),
                          child: FlutterFlowIconButton(
                            borderColor: Colors.transparent,
                            borderRadius: 50.0,
                            buttonSize: 60.0,
                            fillColor: Colors.white,
                            icon: const Icon(
                              FFIcons.kcards3,
                              color: Color(0xFFD8DBE0),
                              size: 39.0,
                            ),
                            onPressed: () {
                              print('IconButton pressed ...');
                            },
                          ),
                        ),
                        FlutterFlowIconButton(
                          borderColor: Colors.transparent,
                          borderRadius: 100.0,
                          buttonSize: 60.0,
                          fillColor: Colors.white,
                          icon: const Icon(
                            FFIcons.ksolarSystem,
                            color: Color(0xFFD8DBE0),
                            size: 33.0,
                          ),
                          onPressed: () {
                            print('IconButton pressed ...');
                          },
                        ),
                        FlutterFlowIconButton(
                          borderColor: Colors.transparent,
                          borderRadius: 100.0,
                          buttonSize: 60.0,
                          fillColor: Colors.white,
                          icon: const Icon(
                            FFIcons.ksparkles1,
                            color: Color(0xFFD8DBE0),
                            size: 32.0,
                          ),
                          onPressed: () {
                            print('IconButton pressed ...');
                          },
                        ),
                        FlutterFlowIconButton(
                          borderColor: Colors.transparent,
                          borderRadius: 100.0,
                          buttonSize: 60.0,
                          fillColor: Colors.white,
                          icon: const Icon(
                            FFIcons.kchatFilled,
                            color: Color(0xFF747E90),
                            size: 30.0,
                          ),
                          onPressed: () {
                            print('IconButton pressed ...');
                          },
                        ),
                        FlutterFlowIconButton(
                          borderColor: Colors.transparent,
                          borderRadius: 100.0,
                          buttonSize: 60.0,
                          fillColor: Colors.white,
                          icon: const Icon(
                            FFIcons.kprofile,
                            color: Color(0xFFD8DBE0),
                            size: 32.0,
                          ),
                           onPressed: () {
                            print('IconButton pressed ...');
                          },
                        ),
                            ]),
                                    ),
                                  
                                  ),
                  ),
                ],
              ),
              
            ),
          ),
          _IgnorePointerWithSemantics(
            ignore: _ignore,
            child: PageView(
              physics: const NeverScrollableScrollPhysics(),
              controller: _pageController,
              children: [
                GestureDetector(
                  onTap: () {
                    _pageController.nextPage(duration: const Duration(milliseconds: 1000), curve: Curves.easeInCubic);
                    _advanceCutouts();
                    calculatePositionImage();
                    setState(() {
                       _ignore = true;
                        opacityText2 = 1.0;
                    },);
                    setState(() {
                      _riveController3.isActive = true;
                    });
                   
                  },
                  child: Stack(
                    children: [
                      Positioned.fill(
                          child: ClipPath(
                            child: Container(
                              color: Colors.black.withOpacity(0.8),
                            ),
                          ),
                        ),
                              
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Align(
                            alignment: Alignment.bottomRight,
                            child: SizedBox(
                            height: MediaQuery.of(context).size.height * 0.35,
                            width: MediaQuery.of(context).size.width * 0.9,
                            child: RiveAnimation.asset(
                                                        'assets/rive_animations/fs_tutorial/23-2.riv',
                                                        fit: BoxFit.fitHeight,
                                                        onInit: _onInit2,
                                                        alignment: Alignment.bottomRight
                              ),
                                                              ),
                          ),
                          Align(
                            alignment: Alignment.centerLeft,
                            child: Padding(
                              padding: const EdgeInsets.fromLTRB(20, 0, 30, 0),
                              child: AnimatedOpacity(
                                opacity: opacityText1,
                                duration: const Duration(milliseconds: 300),
                                child: RichText(
                                  textScaler: MediaQuery.of(context).textScaler,
                                  text: TextSpan(
                                    children: [
                                       TextSpan(
                                                text: getRemoteConfigString('tutorial_chat_t1'),
                                                style:
                                                    FlutterFlowTheme.of(context).bodyMedium.override(
                                                          fontFamily: 'BT Beau Sans',
                                                          color: Colors.white,
                                                          letterSpacing: 0.0,
                                                          useGoogleFonts: false,
                                                        ),
                                              ),
                                              TextSpan(
                                                text: getRemoteConfigString('tutorial_chat_t2'),
                                                style:
                                                    FlutterFlowTheme.of(context).bodyMedium.override(
                                                          fontFamily: 'BT Beau Sans',
                                                          color: const Color(0xFFFF9EDE),
                                                          letterSpacing: 0.0,
                                                          useGoogleFonts: false,
                                                        ),
                                              ),
                                  ]),
                                ),
                              ),
                            ),
                          ),
                        Align(
                          alignment: Alignment.topLeft,
                          child: ClipRect(
                                              child: SizedBox(
                                              height: MediaQuery.of(context).size.height * 0.3,
                                              width: MediaQuery.of(context).size.width,
                                              child: RiveAnimation.asset(
                            'assets/rive_animations/fs_tutorial/23-1.riv',
                            fit: BoxFit.fitHeight,
                            onInit: _onInit1,
                            alignment: Alignment.centerLeft
                          ),
                                  ),
                                     ),
                        ),
                        ],
                      ),
                    ],
                  ),
                ),
                Stack(
                  children: [
                    Positioned.fill(
                            child: ClipPath(
                              clipper: MultiCutoutClipper(_cutoutRects), // Mul
                              child: Container(
                                color: Colors.black.withOpacity(0.8),
                              ),
                            ),
                      ),

                    Positioned(
                      bottom: MediaQuery.of(context).size.height - roundProfileImage + 10,
                      left: 0,
                      child: ClipRect(
                                            child: SizedBox(
                                            height: MediaQuery.of(context).size.height * 0.2,
                                            width: MediaQuery.of(context).size.width * 0.3,
                                            child: RiveAnimation.asset(
                          'assets/rive_animations/fs_tutorial/24-1.riv',
                          fit: BoxFit.fill,
                          onInit: _onInit1,
                          alignment: Alignment.centerLeft
                        ),
                                ),
                                   ),
                    ),
            
                    Positioned
                      (top: roundProfileImage, left: 0, right: 0,
                        child: Padding(
                          padding: const EdgeInsets.fromLTRB(20, 0, 50, 0),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              AnimatedOpacity(
                                opacity: opacityText2,
                                duration: const Duration(milliseconds: 300),
                                child: RichText(
                                      textScaler: MediaQuery.of(context).textScaler,
                                      text: TextSpan(
                                        children: [
                                          TextSpan(
                                            text: getRemoteConfigString('tutorial_chat_t3'),
                                            style:
                                                FlutterFlowTheme.of(context).bodyMedium.override(
                                                      fontFamily: 'BT Beau Sans',
                                                      color: Colors.white,
                                                      letterSpacing: 0.0,
                                                      useGoogleFonts: false,
                                                    ),
                                          ),
                                          TextSpan(
                                            text: getRemoteConfigString('tutorial_chat_t4'),
                                            style:
                                                FlutterFlowTheme.of(context).bodyMedium.override(
                                                      fontFamily: 'BT Beau Sans',
                                                      color: Colors.white,
                                                      letterSpacing: 0.0,
                                                      useGoogleFonts: false,
                                                    ),
                                          ),
                                          TextSpan(
                                            text: getRemoteConfigString('tutorial_chat_t5'),
                                            style:
                                                FlutterFlowTheme.of(context).bodyMedium.override(
                                                      fontFamily: 'BT Beau Sans',
                                                      color: const Color(0xFFFF9EDE),
                                                      letterSpacing: 0.0,
                                                      useGoogleFonts: false,
                                                    ),
                                          )
                                  ]),
                                    ),
                              ),

                               AnimatedOpacity(
                                opacity: opacityText2,
                                duration: const Duration(milliseconds: 300),
                                child: Padding(
                                  padding: const EdgeInsets.fromLTRB(0, 15, 0, 0),
                                  child: RichText(
                                    textScaler: MediaQuery.of(context).textScaler,
                                    text: TextSpan(
                                      children: [
                                        TextSpan(
                                          text: getRemoteConfigString('tutorial_chat_t6a'),
                                          style:
                                              FlutterFlowTheme.of(context).bodyMedium.override(
                                                    fontFamily: 'BT Beau Sans',
                                                    color: Colors.white,
                                                    letterSpacing: 0.0,
                                                    useGoogleFonts: false,
                                                  ),
                                        ),
                                                              ]),
                                  ),
                                ),
                              ),
                              
                              
                            ],
                          ),
                        ),
                      ),

                    Positioned(
                    top: roundProfileImage + 60,
                    right: 0,
                    child: ClipRect(
                                          child: SizedBox(
                                          height: MediaQuery.of(context).size.height * 0.4,
                                          width: MediaQuery.of(context).size.width * 0.75,
                                          child: RiveAnimation.asset(
                        'assets/rive_animations/fs_tutorial/24-3.riv',
                        fit: BoxFit.fill,
                        onInit: _onInit4,
                        alignment: Alignment.bottomRight
                      ),
                              ),
                                  ),
                  ),
                  ],
                ),
              ],
            ),
          ),
          SafeArea(
          child: Align(
                      alignment: Alignment.bottomLeft,
                      child: Padding(
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(0.0, 50.0, 30.0, 0.0),
                        child: FFButtonWidget(
                          onPressed: () {
                            logSkippedIntro(runtimeType.toString());
                            GoRouter.of(context).goNamed('Discovery');
                          },
                          text: 'Skip',
                          options: FFButtonOptions(
                            height: 40.0,
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                16.0, 0.0, 16.0, 0.0),
                            iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 0.0, 0.0, 0.0),
                            color: const Color(0x004B39EF),
                            textStyle:
                                FlutterFlowTheme.of(context).titleSmall.override(
                                      fontFamily: 'BT Beau Sans',
                                      color: Colors.white,
                                      fontSize: 10.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.normal,
                                      useGoogleFonts: false,
                                    ),
                            elevation: 0.0,
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                        ),
                      ),
                    ),
        ),
        ],
      ),
    );
  }
}

class MultiCutoutClipper extends CustomClipper<Path> {
  final List<RRect> cutoutRects; // List of rounded rectangles

  MultiCutoutClipper(this.cutoutRects);

  @override
  Path getClip(Size size) {
    Path path = Path();

    // Full screen rectangle
    path.addRect(Rect.fromLTWH(0, 0, size.width, size.height));

    // Subtract each cutout rectangle from the path
    for (final cutout in cutoutRects) {
      path = Path.combine(
        PathOperation.difference,
        path,
        Path()..addRRect(cutout),
      );
    }

    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return true; // Reclip if the cutouts change
  }
}

class _IgnorePointerWithSemantics extends SingleChildRenderObjectWidget {
  const _IgnorePointerWithSemantics({
    super.child,
    required this.ignore,
  });

  /// Determines whether the pointer events should be ignored.
  final bool ignore;

  @override
  _RenderIgnorePointerWithSemantics createRenderObject(BuildContext context) {
    return _RenderIgnorePointerWithSemantics(ignore: ignore);
  }

  @override
  void updateRenderObject(
      BuildContext context, _RenderIgnorePointerWithSemantics renderObject) {
    renderObject.ignore = ignore;
  }
}

class _RenderIgnorePointerWithSemantics extends RenderProxyBox {
  _RenderIgnorePointerWithSemantics({required bool ignore}) : _ignore = ignore;

  bool _ignore;

  bool get ignore => _ignore;
  set ignore(bool value) {
    if (_ignore == value) return;
    _ignore = value;
    markNeedsSemanticsUpdate(); // Update semantics when the ignore state changes.
  }

  @override
  bool hitTest(BoxHitTestResult result, {required Offset position}) {
    // Use the ignore property to determine whether to process hit tests.
    return !ignore && super.hitTest(result, position: position);
  }
}


