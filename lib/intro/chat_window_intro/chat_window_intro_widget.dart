import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/intro/power_board_intro/power_board_intro_widget.dart';
import 'package:rive/rive.dart' hide LinearGradient, Image;
import '/flutter_flow/custom_functions.dart' as functions;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter/rendering.dart';
import 'chat_window_intro_model.dart';
import 'package:chyrpe/intro/intro_amplitude_commons.dart';
export 'chat_window_intro_model.dart';

class ChatWindowIntroWidget extends StatefulWidget {
  const ChatWindowIntroWidget({super.key});

  @override
  State<ChatWindowIntroWidget> createState() => _ChatWindowIntroWidgetState();
}

class _ChatWindowIntroWidgetState extends State<ChatWindowIntroWidget> {
  late ChatWindowIntroModel _model;
  final GlobalKey chatWindowPowerBoardBtn = GlobalKey();
  List<RRect> _cutoutRects = [];
  final _pageController = PageController();

  double opacityText1 = 0.0;
  double opacityText2 = 0.0;

  late StateMachineController _riveController1;
  late StateMachineController _riveController2;
  late StateMachineController _riveController3;

  void _onInit1(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    art.addController(ctrl);
    _riveController1 = ctrl;
      _riveController1.isActive = true;
      }
     // Keep it inactive initially
  }

  void _onInit2(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    art.addController(ctrl);
    _riveController2 = ctrl;
      _riveController2.isActive = false;
      }
     // Keep it inactive initially
  }

  void _onInit3(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    Future.delayed(const Duration(milliseconds: 500), () {
        setState(() {
          opacityText2 = 1.0;
        });
      });
    Future.delayed(const Duration(milliseconds: 800), () {
    art.addController(ctrl);
    _riveController3 = ctrl;
      _riveController3.isActive = true;
      }
    );}
     // Keep it inactive initially
  }


  var powerBoardBtnPosition = 0.0;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => ChatWindowIntroModel());

    _model.textController ??= TextEditingController();
    _model.textFieldFocusNode ??= FocusNode();

    logPageVisitIntro(runtimeType.toString());

    WidgetsBinding.instance.addPostFrameCallback((_) {

      Future.delayed(const Duration(milliseconds: 300), () {
      _calculateCutouts();
      calculatePositionCard();
      });

      Future.delayed(const Duration(milliseconds: 1600), () {
        setState(() {
          opacityText1 = 1.0;
        });
      });

      Future.delayed(const Duration(milliseconds: 1800), () {
        setState(() {
          _riveController2.isActive = true;
        });
      });

  });
  }

   void calculatePositionCard() {
    final RenderBox? renderBox = chatWindowPowerBoardBtn.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      final Offset offset = renderBox.localToGlobal(Offset.zero);
      final double elementBottom = offset.dy;
      setState(() {
        powerBoardBtnPosition = elementBottom + 80; // 10px below the bottom
      });
    }
  }

void _calculateCutouts() {
  List<RRect> rects = [];
  
    // Only add profile picture cutout when step is 1
    final RenderBox profileBox = chatWindowPowerBoardBtn.currentContext!.findRenderObject() as RenderBox;
    final Size profileSize = profileBox.size;
    final Offset profilePosition = profileBox.localToGlobal(Offset.zero);
    rects.add(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(
          profilePosition.dx,
          profilePosition.dy,
          profileSize.width,
          profileSize.height,
        ),
        const Radius.circular(100),
      ),
    );
    
  setState(() {
    _cutoutRects = rects;
  });
}

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Stack(
        children: [
          Scaffold(
            key: scaffoldKey,
            backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
            appBar: AppBar(
              backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
              automaticallyImplyLeading: false,
              title: Align(
                alignment: const AlignmentDirectional(0.0, 0.0),
                child: StreamBuilder<PublicProfileRecord>(
                  stream: _model.otherUser(
                    requestFn: () => PublicProfileRecord.getDocument(functions
                        .getPublicProfileUidFromJSON(
                            getRemoteConfigString('intro_test_data'))[1]
                        .publicProfile!),
                  ),
                  builder: (context, snapshot) {
                    // Customize what your widget looks like when it's loading.
                    if (!snapshot.hasData) {
                      return Center(
                        child: SizedBox(
                          width: 50.0,
                          height: 50.0,
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                              FlutterFlowTheme.of(context).accent2,
                            ),
                          ),
                        ),
                      );
                    }
          
                    final stackPublicProfileRecord = snapshot.data!;
          
                    return Stack(
                      alignment: const AlignmentDirectional(0.0, 0.0),
                      children: [
                        Align(
                          alignment: const AlignmentDirectional(-1.0, 0.0),
                          child: FlutterFlowIconButton(
                            borderRadius: 20.0,
                            borderWidth: 1.0,
                            buttonSize: 40.0,
                            icon: FaIcon(
                              FontAwesomeIcons.chevronLeft,
                              color: FlutterFlowTheme.of(context).secondaryText,
                              size: 24.0,
                            ),
                            showLoadingIndicator: true,
                            onPressed: () {
                              print('IconButton pressed ...');
                            },
                          ),
                        ),
                        Align(
                          alignment: const AlignmentDirectional(0.0, 0.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Container(
                                width: 35.0,
                                height: 35.0,
                                decoration: BoxDecoration(
                                  color: FlutterFlowTheme.of(context)
                                      .secondaryBackground,
                                  image: DecorationImage(
                                    fit: BoxFit.cover,
                                    image: Image.network(
                                      stackPublicProfileRecord.profilePhoto,
                                    ).image,
                                  ),
                                  shape: BoxShape.circle,
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 3.0, 0.0, 0.0),
                                child: Text(
                                  stackPublicProfileRecord.publicName,
                                  style: FlutterFlowTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        color: FlutterFlowTheme.of(context)
                                            .secondaryText,
                                        fontSize: 12.0,
                                        letterSpacing: 0.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Align(
                          alignment: const AlignmentDirectional(1.0, -1.0),
                          child: FlutterFlowIconButton(
                            key:chatWindowPowerBoardBtn,
                            borderRadius: 8.0,
                            buttonSize: 40.0,
                            icon: const Icon(
                              Icons.settings_power,
                              color: Color(0xFF9200D6),
                              size: 24.0,
                            ),
                            onPressed: () async {
                               _pageController.nextPage(duration: const Duration(milliseconds: 1000), curve: Curves.easeInCubic);
                              await showModalBottomSheet(
                                isScrollControlled: true,
                                barrierColor: Colors.transparent,
                                backgroundColor: const Color.fromARGB(0, 0, 0, 0),
                                isDismissible: false,
                                enableDrag: false,
                                context: context,
                                builder: (context) {
                                  return GestureDetector(
                                    onTap: () => FocusScope.of(context).unfocus(),
                                    child: Container(
                                      child: Padding(
                                        padding: MediaQuery.viewInsetsOf(context),
                                        child: SizedBox(
                                    height:
                                        MediaQuery.sizeOf(context).height < 600 ? MediaQuery.sizeOf(context).height * 0.52 : MediaQuery.sizeOf(context).height * 0.75,
                                         child: const PowerBoardIntroWidget()),
                                      ),
                                    ),
                                  );
                                },
                              ).then((value) => safeSetState(() {}));
                             
                            },
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),
              actions: const [],
              bottom: PreferredSize(
                preferredSize: const Size.fromHeight(40.0),
                child: Container(
                  decoration: const BoxDecoration(),
                  child: Container(
                    width: MediaQuery.sizeOf(context).width * 1.0,
                    height: 30.0,
                    decoration: BoxDecoration(
                      color: FlutterFlowTheme.of(context).secondaryBackground,
                    ),
                    child: Align(
                      alignment: const AlignmentDirectional(0.0, 0.0),
                      child: Text(
                        'Explicit messages are currently disallowed.',
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'BT Beau Sans',
                              letterSpacing: 0.0,
                              useGoogleFonts: false,
                            ),
                      ),
                    ),
                  ),
                ),
              ),
              centerTitle: false,
              elevation: 2.0,
            ),
            body: SafeArea(
              top: true,
              child: Align(
                alignment: const AlignmentDirectional(0.0, -1.0),
                child: Container(
                  height: MediaQuery.sizeOf(context).height * 1.0,
                  decoration: const BoxDecoration(),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Padding(
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(10.0, 0.0, 10.0, 15.0),
                        child: Container(
                          width: MediaQuery.sizeOf(context).width * 1.0,
                          decoration: BoxDecoration(
                            color: FlutterFlowTheme.of(context).secondaryBackground,
                            borderRadius: BorderRadius.circular(24.0),
                            border: Border.all(
                              color: const Color(0xFFE9EAEE),
                              width: 1.0,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.max,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Form(
                                key: _model.formKey,
                                autovalidateMode: AutovalidateMode.disabled,
                                child: Container(
                                  decoration: const BoxDecoration(),
                                  child: Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        15.0, 0.0, 10.0, 0.0),
                                    child: SizedBox(
                                      width: MediaQuery.sizeOf(context).width * 0.7,
                                      child: TextFormField(
                                        controller: _model.textController,
                                        focusNode: _model.textFieldFocusNode,
                                        autofocus: false,
                                        textCapitalization:
                                            TextCapitalization.sentences,
                                        textInputAction: TextInputAction.done,
                                        readOnly: true,
                                        obscureText: false,
                                        decoration: InputDecoration(
                                          hintText: 'Type a message...',
                                          hintStyle: FlutterFlowTheme.of(context)
                                              .labelMedium
                                              .override(
                                                fontFamily: 'BT Beau Sans',
                                                letterSpacing: 0.0,
                                                useGoogleFonts: false,
                                              ),
                                          enabledBorder: InputBorder.none,
                                          focusedBorder: InputBorder.none,
                                          errorBorder: InputBorder.none,
                                          focusedErrorBorder: InputBorder.none,
                                        ),
                                        style: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .override(
                                              fontFamily: 'BT Beau Sans',
                                              fontSize: 16.0,
                                              letterSpacing: 0.0,
                                              useGoogleFonts: false,
                                            ),
                                        maxLines: 5,
                                        minLines: 1,
                                        maxLength: 1000,
                                        maxLengthEnforcement:
                                            MaxLengthEnforcement.enforced,
                                        buildCounter: (context,
                                                {required currentLength,
                                                required isFocused,
                                                maxLength}) =>
                                            null,
                                        validator: _model.textControllerValidator
                                            .asValidator(context),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Flexible(
                                child: Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      0.0, 0.0, 10.0, 0.0),
                                  child: FFButtonWidget(
                                    onPressed: () {
                                      print('Button pressed ...');
                                    },
                                    text: 'Send',
                                    options: FFButtonOptions(
                                      height: 40.0,
                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                          0.0, 0.0, 0.0, 0.0),
                                      iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                          0.0, 0.0, 0.0, 0.0),
                                      color: FlutterFlowTheme.of(context)
                                          .secondaryBackground,
                                      textStyle: FlutterFlowTheme.of(context)
                                          .titleSmall
                                          .override(
                                            fontFamily: 'BT Beau Sans',
                                            color: FlutterFlowTheme.of(context)
                                                .primaryText,
                                            letterSpacing: 0.0,
                                            useGoogleFonts: false,
                                          ),
                                      elevation: 0.0,
                                      borderSide: const BorderSide(
                                        color: Colors.transparent,
                                        width: 1.0,
                                      ),
                                      borderRadius: BorderRadius.circular(8.0),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          
          _IgnorePointerWithSemantics(
            child: PageView(
              controller: _pageController,
              children: [
                Stack(
                  children: [
                    Positioned.fill(
                        child: ClipPath(
                          clipper: MultiCutoutClipper(_cutoutRects), // Multiple cutouts
                          child: Container(
                            color: Colors.black.withOpacity(0.8),
                          ),
                        ),
                      ),
                      Positioned(
                        bottom: MediaQuery.of(context).size.height - powerBoardBtnPosition,
                        right: 0,
                        child: ClipRect(
                                                child: SizedBox(
                                                height: MediaQuery.of(context).size.height * 0.1,
                                                width: MediaQuery.of(context).size.width * 0.8,
                                                child: RiveAnimation.asset(
                            'assets/rive_animations/fs_tutorial/26-2.riv',
                            fit: BoxFit.fill,
                            onInit: _onInit2,
                            alignment: Alignment.topRight
                                                  ),
                                  ),
                                     ),
                      ),
                      Positioned(
                        top: powerBoardBtnPosition, left: 0, right: 0,
                        child: Padding(
                          padding: const EdgeInsets.fromLTRB(20, 0, 30, 0),
                          child: AnimatedOpacity(
                            opacity: opacityText1,
                            duration: const Duration(milliseconds: 300),
                            child: RichText(
                                        textScaler: MediaQuery.of(context).textScaler,
                                        text: TextSpan(
                                          children: [
                                            TextSpan(
                                              text: getRemoteConfigString('tutorial_chat_window_t1'),
                                              style:
                                                  FlutterFlowTheme.of(context).bodyMedium.override(
                                                        fontFamily: 'BT Beau Sans',
                                                        color: Colors.white,
                                                        letterSpacing: 0.0,
                                                        useGoogleFonts: false,
                                                      ),
                                            ),
                                            TextSpan(
                                              text: getRemoteConfigString('tutorial_chat_window_t2'),
                                              style:
                                                  FlutterFlowTheme.of(context).bodyMedium.override(
                                                        fontFamily: 'BT Beau Sans',
                                                        color: const Color(0xFFFF9EDE),
                                                        letterSpacing: 0.0,
                                                        useGoogleFonts: false,
                                                      ),
                                          )]
                                        )
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        top: powerBoardBtnPosition + 100,
                        left: 0,
                        child: ClipRect(
                                                child: SizedBox(
                                                height: MediaQuery.of(context).size.height * 0.6,
                                                width: MediaQuery.of(context).size.width,
                                                child: RiveAnimation.asset(
                            'assets/rive_animations/fs_tutorial/26-1.riv',
                            fit: BoxFit.fitHeight,
                            onInit: _onInit1,
                            alignment: Alignment.bottomLeft
                                                  ),
                                  ),
                                     ),
                      ),
                  ],
                ),
                Stack(
                  children: [
                    Positioned.fill(
                        child: ClipPath(
                          child: Container(
                            color: Colors.black.withOpacity(0.8),
                          ),
                        ),
                      ),
                      Positioned(
                        top: 10,
                        right: 0,
                        child: ClipRect(
                                                child: SizedBox(
                                                height: 80,
                                                width: MediaQuery.of(context).size.width * 0.7,
                                                child: RiveAnimation.asset(
                            'assets/rive_animations/fs_tutorial/27-2.riv',
                            fit: BoxFit.fitHeight,
                            onInit: _onInit3,
                            alignment: Alignment.topRight
                                                  ),
                                  ),
                                     ),
                      ),
                      Positioned(
                        top: 80,
                        left: 0,
                        child: ClipRect(
                                                child: SizedBox(
                                                height: 20,
                                                width: 35,
                                                child: RiveAnimation.asset(
                            'assets/rive_animations/fs_tutorial/27-1.riv',
                            fit: BoxFit.contain,
                            onInit: _onInit1,
                            alignment: Alignment.bottomLeft
                                                  ),
                                  ),
                                     ),
                      ),
                      SafeArea(
                        child: Align(
                          alignment: Alignment.topLeft,
                          child: Column(
                            children: [
                              
                              Column(
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.fromLTRB(40, 20, 50, 5),
                                    child: AnimatedOpacity(
                                      opacity: opacityText2,
                                      duration: const Duration(milliseconds: 300),
                                      child: RichText(
                                                  textScaler: MediaQuery.of(context).textScaler,
                                                  text: TextSpan(
                                                    children: [
                                                      TextSpan(
                                                        text: getRemoteConfigString('tutorial_chat_window_t3'),
                                                        style:
                                                            FlutterFlowTheme.of(context).bodyMedium.override(
                                                                  fontFamily: 'BT Beau Sans',
                                                                  color: Colors.white,
                                                                  letterSpacing: 0.0,
                                                                  useGoogleFonts: false,
                                                                ),
                                                      ),
                                                      TextSpan(
                                                        text: getRemoteConfigString('tutorial_chat_window_t4'),
                                                        style:
                                                            FlutterFlowTheme.of(context).bodyMedium.override(
                                                                  fontFamily: 'BT Beau Sans',
                                                                  color: const Color(0xFFFF9EDE),
                                                                  letterSpacing: 0.0,
                                                                  useGoogleFonts: false,
                                                                ),
                                                      ),
                                                      TextSpan(
                                                        text: getRemoteConfigString('tutorial_chat_window_t5'),
                                                        style:
                                                            FlutterFlowTheme.of(context).bodyMedium.override(
                                                                  fontFamily: 'BT Beau Sans',
                                                                  color: Colors.white,
                                                                  letterSpacing: 0.0,
                                                                  useGoogleFonts: false,
                                                                ),
                                                      ),
                                                       TextSpan(
                                                        text: getRemoteConfigString('tutorial_chat_window_t6'),
                                                        style:
                                                            FlutterFlowTheme.of(context).bodyMedium.override(
                                                                  fontFamily: 'BT Beau Sans',
                                                                  color: const Color(0xFFFF9EDE),
                                                                  letterSpacing: 0.0,
                                                                  useGoogleFonts: false,
                                                                ),
                                                      ),
                                                       TextSpan(
                                                        text: getRemoteConfigString('tutorial_chat_window_t7'),
                                                        style:
                                                            FlutterFlowTheme.of(context).bodyMedium.override(
                                                                  fontFamily: 'BT Beau Sans',
                                                                  color: Colors.white,
                                                                  letterSpacing: 0.0,
                                                                  useGoogleFonts: false,
                                                                ),
                                                      ),
                                              ]),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                          
                              Padding(
                                padding: const EdgeInsets.fromLTRB(40, 5, 50, 10),
                                child: RichText(
                                        textScaler: MediaQuery.of(context).textScaler,
                                        text: TextSpan(
                                          children: [
                                            TextSpan(
                                              text: getRemoteConfigString('tutorial_chat_window_t8'),
                                              style:
                                                  FlutterFlowTheme.of(context).bodyMedium.override(
                                                        fontFamily: 'BT Beau Sans',
                                                        color: Colors.white,
                                                        letterSpacing: 0.0,
                                                        useGoogleFonts: false,
                                                      ),
                                            ),
                                            TextSpan(
                                              text: getRemoteConfigString('tutorial_chat_window_t9'),
                                              style:
                                                  FlutterFlowTheme.of(context).bodyMedium.override(
                                                        fontFamily: 'BT Beau Sans',
                                                        color: const Color(0xFFFF9EDE),
                                                        letterSpacing: 0.0,
                                                        useGoogleFonts: false,
                                                      ),
                                            ),
                                            TextSpan(
                                              text: getRemoteConfigString('tutorial_chat_window_t10'),
                                              style:
                                                  FlutterFlowTheme.of(context).bodyMedium.override(
                                                        fontFamily: 'BT Beau Sans',
                                                        color: Colors.white,
                                                        letterSpacing: 0.0,
                                                        useGoogleFonts: false,
                                                      ),
                                            ),
                                    ]),
                                                        ),
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        
          SafeArea(
          child: Align(
                      alignment: Alignment.bottomLeft,
                      child: Padding(
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(0.0, 50.0, 30.0, 0.0),
                        child: FFButtonWidget(
                          onPressed: () {
                            logSkippedIntro(runtimeType.toString());
                            GoRouter.of(context).goNamed('Discovery');
                          },
                          text: 'Skip',
                          options: FFButtonOptions(
                            height: 40.0,
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                16.0, 0.0, 16.0, 0.0),
                            iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 0.0, 0.0, 0.0),
                            color: const Color(0x004B39EF),
                            textStyle:
                                FlutterFlowTheme.of(context).titleSmall.override(
                                      fontFamily: 'BT Beau Sans',
                                      color: Colors.white,
                                      fontSize: 10.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.normal,
                                      useGoogleFonts: false,
                                    ),
                            elevation: 0.0,
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                        ),
                      ),
                    ),
        ),
        
        ],
      ),
    );
  }
}

class MultiCutoutClipper extends CustomClipper<Path> {
  final List<RRect> cutoutRects; // List of rounded rectangles

  MultiCutoutClipper(this.cutoutRects);

  @override
  Path getClip(Size size) {
    Path path = Path();

    // Full screen rectangle
    path.addRect(Rect.fromLTWH(0, 0, size.width, size.height));

    // Subtract each cutout rectangle from the path
    for (final cutout in cutoutRects) {
      path = Path.combine(
        PathOperation.difference,
        path,
        Path()..addRRect(cutout),
      );
    }

    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return true; // Reclip if the cutouts change
  }
}

class _IgnorePointerWithSemantics extends SingleChildRenderObjectWidget {
  const _IgnorePointerWithSemantics({
    super.child,
  });

  @override
  _RenderIgnorePointerWithSemantics createRenderObject(BuildContext context) {
    return _RenderIgnorePointerWithSemantics();
  }
}

class _RenderIgnorePointerWithSemantics extends RenderProxyBox {
  _RenderIgnorePointerWithSemantics();

  @override
  bool hitTest(BoxHitTestResult result, { required Offset position }) => false;
}


