import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/intro/intro_card1/intro_card1_widget.dart';
import 'discovery_intro_widget.dart' show DiscoveryIntroWidget;
import 'package:flutter/material.dart';
import 'package:chyrpe/intro/discovery_intro1_page_view_overlay/discovery_intro1_page_view_overlay_widget.dart';

class DiscoveryIntroModel extends FlutterFlowModel<DiscoveryIntroWidget> {
  ///  Local state fields for this page.

  PublicProfileRecord? likedUser;

  DocumentReference? like;

  bool loadingVisible = true;
  // Model for IntroCard1 component.
  late IntroCard1Model introCard1Model;

  DiscoveryIntro1PageViewOverlayModel discoveryIntroModel = DiscoveryIntro1PageViewOverlayModel();

  @override
  void initState(BuildContext context) {
    introCard1Model = createModel(context, () => IntroCard1Model());
  }

  @override
  void dispose() {}

  /// Action blocks.
  Future likeAndMatchStage2(
    BuildContext context, {
    PublicProfileRecord? otherUser,
  }) async {}
}
