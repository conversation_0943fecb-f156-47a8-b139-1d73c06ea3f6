import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/intro/intro_card1/intro_card1_widget.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import 'package:badges/badges.dart' as badges;
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'discovery_intro_model.dart';
export 'discovery_intro_model.dart';
import 'package:chyrpe/intro/discovery_intro1_page_view_overlay/discovery_intro1_page_view_overlay_widget.dart';
import 'package:chyrpe/intro/intro_amplitude_commons.dart';

class DiscoveryIntroWidget extends StatefulWidget {
  const DiscoveryIntroWidget({super.key});

  @override
  State<DiscoveryIntroWidget> createState() => _DiscoveryIntroWidgetState();
}

class _DiscoveryIntroWidgetState extends State<DiscoveryIntroWidget> {
  late DiscoveryIntroModel _model;
  final GlobalKey discoveryIntro1ToProfileHomeBtn = GlobalKey(); // Key for the profile picture
  
  List<RRect> _cutoutRects = []; // List to hold cutout rectangles

  final scaffoldKey = GlobalKey<ScaffoldState>();


  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => DiscoveryIntroModel());

    logPageVisitIntro(runtimeType.toString());
     WidgetsBinding.instance.addPostFrameCallback((_) {

      Future.delayed(const Duration(milliseconds: 500), () {
      _calculateCutouts();
      });
    });
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

    int _currentCutoutStep = 0;
    bool _page2One = false;

  void _advanceCutouts() {
  setState(() {
    _currentCutoutStep++;
    _calculateCutouts();
  });
}

void _calculateCutouts() {
  List<RRect> rects = [];
  
  if (_currentCutoutStep == 2 && discoveryIntro1ToProfileHomeBtn.currentContext != null) {
    // Only add profile picture cutout when step is 1
    final RenderBox profileBox = discoveryIntro1ToProfileHomeBtn.currentContext!.findRenderObject() as RenderBox;
    final Size profileSize = profileBox.size;
    final Offset profilePosition = profileBox.localToGlobal(Offset.zero);
    rects.add(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(
          profilePosition.dx - 10,
          profilePosition.dy - 10,
          profileSize.width + 20,
          profileSize.height + 20,
        ),
        const Radius.circular(100),
      ),
    );
  }
  
  setState(() {
    _cutoutRects = rects;
  });
}

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Stack(
        children: [Scaffold(
          key: scaffoldKey,
          backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
          appBar: AppBar(
            backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
            automaticallyImplyLeading: false,
            title: Align(
              alignment: const AlignmentDirectional(0.0, 0.0),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Align(
                    alignment: const AlignmentDirectional(0.0, 0.0),
                    child: badges.Badge(
                      badgeContent: Text(
                        '1',
                        style: FlutterFlowTheme.of(context).titleSmall.override(
                              fontFamily: 'BT Beau Sans',
                              color: const Color(0xFFFF2551),
                              fontSize: 12.0,
                              letterSpacing: 0.0,
                              useGoogleFonts: false,
                            ),
                      ),
                      showBadge: false,
                      shape: badges.BadgeShape.circle,
                      badgeColor: const Color(0xFFFF2551),
                      elevation: 0.0,
                      padding: const EdgeInsetsDirectional.fromSTEB(3.0, 3.0, 3.0, 3.0),
                      position: badges.BadgePosition.topEnd(),
                      animationType: badges.BadgeAnimationType.scale,
                      toAnimate: true,
                      child: Align(
                        alignment: const AlignmentDirectional(0.0, 0.0),
                        child: FlutterFlowIconButton(
                          borderColor: const Color(0x004B39EF),
                          borderRadius: 20.0,
                          borderWidth: 1.0,
                          buttonSize: 40.0,
                          fillColor: const Color(0x004B39EF),
                          icon: Icon(
                            FFIcons.knotification,
                            color: FlutterFlowTheme.of(context).primaryText,
                            size: 30.0,
                          ),
                          showLoadingIndicator: true,
                          onPressed: () {
                            print('IconButton pressed ...');
                          },
                        ),
                      ),
                    ),
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8.0),
                        child: SvgPicture.asset(
                          'assets/images/ColorfulLogoA.svg',
                          width: 150.0,
                          height: 28.0,
                          fit: BoxFit.contain,
                        ),
                      ),
                    ],
                  ),
                  Align(
                    alignment: const AlignmentDirectional(1.0, 0.0),
                    child: FlutterFlowIconButton(
                      borderColor: const Color(0x004B39EF),
                      borderRadius: 20.0,
                      borderWidth: 1.0,
                      buttonSize: 40.0,
                      fillColor: const Color(0x004B39EF),
                      icon: Icon(
                        FFIcons.kfilter,
                        color: FlutterFlowTheme.of(context).primaryText,
                        size: 30.0,
                      ),
                      onPressed: () {
                        print('IconButton pressed ...');
                      },
                    ),
                  ),
                ],
              ),
            ),
            actions: const [],
            centerTitle: false,
            elevation: 0.0,
          ),
          body: SafeArea(
            top: true,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(
                  child: StreamBuilder<PublicProfileRecord>(
                    stream: PublicProfileRecord.getDocument(
                      functions
                        .getPublicProfileUidFromJSON(
                            getRemoteConfigString('intro_test_data'))
                        .firstOrNull!
                        .publicProfile!),
                        builder: (context, snapshot) {
                            if (!snapshot.hasData) {
                              return Container();
                            }
                        
                        final introCard1PublicProfileRecord = snapshot.data!;
                      return Container(
                        width: MediaQuery.sizeOf(context).width * 1.0,
                        height: MediaQuery.sizeOf(context).height * 1.0,
                        decoration: const BoxDecoration(),
                        child: wrapWithModel(
                          model: _model.introCard1Model,
                          updateCallback: () => safeSetState(() {}),
                          child: IntroCard1Widget(publicProfile: introCard1PublicProfileRecord),
                        ),
                      );
                    }
                  ),
                ),
                
                Container(
                  width: MediaQuery.sizeOf(context).width * 1.0,
                  height: 70.0,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                  ),
                  child: Padding(
                    padding: const EdgeInsetsDirectional.fromSTEB(20.0, 0.0, 20.0, 0.0),
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Align(
                          alignment: const AlignmentDirectional(0.0, 0.0),
                          child: FlutterFlowIconButton(
                            borderRadius: 50.0,
                            buttonSize: 60.0,
                            fillColor: Colors.white,
                            icon: const Icon(
                              FFIcons.kcards3,
                              color: Color(0xFF747E90),
                              size: 39.0,
                            ),
                            onPressed: () {
                              print('IconButton pressed ...');
                            },
                          ),
                        ),
                        FlutterFlowIconButton(
                          borderColor: Colors.transparent,
                          borderRadius: 100.0,
                          buttonSize: 60.0,
                          fillColor: Colors.white,
                          icon: const Icon(
                            FFIcons.ksolarSystem,
                            color: Color(0xFFD8DBE0),
                            size: 33.0,
                          ),
                          onPressed: () {
                            print('IconButton pressed ...');
                          },
                        ),
                        FlutterFlowIconButton(
                          borderColor: Colors.transparent,
                          borderRadius: 100.0,
                          buttonSize: 40.0,
                          fillColor: Colors.white,
                          icon: const Icon(
                            FFIcons.ksparkles1,
                            color: Color(0xFFD8DBE0),
                            size: 32.0,
                          ),
                          onPressed: () {
                            print('IconButton pressed ...');
                          },
                        ),
                        FlutterFlowIconButton(
                          borderColor: Colors.transparent,
                          borderRadius: 100.0,
                          buttonSize: 60.0,
                          fillColor: Colors.white,
                          icon: const Icon(
                            FFIcons.kchat,
                            color: Color(0xFFD8DBE0),
                            size: 30.0,
                          ),
                          onPressed: () {
                            print('IconButton pressed ...');
                          },
                        ),
                        FlutterFlowIconButton(
                          key: discoveryIntro1ToProfileHomeBtn,
                          borderColor: Colors.transparent,
                          borderRadius: 100.0,
                          buttonSize: 60.0,
                          fillColor: Colors.white,
                          icon: const Icon(
                            FFIcons.kprofile,
                            color: Color(0xFFD8DBE0),
                            size: 32.0,
                          ),
                          onPressed: () {
                            context.pushReplacementNamed(
                            'ProfileHomeIntro',
                            extra: <String, dynamic>{
                              kTransitionInfoKey: const TransitionInfo(
                                hasTransition: true,
                                transitionType: PageTransitionType.leftToRight,
                                duration: Duration(milliseconds: 100),
                              ),
                            },
                          );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
            GestureDetector(
              onTap: () {
                if (_currentCutoutStep < 2) {
                _model.discoveryIntroModel.pageViewController?.nextPage(duration: const Duration(milliseconds: 300), curve: Curves.ease);
                _advanceCutouts();
                setState(() {
                   _page2One = true;
                });
                } else {
                  context.goNamed(
                            'ProfileHomeIntro',
                            extra: <String, dynamic>{
                              kTransitionInfoKey: const TransitionInfo(
                                hasTransition: true,
                                transitionType: PageTransitionType.leftToRight,
                                duration: Duration(milliseconds: 100),
                              ),
                            },
                          );
                }
              },
              child: Stack(
                children: [
                  Positioned.fill(
                    child: ClipPath(
                      clipper: MultiCutoutClipper(_cutoutRects), // Multiple cutouts
                      child: Container(
                        color: Colors.black.withOpacity(0.8),
                      ),
                    ),
                  ),
                  Padding(
                        padding: const EdgeInsets.fromLTRB(0, 0, 0, 50),
                        child: wrapWithModel(model: _model.discoveryIntroModel, 
                        child: DiscoveryIntro1PageViewOverlayWidget(callback:() => ()),
                        updateCallback: () {
                          
                        },)

                      ),
                ],
              ),
            ),
        
        SafeArea(
          child: Align(
                      alignment: Alignment.bottomLeft,
                      child: Padding(
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(0.0, 50.0, 30.0, 0.0),
                        child: FFButtonWidget(
                          onPressed: () {
                            logSkippedIntro(runtimeType.toString());
                            GoRouter.of(context).goNamed('Discovery');
                          },
                          text: 'Skip Tutorial',
                          options: FFButtonOptions(
                            height: 40.0,
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                16.0, 0.0, 16.0, 0.0),
                            iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 0.0, 0.0, 0.0),
                            color: const Color(0x004B39EF),
                            textStyle:
                                FlutterFlowTheme.of(context).titleSmall.override(
                                      fontFamily: 'BT Beau Sans',
                                      color: Colors.white,
                                      fontSize: 10.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.normal,
                                      useGoogleFonts: false,
                                    ),
                            elevation: 0.0,
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                        ),
                      ),
                    ),
        ),
                  if (_page2One == false)
                  SafeArea(
                    child: Align(
                      alignment: Alignment.bottomRight,
                      child: Padding(
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(0.0, 50.0, 0.0, 0.0),
                        child: IgnorePointer(
                          child: FFButtonWidget(
                            onPressed: () {
                             
                            },
                            text: 'Tap on screen to proceed',
                            options: FFButtonOptions(
                              height: 40.0,
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  16.0, 0.0, 16.0, 0.0),
                              iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 0.0, 0.0, 0.0),
                              color: const Color(0x004B39EF),
                              textStyle:
                                  FlutterFlowTheme.of(context).titleSmall.override(
                                        fontFamily: 'BT Beau Sans',
                                        color: Colors.white,
                                        fontSize: 10.0,
                                        letterSpacing: 0.0,
                                        fontWeight: FontWeight.normal,
                                        useGoogleFonts: false,
                                      ),
                              elevation: 0.0,
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
        ]
      ),
    );
  }
}

class MultiCutoutClipper extends CustomClipper<Path> {
  final List<RRect> cutoutRects; // List of rounded rectangles

  MultiCutoutClipper(this.cutoutRects);

  @override
  Path getClip(Size size) {
    Path path = Path();

    // Full screen rectangle
    path.addRect(Rect.fromLTWH(0, 0, size.width, size.height));

    // Subtract each cutout rectangle from the path
    for (final cutout in cutoutRects) {
      path = Path.combine(
        PathOperation.difference,
        path,
        Path()..addRRect(cutout),
      );
    }

    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return true; // Reclip if the cutouts change
  }
}

