import '/flutter_flow/flutter_flow_util.dart';
import 'discovery_intro1_page_view_overlay_widget.dart'
    show DiscoveryIntro1PageViewOverlayWidget;
import 'package:flutter/material.dart';

class DiscoveryIntro1PageViewOverlayModel
    extends FlutterFlowModel<DiscoveryIntro1PageViewOverlayWidget> {
  ///  State fields for stateful widgets in this component.

  // State field(s) for PageView widget.
  PageController? pageViewController;

  int get pageViewCurrentIndex => pageViewController != null &&
          pageViewController!.hasClients &&
          pageViewController!.page != null
      ? pageViewController!.page!.round()
      : 0;

  @override
  void initState(BuildContext context) {
    
  }

  @override
  void dispose() {}
}
