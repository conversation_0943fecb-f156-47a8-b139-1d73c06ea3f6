import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:rive/rive.dart' hide LinearGradient;
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'discovery_intro1_page_view_overlay_model.dart';
export 'discovery_intro1_page_view_overlay_model.dart';


class DiscoveryIntro1PageViewOverlayWidget extends StatefulWidget {
  const DiscoveryIntro1PageViewOverlayWidget({
    super.key,
    required this.callback,
  });

  final Function()? callback;

  @override
  State<DiscoveryIntro1PageViewOverlayWidget> createState() =>
      _DiscoveryIntro1PageViewOverlayWidgetState();
}

class _DiscoveryIntro1PageViewOverlayWidgetState
    extends State<DiscoveryIntro1PageViewOverlayWidget> {
  late DiscoveryIntro1PageViewOverlayModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

    late StateMachineController _riveController1;
    late StateMachineController _riveController2;
    late StateMachineController _riveController3;
    late StateMachineController _riveController4;
    late StateMachineController _riveController5;
    late StateMachineController _riveController6;
    final bool _rive2Visible = false;

    double opacityText1 = 0.0;
    double opacityText2 = 0.0;
    double opacityText3 = 0.0;

    void _onInit1(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) {
    art.addController(ctrl);
    _riveController1 = ctrl;
    _riveController1.isActive = true;
      }
     // Keep it inactive initially
  }

  void _onInit2(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    Future.delayed(const Duration(milliseconds: 100), () {
    art.addController(ctrl);
    _riveController2 = ctrl;
      _riveController2.isActive = true;
      });
      }
     // Keep it inactive initially
  }

  void _onInit3(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    art.addController(ctrl);
    _riveController3 = ctrl;
  _riveController3.isActive = true;
  Future.delayed(const Duration(milliseconds: 400), () {
    setState(() {
        opacityText2 = 1;
      },); 
  });
      }
     // Keep it inactive initially
  }

    void _onInit4(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    Future.delayed(const Duration(milliseconds: 1000), () {
    art.addController(ctrl);
    _riveController4 = ctrl;
      _riveController4.isActive = true;
      });
      }
     // Keep it inactive initially
  }

  void _onInit5(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    art.addController(ctrl);
    _riveController5 = ctrl;
      _riveController5.isActive = true;
      Future.delayed(const Duration(milliseconds: 300), () {
    setState(() {
        opacityText3 = 1;
      },); 
  });
      }
     // Keep it inactive initially
  }

    void _onInit6(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    Future.delayed(const Duration(milliseconds: 1000), () {
    art.addController(ctrl);
    _riveController6 = ctrl;
      _riveController6.isActive = true;
      });
      }
     // Keep it inactive initially
  }


  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => DiscoveryIntro1PageViewOverlayModel());

    // On component load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      

      await Future.delayed(const Duration(milliseconds: 900));
      setState(() {
        opacityText1 = 1;
      },); 

    

      // await Future.delayed(const Duration(milliseconds: 4000));
      // await _model.pageViewController?.nextPage(
      //   duration: Duration(milliseconds: 300),
      //   curve: Curves.ease,
      // );
      // await Future.delayed(const Duration(milliseconds: 800));
      // setState(() {
      //   opacityText2 = 1;
      // },); 
      // await Future.delayed(const Duration(milliseconds: 8000));
      // await _model.pageViewController?.nextPage(
      //   duration: Duration(milliseconds: 300),
      //   curve: Curves.ease,
      // );
      // await Future.delayed(const Duration(milliseconds: 500));
      // setState(() {
      //   opacityText3 = 1;
      // },); 
      // await widget.callback?.call();
    });
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: MediaQuery.sizeOf(context).width,
      height: MediaQuery.sizeOf(context).height * 1.0,
      child: Padding(
        padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 40.0),
        child: PageView(
          physics: const NeverScrollableScrollPhysics(),
          controller: _model.pageViewController ??=
              PageController(initialPage: 0),
          scrollDirection: Axis.horizontal,
          children: [
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(40.0, 0.0, 0.0, 0.0),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                Align(
                  alignment: Alignment.topRight,
                  child: Container(
                     transform: Matrix4.translationValues(0.0, 20, 0.0),
                     height: MediaQuery.of(context).size.height * 0.3,
                     child: IntrinsicWidth(
                       child: RiveAnimation.asset(
                              'assets/rive_animations/1-2.riv',
                              fit: BoxFit.fitHeight,
                              onInit: _onInit2,
                            ),
                     ),
                   ),
                ),
                  AnimatedOpacity(
                    opacity: opacityText1,
                    duration: const Duration(milliseconds: 300),
                    child: RichText(
                      text: TextSpan(children: [
                        TextSpan(text: getRemoteConfigString('tutorial_discovery1_t1'),
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'Lora',
                              color: FlutterFlowTheme.of(context).primaryBackground,
                              fontSize: 15.0,
                              letterSpacing: 0.0,
                              fontWeight: FontWeight.w500,
                              fontStyle: FontStyle.italic,
                              useGoogleFonts: false,
                            ),
                      ), 
                      TextSpan(
                        text: getRemoteConfigString('tutorial_discovery1_t2'),
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'BT Beau Sans',
                              color:
                                  FlutterFlowTheme.of(context).primaryBackground,
                              letterSpacing: 0.0,
                              useGoogleFonts: false,
                            ),
                      )
                    ]
                      ),
                      
                    ),
                  ),
                  SizedBox(
                    height: MediaQuery.of(context).size.height * 0.3,
                    child: IntrinsicWidth(
                      child: RiveAnimation.asset(
                            'assets/rive_animations/1-1.riv',
                            fit: BoxFit.fitHeight,
                            onInit: _onInit1,
                          ),
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(20.0, 0.0, 0.0, 0.0),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Align(
                   alignment: Alignment.topLeft,
                   child: Container(
                      transform: Matrix4.translationValues(0.0, 0.0, 0.0),
                      height: MediaQuery.of(context).size.height * 0.3,
                      child: IntrinsicWidth(
                        child: RiveAnimation.asset(
                              'assets/rive_animations/fs_tutorial/2-1.riv',
                              fit: BoxFit.fitHeight,
                              onInit: _onInit3,
                            ),
                      ),
                    ),
                 ),
                  AnimatedOpacity(
                    opacity: opacityText2,
                    duration: const Duration(milliseconds: 300),
                    child: RichText(
                      textScaler: MediaQuery.of(context).textScaler,
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: getRemoteConfigString('tutorial_discovery1_t3'),
                            style:
                                FlutterFlowTheme.of(context).bodyMedium.override(
                                      fontFamily: 'BT Beau Sans',
                                      color: Colors.white,
                                      letterSpacing: 0.0,
                                      useGoogleFonts: false,
                                    ),
                          ),
                          TextSpan(
                            text: getRemoteConfigString('tutorial_discovery1_t4'),
                            style:
                                FlutterFlowTheme.of(context).bodyMedium.override(
                                      fontFamily: 'BT Beau Sans',
                                      color: const Color(0xFFFF9EDE),
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.bold,
                                      useGoogleFonts: false,
                                    ),
                          ),
                          TextSpan(
                            text:
                                getRemoteConfigString('tutorial_discovery1_t5'),
                            style:
                                FlutterFlowTheme.of(context).bodyMedium.override(
                                      fontFamily: 'BT Beau Sans',
                                      color: Colors.white,
                                      letterSpacing: 0.0,
                                      useGoogleFonts: false,
                                    ),
                          ),
                        ],
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'BT Beau Sans',
                              color:
                                  FlutterFlowTheme.of(context).primaryBackground,
                              letterSpacing: 0.0,
                              useGoogleFonts: false,
                            ),
                      ),
                    ),
                  ),
                  Align(
                    alignment: Alignment.bottomRight,
                    child:
                      SizedBox(
                         height: MediaQuery.of(context).size.height * 0.3,
                         width: MediaQuery.of(context).size.width * 0.5,
                         child: RiveAnimation.asset(
                               'assets/rive_animations/fs_tutorial/2-2.riv',
                               fit: BoxFit.fitHeight,
                               onInit: _onInit4,
                             ),
                       ),
                  ),
                  
                ],
              ),
            ),
            Align(
              alignment: const AlignmentDirectional(0.0, 1.0),
              child: Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Align(
                    alignment: Alignment.topRight,
                    child:
                      SizedBox(
                         height: MediaQuery.of(context).size.height * 0.4,
                         width: MediaQuery.of(context).size.width * 0.8,
                         child: RiveAnimation.asset(
                               'assets/rive_animations/fs_tutorial/3-2.riv',
                               fit: BoxFit.fitHeight,
                               onInit: _onInit6,
                             ),
                       ),
                  ),
                    Row(
                      children: [
                        Padding(
                          padding: const EdgeInsets.fromLTRB(0, 30, 0, 0),
                          child: SizedBox(
                           width: 60,
                           height: 50,
                           child: RiveAnimation.asset(
                                 'assets/rive_animations/fs_tutorial/3-1.riv',
                                 fit: BoxFit.cover,
                                 onInit: _onInit5,
                               ),
                                                 ),
                        ),
                        Flexible(
                          child: Padding(
                            padding:
                                const EdgeInsetsDirectional.fromSTEB(10.0, 0.0, 30.0, 0.0),
                            child: AnimatedOpacity(
                              opacity: opacityText3,
                              duration: const Duration(milliseconds: 300),
                              child: RichText( text: TextSpan(children: [TextSpan(
                                text: getRemoteConfigString('tutorial_discovery1_t6'),
                                style: FlutterFlowTheme.of(context).bodyMedium.override(
                                      fontFamily: 'BT Beau Sans',
                                      color: FlutterFlowTheme.of(context)
                                          .primaryBackground,
                                      letterSpacing: 0.0,
                                      useGoogleFonts: false,
                                    ),
                              ),])),
                            )
                          ),
                        ),
                      ],
                    ),
                    
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
