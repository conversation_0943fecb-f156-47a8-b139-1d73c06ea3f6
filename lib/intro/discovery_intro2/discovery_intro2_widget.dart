import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/intro/intro_card1/intro_card1_widget.dart';
import 'package:flutter/rendering.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import 'package:rive/rive.dart' hide LinearGradient, Image;
import 'package:badges/badges.dart' as badges;
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'discovery_intro2_model.dart';
import 'package:chyrpe/intro/intro_amplitude_commons.dart';
export 'discovery_intro2_model.dart';

class DiscoveryIntro2Widget extends StatefulWidget {
  const DiscoveryIntro2Widget({super.key});

  @override
  State<DiscoveryIntro2Widget> createState() => _DiscoveryIntro2WidgetState();
}

class _DiscoveryIntro2WidgetState extends State<DiscoveryIntro2Widget> {
  late DiscoveryIntro2Model _model;
  final GlobalKey discoveryIntro2aCard = GlobalKey(); 

  late StateMachineController _riveController1;
  late StateMachineController _riveController2;
  late StateMachineController _riveController3;
  bool textVisible1 = false;
  PublicProfileRecord? publicProfile;

  void _onInit1(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    art.addController(ctrl);
    _riveController1 = ctrl;
      _riveController1.isActive = true;
      }
     // Keep it inactive initially
  }

  void _onInit2(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    art.addController(ctrl);
    _riveController2 = ctrl;
      _riveController2.isActive = false;
      }
     // Keep it inactive initially
  }
  
  void _onInit3(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    Future.delayed(const Duration(milliseconds: 300));
    art.addController(ctrl);
    _riveController3 = ctrl;
      _riveController3.isActive = true;
      }
     // Keep it inactive initially
  }

  List<RRect> _cutoutRects = [];

  final scaffoldKey = GlobalKey<ScaffoldState>();
  final _controller = PageController();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => DiscoveryIntro2Model());

    logPageVisitIntro(runtimeType.toString());


     WidgetsBinding.instance.addPostFrameCallback((_) {

      Future.delayed(const Duration(milliseconds: 200), () {
      calculatePositionCard();
      });

      Future.delayed(const Duration(milliseconds: 1000), () {
      setState(() {
        textVisible1 = true;
        _riveController2.isActive = true;
      },);
      });

      // Future.delayed(Duration(milliseconds: 1500), () {
      // setState(() {
      //   _riveController2.isActive = true;
      // },);
      // });

      // Future.delayed(Duration(milliseconds: 5000), () {
      // _controller.nextPage(duration: Duration(milliseconds: 1000), curve: Curves.easeInCubic);
      // _currentCutoutStep = 1;
      // _advanceCutouts();
      // });
    });
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  int _currentCutoutStep = 0;

  void _advanceCutouts() {
  setState(() {
    _currentCutoutStep++;
    _calculateCutouts();
  });
}

var cardTextBottomPosition = 0.0;
var cardBottomPosition = 0.0;
bool secondTextVisible = false;

  void calculatePositionCard() {
    final RenderBox? renderBox = discoveryIntro2aCard.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      final Offset offset = renderBox.localToGlobal(Offset.zero);
      final double elementBottom = offset.dy;
      setState(() {
        cardTextBottomPosition = elementBottom - 80; // 10px below the bottom
        cardBottomPosition = elementBottom + renderBox.size.height;
      });
    }
  }

void _calculateCutouts() {
  List<RRect> rects = [];
  
  if (_currentCutoutStep >= 1 && discoveryIntro2aCard.currentContext != null) {
    // Only add profile picture cutout when step is 1
    final RenderBox profileBox = discoveryIntro2aCard.currentContext!.findRenderObject() as RenderBox;
    final Size profileSize = profileBox.size;
    final Offset profilePosition = profileBox.localToGlobal(Offset.zero);
    rects.add(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(
          profilePosition.dx,
          profilePosition.dy,
          profileSize.width,
          profileSize.height,
        ),
        const Radius.circular(15),
      ),
    );
    
  setState(() {
    _cutoutRects = rects;
  });
  }
}

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        appBar: AppBar(
          backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
          automaticallyImplyLeading: false,
          title: Align(
            alignment: const AlignmentDirectional(0.0, 0.0),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Align(
                  alignment: const AlignmentDirectional(0.0, 0.0),
                  child: badges.Badge(
                    badgeContent: Text(
                      '1',
                      style: FlutterFlowTheme.of(context).titleSmall.override(
                            fontFamily: 'BT Beau Sans',
                            color: const Color(0xFFFF2551),
                            fontSize: 12.0,
                            letterSpacing: 0.0,
                            useGoogleFonts: false,
                          ),
                    ),
                    showBadge: false,
                    shape: badges.BadgeShape.circle,
                    badgeColor: const Color(0xFFFF2551),
                    elevation: 0.0,
                    padding: const EdgeInsetsDirectional.fromSTEB(3.0, 3.0, 3.0, 3.0),
                    position: badges.BadgePosition.topEnd(),
                    animationType: badges.BadgeAnimationType.scale,
                    toAnimate: true,
                    child: Align(
                      alignment: const AlignmentDirectional(0.0, 0.0),
                      child: FlutterFlowIconButton(
                        borderColor: const Color(0x004B39EF),
                        borderRadius: 20.0,
                        borderWidth: 1.0,
                        buttonSize: 40.0,
                        fillColor: const Color(0x004B39EF),
                        icon: Icon(
                          FFIcons.knotification,
                          color: FlutterFlowTheme.of(context).primaryText,
                          size: 30.0,
                        ),
                        showLoadingIndicator: true,
                        onPressed: () {
                          print('IconButton pressed ...');
                        },
                      ),
                    ),
                  ),
                ),
                Row(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8.0),
                      child: SvgPicture.asset(
                        'assets/images/ColorfulLogoA.svg',
                        width: 150.0,
                        height: 28.0,
                        fit: BoxFit.contain,
                      ),
                    ),
                  ],
                ),
                Align(
                  alignment: const AlignmentDirectional(1.0, 0.0),
                  child: FlutterFlowIconButton(
                    borderColor: const Color(0x004B39EF),
                    borderRadius: 20.0,
                    borderWidth: 1.0,
                    buttonSize: 40.0,
                    fillColor: const Color(0x004B39EF),
                    icon: Icon(
                      FFIcons.kfilter,
                      color: FlutterFlowTheme.of(context).primaryText,
                      size: 30.0,
                    ),
                    onPressed: () {
                      print('IconButton pressed ...');
                    },
                  ),
                ),
              ],
            ),
          ),
          actions: const [],
          centerTitle: false,
          elevation: 0.0,
        ),
        body: GestureDetector(
          child: Column(
              mainAxisSize: MainAxisSize.min,
              
              children: [
                Flexible(
                  child: Container(
                    width: MediaQuery.sizeOf(context).width * 1.0,
                    height: MediaQuery.sizeOf(context).height * 1.0,
                    decoration: const BoxDecoration(),
                    child: GestureDetector(
                onTap: () async {
                    
                  },
                  child: StreamBuilder<PublicProfileRecord>(
                    stream: PublicProfileRecord.getDocument(functions
                        .getPublicProfileUidFromJSON(
                            getRemoteConfigString('intro_test_data'))
                        .firstOrNull!
                        .publicProfile!),
                    builder: (context, snapshot) {
                      if (!snapshot.hasData) {
                        return Container();
                      }
                      final introCard1PublicProfileRecord = snapshot.data!;
                      publicProfile = snapshot.data;
                      // Customize what your widget looks like when it's loading.
                      return Padding(
                        padding: const EdgeInsets.fromLTRB(0, 40, 0, 0),
                        child: wrapWithModel(
                          model: _model.introCard1Model,
                          updateCallback: () => safeSetState(() {}),
                          child: IntroCard1Widget(key: discoveryIntro2aCard, publicProfile: introCard1PublicProfileRecord,),
                        ),
                      );
                    }
                  ),
                    ),
                  ),
                ),
              Container(
                width: MediaQuery.sizeOf(context).width * 1.0,
                height: 70.0,
                decoration: const BoxDecoration(
                  color: Colors.white,
                ),
                child: Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(20.0, 0.0, 20.0, 0.0),
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Align(
                        alignment: const AlignmentDirectional(0.0, 0.0),
                        child: FlutterFlowIconButton(
                          borderRadius: 50.0,
                          buttonSize: 60.0,
                          fillColor: Colors.white,
                          icon: const Icon(
                            FFIcons.kcards3,
                            color: Color(0xFF747E90),
                            size: 39.0,
                          ),
                          onPressed: () {
                            print('IconButton pressed ...');
                          },
                        ),
                      ),
                      FlutterFlowIconButton(
                        borderColor: Colors.transparent,
                        borderRadius: 100.0,
                        buttonSize: 60.0,
                        fillColor: Colors.white,
                        icon: const Icon(
                          FFIcons.ksolarSystem,
                          color: Color(0xFFD8DBE0),
                          size: 33.0,
                        ),
                        onPressed: () {
                          print('IconButton pressed ...');
                        },
                      ),
                      FlutterFlowIconButton(
                        borderColor: Colors.transparent,
                        borderRadius: 100.0,
                        buttonSize: 60.0,
                        fillColor: Colors.white,
                        icon: const Icon(
                          FFIcons.ksparkles1,
                          color: Color(0xFFD8DBE0),
                          size: 32.0,
                        ),
                        onPressed: () {
                          print('IconButton pressed ...');
                        },
                      ),
                      FlutterFlowIconButton(
                        borderColor: Colors.transparent,
                        borderRadius: 100.0,
                        buttonSize: 60.0,
                        fillColor: Colors.white,
                        icon: const Icon(
                          FFIcons.kchat,
                          color: Color(0xFFD8DBE0),
                          size: 30.0,
                        ),
                        onPressed: () {
                          print('IconButton pressed ...');
                        },
                      ),
                      FlutterFlowIconButton(
                        borderColor: Colors.transparent,
                        borderRadius: 100.0,
                        buttonSize: 60.0,
                        fillColor: Colors.white,
                        icon: const Icon(
                          FFIcons.kprofile,
                          color: Color(0xFFD8DBE0),
                          size: 32.0,
                        ),
                        onPressed: () async {
                          context.pushReplacementNamed(
                            'ProfileHomeIntro',
                            extra: <String, dynamic>{
                              kTransitionInfoKey: const TransitionInfo(
                                hasTransition: true,
                                transitionType: PageTransitionType.leftToRight,
                                duration: Duration(milliseconds: 100),
                              ),
                            },
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      GestureDetector(
        onTap: () async {
          if (_currentCutoutStep == 0) {
            setState(() {
                  _riveController2.isActive = true;
                  Future.delayed(const Duration(milliseconds: 500), () {
                     _controller.nextPage(duration: const Duration(milliseconds: 1000), curve: Curves.easeInCubic);
                      _currentCutoutStep = 1;
                      _advanceCutouts();
                       calculatePositionCard();
                        Future.delayed(const Duration(milliseconds: 1200), () {
                          setState(() {
                        _riveController3.isActive = true;
                        });
                      });
                  });
                },);
          } else {

                   
                    context.pushReplacementNamed(
                      'ProfileDetailIntro',
                      queryParameters: {
                        'profile': serializeParam(
                          publicProfile,
                          ParamType.Document,
                        ),
                        'showLikeEtcFields': serializeParam(
                          true,
                          ParamType.bool,
                        ),
                        'isMatch': serializeParam(
                          false,
                          ParamType.bool,
                        ),
                        'showEverything': serializeParam(
                          true,
                          ParamType.bool,
                        ),
                        'fromLikesScreen': serializeParam(
                          false,
                          ParamType.bool,
                        ),
                        'evolvedUpgradeDetail': serializeParam(
                          false,
                          ParamType.bool,
                        ),
                      }.withoutNulls,
                      extra: <String, dynamic>{
                        'profile': publicProfile,
                      },
                    );
                    safeSetState(() {});
          }
        },
        child: PageView(
          physics: const NeverScrollableScrollPhysics(),
          controller: _controller,
          children: [
            Stack(
              children: [
              Positioned.fill(
                    child: Container(
                        color: Colors.black.withOpacity(0.8),
                      ),
                  ),
                      
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
            ClipRect(
              child: SizedBox(
              height: MediaQuery.of(context).size.height * 0.2,
              child: RiveAnimation.asset(
                  'assets/rive_animations/fs_tutorial/9-2.riv',
                  fit: BoxFit.fitHeight,
                  onInit: _onInit2,
                  alignment: Alignment.topRight
                ),
                        ),
            ),
            
            Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(20.0, 0.0, 20.0, 0.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AnimatedOpacity(
                        opacity: textVisible1 ? 1 : 0,
                        duration: const Duration(milliseconds: 300),
                        child: RichText(
                          textScaler: MediaQuery.of(context).textScaler,
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text: getRemoteConfigString("tutorial_discovery2_t1"),
                                style:
                                    FlutterFlowTheme.of(context).bodyMedium.override(
                                          fontFamily: 'BT Beau Sans',
                                          color: Colors.white,
                                          letterSpacing: 0.0,
                                          useGoogleFonts: false,
                                        ),
                              ),
                              TextSpan(
                                text: getRemoteConfigString("tutorial_discovery2_t2"),
                                style:
                                    FlutterFlowTheme.of(context).bodyMedium.override(
                                          fontFamily: 'BT Beau Sans',
                                          color: Colors.white,
                                          letterSpacing: 0.0,
                                          fontWeight: FontWeight.bold,
                                          useGoogleFonts: false,
                                        ),
                              ),
                              TextSpan(
                                text: getRemoteConfigString("tutorial_discovery2_t3"),
                                style:
                                    FlutterFlowTheme.of(context).bodyMedium.override(
                                          fontFamily: 'BT Beau Sans',
                                          color: const Color(0xFFFF9EDE),
                                          letterSpacing: 0.0,
                                          useGoogleFonts: false,
                                        ),
                              ),
                              TextSpan(
                                text: getRemoteConfigString("tutorial_discovery2_t4"),
                                style:
                                    FlutterFlowTheme.of(context).bodyMedium.override(
                                          fontFamily: 'BT Beau Sans',
                                          color: Colors.white,
                                          letterSpacing: 0.0,
                                          useGoogleFonts: false,
                                        ),
                              )
                            ],
                            style: FlutterFlowTheme.of(context).bodyMedium.override(
                                  fontFamily: 'BT Beau Sans',
                                  color:
                                      FlutterFlowTheme.of(context).primaryBackground,
                                  letterSpacing: 0.0,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
            ClipRect(
              child: SizedBox(
              height: MediaQuery.of(context).size.height * 0.4,
              child: RiveAnimation.asset(
                  'assets/rive_animations/fs_tutorial/9-1.riv',
                  fit: BoxFit.fitHeight,
                  onInit: _onInit1,
                  alignment: Alignment.bottomLeft
                ),
                        ),
            ),
             
                        ],
                      ),
            ]),
            Stack(children: [
            Positioned.fill(
              child: ClipPath(
                clipper: MultiCutoutClipper(_cutoutRects), // Mul
                child: Container(
                  color: Colors.black.withOpacity(0.8),
                ),
              ),
            ),
            
            Positioned(
              left: 0,
              top: cardTextBottomPosition + 55,
               child: ClipRect(
                child: SizedBox(
                height: 100,
                width: 100,
                child: RiveAnimation.asset(
                    'assets/rive_animations/fs_tutorial/10-13-14-15-1.riv',
                    fit: BoxFit.fitHeight,
                    onInit: _onInit3,
                    alignment: Alignment.topRight
                  ),
                          ),
                             ),
             ),
            Positioned(
              right: 0,
              top: cardBottomPosition - 20,
               child: ClipRect(
                child: SizedBox(
                height: 100,
                width: 100,
                child: RiveAnimation.asset(
                    'assets/rive_animations/fs_tutorial/10-13-14-15-2.riv',
                    fit: BoxFit.fitWidth,
                    onInit: _onInit3,
                    alignment: Alignment.topRight
                  ),
                          ),
                             ),
             ),
            Positioned
            (top: cardTextBottomPosition - 5, left: 0, right: 0, child:  Padding(
              padding: const EdgeInsets.fromLTRB(30, 0, 35, 0),
              child: RichText(
                      textScaler: MediaQuery.of(context).textScaler,
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: getRemoteConfigString("tutorial_discovery2_t5"),
                            style:
                                FlutterFlowTheme.of(context).bodyMedium.override(
                                      fontFamily: 'BT Beau Sans',
                                      color: Colors.white,
                                      letterSpacing: 0.0,
                                      useGoogleFonts: false,
                                    ),
                          ),
                          TextSpan(
                            text: getRemoteConfigString("tutorial_discovery2_t6"),
                            style:
                                FlutterFlowTheme.of(context).bodyMedium.override(
                                      fontFamily: 'BT Beau Sans',
                                      color: Colors.white,
                                      letterSpacing: 0.0,
                                      useGoogleFonts: false,
                                    ),
                          ),
                          TextSpan(
                            text: getRemoteConfigString("tutorial_discovery2_t7"),
                            style:
                                FlutterFlowTheme.of(context).bodyMedium.override(
                                      fontFamily: 'BT Beau Sans',
                                      color: const Color(0xFFFF9EDE),
                                      letterSpacing: 0.0,
                                      useGoogleFonts: false,
                                    ),
                          ),
                          TextSpan(
                            text: getRemoteConfigString("tutorial_discovery2_t8"),
                            style:
                                FlutterFlowTheme.of(context).bodyMedium.override(
                                      fontFamily: 'BT Beau Sans',
                                      color: Colors.white,
                                      letterSpacing: 0.0,
                                      useGoogleFonts: false,
                                    ),
                          )
                        ],
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'BT Beau Sans',
                              color:
                                  FlutterFlowTheme.of(context).primaryBackground,
                              letterSpacing: 0.0,
                              useGoogleFonts: false,
                            ),
                      ),
                    ),
            ),
            ),
            SafeArea(
          child: Align(
                      alignment: Alignment.bottomLeft,
                      child: Padding(
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(0.0, 50.0, 30.0, 0.0),
                        child: FFButtonWidget(
                          onPressed: () {
                            logSkippedIntro(runtimeType.toString());
                            GoRouter.of(context).goNamed('Discovery');
                          },
                          text: 'Skip',
                          options: FFButtonOptions(
                            height: 40.0,
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                16.0, 0.0, 16.0, 0.0),
                            iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 0.0, 0.0, 0.0),
                            color: const Color(0x004B39EF),
                            textStyle:
                                FlutterFlowTheme.of(context).titleSmall.override(
                                      fontFamily: 'BT Beau Sans',
                                      color: Colors.white,
                                      fontSize: 10.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.normal,
                                      useGoogleFonts: false,
                                    ),
                            elevation: 0.0,
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                        ),
                      ),
                    ),
        ),
            
            ]
            
            )
        ]
              ),
      ),]
    );
  }
}

class MultiCutoutClipper extends CustomClipper<Path> {
  final List<RRect> cutoutRects; // List of rounded rectangles

  MultiCutoutClipper(this.cutoutRects);

  @override
  Path getClip(Size size) {
    Path path = Path();

    // Full screen rectangle
    path.addRect(Rect.fromLTWH(0, 0, size.width, size.height));

    // Subtract each cutout rectangle from the path
    for (final cutout in cutoutRects) {
      path = Path.combine(
        PathOperation.difference,
        path,
        Path()..addRRect(cutout),
      );
    }

    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return true; // Reclip if the cutouts change
  }
}


class _IgnorePointerWithSemantics extends SingleChildRenderObjectWidget {
  const _IgnorePointerWithSemantics();

  @override
  _RenderIgnorePointerWithSemantics createRenderObject(BuildContext context) {
    return _RenderIgnorePointerWithSemantics();
  }
}

class _RenderIgnorePointerWithSemantics extends RenderProxyBox {
  _RenderIgnorePointerWithSemantics();

  @override
  bool hitTest(BoxHitTestResult result, { required Offset position }) => false;
}