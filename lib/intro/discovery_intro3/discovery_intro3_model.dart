import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/intro/intro_card2/intro_card2_widget.dart';
import 'discovery_intro3_widget.dart' show DiscoveryIntro3Widget;
import 'package:flutter/material.dart';
import 'package:flutter_card_swiper/flutter_card_swiper.dart';

class DiscoveryIntro3Model extends FlutterFlowModel<DiscoveryIntro3Widget> {
  ///  Local state fields for this page.

  PublicProfileRecord? likedUser;

  DocumentReference? like;

  bool loadingVisible = true;

  ///  State fields for stateful widgets in this page.

  // State field(s) for SwipeableStack widget.
  late CardSwiperController swipeableStackController;
  // Models for IntroCard2 dynamic component.
  late FlutterFlowDynamicModels<IntroCard2Model> introCard2Models;


  final bigController = PageController(initialPage: 0);
  final smallController = PageController(initialPage: 0);

  @override
  void initState(BuildContext context) {
    swipeableStackController = CardSwiperController();
    introCard2Models = FlutterFlowDynamicModels(() => IntroCard2Model());
  }

  @override
  void dispose() {
    introCard2Models.dispose();
  }

  /// Action blocks.
  Future likeAndMatchStage2(
    BuildContext context, {
    PublicProfileRecord? otherUser,
  }) async {}
}
