import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_swipeable_stack.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import '/flutter_flow/flutter_flow_widgets.dart';
import '/intro/intro_card2/intro_card2_widget.dart';
import 'package:rive/rive.dart' hide LinearGradient, Image;
import 'package:badges/badges.dart' as badges;
import 'package:flutter/rendering.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'discovery_intro3_model.dart';
import 'package:chyrpe/intro/intro_amplitude_commons.dart';
export 'discovery_intro3_model.dart';

class DiscoveryIntro3Widget extends StatefulWidget {
  const DiscoveryIntro3Widget({super.key});

  @override
  State<DiscoveryIntro3Widget> createState() => _DiscoveryIntro3WidgetState();
}

class _DiscoveryIntro3WidgetState extends State<DiscoveryIntro3Widget> {
  late DiscoveryIntro3Model _model;

  final GlobalKey discoveryIntro3Card = GlobalKey();


  late StateMachineController _riveController1;
  late StateMachineController _riveController2;
  int pageIndex = 0;
  int pageIndexBig = 0;

  late PublicProfileRecord publicProfile;

  void showYesButton() {
    setState(() {
                      _showSave = false;
                      _showX = false;
                      _showYes = true;
                    });
  }

  void swipingCallback(index) async {
    if (index == 0) {
    setState(() {
      setState(() {
        pageIndex += 1;
        pageIndexBig += 1;
        _updateIgnoreState();
        },);
      secondPage = true;
    });
      _model.bigController.nextPage(duration: const Duration(milliseconds: 1), curve: Curves.easeInCubic);
    changePageDelayed();      
    showYesButton();
    } else { 
      
      
                    context.pushReplacementNamed(
                                        'MatchStage2Intro',
                                        queryParameters: {
                                          'publicProfile': serializeParam(
                                            publicProfile,
                                            ParamType.Document,
                                          ),
                                        }.withoutNulls,
                                        extra: <String, dynamic>{
                                          'publicProfile':
                                              publicProfile,
                                          kTransitionInfoKey: const TransitionInfo(
                                            hasTransition: true,
                                            transitionType:
                                                PageTransitionType.bottomToTop,
                                          ),
                                        },
                                      );
    }
  }

  void _onInit1(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    art.addController(ctrl);
    _riveController1 = ctrl;
      _riveController1.isActive = true;
      }
     // Keep it inactive initially
  }

  void _onInit2(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    Future.delayed(const Duration(milliseconds: 800), () {
    art.addController(ctrl);
    _riveController2 = ctrl;
      _riveController2.isActive = true;
      });
      }
     // Keep it inactive initially
  }

  List<RRect> _cutoutRects = [];
  bool _showSave = false;
  bool _showX = false;
  bool _showYes = false;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  void changePageDelayed() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
    Future.delayed(const Duration(milliseconds: 2500), () {
      setState(() {
      pageIndex += 1;
      pageIndexBig += 1;
      _updateIgnoreState();
      },);
      _model.bigController.nextPage(duration: const Duration(milliseconds: 300), curve: Curves.easeInCubic);
      });
    });
  }

  bool _ignore = false;

  void _updateIgnoreState() {
    // Manually determine if the ignore state should be updated
    final smallPage = pageIndex;
    final bigPage = pageIndexBig;

    bool newIgnoreState = (smallPage == 2 || bigPage == 2);
    if (_ignore != newIgnoreState) {
      setState(() {
        _ignore = newIgnoreState;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => DiscoveryIntro3Model());

    logPageVisitIntro(runtimeType.toString());

    WidgetsBinding.instance.addPostFrameCallback((_) {

      try {

      Future.delayed(const Duration(milliseconds: 100), () {
      calculatePositionCard();
      _calculateCutouts();
      });

      // Future.delayed(Duration(milliseconds: 1500), () {
      // _model.smallController.nextPage(duration: Duration(milliseconds: 1000), curve: Curves.easeInCubic);
      // setState(() => _showSave = true);
      // });

      // Future.delayed(Duration(milliseconds: 4500), () {
      // _model.smallController.nextPage(duration: Duration(milliseconds: 1000), curve: Curves.easeInCubic);
      // setState(() {
      //   _showSave = false;
      //   _showX = true;
      // });
      // });
      } catch(e) {
      }
    });
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  int _currentCutoutStep = 0;

  void _advanceCutouts() {
  setState(() {
    _currentCutoutStep++;
    _calculateCutouts();
  });
}

var cardTextBottomPosition = 0.0;
var cardBottomPosition = 0.0;
bool secondTextVisible = false;
bool secondPage = false;

  void calculatePositionCard() {
    final RenderBox? renderBox = discoveryIntro3Card.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      final Offset offset = renderBox.localToGlobal(Offset.zero);
      final double elementBottom = offset.dy;
      setState(() {
        cardTextBottomPosition = elementBottom - 80; // 10px below the bottom
        cardBottomPosition = elementBottom + renderBox.size.height;
      });
    }
  }

void _calculateCutouts() {
  List<RRect> rects = [];
  
  if (_currentCutoutStep >= 0 && discoveryIntro3Card.currentContext != null) {
    // Only add profile picture cutout when step is 1
    final RenderBox profileBox = discoveryIntro3Card.currentContext!.findRenderObject() as RenderBox;
    final Size profileSize = profileBox.size;
    final Offset profilePosition = profileBox.localToGlobal(Offset.zero);
    rects.add(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(
          profilePosition.dx + 7.5,
          profilePosition.dy + 7.5,
          profileSize.width - 15,
          profileSize.height - 15,
        ),
        const Radius.circular(12),
      ),
    );
    
  setState(() {
    _cutoutRects = rects;
  });
  }
}

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Stack(
        children: [
          Scaffold(
            key: scaffoldKey,
            backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
            appBar: AppBar(
              backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
              automaticallyImplyLeading: false,
              title: Align(
                alignment: const AlignmentDirectional(0.0, 0.0),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Align(
                      alignment: const AlignmentDirectional(0.0, 0.0),
                      child: badges.Badge(
                        badgeContent: Text(
                          '1',
                          style: FlutterFlowTheme.of(context).titleSmall.override(
                                fontFamily: 'BT Beau Sans',
                                color: const Color(0xFFFF2551),
                                fontSize: 12.0,
                                letterSpacing: 0.0,
                                useGoogleFonts: false,
                              ),
                        ),
                        showBadge: false,
                        shape: badges.BadgeShape.circle,
                        badgeColor: const Color(0xFFFF2551),
                        elevation: 0.0,
                        padding: const EdgeInsetsDirectional.fromSTEB(3.0, 3.0, 3.0, 3.0),
                        position: badges.BadgePosition.topEnd(),
                        animationType: badges.BadgeAnimationType.scale,
                        toAnimate: true,
                        child: Align(
                          alignment: const AlignmentDirectional(0.0, 0.0),
                          child: FlutterFlowIconButton(
                            borderColor: const Color(0x004B39EF),
                            borderRadius: 20.0,
                            borderWidth: 1.0,
                            buttonSize: 40.0,
                            fillColor: const Color(0x004B39EF),
                            icon: Icon(
                              FFIcons.knotification,
                              color: FlutterFlowTheme.of(context).primaryText,
                              size: 30.0,
                            ),
                            showLoadingIndicator: true,
                            onPressed: () {
                              print('IconButton pressed ...');
                            },
                          ),
                        ),
                      ),
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8.0),
                          child: SvgPicture.asset(
                            'assets/images/ColorfulLogoA.svg',
                            width: 150.0,
                            height: 28.0,
                            fit: BoxFit.contain,
                          ),
                        ),
                      ],
                    ),
                    Align(
                      alignment: const AlignmentDirectional(1.0, 0.0),
                      child: FlutterFlowIconButton(
                        borderColor: const Color(0x004B39EF),
                        borderRadius: 20.0,
                        borderWidth: 1.0,
                        buttonSize: 40.0,
                        fillColor: const Color(0x004B39EF),
                        icon: Icon(
                          FFIcons.kfilter,
                          color: FlutterFlowTheme.of(context).primaryText,
                          size: 30.0,
                        ),
                        onPressed: () {
                          print('IconButton pressed ...');
                        },
                      ),
                    ),
                  ],
                ),
              ),
              actions: const [],
              centerTitle: false,
              elevation: 0.0,
            ),
            body: SafeArea(
              top: true,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Flexible(
                    child: Container(
                      width: MediaQuery.sizeOf(context).width * 1.0,
                      height: MediaQuery.sizeOf(context).height * 1.0,
                      decoration: const BoxDecoration(),
                      child: Padding(
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(0.0, 40.0, 0.0, 0.0),
                        child: Builder(
                      builder: (context) {
                        final introProfile = functions
                            .getPublicProfileUidFromJSON(
                                getRemoteConfigString('intro_test_data'))
                            .toList();

                            return FlutterFlowSwipeableStack(
                              key: discoveryIntro3Card,
                              controller: _model.swipeableStackController,
                              onSwipeFn: (index) {},
                              onLeftSwipe: (index) {swipingCallback(index);},
                              onRightSwipe: (index) {swipingCallback(index);},
                              onUpSwipe: (index) {swipingCallback(index);},
                              onDownSwipe: (index) {swipingCallback(index);},
                              itemBuilder: (context, introProfileIndex) {

                                final introProfileItem =
                                introProfile[introProfileIndex];
                            return StreamBuilder<PublicProfileRecord>(
                              stream: PublicProfileRecord.getDocument(
                                  introProfileItem.publicProfile!),
                              builder: (context, snapshot) {
                                // Customize what your widget looks like when it's loading.
                                if (!snapshot.hasData) {
                                  return Container();
                                }
                                final introCard2PublicProfileRecord =
                                    snapshot.data!;
                                    publicProfile = snapshot.data!;
                                return wrapWithModel(
                                        model: _model.introCard2Models.getModel(
                                    introProfileItem.uid,
                                    introProfileIndex,
                                  ),
                                        updateCallback: () => safeSetState(() {}),
                                        updateOnChange: true,
                                        child: IntroCard2Widget(
                                          key: Key(
                                      'Keyw83_${introProfileItem.uid}',
                                    ),
                                          publicProfile: introCard2PublicProfileRecord,
                                          showSave: !_showSave,
                                          showX: !_showX,
                                          showYes: !_showYes,
                                          callbackYes: () async {
                                            _model.swipeableStackController.swipeLeft();},
                                          callbackX: () async {
                                            _model.swipeableStackController.swipeLeft();},
                                          callbackSave: () async {
                                            _model.swipeableStackController.swipeLeft();},
                                        )
                                );
                         
                              },
                            );
                          },
                          itemCount: introProfile.length,
                          loop: false,
                          cardDisplayCount: 2,
                          scale: 0.7,
                        );
                      },
                    ),
                  ),
                ),
              ),
                  Container(
                    width: MediaQuery.sizeOf(context).width * 1.0,
                    height: 70.0,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                    ),
                    child: Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(20.0, 0.0, 20.0, 0.0),
                      child: Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Align(
                            alignment: const AlignmentDirectional(0.0, 0.0),
                            child: FlutterFlowIconButton(
                              borderRadius: 50.0,
                              buttonSize: 60.0,
                              fillColor: Colors.white,
                              icon: const Icon(
                                FFIcons.kcards3,
                                color: Color(0xFF747E90),
                                size: 39.0,
                              ),
                              onPressed: () {
                                print('IconButton pressed ...');
                              },
                            ),
                          ),
                          FlutterFlowIconButton(
                            borderColor: Colors.transparent,
                            borderRadius: 100.0,
                            buttonSize: 60.0,
                            fillColor: Colors.white,
                            icon: const Icon(
                              FFIcons.ksolarSystem,
                              color: Color(0xFFD8DBE0),
                              size: 33.0,
                            ),
                            onPressed: () {
                              print('IconButton pressed ...');
                            },
                          ),
                          FlutterFlowIconButton(
                            borderColor: Colors.transparent,
                            borderRadius: 100.0,
                            buttonSize: 60.0,
                            fillColor: Colors.white,
                            icon: const Icon(
                              FFIcons.ksparkles1,
                              color: Color(0xFFD8DBE0),
                              size: 32.0,
                            ),
                            onPressed: () {
                              print('IconButton pressed ...');
                            },
                          ),
                          FlutterFlowIconButton(
                            borderColor: Colors.transparent,
                            borderRadius: 100.0,
                            buttonSize: 60.0,
                            fillColor: Colors.white,
                            icon: const Icon(
                              FFIcons.kchat,
                              color: Color(0xFFD8DBE0),
                              size: 30.0,
                            ),
                            onPressed: () {
                              print('IconButton pressed ...');
                            },
                          ),
                          FlutterFlowIconButton(
                            borderColor: Colors.transparent,
                            borderRadius: 100.0,
                            buttonSize: 60.0,
                            fillColor: Colors.white,
                            icon: const Icon(
                              FFIcons.kprofile,
                              color: Color(0xFFD8DBE0),
                              size: 32.0,
                            ),
                            onPressed: () async {
                              context.pushReplacementNamed(
                                'ProfileHomeIntro',
                                extra: <String, dynamic>{
                                  kTransitionInfoKey: const TransitionInfo(
                                    hasTransition: true,
                                    transitionType: PageTransitionType.leftToRight,
                                    duration: Duration(milliseconds: 100),
                                  ),
                                },
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          _IgnorePointerWithSemantics(
            ignore: _ignore,
            child: GestureDetector(
              onTap: () {
                if (pageIndex == 0) {
                  setState(() => _showSave = true);
                  
                }
                if (pageIndex == 1) {
                  setState(() {
                      _showSave = false;
                      _showX = true;
                    });
                }
                if (pageIndex == 2) {
                  showYesButton;
                }
                setState(() {
                  pageIndex+=1;
                  _updateIgnoreState();
                  _model.smallController.nextPage(duration: const Duration(milliseconds: 200), curve: Curves.easeIn);
                },);
                _updateIgnoreState();
              },
              child: PageView(
                physics: const NeverScrollableScrollPhysics(),
                controller: _model.bigController,
                children: [
                  Stack(
                    children: [
                      Positioned.fill(
                            child: ClipPath(
                              clipper: MultiCutoutClipper(_cutoutRects), // Mul
                              child: Container(
                                color: Colors.black.withOpacity(0.8),
                              ),
                            ),
                      ),
                      Positioned(
                        top: cardTextBottomPosition,
                        child: SizedBox(
                          width: MediaQuery.of(context).size.width,
                          height: MediaQuery.of(context).size.height,
                          child: Padding(
                            padding: const EdgeInsets.fromLTRB(30, 0, 30, 10),
                            child: PageView(
                              physics: const NeverScrollableScrollPhysics(),
                              controller: _model.smallController,
                              children: [
                                RichText(
                                  textScaler: MediaQuery.of(context).textScaler,
                                  text: TextSpan(
                                    children: [
                                      TextSpan(
                                        text: getRemoteConfigString('tutorial_discovery3_t1'),
                                        style:
                                            FlutterFlowTheme.of(context).bodyMedium.override(
                                                  fontFamily: 'BT Beau Sans',
                                                  color: Colors.white,
                                                  letterSpacing: 0.0,
                                                  useGoogleFonts: false,
                                                ),
                                      ),
                                      TextSpan(
                                        text: getRemoteConfigString('tutorial_discovery3_t2'),
                                        style:
                                            FlutterFlowTheme.of(context).bodyMedium.override(
                                                  fontFamily: 'BT Beau Sans',
                                                  color: Colors.white,
                                                  letterSpacing: 0.0,
                                                  useGoogleFonts: false,
                                                ),
                                      ),
                              ]),
                                ),
                                RichText(
                                  textScaler: MediaQuery.of(context).textScaler,
                                  text: TextSpan(
                                    children: [
                                      TextSpan(
                                        text: getRemoteConfigString('tutorial_discovery3_t3'),
                                        style:
                                            FlutterFlowTheme.of(context).bodyMedium.override(
                                                  fontFamily: 'BT Beau Sans',
                                                  color: Colors.white,
                                                  letterSpacing: 0.0,
                                                  useGoogleFonts: false,
                                                ),
                                      ),
                                      TextSpan(
                                        text: getRemoteConfigString('tutorial_discovery3_t4'),
                                        style:
                                            FlutterFlowTheme.of(context).bodyMedium.override(
                                                  fontFamily: 'BT Beau Sans',
                                                  color: const Color(0xFFFF9EDE),
                                                  letterSpacing: 0.0,
                                                  useGoogleFonts: false,
                                                ),
                                      ),
                              ]),
                                ),
                                RichText(
                                  textScaler: MediaQuery.of(context).textScaler,
                                  text: TextSpan(
                                    children: [
                                      TextSpan(
                                        text: getRemoteConfigString('tutorial_discovery3_t5'),
                                        style:
                                            FlutterFlowTheme.of(context).bodyMedium.override(
                                                  fontFamily: 'BT Beau Sans',
                                                  color: Colors.white,
                                                  letterSpacing: 0.0,
                                                  useGoogleFonts: false,
                                                ),
                                      ),
                                      TextSpan(
                                        text: getRemoteConfigString('tutorial_discovery3_t6'),
                                        style:
                                            FlutterFlowTheme.of(context).bodyMedium.override(
                                                  fontFamily: 'BT Beau Sans',
                                                  color: const Color(0xFFFF9EDE),
                                                  letterSpacing: 0.0,
                                                  useGoogleFonts: false,
                                                ),
                                      ),
                                      TextSpan(
                                        text: getRemoteConfigString('tutorial_discovery3_t7'),
                                        style:
                                            FlutterFlowTheme.of(context).bodyMedium.override(
                                                  fontFamily: 'BT Beau Sans',
                                                  color: Colors.white,
                                                  letterSpacing: 0.0,
                                                  useGoogleFonts: false,
                                                ),
                                      ),
                                       TextSpan(
                                        text: getRemoteConfigString('tutorial_discovery3_t8'),
                                        style:
                                            FlutterFlowTheme.of(context).bodyMedium.override(
                                                  fontFamily: 'BT Beau Sans',
                                                  color: const Color(0xFFFF9EDE),
                                                  letterSpacing: 0.0,
                                                  useGoogleFonts: false,
                                                ),
                                      ),
                                       TextSpan(
                                        text: getRemoteConfigString('tutorial_discovery3_t9'),
                                        style:
                                            FlutterFlowTheme.of(context).bodyMedium.override(
                                                  fontFamily: 'BT Beau Sans',
                                                  color: Colors.white,
                                                  letterSpacing: 0.0,
                                                  useGoogleFonts: false,
                                                ),
                                      ),
                                      TextSpan(
                                        text: getRemoteConfigString('tutorial_discovery3_t10'),
                                        style:
                                            FlutterFlowTheme.of(context).bodyMedium.override(
                                                  fontFamily: 'BT Beau Sans',
                                                  color: const Color(0xFFFF9EDE),
                                                  letterSpacing: 0.0,
                                                  useGoogleFonts: false,
                                                ),
                                      ),
                              ]),
                                ),
                              ]
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                  Stack(
                    children: [
                      Positioned.fill(
                            child: Container(
                              color: Colors.black.withOpacity(0.8),
                            ),
                      ),
                      Align(
                        alignment: Alignment.center,
                        child: Column(
                          children: [
                            ClipRect(
                    child: Align(
                      alignment: Alignment.bottomRight,
                      child: SizedBox(
                      height: MediaQuery.of(context).size.height * 0.4,
                      width: MediaQuery.of(context).size.width * 0.6,
                      child: RiveAnimation.asset(
                          'assets/rive_animations/fs_tutorial/16-2.riv',
                          fit: BoxFit.fitWidth,
                          onInit: _onInit2,
                          alignment: Alignment.bottomRight
                        ),
                                ),
                    ),
                                 ),
                            RichText(
                              textScaler: MediaQuery.of(context).textScaler,
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                    text: getRemoteConfigString('tutorial_discovery3_t11'),
                                    style:
                                        FlutterFlowTheme.of(context).bodyMedium.override(
                                              fontFamily: 'BT Beau Sans',
                                              color: Colors.white,
                                              letterSpacing: 0.0,
                                              useGoogleFonts: false,
                                            ),
                                  ),
                              ]),
                            ),
                  ClipRect(
                    child: Align(
                      alignment: Alignment.topLeft,
                      child: SizedBox(
                      height: MediaQuery.of(context).size.height * 0.4,
                      width: MediaQuery.of(context).size.width * 0.95,
                      child: RiveAnimation.asset(
                          'assets/rive_animations/fs_tutorial/16-1.riv',
                          fit: BoxFit.fitHeight,
                          onInit: _onInit1,
                          alignment: Alignment.bottomLeft
                        ),
                                ),
                    ),
                                 ),
                          ],
                        ),
                      ),
                    ],
                  ),
                Stack(
                    children: [
                      Positioned.fill(
                            child: ClipPath(
                              clipper: MultiCutoutClipper(_cutoutRects), // Mul
                              child: Container(
                                color: Colors.black.withOpacity(0.8),
                              ),
                            ),
                      ),
                      Positioned
                      (top: cardTextBottomPosition, left: 0, right: 0,
                        child: Padding(
                          padding: const EdgeInsets.fromLTRB(30, 0, 30, 50),
                          child: RichText(
                                textScaler: MediaQuery.of(context).textScaler,
                                text: TextSpan(
                                  children: [
                                    TextSpan(
                                      text: getRemoteConfigString('tutorial_discovery3_t12'),
                                      style:
                                          FlutterFlowTheme.of(context).bodyMedium.override(
                                                fontFamily: 'BT Beau Sans',
                                                color: Colors.white,
                                                letterSpacing: 0.0,
                                                useGoogleFonts: false,
                                              ),
                                    ),
                                    TextSpan(
                                      text: getRemoteConfigString('tutorial_discovery3_t13'),
                                      style:
                                          FlutterFlowTheme.of(context).bodyMedium.override(
                                                fontFamily: 'BT Beau Sans',
                                                color: const Color(0xFFFF9EDE),
                                                letterSpacing: 0.0,
                                                useGoogleFonts: false,
                                              ),
                                    ),
                                    TextSpan(
                                      text: getRemoteConfigString('tutorial_discovery3_t14'),
                                      style:
                                          FlutterFlowTheme.of(context).bodyMedium.override(
                                                fontFamily: 'BT Beau Sans',
                                                color: Colors.white,
                                                letterSpacing: 0.0,
                                                useGoogleFonts: false,
                                              ),
                                    ),
                                    TextSpan(
                                      text: getRemoteConfigString('tutorial_discovery3_t15'),
                                      style:
                                          FlutterFlowTheme.of(context).bodyMedium.override(
                                                fontFamily: 'BT Beau Sans',
                                                color: const Color(0xFFFF9EDE),
                                                letterSpacing: 0.0,
                                                useGoogleFonts: false,
                                              ),
                                    ),
                                    TextSpan(
                                      text: getRemoteConfigString('tutorial_discovery3_t16'),
                                      style:
                                          FlutterFlowTheme.of(context).bodyMedium.override(
                                                fontFamily: 'BT Beau Sans',
                                                color: Colors.white,
                                                letterSpacing: 0.0,
                                                useGoogleFonts: false,
                                              ),
                                    ),
                                    TextSpan(
                                      text: getRemoteConfigString('tutorial_discovery3_t17'),
                                      style:
                                          FlutterFlowTheme.of(context).bodyMedium.override(
                                                fontFamily: 'BT Beau Sans',
                                                color: const Color(0xFFFF9EDE),
                                                letterSpacing: 0.0,
                                                useGoogleFonts: false,
                                              ),
                                    ),
                            ]),
                              ),
                        ),
                      ),
                      
                    ],
                  ),
                ],
              ),
            ),
          ),
          if (!secondPage)
          Positioned(
                left: 0,
                top: cardTextBottomPosition + 60,
                 child: ClipRect(
                  child: SizedBox(
                  height: 100,
                  width: 100,
                  child: RiveAnimation.asset(
                      'assets/rive_animations/fs_tutorial/10-13-14-15-1.riv',
                      fit: BoxFit.fitHeight,
                      onInit: _onInit1,
                      alignment: Alignment.topRight
                    ),
                            ),
                               ),
               ),
          
          if (!secondPage)
          Positioned(
                right: 0,
                top: cardBottomPosition - 20,
                 child: ClipRect(
                  child: SizedBox(
                  height: 100,
                  width: 100,
                  child: RiveAnimation.asset(
                      'assets/rive_animations/fs_tutorial/10-13-14-15-2.riv',
                      fit: BoxFit.fitWidth,
                      onInit: _onInit1,
                      alignment: Alignment.topRight
                    ),
                            ),
                               ),
               ),
               SafeArea(
          child: Align(
                      alignment: Alignment.bottomLeft,
                      child: Padding(
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(0.0, 50.0, 30.0, 0.0),
                        child: FFButtonWidget(
                          onPressed: () {
                            logSkippedIntro(runtimeType.toString());
                            GoRouter.of(context).goNamed('Discovery');
                          },
                          text: 'Skip',
                          options: FFButtonOptions(
                            height: 40.0,
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                16.0, 0.0, 16.0, 0.0),
                            iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 0.0, 0.0, 0.0),
                            color: const Color(0x004B39EF),
                            textStyle:
                                FlutterFlowTheme.of(context).titleSmall.override(
                                      fontFamily: 'BT Beau Sans',
                                      color: Colors.white,
                                      fontSize: 10.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.normal,
                                      useGoogleFonts: false,
                                    ),
                            elevation: 0.0,
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                        ),
                      ),
                    ),
        ),
          
        ],
      ),
    );
  }
}

class MultiCutoutClipper extends CustomClipper<Path> {
  final List<RRect> cutoutRects; // List of rounded rectangles

  MultiCutoutClipper(this.cutoutRects);

  @override
  Path getClip(Size size) {
    Path path = Path();

    // Full screen rectangle
    path.addRect(Rect.fromLTWH(0, 0, size.width, size.height));

    // Subtract each cutout rectangle from the path
    for (final cutout in cutoutRects) {
      path = Path.combine(
        PathOperation.difference,
        path,
        Path()..addRRect(cutout),
      );
    }

    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return true; // Reclip if the cutouts change
  }
}


class _IgnorePointerWithSemantics extends SingleChildRenderObjectWidget {
  const _IgnorePointerWithSemantics({
    super.child,
    required this.ignore,
  });

  /// Determines whether the pointer events should be ignored.
  final bool ignore;

  @override
  _RenderIgnorePointerWithSemantics createRenderObject(BuildContext context) {
    return _RenderIgnorePointerWithSemantics(ignore: ignore);
  }

  @override
  void updateRenderObject(
      BuildContext context, _RenderIgnorePointerWithSemantics renderObject) {
    renderObject.ignore = ignore;
  }
}

class _RenderIgnorePointerWithSemantics extends RenderProxyBox {
  _RenderIgnorePointerWithSemantics({required bool ignore}) : _ignore = ignore;

  bool _ignore;

  bool get ignore => _ignore;
  set ignore(bool value) {
    if (_ignore == value) return;
    _ignore = value;
    markNeedsSemanticsUpdate(); // Update semantics when the ignore state changes.
  }

  @override
  bool hitTest(BoxHitTestResult result, {required Offset position}) {
    // Use the ignore property to determine whether to process hit tests.
    return !ignore && super.hitTest(result, position: position);
  }
}


