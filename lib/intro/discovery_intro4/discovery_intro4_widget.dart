import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import '/intro/intro_card1/intro_card1_widget.dart';
import 'package:rive/rive.dart' hide LinearGradient, Image;
import 'package:badges/badges.dart' as badges;
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'discovery_intro4_model.dart';
import 'package:chyrpe/intro/intro_amplitude_commons.dart';
export 'discovery_intro4_model.dart';

class DiscoveryIntro4Widget extends StatefulWidget {
  const DiscoveryIntro4Widget({super.key});

  @override
  State<DiscoveryIntro4Widget> createState() => _DiscoveryIntro4WidgetState();
}

class _DiscoveryIntro4WidgetState extends State<DiscoveryIntro4Widget> {
  late DiscoveryIntro4Model _model;
  final GlobalKey discoveryIntro4ChatBtn = GlobalKey();
  List<RRect> _cutoutRects = [];

  double opacityText1 = 0.0;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => DiscoveryIntro4Model());

    logPageVisitIntro(runtimeType.toString());

    Future.delayed(const Duration(milliseconds: 400), () {
      _calculateCutouts();
      calculatePositionBtn();
    });

    Future.delayed(const Duration(milliseconds: 1800), () {
      setState(() {
      setState(() {
          opacityText1 = 1;
        },); 
        _riveController2.isActive = true;
      });
    });
  }

  late StateMachineController _riveController1;
  late StateMachineController _riveController2;

  void _onInit1(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    art.addController(ctrl);
    _riveController1 = ctrl;
      _riveController1.isActive = true;
      }
     // Keep it inactive initially
  }

  void _onInit2(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    art.addController(ctrl);
    _riveController2 = ctrl;
      _riveController2.isActive = false;
      }
     // Keep it inactive initially
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  final int _currentCutoutStep = 0;
  var btnTextBottomPosition = 0.0;

  void calculatePositionBtn() {
    final RenderBox? renderBox =
        discoveryIntro4ChatBtn.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      final Offset offset = renderBox.localToGlobal(Offset.zero);
      final double elementBottom = offset.dy;
      setState(() {
        btnTextBottomPosition = elementBottom - 80; // 10px below the bottom
      });
    }
  }

  void _calculateCutouts() {
    List<RRect> rects = [];

    if (_currentCutoutStep >= 0 &&
        discoveryIntro4ChatBtn.currentContext != null) {
      // Only add profile picture cutout when step is 1
      final RenderBox profileBox = discoveryIntro4ChatBtn.currentContext!
          .findRenderObject() as RenderBox;
      final Size profileSize = profileBox.size;
      final Offset profilePosition = profileBox.localToGlobal(Offset.zero);
      rects.add(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(
            profilePosition.dx,
            profilePosition.dy,
            profileSize.width,
            profileSize.height,
          ),
          const Radius.circular(100),
        ),
      );

      setState(() {
        _cutoutRects = rects;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Stack(children: [
        Scaffold(
          key: scaffoldKey,
          backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
          appBar: AppBar(
            backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
            automaticallyImplyLeading: false,
            title: Align(
              alignment: const AlignmentDirectional(0.0, 0.0),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Align(
                    alignment: const AlignmentDirectional(0.0, 0.0),
                    child: badges.Badge(
                      badgeContent: Text(
                        '1',
                        style: FlutterFlowTheme.of(context).titleSmall.override(
                              fontFamily: 'BT Beau Sans',
                              color: const Color(0xFFFF2551),
                              fontSize: 12.0,
                              letterSpacing: 0.0,
                              useGoogleFonts: false,
                            ),
                      ),
                      showBadge: false,
                      shape: badges.BadgeShape.circle,
                      badgeColor: const Color(0xFFFF2551),
                      elevation: 0.0,
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(3.0, 3.0, 3.0, 3.0),
                      position: badges.BadgePosition.topEnd(),
                      animationType: badges.BadgeAnimationType.scale,
                      toAnimate: true,
                      child: Align(
                        alignment: const AlignmentDirectional(0.0, 0.0),
                        child: FlutterFlowIconButton(
                          borderColor: const Color(0x004B39EF),
                          borderRadius: 20.0,
                          borderWidth: 1.0,
                          buttonSize: 40.0,
                          fillColor: const Color(0x004B39EF),
                          icon: Icon(
                            FFIcons.knotification,
                            color: FlutterFlowTheme.of(context).primaryText,
                            size: 30.0,
                          ),
                          showLoadingIndicator: true,
                          onPressed: () {
                            print('IconButton pressed ...');
                          },
                        ),
                      ),
                    ),
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8.0),
                        child: SvgPicture.asset(
                          'assets/images/ColorfulLogoA.svg',
                          width: 150.0,
                          height: 28.0,
                          fit: BoxFit.contain,
                        ),
                      ),
                    ],
                  ),
                  Align(
                    alignment: const AlignmentDirectional(1.0, 0.0),
                    child: FlutterFlowIconButton(
                      borderColor: const Color(0x004B39EF),
                      borderRadius: 20.0,
                      borderWidth: 1.0,
                      buttonSize: 40.0,
                      fillColor: const Color(0x004B39EF),
                      icon: Icon(
                        FFIcons.kfilter,
                        color: FlutterFlowTheme.of(context).primaryText,
                        size: 30.0,
                      ),
                      onPressed: () {
                        print('IconButton pressed ...');
                      },
                    ),
                  ),
                ],
              ),
            ),
            actions: const [],
            centerTitle: false,
            elevation: 0.0,
          ),
          body: SafeArea(
            top: true,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(
                  child: Container(
                    width: MediaQuery.sizeOf(context).width * 1.0,
                    height: MediaQuery.sizeOf(context).height * 1.0,
                    decoration: const BoxDecoration(),
                    child: StreamBuilder<PublicProfileRecord>(
                    stream: PublicProfileRecord.getDocument(functions
                        .getPublicProfileUidFromJSON(
                            getRemoteConfigString('intro_test_data'))
                        .lastOrNull!
                        .publicProfile!),
                    builder: (context, snapshot) {
                        if (!snapshot.hasData) {
                          return Container();
                        }
                        
                        final introCard1PublicProfileRecord = snapshot.data!;

                        return wrapWithModel(
                          model: _model.introCard1Model,
                          updateCallback: () => safeSetState(() {}),
                          child: IntroCard1Widget(publicProfile: introCard1PublicProfileRecord),
                        );
                      }
                    ),
                  ),
                ),
                
                Container(
                  width: MediaQuery.sizeOf(context).width * 1.0,
                  height: 70.0,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                  ),
                  child: Padding(
                    padding:
                        const EdgeInsetsDirectional.fromSTEB(20.0, 0.0, 20.0, 0.0),
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Align(
                          alignment: const AlignmentDirectional(0.0, 0.0),
                          child: FlutterFlowIconButton(
                            borderRadius: 50.0,
                            buttonSize: 60.0,
                            fillColor: Colors.white,
                            icon: const Icon(
                              FFIcons.kcards3,
                              color: Color(0xFF747E90),
                              size: 39.0,
                            ),
                            onPressed: () {
                              print('IconButton pressed ...');
                            },
                          ),
                        ),
                        FlutterFlowIconButton(
                          borderColor: Colors.transparent,
                          borderRadius: 100.0,
                          buttonSize: 60.0,
                          fillColor: Colors.white,
                          icon: const Icon(
                            FFIcons.ksolarSystem,
                            color: Color(0xFFD8DBE0),
                            size: 33.0,
                          ),
                          onPressed: () {
                            print('IconButton pressed ...');
                          },
                        ),
                        FlutterFlowIconButton(
                          borderColor: Colors.transparent,
                          borderRadius: 100.0,
                          buttonSize: 60.0,
                          fillColor: Colors.white,
                          icon: const Icon(
                            FFIcons.ksparkles1,
                            color: Color(0xFFD8DBE0),
                            size: 32.0,
                          ),
                          onPressed: () {
                            print('IconButton pressed ...');
                          },
                        ),
                        FlutterFlowIconButton(
                          key: discoveryIntro4ChatBtn,
                          borderColor: Colors.transparent,
                          borderRadius: 100.0,
                          buttonSize: 60.0,
                          fillColor: Colors.white,
                          icon: const Icon(
                            FFIcons.kchat,
                            color: Color(0xFFD8DBE0),
                            size: 30.0,
                          ),
                          onPressed: () {
                            GoRouter.of(context).pushReplacementNamed("ChatIntro");
                          },
                        ),
                        FlutterFlowIconButton(
                          borderColor: Colors.transparent,
                          borderRadius: 100.0,
                          buttonSize: 60.0,
                          fillColor: Colors.white,
                          icon: const Icon(
                            FFIcons.kprofile,
                            color: Color(0xFFD8DBE0),
                            size: 32.0,
                          ),
                          onPressed: () async {
                           
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        Positioned.fill(
          child: ClipPath(
            clipper: MultiCutoutClipper(_cutoutRects), // Mul
            child: Container(
              color: Colors.black.withOpacity(0.8),
            ),
          ),
        ),
        Positioned(
          bottom: (MediaQuery.of(context).size.height - btnTextBottomPosition + 10),
          child: ClipRect(
                    child: SizedBox(
                    height: MediaQuery.of(context).size.height * 0.7,
                    width: MediaQuery.of(context).size.width,
                    child: RiveAnimation.asset(
                        'assets/rive_animations/fs_tutorial/21-1.riv',
                        fit: BoxFit.fitHeight,
                        onInit: _onInit1,
                        alignment: Alignment.centerLeft
                      ),
                              ),
                                 ),
        ),
        Positioned(
          top: btnTextBottomPosition + 45,
          right: 0,
          child: ClipRect(
                    child: SizedBox(
                    height: 50,
                    width: 100,
                    child: RiveAnimation.asset(
                        'assets/rive_animations/fs_tutorial/21-2.riv',
                        fit: BoxFit.fitHeight,
                        onInit: _onInit2,
                        alignment: Alignment.centerLeft
                      ),
                              ),
                                 ),
        ),
        Positioned(
          top: btnTextBottomPosition,
          left: 0,
          right: 0,
          child: Padding(
            padding: const EdgeInsets.fromLTRB(30, 0, 30, 50),
            child: AnimatedOpacity(
              opacity: opacityText1,
              duration: const Duration(milliseconds: 300),
              child: RichText(
                textScaler: MediaQuery.of(context).textScaler,
                text: TextSpan(children: [
                  TextSpan(
                    text: getRemoteConfigString('tutorial_discovery4_t1'),
                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                          fontFamily: 'BT Beau Sans',
                          color: Colors.white,
                          letterSpacing: 0.0,
                          useGoogleFonts: false,
                        ),
                  ),
                  TextSpan(
                    text: getRemoteConfigString('tutorial_discovery4_t2'),
                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                          fontFamily: 'BT Beau Sans',
                          color: const Color(0xFFFF9EDE),
                          letterSpacing: 0.0,
                          useGoogleFonts: false,
                        ),
                  ),
                ]),
              ),
            ),
          ),
        ),
        SafeArea(
          child: Align(
                      alignment: Alignment.bottomLeft,
                      child: Padding(
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(0.0, 50.0, 30.0, 0.0),
                        child: FFButtonWidget(
                          onPressed: () {
                            logSkippedIntro(runtimeType.toString());
                            GoRouter.of(context).goNamed('Discovery');
                          },
                          text: 'Skip',
                          options: FFButtonOptions(
                            height: 40.0,
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                16.0, 0.0, 16.0, 0.0),
                            iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 0.0, 0.0, 0.0),
                            color: const Color(0x004B39EF),
                            textStyle:
                                FlutterFlowTheme.of(context).titleSmall.override(
                                      fontFamily: 'BT Beau Sans',
                                      color: Colors.white,
                                      fontSize: 10.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.normal,
                                      useGoogleFonts: false,
                                    ),
                            elevation: 0.0,
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                        ),
                      ),
                    ),
        ),
      ]),
    );
  }
}

class MultiCutoutClipper extends CustomClipper<Path> {
  final List<RRect> cutoutRects; // List of rounded rectangles

  MultiCutoutClipper(this.cutoutRects);

  @override
  Path getClip(Size size) {
    Path path = Path();

    // Full screen rectangle
    path.addRect(Rect.fromLTWH(0, 0, size.width, size.height));

    // Subtract each cutout rectangle from the path
    for (final cutout in cutoutRects) {
      path = Path.combine(
        PathOperation.difference,
        path,
        Path()..addRRect(cutout),
      );
    }

    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return true; // Reclip if the cutouts change
  }
}


