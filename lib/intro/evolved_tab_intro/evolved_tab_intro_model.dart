import '/evolved_page/evolved_tile_component/evolved_tile_component_widget.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/intro/intro_card1/intro_card1_model.dart';
import 'evolved_tab_intro_widget.dart' show EvolvedTabIntroWidget;
import 'package:flutter/material.dart';

class EvolvedTabIntroModel extends FlutterFlowModel<EvolvedTabIntroWidget> {
  ///  Local state fields for this page.

  String activeTab = 'Likes';

  ///  State fields for stateful widgets in this page.

  // Models for EvolvedTileComponent dynamic component.
  late FlutterFlowDynamicModels<EvolvedTileComponentModel>
      evolvedTileComponentModels;

    // Model for IntroCard1 component.
  late IntroCard1Model introCard1Model;
  // Stores action output result for [Firestore Query - Query a collection] action in IntroCard1 widget.

  @override
  void initState(BuildContext context) {
    evolvedTileComponentModels =
        FlutterFlowDynamicModels(() => EvolvedTileComponentModel());
    introCard1Model = IntroCard1Model();
  }

  @override
  void dispose() {
    evolvedTileComponentModels.dispose();
  }
}
