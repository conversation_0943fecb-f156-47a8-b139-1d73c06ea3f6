import '/backend/backend.dart';
import '/evolved_page/evolved_tile_component/evolved_tile_component_widget.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/intro/intro_card1/intro_card1_widget.dart';
import 'package:rive/rive.dart' hide LinearGradient, Image;
import 'package:badges/badges.dart' as badges;
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'evolved_tab_intro_model.dart';
import 'package:flutter/rendering.dart';
import 'package:chyrpe/intro/intro_amplitude_commons.dart';
export 'evolved_tab_intro_model.dart';

class EvolvedTabIntroWidget extends StatefulWidget {
  const EvolvedTabIntroWidget({super.key});

  @override
  State<EvolvedTabIntroWidget> createState() => _EvolvedTabIntroWidgetState();
}


class _EvolvedTabIntroWidgetState extends State<EvolvedTabIntroWidget> {
  late EvolvedTabIntroModel _model;
  final GlobalKey evolvedTabBtn = GlobalKey(); 
  final GlobalKey evolvedRow1 = GlobalKey(); 

  late StateMachineController _riveController1;
  late StateMachineController _riveController2;
  late StateMachineController _riveController3;

  var profileStream = PublicProfileRecord.getDocument(functions
                              .getPublicProfileUidFromJSON(
                                  getRemoteConfigString('intro_test_data'))
                              .lastOrNull!
                              .publicProfile!);

  int currentPage = 0;

  void _onInit1(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    art.addController(ctrl);
    _riveController1 = ctrl;
      _riveController1.isActive = true;
      }
     // Keep it inactive initially
  }

  void _onInit2(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    Future.delayed(const Duration(milliseconds: 1600), () {
      setState(() {
        opacityText1 = 1.0;
      });
    });
    Future.delayed(const Duration(milliseconds: 1800), () {
       art.addController(ctrl);
    _riveController2 = ctrl;
      _riveController2.isActive = true;
    });
      }
     // Keep it inactive initially
  }

  void _onInit3(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
     Future.delayed(const Duration(milliseconds: 800), () {
     setState(() {
       opacityText3 = 1.0;
     });
    });
    Future.delayed(const Duration(milliseconds: 1000), () {
       art.addController(ctrl);
    _riveController3 = ctrl;
      _riveController3.isActive = true;
    });
      }
     // Keep it inactive initially
  }

  List<RRect> _cutoutRects = [];

  final _pageController = PageController();

  final scaffoldKey = GlobalKey<ScaffoldState>();
  final scaffoldKey2 = GlobalKey<ScaffoldState>();
  final scaffoldKey3 = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => EvolvedTabIntroModel());

    logPageVisitIntro(runtimeType.toString());

    WidgetsBinding.instance.addPostFrameCallback((_) {

      Future.delayed(const Duration(milliseconds: 200), () {
        
      _calculateCutouts();
      calculatePositionEvolvedTabBtn();
      calculatePositionEvolvedRow();
      });

  });
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  int _currentCutoutStep = 0;
  var evolvedTabBtnPosition = 0.0;
  var evolvedRowPosition = 0.0;
  double opacityText1 = 0.0;
  double opacityText2 = 0.0;
  double opacityText3 = 0.0;

  void _advanceCutouts() {
  setState(() {
    _currentCutoutStep++;
    _calculateCutouts();
  });
}

void calculatePositionEvolvedTabBtn() {
    final RenderBox? renderBox = evolvedTabBtn.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      final Offset offset = renderBox.localToGlobal(Offset.zero);
      final double elementBottom = offset.dy;
      setState(() {
        evolvedTabBtnPosition = elementBottom - 80; // 10px below the bottom
      });
    }
  }

void calculatePositionEvolvedRow() {
    final RenderBox? renderBox = evolvedRow1.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      final Offset offset = renderBox.localToGlobal(Offset.zero);
      final double elementBottom = offset.dy;
      setState(() {
        evolvedRowPosition = elementBottom + 80; // 10px below the bottom
      });
    }
  }

  void _calculateCutouts() {
  List<RRect> rects = [];

  
  if (_currentCutoutStep == 0 && evolvedTabBtn.currentContext != null) {
    // Only add profile picture cutout when step is 1
    final RenderBox profileBox = evolvedTabBtn.currentContext!.findRenderObject() as RenderBox;
    final Size profileSize = profileBox.size;
    final Offset profilePosition = profileBox.localToGlobal(Offset.zero);
    rects.add(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(
          profilePosition.dx,
          profilePosition.dy,
          profileSize.width,
          profileSize.height,
        ),
        const Radius.circular(100),
      ),
    );
  }

   if (_currentCutoutStep == 1 && evolvedRow1.currentContext != null) {
    // Only add profile picture cutout when step is 1
    final RenderBox profileBox = evolvedRow1.currentContext!.findRenderObject() as RenderBox;
    final Size profileSize = profileBox.size;
    final Offset profilePosition = profileBox.localToGlobal(Offset.zero);
    rects.add(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(
          profilePosition.dx,
          profilePosition.dy,
          profileSize.width,
          profileSize.height,
        ),
        const Radius.circular(15),
      ),
    );
}
setState(() {
    _cutoutRects = rects;
  });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Stack(
        children: [

          // Container(height: MediaQuery.of(context).size.height, width: MediaQuery.of(context).size.width, color: Colors.white),

          Opacity(
            opacity: _currentCutoutStep == 2 ? 1 : 0,
            child: Scaffold(
                            key: scaffoldKey3,
                            backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
                            appBar: AppBar(
                    backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
                    automaticallyImplyLeading: false,
                    title: Align(
                      alignment: const AlignmentDirectional(0.0, 0.0),
                      child: Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Align(
                            alignment: const AlignmentDirectional(0.0, 0.0),
                            child: badges.Badge(
                              badgeContent: Text(
                                '1',
                                style: FlutterFlowTheme.of(context).titleSmall.override(
                                      fontFamily: 'BT Beau Sans',
                                      color: const Color(0xFFFF2551),
                                      fontSize: 12.0,
                                      letterSpacing: 0.0,
                                      useGoogleFonts: false,
                                    ),
                              ),
                              showBadge: false,
                              shape: badges.BadgeShape.circle,
                              badgeColor: const Color(0xFFFF2551),
                              elevation: 0.0,
                              padding: const EdgeInsetsDirectional.fromSTEB(3.0, 3.0, 3.0, 3.0),
                              position: badges.BadgePosition.topEnd(),
                              animationType: badges.BadgeAnimationType.scale,
                              toAnimate: true,
                              child: Align(
                                alignment: const AlignmentDirectional(0.0, 0.0),
                                child: FlutterFlowIconButton(
                                  borderColor: const Color(0x004B39EF),
                                  borderRadius: 20.0,
                                  borderWidth: 1.0,
                                  buttonSize: 40.0,
                                  fillColor: const Color(0x004B39EF),
                                  icon: Icon(
                                    FFIcons.knotification,
                                    color: FlutterFlowTheme.of(context).primaryText,
                                    size: 30.0,
                                  ),
                                  showLoadingIndicator: true,
                                  onPressed: () {
                                    print('IconButton pressed ...');
                                  },
                                ),
                              ),
                            ),
                          ),
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              ClipRRect(
                                borderRadius: BorderRadius.circular(8.0),
                                child: SvgPicture.asset(
                                  'assets/images/ColorfulLogoA.svg',
                                  width: 150.0,
                                  height: 28.0,
                                  fit: BoxFit.contain,
                                ),
                              ),
                            ],
                          ),
                          Align(
                            alignment: const AlignmentDirectional(1.0, 0.0),
                            child: FlutterFlowIconButton(
                              borderColor: const Color(0x004B39EF),
                              borderRadius: 20.0,
                              borderWidth: 1.0,
                              buttonSize: 40.0,
                              fillColor: const Color(0x004B39EF),
                              icon: Icon(
                                FFIcons.kfilter,
                                color: FlutterFlowTheme.of(context).primaryText,
                                size: 30.0,
                              ),
                              onPressed: () {
                                print('IconButton pressed ...');
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                    actions: const [],
                    centerTitle: false,
                    elevation: 0.0,
                            ),
                            body: SafeArea(
                    top: true,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Flexible(
                          child: StreamBuilder<PublicProfileRecord>(
                          stream: profileStream,
                          builder: (context, snapshot) {
                              if (!snapshot.hasData) {
                                return Container();
                              }
                        
                        final introCard1PublicProfileRecord = snapshot.data!;
                              return Container(
                                width: MediaQuery.sizeOf(context).width * 1.0,
                                height: MediaQuery.sizeOf(context).height * 1.0,
                                decoration: const BoxDecoration(),
                                child: wrapWithModel(
                                  model: _model.introCard1Model,
                                  updateCallback: () => safeSetState(() {}),
                                  child: IntroCard1Widget(publicProfile: introCard1PublicProfileRecord,),
                                ),
                              );
                            }
                          ),
                        ),
                        Container(
                          width: MediaQuery.sizeOf(context).width * 1.0,
                          height: 70.0,
                          decoration: const BoxDecoration(
                            color: Colors.white,
                          ),
                          child: Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(20.0, 0.0, 20.0, 0.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Align(
                                  alignment: const AlignmentDirectional(0.0, 0.0),
                                  child: FlutterFlowIconButton(
                                    borderRadius: 50.0,
                                    buttonSize: 60.0,
                                    fillColor: Colors.white,
                                    icon: const Icon(
                                      FFIcons.kcards3,
                                      color: Color(0xFF747E90),
                                      size: 39.0,
                                    ),
                                    onPressed: () {
                                      print('IconButton pressed ...');
                                    },
                                  ),
                                ),
                                FlutterFlowIconButton(
                                  borderColor: Colors.transparent,
                                  borderRadius: 100.0,
                                  buttonSize: 60.0,
                                  fillColor: Colors.white,
                                  icon: const Icon(
                                    FFIcons.ksolarSystem,
                                    color: Color(0xFFD8DBE0),
                                    size: 33.0,
                                  ),
                                  onPressed: () {
                                    print('IconButton pressed ...');
                                  },
                                ),
                                FlutterFlowIconButton(
                                  borderColor: Colors.transparent,
                                  borderRadius: 100.0,
                                  buttonSize: 60.0,
                                  fillColor: Colors.white,
                                  icon: const Icon(
                                    FFIcons.ksparkles1,
                                    color: Color(0xFFD8DBE0),
                                    size: 32.0,
                                  ),
                                  onPressed: () {
                                    print('IconButton pressed ...');
                                  },
                                ),
                                FlutterFlowIconButton(
                                  borderColor: Colors.transparent,
                                  borderRadius: 100.0,
                                  buttonSize: 60.0,
                                  fillColor: Colors.white,
                                  icon: const Icon(
                                    FFIcons.kchat,
                                    color: Color(0xFFD8DBE0),
                                    size: 30.0,
                                  ),
                                  onPressed: () {
                                    print('IconButton pressed ...');
                                  },
                                ),
                                FlutterFlowIconButton(
                                  borderColor: Colors.transparent,
                                  borderRadius: 100.0,
                                  buttonSize: 60.0,
                                  fillColor: Colors.white,
                                  icon: const Icon(
                                    FFIcons.kprofile,
                                    color: Color(0xFFD8DBE0),
                                    size: 32.0,
                                  ),
                                  onPressed: () async {
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                            ),
                          ),
          ),
          
          Opacity(
            opacity: _currentCutoutStep == 1 ? 1 : 0,
            child: Scaffold(
                      key: scaffoldKey2,
                      backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
                      appBar: AppBar(
                        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
                        automaticallyImplyLeading: false,
                        title: Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(8.0),
                              child: SvgPicture.asset(
                                'assets/images/ColorfulLogoA.svg',
                                width: 150.0,
                                height: 28.0,
                                fit: BoxFit.contain,
                              ),
                            ),
                          ],
                        ),
                        actions: const [],
                        flexibleSpace: FlexibleSpaceBar(
                          background: Container(
                            width: 100.0,
                            height: 100.0,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  const Color(0xFFDBAEFF),
                                  FlutterFlowTheme.of(context).primaryBackground
                                ],
                                stops: const [0.0, 1.0],
                                begin: const AlignmentDirectional(0.0, -1.0),
                                end: const AlignmentDirectional(0, 1.0),
                              ),
                            ),
                          ),
                        ),
                        bottom: PreferredSize(
                          preferredSize: const Size.fromHeight(70.0),
                          child: Row(
                            key: evolvedRow1,
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              Expanded(
                                child: Text(
                                  'Likes',
                                  textAlign: TextAlign.center,
                                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                                        fontFamily: 'BT Beau Sans',
                                        color: _model.activeTab == 'Likes'
                                            ? FlutterFlowTheme.of(context).primaryText
                                            : const Color(0xFF7D848F),
                                        fontSize: 14.0,
                                        letterSpacing: 0.0,
                                        fontWeight: FontWeight.bold,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                              SizedBox(
                                height: 43.0,
                                child: VerticalDivider(
                                  thickness: 1.0,
                                  color: FlutterFlowTheme.of(context).secondaryText,
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  'Likes Sent',
                                  textAlign: TextAlign.center,
                                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                                        fontFamily: 'BT Beau Sans',
                                        color: _model.activeTab == 'LikesSent'
                                            ? FlutterFlowTheme.of(context).primaryText
                                            : const Color(0xFF7D848F),
                                        fontSize: 14.0,
                                        letterSpacing: 0.0,
                                        fontWeight: FontWeight.bold,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                              SizedBox(
                                height: 43.0,
                                child: VerticalDivider(
                                  thickness: 1.0,
                                  color: FlutterFlowTheme.of(context).secondaryText,
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  'Saved',
                                  textAlign: TextAlign.center,
                                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                                        fontFamily: 'BT Beau Sans',
                                        color: _model.activeTab == 'Saved'
                                            ? FlutterFlowTheme.of(context).primaryText
                                            : const Color(0xFF7D848F),
                                        fontSize: 14.0,
                                        letterSpacing: 0.0,
                                        fontWeight: FontWeight.bold,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        centerTitle: false,
                        elevation: 2.0,
                      ),
                      body: SafeArea(
                        top: true,
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Flexible(
                              child: SizedBox(
                                width: MediaQuery.sizeOf(context).width * 1.0,
                                child: Stack(
                                  alignment: const AlignmentDirectional(0.0, 1.0),
                                  children: [
                                    Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Container(
                                          width: MediaQuery.sizeOf(context).width * 1.0,
                                          height: MediaQuery.sizeOf(context).height * 0.8,
                                          decoration: BoxDecoration(
                                            color: FlutterFlowTheme.of(context)
                                                .primaryBackground,
                                          ),
                                          child: Column(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Flexible(
                                                child: Padding(
                                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                                      3.0, 0.0, 3.0, 0.0),
                                                  child: SingleChildScrollView(
                                                    child: Column(
                                                      mainAxisSize: MainAxisSize.min,
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment.center,
                                                      children: [
                                                        Flexible(
                                                          child: Builder(
                                                            builder: (context) {
                                                              final likesList = functions
                                                                  .getPublicProfileUidFromJSON(
                                                                      getRemoteConfigString(
                                                                          'intro_test_data'))
                                                                  .toList();
                    
                                                              return Wrap(
                                                                spacing: 0.0,
                                                                runSpacing: 11.0,
                                                                alignment:
                                                                    WrapAlignment.start,
                                                                crossAxisAlignment:
                                                                    WrapCrossAlignment.start,
                                                                direction: Axis.horizontal,
                                                                runAlignment:
                                                                    WrapAlignment.start,
                                                                verticalDirection:
                                                                    VerticalDirection.down,
                                                                clipBehavior: Clip.none,
                                                                children: List.generate(
                                                                    likesList.length,
                                                                    (likesListIndex) {
                                                                  final likesListItem =
                                                                      likesList[
                                                                          likesListIndex];
                                                                  return Container(
                                                                    constraints:
                                                                        BoxConstraints(
                                                                      maxWidth:
                                                                          MediaQuery.sizeOf(
                                                                                      context)
                                                                                  .width *
                                                                              0.48,
                                                                    ),
                                                                    decoration:
                                                                        const BoxDecoration(),
                                                                    child: StreamBuilder<
                                                                        PublicProfileRecord>(
                                                                      stream: PublicProfileRecord
                                                                          .getDocument(
                                                                              likesListItem
                                                                                  .publicProfile!),
                                                                      builder: (context,
                                                                          snapshot) {
                                                                        // Customize what your widget looks like when it's loading.
                                                                        if (!snapshot
                                                                            .hasData) {
                                                                          return Center(
                                                                            child: SizedBox(
                                                                              width: 50.0,
                                                                              height: 50.0,
                                                                              child:
                                                                                  CircularProgressIndicator(
                                                                                valueColor:
                                                                                    AlwaysStoppedAnimation<
                                                                                        Color>(
                                                                                  FlutterFlowTheme.of(
                                                                                          context)
                                                                                      .accent2,
                                                                                ),
                                                                              ),
                                                                            ),
                                                                          );
                                                                        }
                    
                                                                        final evolvedTileComponentPublicProfileRecord =
                                                                            snapshot.data!;
                    
                                                                        return wrapWithModel(
                                                                          model: _model
                                                                              .evolvedTileComponentModels
                                                                              .getModel(
                                                                            likesListItem.uid,
                                                                            likesListIndex,
                                                                          ),
                                                                          updateCallback: () =>
                                                                              safeSetState(
                                                                                  () {}),
                                                                          updateOnChange:
                                                                              true,
                                                                          child:
                                                                              EvolvedTileComponentWidget(
                                                                            key: Key(
                                                                              'Key4ap_${likesListItem.uid}',
                                                                            ),
                                                                            message:
                                                                                'Liked your profile',
                                                                            showMessage:
                                                                                false,
                                                                            publicProfile:
                                                                                evolvedTileComponentPublicProfileRecord,
                                                                          ),
                                                                        );
                                                                      },
                                                                    ),
                                                                  );
                                                                }),
                                                              );
                                                            },
                                                          ),
                                                        ),
                                                      ]
                                                          .addToStart(const SizedBox(height: 20.0))
                                                          .addToEnd(const SizedBox(height: 400.0)),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            Container(
                              width: MediaQuery.sizeOf(context).width * 1.0,
                              height: 70.0,
                              decoration: const BoxDecoration(
                                color: Colors.white,
                              ),
                              child: Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(20.0, 0.0, 20.0, 0.0),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Align(
                                      alignment: const AlignmentDirectional(0.0, 0.0),
                                      child: FlutterFlowIconButton(
                                        borderColor: Colors.transparent,
                                        borderRadius: 50.0,
                                        buttonSize: 60.0,
                                        fillColor: Colors.white,
                                        icon: const Icon(
                                          FFIcons.kcards3,
                                          color: Color(0xFFD8DBE0),
                                          size: 39.0,
                                        ),
                                        onPressed: () {
                                          print('IconButton pressed ...');
                                        },
                                      ),
                                    ),
                                    FlutterFlowIconButton(
                                      borderColor: Colors.transparent,
                                      borderRadius: 100.0,
                                      buttonSize: 60.0,
                                      fillColor: Colors.white,
                                      icon: const Icon(
                                        FFIcons.ksolarSystem,
                                        color: Color(0xFFD8DBE0),
                                        size: 33.0,
                                      ),
                                      onPressed: () {
                                        print('IconButton pressed ...');
                                      },
                                    ),
                                    FlutterFlowIconButton(
                                      borderColor: Colors.transparent,
                                      borderRadius: 100.0,
                                      buttonSize: 60.0,
                                      fillColor: Colors.white,
                                      icon: const Icon(
                                        FFIcons.ksparkles1Filled,
                                        color: Color(0xFF747E90),
                                        size: 32.0,
                                      ),
                                      onPressed: () {
                                        print('IconButton pressed ...');
                                      },
                                    ),
                                    FlutterFlowIconButton(
                                      borderColor: Colors.transparent,
                                      borderRadius: 100.0,
                                      buttonSize: 60.0,
                                      fillColor: Colors.white,
                                      icon: const Icon(
                                        FFIcons.kchat,
                                        color: Color(0xFFD8DBE0),
                                        size: 30.0,
                                      ),
                                      onPressed: () {
                                        print('IconButton pressed ...');
                                      },
                                    ),
                                    FlutterFlowIconButton(
                                      borderColor: Colors.transparent,
                                      borderRadius: 100.0,
                                      buttonSize: 60.0,
                                      fillColor: Colors.white,
                                      icon: const Icon(
                                        FFIcons.kprofile,
                                        color: Color(0xFFD8DBE0),
                                        size: 32.0,
                                      ),
                                      onPressed: () async {
                                      
                                          
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
          ),

          Opacity(
            opacity:  _currentCutoutStep == 0 ? 1 : 0,
            child: Scaffold(
                            key: scaffoldKey,
                            backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
                            appBar: AppBar(
                    backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
                    automaticallyImplyLeading: false,
                    title: Align(
                      alignment: const AlignmentDirectional(0.0, 0.0),
                      child: Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Align(
                            alignment: const AlignmentDirectional(0.0, 0.0),
                            child: badges.Badge(
                              badgeContent: Text(
                                '1',
                                style: FlutterFlowTheme.of(context).titleSmall.override(
                                      fontFamily: 'BT Beau Sans',
                                      color: const Color(0xFFFF2551),
                                      fontSize: 12.0,
                                      letterSpacing: 0.0,
                                      useGoogleFonts: false,
                                    ),
                              ),
                              showBadge: false,
                              shape: badges.BadgeShape.circle,
                              badgeColor: const Color(0xFFFF2551),
                              elevation: 0.0,
                              padding: const EdgeInsetsDirectional.fromSTEB(3.0, 3.0, 3.0, 3.0),
                              position: badges.BadgePosition.topEnd(),
                              animationType: badges.BadgeAnimationType.scale,
                              toAnimate: true,
                              child: Align(
                                alignment: const AlignmentDirectional(0.0, 0.0),
                                child: FlutterFlowIconButton(
                                  borderColor: const Color(0x004B39EF),
                                  borderRadius: 20.0,
                                  borderWidth: 1.0,
                                  buttonSize: 40.0,
                                  fillColor: const Color(0x004B39EF),
                                  icon: Icon(
                                    FFIcons.knotification,
                                    color: FlutterFlowTheme.of(context).primaryText,
                                    size: 30.0,
                                  ),
                                  showLoadingIndicator: true,
                                  onPressed: () {
                                    print('IconButton pressed ...');
                                  },
                                ),
                              ),
                            ),
                          ),
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              ClipRRect(
                                borderRadius: BorderRadius.circular(8.0),
                                child: SvgPicture.asset(
                                  'assets/images/ColorfulLogoA.svg',
                                  width: 150.0,
                                  height: 28.0,
                                  fit: BoxFit.contain,
                                ),
                              ),
                            ],
                          ),
                          Align(
                            alignment: const AlignmentDirectional(1.0, 0.0),
                            child: FlutterFlowIconButton(
                              borderColor: const Color(0x004B39EF),
                              borderRadius: 20.0,
                              borderWidth: 1.0,
                              buttonSize: 40.0,
                              fillColor: const Color(0x004B39EF),
                              icon: Icon(
                                FFIcons.kfilter,
                                color: FlutterFlowTheme.of(context).primaryText,
                                size: 30.0,
                              ),
                              onPressed: () {
                                print('IconButton pressed ...');
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                    actions: const [],
                    centerTitle: false,
                    elevation: 0.0,
                            ),
                            body: SafeArea(
                    top: true,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Flexible(
                          child: StreamBuilder<PublicProfileRecord>(
                          stream: profileStream,
                          builder: (context, snapshot) {
                              if (!snapshot.hasData) {
                                return Container();
                              }
                        
                        final introCard1PublicProfileRecord = snapshot.data!;
                              return Container(
                                width: MediaQuery.sizeOf(context).width * 1.0,
                                height: MediaQuery.sizeOf(context).height * 1.0,
                                decoration: const BoxDecoration(),
                                child: wrapWithModel(
                                  model: _model.introCard1Model,
                                  updateCallback: () => safeSetState(() {}),
                                  child: IntroCard1Widget(publicProfile: introCard1PublicProfileRecord,),
                                ),
                              );
                            }
                          ),
                        ),
                        Container(
                          width: MediaQuery.sizeOf(context).width * 1.0,
                          height: 70.0,
                          decoration: const BoxDecoration(
                            color: Colors.white,
                          ),
                          child: Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(20.0, 0.0, 20.0, 0.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Align(
                                  alignment: const AlignmentDirectional(0.0, 0.0),
                                  child: FlutterFlowIconButton(
                                    borderRadius: 50.0,
                                    buttonSize: 60.0,
                                    fillColor: Colors.white,
                                    icon: const Icon(
                                      FFIcons.kcards3,
                                      color: Color(0xFF747E90),
                                      size: 39.0,
                                    ),
                                    onPressed: () {
                                      print('IconButton pressed ...');
                                    },
                                  ),
                                ),
                                FlutterFlowIconButton(
                                  borderColor: Colors.transparent,
                                  borderRadius: 100.0,
                                  buttonSize: 60.0,
                                  fillColor: Colors.white,
                                  icon: const Icon(
                                    FFIcons.ksolarSystem,
                                    color: Color(0xFFD8DBE0),
                                    size: 33.0,
                                  ),
                                  onPressed: () {
                                    print('IconButton pressed ...');
                                  },
                                ),
                                FlutterFlowIconButton(
                                  key: evolvedTabBtn,
                                  borderColor: Colors.transparent,
                                  borderRadius: 100.0,
                                  buttonSize: 60.0,
                                  fillColor: Colors.white,
                                  icon: const Icon(
                                    FFIcons.ksparkles1,
                                    color: Color(0xFFD8DBE0),
                                    size: 32.0,
                                  ),
                                  onPressed: () {
                                    _pageController.nextPage(duration: const Duration(milliseconds: 200), curve: Curves.easeInCubic);
                                    Future.delayed(const Duration(milliseconds: 200), () {_advanceCutouts();});
                                  },
                                ),
                                FlutterFlowIconButton(
                                  borderColor: Colors.transparent,
                                  borderRadius: 100.0,
                                  buttonSize: 60.0,
                                  fillColor: Colors.white,
                                  icon: const Icon(
                                    FFIcons.kchat,
                                    color: Color(0xFFD8DBE0),
                                    size: 30.0,
                                  ),
                                  onPressed: () {
                                    print('IconButton pressed ...');
                                  },
                                ),
                                FlutterFlowIconButton(
                                  borderColor: Colors.transparent,
                                  borderRadius: 100.0,
                                  buttonSize: 60.0,
                                  fillColor: Colors.white,
                                  icon: const Icon(
                                    FFIcons.kprofile,
                                    color: Color(0xFFD8DBE0),
                                    size: 32.0,
                                  ),
                                  onPressed: () async {
                                    
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                            ),
                          ),
          ),
          

          GestureDetector(
            onTap: () {
              if (currentPage >= 2) {
                context.goNamed(
                                        'Discovery',
                                        extra: <String, dynamic>{
                                          kTransitionInfoKey: const TransitionInfo(
                                            hasTransition: true,
                                            transitionType:
                                                PageTransitionType.fade,
                                          ),
                                        },
                                      );
              }
              _pageController.nextPage(duration: const Duration(milliseconds: 200), curve: Curves.easeInCubic);
              
              Future.delayed(const Duration(milliseconds: 200), () {_advanceCutouts();});
            setState(() {
              currentPage += 1;
            },);
              Future.delayed(const Duration(milliseconds: 1400), () {
                setState(() {
                  opacityText2 = 1.0;
                },);
              });
            },
            child: PageView(
              physics: const NeverScrollableScrollPhysics(),
              controller: _pageController,
              children: [
              
            
              Stack(
                  children: [
                    
                    Positioned.fill(
                                child: ClipPath(
                                  clipper: MultiCutoutClipper(_cutoutRects), // Mul
                                  child: Container(
                                    color: Colors.black.withOpacity(0.8),
                                  ),
                                ),
                          ),
            
                    Positioned(
                      bottom: MediaQuery.of(context).size.height - evolvedTabBtnPosition,
                      left: 0,
                      child: SizedBox(
                      height: MediaQuery.of(context).size.height * 0.7,
                      width: MediaQuery.of(context).size.width * 0.9,
                      child: RiveAnimation.asset(
                          'assets/rive_animations/fs_tutorial/28-1.riv',
                          fit: BoxFit.fill,
                          onInit: _onInit1,
                          alignment: Alignment.bottomLeft
                        ),
                                ),
                    ),
            
                    Positioned
                          (top: evolvedTabBtnPosition, left: 0, right: 0,
                            child: Padding(
                              padding: const EdgeInsets.fromLTRB(30, 15, 30, 50),
                                  child: AnimatedOpacity(
                                    opacity: opacityText1,
                                    duration: const Duration(milliseconds: 300),
                                    child: RichText(
                                          textScaler: MediaQuery.of(context).textScaler,
                                          text: TextSpan(
                                            children: [
                                              TextSpan(
                                                text: getRemoteConfigString('tutorial_evolvedtab_t1'),
                                                style:
                                                    FlutterFlowTheme.of(context).bodyMedium.override(
                                                          fontFamily: 'BT Beau Sans',
                                                          color: Colors.white,
                                                          letterSpacing: 0.0,
                                                          useGoogleFonts: false,
                                                        ),
                                              ),
                                              TextSpan(
                                                text: getRemoteConfigString('tutorial_evolvedtab_t2'),
                                                style:
                                                    FlutterFlowTheme.of(context).bodyMedium.override(
                                                          fontFamily: 'BT Beau Sans',
                                                          color: const Color(0xFFFF9EDE),
                                                          letterSpacing: 0.0,
                                                          useGoogleFonts: false,
                                                        ),
                                              ),
                                                
                                              TextSpan(
                                                text: getRemoteConfigString('tutorial_evolvedtab_t3'),
                                                style:
                                                    FlutterFlowTheme.of(context).bodyMedium.override(
                                                          fontFamily: 'BT Beau Sans',
                                                          color: Colors.white,
                                                          letterSpacing: 0.0,
                                                          useGoogleFonts: false,
                                                        ),
                                              ),
                                      ]),),
                                  ),
                            ),
                          ),
            
            
                          Positioned(
                      bottom: MediaQuery.of(context).size.height - evolvedTabBtnPosition - 100,
                      right: 0,
                      child: ClipRect(
                    child: SizedBox(
                    height: 50,
                    width: MediaQuery.of(context).size.width,
                    child: RiveAnimation.asset(
                        'assets/rive_animations/fs_tutorial/28-2.riv',
                        fit: BoxFit.fitHeight,
                        onInit: _onInit2,
                        alignment: Alignment.bottomRight
                      ),
                              ),
                                 ),
                    ),
            
                  ],
                ),
              
              Stack(
                children: [
                  Positioned.fill(
                                child: ClipPath(
                                  clipper: MultiCutoutClipper(_cutoutRects), // Mul
                                  child: Container(
                                    color: Colors.black.withOpacity(0.8),
                                  ),
                                ),
                          ),
            
                  Positioned(
                      top: evolvedRowPosition + 100,
                      left: 0,
                      child: ClipRect(
                    child: SizedBox(
                    height: MediaQuery.of(context).size.height * 0.5,
                    width: MediaQuery.of(context).size.width,
                    child: RiveAnimation.asset(
                        'assets/rive_animations/fs_tutorial/29-1.riv',
                        fit: BoxFit.contain,
                        onInit: _onInit1,
                        alignment: Alignment.topLeft
                      ),
                              ),
                                 ),
                    ),
            
                  Positioned(
                      bottom: MediaQuery.of(context).size.height - evolvedRowPosition - 50,
                      right: 0,
                      child: ClipRect(
                    child: SizedBox(
                    height: 80,
                    width: 100,
                    child: RiveAnimation.asset(
                        'assets/rive_animations/fs_tutorial/29-2.riv',
                        fit: BoxFit.contain,
                        onInit: _onInit2,
                        alignment: Alignment.centerRight
                      ),
                              ),
                                 ),
                    ),
                  Positioned
                          (top: evolvedRowPosition, left: 0, right: 0,
                            child: Padding(
                              padding: const EdgeInsets.fromLTRB(30, 15, 40, 50),
                                  child: AnimatedOpacity(
                                    opacity: opacityText2,
                                    duration: const Duration(milliseconds: 300),
                                    child: RichText(
                                          textScaler: MediaQuery.of(context).textScaler,
                                          text: TextSpan(
                                            children: [
                                              TextSpan(
                                                text: getRemoteConfigString('tutorial_evolvedtab_t4'),
                                                style:
                                                    FlutterFlowTheme.of(context).bodyMedium.override(
                                                          fontFamily: 'BT Beau Sans',
                                                          color: Colors.white,
                                                          letterSpacing: 0.0,
                                                          useGoogleFonts: false,
                                                        ),
                                              ),
                                              TextSpan(
                                                text: getRemoteConfigString('tutorial_evolvedtab_t5'),
                                                style:
                                                    FlutterFlowTheme.of(context).bodyMedium.override(
                                                          fontFamily: 'BT Beau Sans',
                                                          color: const Color(0xFFFF9EDE),
                                                          letterSpacing: 0.0,
                                                          useGoogleFonts: false,
                                                        ),
                                              ),
                                                
                                              TextSpan(
                                                text: getRemoteConfigString('tutorial_evolvedtab_t6'),
                                                style:
                                                    FlutterFlowTheme.of(context).bodyMedium.override(
                                                          fontFamily: 'BT Beau Sans',
                                                          color: Colors.white,
                                                          letterSpacing: 0.0,
                                                          useGoogleFonts: false,
                                                        ),
                                              ),
                                                
                                               TextSpan(
                                                text: getRemoteConfigString('tutorial_evolvedtab_t7'),
                                                style:
                                                    FlutterFlowTheme.of(context).bodyMedium.override(
                                                          fontFamily: 'BT Beau Sans',
                                                          color: const Color(0xFFFF9EDE),
                                                          letterSpacing: 0.0,
                                                          useGoogleFonts: false,
                                                        ),
                                              ),
                                                
                                              TextSpan(
                                                text: getRemoteConfigString('tutorial_evolvedtab_t8'),
                                                style:
                                                    FlutterFlowTheme.of(context).bodyMedium.override(
                                                          fontFamily: 'BT Beau Sans',
                                                          color: Colors.white,
                                                          letterSpacing: 0.0,
                                                          useGoogleFonts: false,
                                                        ),
                                              ),
                                      ]),),
                                  ),
                            ),
                          )
                ],
              ),
              
              Stack(
              children: [
                    
                    Positioned.fill(
                                child: ClipPath(
                                  clipper: MultiCutoutClipper(_cutoutRects), // Mul
                                  child: Container(
                                    color: Colors.black.withOpacity(0.8),
                                  ),
                                ),
                          ),
            
                    Column(
                      children: [
                        ClipRect(
                        child: SizedBox(
                        height: MediaQuery.of(context).size.height * 0.3,
                        width: MediaQuery.of(context).size.width,
                        child: RiveAnimation.asset(
                            'assets/rive_animations/fs_tutorial/30-1.riv',
                            fit: BoxFit.fitHeight,
                            onInit: _onInit1,
                            alignment: Alignment.topLeft
                          ),
                                  ),
                                    ),
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Padding(
                                    padding: const EdgeInsets.fromLTRB(30, 10, 30, 10),
                                        child: AnimatedOpacity(
                                          opacity: opacityText3,
                                          duration: const Duration(milliseconds: 300),
                                          child: RichText(
                                                textScaler: MediaQuery.of(context).textScaler,
                                                text: TextSpan(
                                                  children: [
                                                    TextSpan(
                                                      text: getRemoteConfigString('tutorial_evolvedtab_t9'),
                                                      style:
                                                          FlutterFlowTheme.of(context).bodyMedium.override(
                                                                fontFamily: 'BT Beau Sans',
                                                                color: Colors.white,
                                                                letterSpacing: 0.0,
                                                                useGoogleFonts: false,
                                                              ),
                                                    ),
                                                    TextSpan(
                                                      text: getRemoteConfigString('tutorial_evolvedtab_t10'),
                                                      style:
                                                          FlutterFlowTheme.of(context).bodyMedium.override(
                                                                fontFamily: 'BT Beau Sans',
                                                                color: const Color(0xFFFF9EDE),
                                                                letterSpacing: 0.0,
                                                                useGoogleFonts: false,
                                                              ),
                                                    ),
                                                  ],
                                                ),
                                          ),
                                        ),
                          ),
                        ),
                        ClipRect(
                        child: SizedBox(
                        height: MediaQuery.of(context).size.height * 0.4,
                        width: MediaQuery.of(context).size.width,
                        child: RiveAnimation.asset(
                            'assets/rive_animations/fs_tutorial/30-2.riv',
                            fit: BoxFit.fitHeight,
                            onInit: _onInit3,
                            alignment: Alignment.topRight
                          ),
                                  ),
                                    ),
                      ],
                    ),
                    
                                  
            
                  ],
              ),
              
              ],
            ),
          ),
        ]
      ),
    );
  }
}

class MultiCutoutClipper extends CustomClipper<Path> {
  final List<RRect> cutoutRects; // List of rounded rectangles

  MultiCutoutClipper(this.cutoutRects);

  @override
  Path getClip(Size size) {
    Path path = Path();

    // Full screen rectangle
    path.addRect(Rect.fromLTWH(0, 0, size.width, size.height));

    // Subtract each cutout rectangle from the path
    for (final cutout in cutoutRects) {
      path = Path.combine(
        PathOperation.difference,
        path,
        Path()..addRRect(cutout),
      );
    }

    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return true; // Reclip if the cutouts change
  }
}

class _IgnorePointerWithSemantics extends SingleChildRenderObjectWidget {
  const _IgnorePointerWithSemantics();

  @override
  _RenderIgnorePointerWithSemantics createRenderObject(BuildContext context) {
    return _RenderIgnorePointerWithSemantics();
  }
}

class _RenderIgnorePointerWithSemantics extends RenderProxyBox {
  _RenderIgnorePointerWithSemantics();

  @override
  bool hitTest(BoxHitTestResult result, { required Offset position }) => false;
}


