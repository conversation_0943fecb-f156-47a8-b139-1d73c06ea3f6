import 'package:chyrpe/amplitudeConfig.dart';

/// Extracts a cleaned-up page name from the raw state class name.
/// Defaults to the full string if cleaning fails.
String _extractPageName(String pageName) {
  try {
    if (pageName.isEmpty) {
      throw ArgumentError('Page name cannot be empty');
    }
    return pageName.substring(1).split('Widget')[0];
  } catch (e) {
    print('Error extracting page name: $e');
    return pageName; // Fallback to the original string
  }
}

/// Logs when a tutorial page is visited.
void logPageVisitIntro(String pageName) {
  final cleanedPageName = _extractPageName(pageName);
  analytics.logEvent('Tutorial: Visited $cleanedPageName');
}

/// Logs when a tutorial page is skipped.
void logSkippedIntro(String pageName) {
  final cleanedPageName = _extractPageName(pageName);
  analytics.logEvent('Tutorial: Skipped on $cleanedPageName');
}
