import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/round_arrow_button/round_arrow_button_widget.dart';
import '/general/round_check_button/round_check_button_widget.dart';
import '/general/round_cross_button/round_cross_button_widget.dart';
import '/general/round_sparkles_button/round_sparkles_button_widget.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'intro_card2_model.dart';
export 'intro_card2_model.dart';

class IntroCard2Widget extends StatefulWidget {
  const IntroCard2Widget({
    super.key,
    bool? showSave,
    bool? showX,
    bool? showYes,
    this.callbackYes,
    this.callbackX,
    this.callbackSave,
    required this.publicProfile,
  })  : showSave = showSave ?? false,
        showX = showX ?? false,
        showYes = showYes ?? false;

  final bool showSave;
  final bool showX;
  final bool showYes;
  final Future Function()? callbackYes;
  final Future Function()? callbackX;
  final Future Function()? callbackSave;
  final PublicProfileRecord? publicProfile;

  @override
  State<IntroCard2Widget> createState() => _IntroCard2WidgetState();
}

class _IntroCard2WidgetState extends State<IntroCard2Widget> {
  late IntroCard2Model _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => IntroCard2Model());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.75),
      ),
      child: Stack(
        children: [
          SizedBox(
            width: MediaQuery.sizeOf(context).width * 1.0,
            height: MediaQuery.sizeOf(context).height * 1.0,
            child: Stack(
              children: [
                
                Padding(
                  padding: const EdgeInsets.all(0.0),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(10.75),
                    child: CachedNetworkImage(
                      fadeInDuration: const Duration(milliseconds: 0),
                      fadeOutDuration: const Duration(milliseconds: 0),
                       imageUrl: widget.publicProfile!.profilePhoto,
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                      memCacheWidth: 800,
                    ),
                  ),
                ),
                Align(
                  alignment: const AlignmentDirectional(0.0, 0.0),
                  child: Padding(
                    padding: const EdgeInsets.all(0.0),
                    child: Container(
                      width: MediaQuery.sizeOf(context).width * 1.0,
                      height: MediaQuery.sizeOf(context).height * 1.0,
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [
                            Colors.transparent,
                            Color(0x4B000000),
                            Colors.black
                          ],
                          stops: [0.0, 0.7, 1.0],
                          begin: AlignmentDirectional(0.0, -1.0),
                          end: AlignmentDirectional(0, 1.0),
                        ),
                        borderRadius: BorderRadius.circular(10.75),
                      ),
                    ),
                  ),
                ),
                Align(
                  alignment: const AlignmentDirectional(-1.0, 1.0),
                  child: Card(
                    clipBehavior: Clip.antiAliasWithSaveLayer,
                    color: Colors.transparent,
                    elevation: 0.0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.0),
                    ),
                    child: Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(17.0, 0.0, 80.0, 16.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Flexible(
                            child: Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 0.0, 0.0, 9.0),
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  AutoSizeText(
                                    valueOrDefault<String>(
                                      widget.publicProfile?.publicName,
                                      'Name',
                                    ),
                                    minFontSize: 16.0,
                                    style: FlutterFlowTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'BT Beau Sans',
                                          color:
                                              FlutterFlowTheme.of(context).info,
                                          fontSize: 32.0,
                                          letterSpacing: 0.0,
                                          fontWeight: FontWeight.w500,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        11.0, 0.0, 0.0, 0.0),
                                    child: Text(
                                       valueOrDefault<String>(
                                        widget.publicProfile?.age.toString(),
                                        'Age',
                                      ),
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            fontFamily: 'BT Beau Sans',
                                            color: FlutterFlowTheme.of(context)
                                                .info,
                                            fontSize: 24.0,
                                            letterSpacing: 0.0,
                                            useGoogleFonts: false,
                                          ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          Flexible(
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                Text(
                                  valueOrDefault<String>(
                                    widget.publicProfile?.publicRole,
                                    'Switch',
                                  ),
                                  style: FlutterFlowTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        color:
                                            FlutterFlowTheme.of(context).info,
                                        fontSize: 16.0,
                                        letterSpacing: 0.0,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ].divide(const SizedBox(width: 4.0)),
                            ),
                          ),
                          Align(
                            alignment: const AlignmentDirectional(-1.0, 1.0),
                            child: Builder(
                              builder: (context) {
                                if (true) {
                                  return Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        0.0, 0.0, 0.0, 2.0),
                                    child: AuthUserStreamWidget(
                                      builder: (context) => Text(
                                      valueOrDefault<String>(
                                        widget.publicProfile?.city,
                                        'City',),
                                        textAlign: TextAlign.start,
                                        style: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .override(
                                              fontFamily: 'BT Beau Sans',
                                              color:
                                                  FlutterFlowTheme.of(context)
                                                      .info,
                                              fontSize: 16.0,
                                              letterSpacing: 0.0,
                                              useGoogleFonts: false,
                                            ),
                                      ),
                                    ),
                                  );
                                } else {
                                  return Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        0.0, 0.0, 0.0, 2.0),
                                    child: AuthUserStreamWidget(
                                      builder: (context) => Text(
                                        valueOrDefault(
                                            currentUserDocument?.city, ''),
                                        textAlign: TextAlign.start,
                                        style: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .override(
                                              fontFamily: 'BT Beau Sans',
                                              color:
                                                  FlutterFlowTheme.of(context)
                                                      .info,
                                              fontSize: 16.0,
                                              letterSpacing: 0.0,
                                              useGoogleFonts: false,
                                            ),
                                      ),
                                    ),
                                  );
                                }
                              },
                            ),
                          ),
                          Align(
                            alignment: const AlignmentDirectional(-1.0, 0.0),
                            child: Container(
                              width: MediaQuery.sizeOf(context).width * 0.6,
                              decoration: const BoxDecoration(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Stack(
                      children: [
                        Align(
                          alignment: const AlignmentDirectional(1.0, -1.0),
                          child: Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 10.0, 10.0, 0.0),
                            child: Container(
                              width: 40.0,
                              height: 40.0,
                              decoration: BoxDecoration(
                                color: FlutterFlowTheme.of(context)
                                    .primaryBackground,
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: FlutterFlowTheme.of(context)
                                      .secondaryText,
                                ),
                              ),
                              child: Align(
                                alignment: const AlignmentDirectional(0.0, 0.0),
                                child: Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      2.0, 2.0, 2.0, 2.0),
                                  child: Icon(
                                    FFIcons.kcardWithProfile1,
                                    color: FlutterFlowTheme.of(context)
                                        .secondaryText,
                                    size: 28.0,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                        Align(
                          alignment: const AlignmentDirectional(1.0, 0.0),
                          child: Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 10.0, 10.0, 0.0),
                            child: Container(
                              width: 40.0,
                              height: 40.0,
                              decoration: const BoxDecoration(
                                color: Color(0xCD000000),
                                shape: BoxShape.circle,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    Stack(
                      children: [
                        Align(
                          alignment: const AlignmentDirectional(1.0, -1.0),
                          child: Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 10.0, 10.0, 0.0),
                            child: Container(
                              width: 40.0,
                              height: 40.0,
                              decoration: BoxDecoration(
                                color: FlutterFlowTheme.of(context)
                                    .primaryBackground,
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: FlutterFlowTheme.of(context)
                                      .secondaryText,
                                ),
                              ),
                              child: Align(
                                alignment: const AlignmentDirectional(0.0, 0.0),
                                child: Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      2.0, 2.0, 2.0, 2.0),
                                  child: Icon(
                                    Icons.star_border_rounded,
                                    color: FlutterFlowTheme.of(context)
                                        .secondaryText,
                                    size: 28.0,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                        Align(
                          alignment: const AlignmentDirectional(1.0, 0.0),
                          child: Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 10.0, 10.0, 0.0),
                            child: Container(
                              width: 40.0,
                              height: 40.0,
                              decoration: const BoxDecoration(
                                color: Color(0xCD000000),
                                shape: BoxShape.circle,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
          Align(
            alignment: const AlignmentDirectional(1.0, 0.0),
            child: Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 14.0, 34.0),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Stack(
                    children: [
                      wrapWithModel(
                        model: _model.roundCheckButtonModel,
                        updateCallback: () => safeSetState(() {}),
                        updateOnChange: true,
                        child: RoundCheckButtonWidget(
                          action: () async {
                            await widget.callbackYes?.call();
                          },
                        ),
                      ),
                      if (widget.showYes)
                        Container(
                          width: 62.0,
                          height: 62.0,
                          decoration: const BoxDecoration(
                            color: Color(0xCD000000),
                            shape: BoxShape.circle,
                          ),
                        ),
                    ],
                  ),
                  Stack(
                    children: [
                      wrapWithModel(
                        model: _model.roundSparklesButtonModel,
                        updateCallback: () => safeSetState(() {}),
                        updateOnChange: true,
                        child: RoundSparklesButtonWidget(
                          action: () async {},
                        ),
                      ),
                      Container(
                        width: 62.0,
                        height: 62.0,
                        decoration: const BoxDecoration(
                          color: Color(0xCD000000),
                          shape: BoxShape.circle,
                        ),
                      ),
                    ],
                  ),
                  Stack(
                    children: [
                      wrapWithModel(
                        model: _model.roundCrossButtonModel,
                        updateCallback: () => safeSetState(() {}),
                        updateOnChange: true,
                        child: RoundCrossButtonWidget(
                          action: () async {
                            await widget.callbackX?.call();
                          },
                        ),
                      ),
                      if (widget.showX)
                        Container(
                          width: 62.0,
                          height: 62.0,
                          decoration: const BoxDecoration(
                            color: Color(0xCD000000),
                            shape: BoxShape.circle,
                          ),
                        ),
                    ],
                  ),
                  Stack(
                    children: [
                      wrapWithModel(
                        model: _model.roundArrowButtonModel,
                        updateCallback: () => safeSetState(() {}),
                        updateOnChange: true,
                        child: RoundArrowButtonWidget(
                          action: () async {
                            await widget.callbackSave?.call();
                          },
                        ),
                      ),
                      if (widget.showSave)
                        Container(
                          width: 62.0,
                          height: 62.0,
                          decoration: const BoxDecoration(
                            color: Color(0xCD000000),
                            shape: BoxShape.circle,
                          ),
                        ),
                    ],
                  ),
                ].divide(const SizedBox(height: 15.0)),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
