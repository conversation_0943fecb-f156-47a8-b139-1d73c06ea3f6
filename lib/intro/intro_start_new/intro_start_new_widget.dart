import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import 'package:flutter/material.dart';
import 'intro_start_new_model.dart';
export 'intro_start_new_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';

class IntroStartNewWidget extends StatefulWidget {
  const IntroStartNewWidget({super.key});

  @override
  State<IntroStartNewWidget> createState() => _IntroStartNewWidgetState();
}

class _IntroStartNewWidgetState extends State<IntroStartNewWidget> {
  late IntroStartNewModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => IntroStartNewModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: MediaQuery.sizeOf(context).width * 1.0,
      child: Stack(
        children: [
          Align(
            alignment: const AlignmentDirectional(0.0, 0.0),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8.0),
              child: Image.asset(
                'assets/images/background_waiting_list-01-lesser-corners.png',
                width: MediaQuery.sizeOf(context).width * 1.0,
                height: MediaQuery.sizeOf(context).height * 1.0,
                fit: BoxFit.cover,
                alignment: const Alignment(0.0, 0.0),
              ),
            ),
          ),
          Align(
            alignment: const AlignmentDirectional(1.0, 1.0),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8.0),
              child: Image.asset(
                'assets/images/LineArtGirl1White.webp',
                width: MediaQuery.sizeOf(context).width * 0.8,
                height: MediaQuery.sizeOf(context).height * 0.7,
                fit: BoxFit.fitWidth,
                alignment: const Alignment(1.0, 1.0),
              ),
            ),
          ),
          Align(
            alignment: const AlignmentDirectional(0.0, 0.0),
            child: Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(30.0, 90.0, 30.0, 60.0),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    width: MediaQuery.sizeOf(context).width * 0.7,
                    decoration: const BoxDecoration(),
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Align(
                          alignment: const AlignmentDirectional(0.0, -1.0),
                          child: Text(
                            getRemoteConfigString(
                                'discovery_welcome_intro_title'),
                            textAlign: TextAlign.center,
                            style: FlutterFlowTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'BT Beau Sans',
                                  color: FlutterFlowTheme.of(context).info,
                                  fontSize: 24.0,
                                  letterSpacing: 0.0,
                                  fontWeight: FontWeight.bold,
                                  useGoogleFonts: false,
                                  lineHeight: 1.2,
                                ),
                          ),
                        ),
                        Align(
                          alignment: const AlignmentDirectional(0.0, -1.0),
                          child: Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 18.0, 0.0, 0.0),
                            child: Text(
                              getRemoteConfigString(
                                  'discovery_welcome_intro_subtitle'),
                              textAlign: TextAlign.center,
                              style: FlutterFlowTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'BT Beau Sans',
                                    color: FlutterFlowTheme.of(context).info,
                                    fontSize: 13.0,
                                    letterSpacing: 0.0,
                                    fontWeight: FontWeight.normal,
                                    useGoogleFonts: false,
                                    lineHeight: 1.2,
                                  ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Padding(
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 10.0),
                        child: Text(
                          getRemoteConfigString(
                              'discovery_welcome_intro_time_est'),
                          style:
                              FlutterFlowTheme.of(context).bodyMedium.override(
                                    fontFamily: 'BT Beau Sans',
                                    color: Colors.white,
                                    fontSize: 13.0,
                                    letterSpacing: 0.0,
                                    useGoogleFonts: false,
                                  ),
                        ),
                      ),
                      Container(
  width: 176.0,
  height: 45.0,
  decoration: BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.circular(100.0),
    boxShadow: const [
      BoxShadow(
        color: Colors.black12,
        blurRadius: 4.0,
        offset: Offset(0, 2),
      ),
    ],
  ),
  child: InkWell(
    onTap: () async {
      analytics.logEvent('Tutorial: Started from Discovery');
      context.pushNamed(
        'DiscoveryIntro',
        extra: <String, dynamic>{
          kTransitionInfoKey: const TransitionInfo(
            hasTransition: true,
            transitionType: PageTransitionType.fade,
          ),
        },
      );

      FFAppState().introCompleted = true;
      safeSetState(() {});
    },
    borderRadius: BorderRadius.circular(100.0),
    child: Center(
      child: ShaderMask(
        shaderCallback: (bounds) => const LinearGradient(
          colors: [Color(0xFFFF9EDE), Color(0xFF91B2FB)], // Gradient colors
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ).createShader(Rect.fromLTWH(0, 0, bounds.width, bounds.height)),
        child: Text(
          getRemoteConfigString('discovery_welcome_intro_start_btn'),
          style: FlutterFlowTheme.of(context)
              .titleSmall
              .override(
                fontFamily: 'BT Beau Sans',
                color: Colors.white, // Base color for ShaderMask
                fontSize: 19.0,
                letterSpacing: 0.0,
                useGoogleFonts: false,
              ),
        ),
      ),
    ),
  ),
),

                      FFButtonWidget(
                        onPressed: () async {
                           analytics.logEvent('Tutorial: Skipped from Discovery');
                          FFAppState().introCompleted = true;
                          safeSetState(() {});

                          context.goNamed('TempLoaderScreen');
                        },
                        text: getRemoteConfigString(
                            'discovery_welcome_intro_skip_btn'),
                        options: FFButtonOptions(
                          width: 176.0,
                          height: 45.0,
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              16.0, 0.0, 16.0, 0.0),
                          iconPadding: const EdgeInsetsDirectional.fromSTEB(
                              0.0, 0.0, 0.0, 0.0),
                          color: const Color(0x00FFFFFF),
                          textStyle:
                              FlutterFlowTheme.of(context).titleSmall.override(
                                    fontFamily: 'BT Beau Sans',
                                    color: FlutterFlowTheme.of(context)
                                        .primaryBackground,
                                    fontSize: 19.0,
                                    letterSpacing: 0.0,
                                    useGoogleFonts: false,
                                  ),
                          elevation: 0.0,
                          borderRadius: BorderRadius.circular(100.0),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
