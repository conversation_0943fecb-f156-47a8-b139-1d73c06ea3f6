import 'package:chyrpe/auth/firebase_auth/auth_util.dart';

import '/backend/backend.dart';
import '/discovery/like_match_chat_popup_men/like_match_chat_popup_men_widget.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import 'package:rive/rive.dart' hide LinearGradient, Image;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'match_stage2_intro_model.dart';
import 'package:chyrpe/intro/intro_amplitude_commons.dart';
export 'match_stage2_intro_model.dart';

class MatchStage2IntroWidget extends StatefulWidget {
  const MatchStage2IntroWidget({
    super.key,
    required this.publicProfile,
  });

  final PublicProfileRecord? publicProfile;

  @override
  State<MatchStage2IntroWidget> createState() => _MatchStage2IntroWidgetState();
}

class _MatchStage2IntroWidgetState extends State<MatchStage2IntroWidget> {
  late MatchStage2IntroModel _model;
  final GlobalKey matchStage2IntroBadge = GlobalKey(); 
  final GlobalKey matchStage2IntroMessage = GlobalKey(); 
  
  List<RRect> _cutoutRects = [];

var cardTextBottomPosition = 0.0;
var messageBottomPosition = 0.0;
double textVisibility = 0.0;

  void calculatePositionCard() {
    final RenderBox? renderBox = matchStage2IntroMessage.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      final Offset offset = renderBox.localToGlobal(Offset.zero);
      final double elementBottom = offset.dy;
      setState(() {
        messageBottomPosition = elementBottom; // 10px below the bottom
      });
    }
  }

  late StateMachineController _riveController1;
  late StateMachineController _riveController2;

  void _onInit1(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    art.addController(ctrl);
    _riveController1 = ctrl;
      _riveController1.isActive = true;
      }
     // Keep it inactive initially
  }

  void _onInit2(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    art.addController(ctrl);
    _riveController2 = ctrl;
      _riveController2.isActive = false;
      }
     // Keep it inactive initially
  }

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => MatchStage2IntroModel());

    _model.textController ??= TextEditingController();
    _model.textFieldFocusNode ??= FocusNode();

    logPageVisitIntro(runtimeType.toString());

     WidgetsBinding.instance.addPostFrameCallback((_) {

      Future.delayed(const Duration(milliseconds: 300), () {
      _calculateCutouts();
      calculatePositionCard();
      });

      Future.delayed(const Duration(milliseconds: 1100), () {
      setState(() {
        textVisibility = 1.0;
        _riveController2.isActive = true;
      });
      });

  });
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

final int _currentCutoutStep = 0;


bool secondTextVisible = false;

void _calculateCutouts() {
  List<RRect> rects = [];
  
  
    if (matchStage2IntroBadge.currentContext != null) {
    // Only add profile picture cutout when step is 1
    final RenderBox profileBox = matchStage2IntroBadge.currentContext!.findRenderObject() as RenderBox;
    final Size profileSize = profileBox.size;
    final Offset profilePosition = profileBox.localToGlobal(Offset.zero);
    rects.add(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(
          profilePosition.dx,
          profilePosition.dy,
          profileSize.width,
          profileSize.height,
        ),
        const Radius.circular(100),
      ),
    );
    }

    if (matchStage2IntroMessage.currentContext != null) {
    final RenderBox profileBox2 = matchStage2IntroMessage.currentContext!.findRenderObject() as RenderBox;
    final Size profileSize2 = profileBox2.size;
    final Offset profilePosition2 = profileBox2.localToGlobal(Offset.zero);
    rects.add(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(
          profilePosition2.dx,
          profilePosition2.dy,
          profileSize2.width,
          profileSize2.height,
        ),
        const Radius.circular(12),
      ),
    );
    }
    
  setState(() {
    _cutoutRects = rects;
  });

}

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        context.pushReplacementNamed(
                                        'DiscoveryIntro4',
                                        extra: <String, dynamic>{
                                          kTransitionInfoKey: const TransitionInfo(
                                            hasTransition: true,
                                            transitionType:
                                                PageTransitionType.topToBottom,
                                          ),
                                        },
                                      );
      },
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        body: Stack(
          children: [
            Stack(
              alignment: const AlignmentDirectional(0.0, 1.0),
              children: [
                ClipRRect(
              borderRadius: BorderRadius.circular(0.0),
              child: Image.network(
                widget.publicProfile!.profilePhoto,
                width: MediaQuery.sizeOf(context).width * 1.0,
                height: MediaQuery.sizeOf(context).height * 1.0,
                fit: BoxFit.cover,
              ),
            ),
                SizedBox(
                  width: MediaQuery.of(context).size.width,
                  
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 50.0, 10.0, 0.0),
                            child: Container(
                              key: matchStage2IntroBadge,
                              height: 51.0,
                              decoration: const BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    Color(0xFFED9DD3),
                                    Color(0xFF6BB0E5)
                                  ],
                                  stops: [0.0, 1.0],
                                  begin: AlignmentDirectional(-1.0, 0.0),
                                  end: AlignmentDirectional(1.0, 0),
                                ),
                                borderRadius: BorderRadius.only(
                                  bottomLeft: Radius.circular(100.0),
                                  bottomRight: Radius.circular(100.0),
                                  topLeft: Radius.circular(100.0),
                                  topRight: Radius.circular(100.0),
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    15.0, 0.0, 0.0, 0.0),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    FaIcon(
                                      FontAwesomeIcons.check,
                                      color:
                                          FlutterFlowTheme.of(context).info,
                                      size: 24.0,
                                    ),
                                    Padding(
                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                          12.0, 0.0, 23.0, 0.0),
                                      child: Text(
                                        'You and ${widget.publicProfile?.publicName} matched',
                                        style: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .override(
                                              fontFamily: 'BT Beau Sans',
                                              color:
                                                  FlutterFlowTheme.of(context)
                                                      .info,
                                              fontSize: 16.0,
                                              letterSpacing: 0.0,
                                              fontWeight: FontWeight.w500,
                                              useGoogleFonts: false,
                                            ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      Column(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 0.0, 0.0, 34.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Align(
                                  alignment: const AlignmentDirectional(-1.0, 1.0),
                                  child: Text(
                                    valueOrDefault<String>(
                                      widget.publicProfile?.publicName,
                                      'Name',
                                    ),
                                    style: FlutterFlowTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'BT Beau Sans',
                                          color: FlutterFlowTheme.of(context)
                                              .info,
                                          fontSize: 32.0,
                                          letterSpacing: 0.0,
                                          fontWeight: FontWeight.w600,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ),
                                Align(
                                  alignment: const AlignmentDirectional(-1.0, 1.0),
                                  child: Text(
                                    valueOrDefault<String>(
                                      widget.publicProfile?.age.toString(),
                                      '99',
                                    ),
                                    style: FlutterFlowTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'BT Beau Sans',
                                          color: FlutterFlowTheme.of(context)
                                              .info,
                                          fontSize: 24.0,
                                          letterSpacing: 0.0,
                                          fontWeight: FontWeight.normal,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ),
                              ].divide(const SizedBox(width: 11.0)),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                30.0, 0.0, 30.0, 10.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      13.0, 0.0, 0.0, 0.0),
                                  child: Container(
                                    constraints: BoxConstraints(
                                      maxWidth:
                                          MediaQuery.sizeOf(context).width *
                                              0.7,
                                    ),
                                    decoration: const BoxDecoration(
                                      color: Color(0xFFC79CE4),
                                      borderRadius: BorderRadius.only(
                                        bottomLeft: Radius.circular(10.0),
                                        bottomRight: Radius.circular(0.0),
                                        topLeft: Radius.circular(10.0),
                                        topRight: Radius.circular(10.0),
                                      ),
                                      shape: BoxShape.rectangle,
                                    ),
                                    child: Padding(
                                      key: matchStage2IntroMessage,
                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                          14.0, 14.0, 15.0, 14.0),
                                      child: Text(
                                        'Example',
                                        style: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .override(
                                              fontFamily: 'BT Beau Sans',
                                              color:
                                                  FlutterFlowTheme.of(context)
                                                      .info,
                                              fontSize: 16.0,
                                              letterSpacing: 0.0,
                                              useGoogleFonts: false,
                                            ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SafeArea(
                            child: Stack(
                              children: [
                                Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      35.0, 0.0, 35.0, 0.0),
                                  child: Container(
                                    width: double.infinity,
                                    constraints: const BoxConstraints(
                                      minHeight: 51.0,
                                      maxHeight: 200.0,
                                    ),
                                    decoration: BoxDecoration(
                                      color: FlutterFlowTheme.of(context)
                                          .secondaryBackground,
                                      borderRadius: BorderRadius.circular(11.0),
                                      border: Border.all(
                                        color: const Color(0xFFE9EAEE),
                                        width: 1.0,
                                      ),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Form(
                                          key: _model.formKey,
                                          autovalidateMode:
                                              AutovalidateMode.disabled,
                                          child: Padding(
                                            padding: const EdgeInsetsDirectional
                                                .fromSTEB(
                                                    10.0, 0.0, 10.0, 0.0),
                                            child: SizedBox(
                                              width:
                                                  MediaQuery.sizeOf(context)
                                                          .width *
                                                      0.7,
                                              child: TextFormField(
                                                controller:
                                                    _model.textController,
                                                focusNode:
                                                    _model.textFieldFocusNode,
                                                autofocus: false,
                                                obscureText: false,
                                                decoration: InputDecoration(
                                                  hintText: 'Answer here...',
                                                  hintStyle: FlutterFlowTheme
                                                          .of(context)
                                                      .labelMedium
                                                      .override(
                                                        fontFamily:
                                                            'BT Beau Sans',
                                                        letterSpacing: 0.0,
                                                        useGoogleFonts: false,
                                                      ),
                                                  enabledBorder:
                                                      InputBorder.none,
                                                  focusedBorder:
                                                      InputBorder.none,
                                                  errorBorder:
                                                      InputBorder.none,
                                                  focusedErrorBorder:
                                                      InputBorder.none,
                                                ),
                                                style: FlutterFlowTheme.of(
                                                        context)
                                                    .bodyMedium
                                                    .override(
                                                      fontFamily:
                                                          'BT Beau Sans',
                                                      fontSize: 16.0,
                                                      letterSpacing: 0.0,
                                                      useGoogleFonts: false,
                                                    ),
                                                maxLines: 5,
                                                minLines: 1,
                                                maxLength: 1000,
                                                maxLengthEnforcement:
                                                    MaxLengthEnforcement
                                                        .enforced,
                                                buildCounter: (context,
                                                        {required currentLength,
                                                        required isFocused,
                                                        maxLength}) =>
                                                    null,
                                                validator: _model
                                                    .textControllerValidator
                                                    .asValidator(context),
                                              ),
                                            ),
                                          ),
                                        ),
                                        FFButtonWidget(
                                          onPressed: () {
                                            print('Button pressed ...');
                                          },
                                          text: 'Send',
                                          options: FFButtonOptions(
                                            width: 70.0,
                                            height: 40.0,
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(
                                                    0.0, 0.0, 0.0, 0.0),
                                            iconPadding:
                                                const EdgeInsetsDirectional.fromSTEB(
                                                    0.0, 0.0, 0.0, 0.0),
                                            color: const Color(0x00F0F2F4),
                                            textStyle: FlutterFlowTheme.of(
                                                    context)
                                                .titleSmall
                                                .override(
                                                  fontFamily: 'BT Beau Sans',
                                                  color: FlutterFlowTheme.of(
                                                          context)
                                                      .primaryText,
                                                  letterSpacing: 0.0,
                                                  fontWeight: FontWeight.w600,
                                                  useGoogleFonts: false,
                                                ),
                                            elevation: 0.0,
                                            borderSide: const BorderSide(
                                              color: Colors.transparent,
                                            ),
                                            borderRadius:
                                                BorderRadius.circular(8.0),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                Builder(
                                  builder: (context) => Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        35.0, 0.0, 35.0, 0.0),
                                    child: InkWell(
                                      splashColor: Colors.transparent,
                                      focusColor: Colors.transparent,
                                      hoverColor: Colors.transparent,
                                      highlightColor: Colors.transparent,
                                      onTap: () async {
                                        await showDialog(
                                          barrierDismissible: false,
                                          context: context,
                                          builder: (dialogContext) {
                                            return Dialog(
                                              elevation: 0,
                                              insetPadding: EdgeInsets.zero,
                                              backgroundColor:
                                                  Colors.transparent,
                                              alignment:
                                                  const AlignmentDirectional(0.0, 0.0)
                                                      .resolve(
                                                          Directionality.of(
                                                              context)),
                                              child: GestureDetector(
                                                onTap: () {
                                                  FocusScope.of(dialogContext)
                                                      .unfocus();
                                                  FocusManager
                                                      .instance.primaryFocus
                                                      ?.unfocus();
                                                },
                                                child:
                                                    const LikeMatchChatPopupMenWidget(),
                                              ),
                                            );
                                          },
                                        );
                                      },
                                      child: Container(
                                        width:
                                            MediaQuery.sizeOf(context).width *
                                                1.0,
                                        height: 55.0,
                                        decoration: const BoxDecoration(
                                          color: Color(0x00F0F2F4),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          FFButtonWidget(
                            onPressed: () {
                              print('Button pressed ...');
                            },
                            text: 'Don\'t send message now',
                            options: FFButtonOptions(
                              height: 40.0,
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  24.0, 0.0, 24.0, 0.0),
                              iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 0.0, 0.0, 0.0),
                              color: const Color(0x004B39EF),
                              textStyle: FlutterFlowTheme.of(context)
                                  .titleSmall
                                  .override(
                                    fontFamily: 'BT Beau Sans',
                                    color: Colors.white,
                                    letterSpacing: 0.0,
                                    fontWeight: FontWeight.w600,
                                    useGoogleFonts: false,
                                  ),
                              elevation: 3.0,
                              borderSide: const BorderSide(
                                color: Colors.transparent,
                                width: 1.0,
                              ),
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Positioned.fill(
              child: ClipPath(
                clipper: MultiCutoutClipper(_cutoutRects), // Multiple cutouts
                child: Container(
                  color: Colors.black.withOpacity(0.8),
                ),
              ),
            ),
            Column(
              children: [

                ClipRect(
                  child: Align(
                    alignment: Alignment.bottomLeft,
                    child: SizedBox(
                    height: MediaQuery.of(context).size.height * 0.4,
                    width: MediaQuery.of(context).size.width * 0.6,
                    child: RiveAnimation.asset(
                        'assets/rive_animations/fs_tutorial/20-1.riv',
                        fit: BoxFit.fitWidth,
                        onInit: _onInit1,
                        alignment: Alignment.bottomLeft
                      ),
                              ),
                  ),
                               ),

                AnimatedOpacity(
                  opacity: textVisibility,
                  duration: const Duration(milliseconds: 300),
                  child: Align(
                    alignment: Alignment.centerLeft, // Ensures horizontal centering
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(20, 10, 20, 0),
                      child: RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: getRemoteConfigString('tutorial_matchstage2_t1'),
                              style: FlutterFlowTheme.of(context).bodyMedium.override(
                                    color:
                                        FlutterFlowTheme.of(context).primaryBackground,
                                    fontSize: 15.0,
                                    letterSpacing: 0.0,
                                    fontWeight: FontWeight.w500,
                                    useGoogleFonts: false,
                                  ),
                            ),
                            TextSpan(
                              text: getRemoteConfigString('tutorial_matchstage2_t2'),
                              style: FlutterFlowTheme.of(context).bodyMedium.override(
                                    color:
                                        const Color(0xFFFF9EDE),
                                    fontSize: 15.0,
                                    letterSpacing: 0.0,
                                    fontWeight: FontWeight.w500,
                                    useGoogleFonts: false,
                                  ),
                            ),
                            TextSpan(
                              text: getRemoteConfigString('tutorial_matchstage2_t3'),
                              style: FlutterFlowTheme.of(context).bodyMedium.override(
                                    color:
                                        FlutterFlowTheme.of(context).primaryBackground,
                                    fontSize: 15.0,
                                    letterSpacing: 0.0,
                                    fontWeight: FontWeight.w500,
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ],
                        ),
                        textAlign: TextAlign.left, // Ensures text is center-aligned
                      ),
                    ),
                  ),
                ),
              
                Align(
                  alignment: Alignment.bottomRight,
                  child: ClipRect(
                    child: SizedBox(
                    height: MediaQuery.of(context).size.height * 0.4,
                    width: MediaQuery.of(context).size.width,
                    child: RiveAnimation.asset(
                        'assets/rive_animations/fs_tutorial/20-2.riv',
                        fit: BoxFit.fitHeight,
                        onInit: _onInit2,
                        alignment: Alignment.bottomRight
                      ),
                              ),
                                 ),
                ),
              
              ],
            ),
            Positioned(
              top: messageBottomPosition,
              right: 0,
              child: Padding(
                 padding: const EdgeInsetsDirectional.fromSTEB(
                                  30.0, 0.0, 30.0, 10.0),
                child: Padding(
                                        padding: const EdgeInsetsDirectional.fromSTEB(
                                            13.0, 0.0, 0.0, 0.0),
                                        child: Container(
                                          constraints: BoxConstraints(
                                            maxWidth:
                                                MediaQuery.sizeOf(context).width *
                                                    0.7,
                                          ),
                                          decoration: const BoxDecoration(
                                            color: Color(0xFFC79CE4),
                                            borderRadius: BorderRadius.only(
                                              bottomLeft: Radius.circular(10.0),
                                              bottomRight: Radius.circular(0.0),
                                              topLeft: Radius.circular(10.0),
                                              topRight: Radius.circular(10.0),
                                            ),
                                            shape: BoxShape.rectangle,
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsetsDirectional.fromSTEB(
                                                14.0, 14.0, 15.0, 14.0),
                                            child: Text(
                                              "${getRemoteConfigString('tutorial_matchstage2_m_p1')}${currentUserDocument?.publicName}${getRemoteConfigString('tutorial_matchstage2_m_p2')}",
                                              style: FlutterFlowTheme.of(context)
                                                  .bodyMedium
                                                  .override(
                                                    fontFamily: 'BT Beau Sans',
                                                    color:
                                                        FlutterFlowTheme.of(context)
                                                            .info,
                                                    fontSize: 16.0,
                                                    letterSpacing: 0.0,
                                                    useGoogleFonts: false,
                                                  ),
                                            ),
                                          ),
                                        ),
                                      ),
              ),
            ),
            SafeArea(
          child: Align(
                      alignment: Alignment.bottomLeft,
                      child: Padding(
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(0.0, 50.0, 30.0, 0.0),
                        child: FFButtonWidget(
                          onPressed: () {
                            logSkippedIntro(runtimeType.toString());
                            GoRouter.of(context).goNamed('Discovery');
                          },
                          text: 'Skip',
                          options: FFButtonOptions(
                            height: 40.0,
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                16.0, 0.0, 16.0, 0.0),
                            iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 0.0, 0.0, 0.0),
                            color: const Color(0x004B39EF),
                            textStyle:
                                FlutterFlowTheme.of(context).titleSmall.override(
                                      fontFamily: 'BT Beau Sans',
                                      color: Colors.white,
                                      fontSize: 10.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.normal,
                                      useGoogleFonts: false,
                                    ),
                            elevation: 0.0,
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                        ),
                      ),
                    ),
        ),
          
          ],
        ),
      ),
    );
  }
}

class MultiCutoutClipper extends CustomClipper<Path> {
  final List<RRect> cutoutRects; // List of rounded rectangles

  MultiCutoutClipper(this.cutoutRects);

  @override
  Path getClip(Size size) {
    Path path = Path();

    // Full screen rectangle
    path.addRect(Rect.fromLTWH(0, 0, size.width, size.height));

    // Subtract each cutout rectangle from the path
    for (final cutout in cutoutRects) {
      path = Path.combine(
        PathOperation.difference,
        path,
        Path()..addRRect(cutout),
      );
    }

    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return true; // Reclip if the cutouts change
  }
}
