import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/general/info_sheet_scrollable/info_sheet_scrollable_widget.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'own_profile_intro_model.dart';
import 'package:chyrpe/intro/intro_amplitude_commons.dart';
export 'own_profile_intro_model.dart';

class OwnProfileIntroWidget extends StatefulWidget {
  const OwnProfileIntroWidget({
    super.key,
    required this.profile,
  });

  final PublicProfileRecord? profile;

  @override
  State<OwnProfileIntroWidget> createState() => _OwnProfileIntroWidgetState();
}

class _OwnProfileIntroWidgetState extends State<OwnProfileIntroWidget> {
  late OwnProfileIntroModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => OwnProfileIntroModel());

    logPageVisitIntro(runtimeType.toString());

    // On page load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      _model.moreAboutMe = widget.profile!.moreAboutMe
          .map((e) => e.option)
          .toList()
          .toList()
          .cast<String>();
      safeSetState(() {});
    });
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        context.pushReplacementNamed(
                                  'DiscoveryIntro2',
                                  extra: <String, dynamic>{
                                    kTransitionInfoKey: const TransitionInfo(
                                      hasTransition: true,
                                      transitionType: PageTransitionType.fade,
                                    ),
                                  },
                                );
      },
      child: Stack(
        children: [Scaffold(
          key: scaffoldKey,
          backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
          body: SafeArea(
            top: true,
            child: Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 45.0),
              child: Container(
                width: MediaQuery.sizeOf(context).width * 1.0,
                height: double.infinity,
                decoration: BoxDecoration(
                  color: FlutterFlowTheme.of(context).secondaryBackground,
                ),
                child: SizedBox(
                  width: double.infinity,
                  height: double.infinity,
                  child: Stack(
                    children: [
                      SingleChildScrollView(
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                              SizedBox(
                                width: MediaQuery.sizeOf(context).width * 1.0,
                                height: MediaQuery.sizeOf(context).height * 0.72,
                                child: Stack(
                                  children: [
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(10.75),
                                      child: Image.network(
                                        widget.profile!.profilePhoto,
                                        width: MediaQuery.sizeOf(context).width *
                                            1.0,
                                        height:
                                            MediaQuery.sizeOf(context).height *
                                                0.72,
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                     Container(
                                        width: MediaQuery.sizeOf(context).width *
                                            1.0,
                                        height:
                                            MediaQuery.sizeOf(context).height *
                                                0.72,
                                        decoration: BoxDecoration(
                                          gradient: const LinearGradient(
                                            colors: [
                                              Colors.transparent,
                                              Color(0x4B000000),
                                              Colors.black
                                            ],
                                            stops: [0.0, 0.7, 1.0],
                                            begin:
                                                AlignmentDirectional(0.0, -1.0),
                                            end: AlignmentDirectional(0, 1.0),
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(10.75),
                                        ),
                                     ),
                                    Align(
                                      alignment: const AlignmentDirectional(-1.0, 1.0),
                                      child: Card(
                                        clipBehavior: Clip.antiAliasWithSaveLayer,
                                        color: Colors.transparent,
                                        elevation: 0.0,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(12.0),
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsetsDirectional.fromSTEB(
                                              17.0, 0.0, 0.0, 16.0),
                                          child: Column(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Padding(
                                                padding: const EdgeInsetsDirectional
                                                    .fromSTEB(0.0, 0.0, 0.0, 9.0),
                                                child: Row(
                                                  mainAxisSize: MainAxisSize.max,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  children: [
                                                    AutoSizeText(
                                                      valueOrDefault<String>(
                                                        widget
                                                            .profile?.publicName,
                                                        'name',
                                                      ),
                                                      minFontSize: 16.0,
                                                      style: FlutterFlowTheme.of(
                                                              context)
                                                          .bodyMedium
                                                          .override(
                                                            fontFamily:
                                                                'BT Beau Sans',
                                                            color: FlutterFlowTheme
                                                                    .of(context)
                                                                .info,
                                                            fontSize: 32.0,
                                                            letterSpacing: 0.0,
                                                            fontWeight:
                                                                FontWeight.w600,
                                                            useGoogleFonts: false,
                                                          ),
                                                    ),
                                                    Padding(
                                                      padding:
                                                          const EdgeInsetsDirectional
                                                              .fromSTEB(11.0, 0.0,
                                                                  0.0, 0.0),
                                                      child: Text(
                                                        valueOrDefault<String>(
                                                          widget.profile?.age
                                                              .toString(),
                                                          '20',
                                                        ),
                                                        style:
                                                            FlutterFlowTheme.of(
                                                                    context)
                                                                .bodyMedium
                                                                .override(
                                                                  fontFamily:
                                                                      'BT Beau Sans',
                                                                  color: FlutterFlowTheme.of(
                                                                          context)
                                                                      .info,
                                                                  fontSize: 24.0,
                                                                  letterSpacing:
                                                                      0.0,
                                                                  useGoogleFonts:
                                                                      false,
                                                                ),
                                                      ),
                                                    ),
                                                    if (widget.profile!
                                                            .supporterBadgeShown &&
                                                        getRemoteConfigBool(
                                                            'profile_supporter_star_active'))
                                                      Padding(
                                                        padding:
                                                            const EdgeInsetsDirectional
                                                                .fromSTEB(
                                                                    12.0,
                                                                    0.0,
                                                                    0.0,
                                                                    0.0),
                                                        child: ClipRRect(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(8.0),
                                                          child: Image.asset(
                                                            'assets/images/Supporter_Badge_Star.png',
                                                            width: 26.0,
                                                            height: 26.0,
                                                            fit: BoxFit.cover,
                                                          ),
                                                        ),
                                                      ),
                                                  ],
                                                ),
                                              ),
                                              if (widget
                                                      .profile!.publicRoleShown &&
                                                  !getRemoteConfigBool(
                                                      'hideRoleForAll') &&
                                                  widget.profile!
                                                      .hasPublicRole())
                                                Row(
                                                  mainAxisSize: MainAxisSize.max,
                                                  children: [
                                                    Text(
                                                      widget.profile?.specificRole !=
                                                                  null &&
                                                              widget.profile
                                                                      ?.specificRole !=
                                                                  ''
                                                          ? '${widget.profile?.publicRole} (${widget.profile?.specificRole})'
                                                          : widget.profile!
                                                              .publicRole,
                                                      style: FlutterFlowTheme.of(
                                                              context)
                                                          .bodyMedium
                                                          .override(
                                                            fontFamily:
                                                                'BT Beau Sans',
                                                            color: FlutterFlowTheme
                                                                    .of(context)
                                                                .info,
                                                            fontSize: 16.0,
                                                            letterSpacing: 0.0,
                                                            useGoogleFonts: false,
                                                          ),
                                                    ),
                                                  ].divide(const SizedBox(width: 4.0)),
                                                ),
                                              Align(
                                                alignment: const AlignmentDirectional(
                                                    -1.0, 0.0),
                                                child: Builder(
                                                  builder: (context) {
                                                    if (!widget
                                                        .profile!.cityHidden) {
                                                      return Padding(
                                                        padding:
                                                            const EdgeInsetsDirectional
                                                                .fromSTEB(
                                                                    0.0,
                                                                    0.0,
                                                                    0.0,
                                                                    2.0),
                                                        child: Text(
                                                          valueOrDefault<String>(
                                                            widget.profile?.city,
                                                            'City',
                                                          ),
                                                          textAlign:
                                                              TextAlign.start,
                                                          style:
                                                              FlutterFlowTheme.of(
                                                                      context)
                                                                  .bodyMedium
                                                                  .override(
                                                                    fontFamily:
                                                                        'BT Beau Sans',
                                                                    color: FlutterFlowTheme.of(
                                                                            context)
                                                                        .info,
                                                                    fontSize:
                                                                        16.0,
                                                                    letterSpacing:
                                                                        0.0,
                                                                    useGoogleFonts:
                                                                        false,
                                                                  ),
                                                        ),
                                                      );
                                                    } else {
                                                      return Padding(
                                                        padding:
                                                            const EdgeInsetsDirectional
                                                                .fromSTEB(
                                                                    0.0,
                                                                    0.0,
                                                                    0.0,
                                                                    2.0),
                                                        child: Text(
                                                          getRemoteConfigString(
                                                              'profile_location_private'),
                                                          textAlign:
                                                              TextAlign.start,
                                                          style:
                                                              FlutterFlowTheme.of(
                                                                      context)
                                                                  .bodyMedium
                                                                  .override(
                                                                    fontFamily:
                                                                        'BT Beau Sans',
                                                                    color: FlutterFlowTheme.of(
                                                                            context)
                                                                        .info,
                                                                    fontSize:
                                                                        16.0,
                                                                    letterSpacing:
                                                                        0.0,
                                                                    useGoogleFonts:
                                                                        false,
                                                                  ),
                                                        ),
                                                      );
                                                    }
                                                  },
                                                ),
                                              ),
                                              if (getRemoteConfigBool(
                                                  'showKinks'))
                                                Align(
                                                  alignment: const AlignmentDirectional(
                                                      -1.0, 0.0),
                                                  child: Container(
                                                    width:
                                                        MediaQuery.sizeOf(context)
                                                                .width *
                                                            0.6,
                                                    decoration: const BoxDecoration(),
                                                    child: Padding(
                                                      padding:
                                                          const EdgeInsetsDirectional
                                                              .fromSTEB(0.0, 9.0,
                                                                  0.0, 0.0),
                                                      child: Builder(
                                                        builder: (context) {
                                                          final kinks = widget
                                                                  .profile
                                                                  ?.playPreferences
                                                                  .toList() ??
                                                              [];
        
                                                          return Wrap(
                                                            spacing: 10.0,
                                                            runSpacing: 9.0,
                                                            alignment:
                                                                WrapAlignment
                                                                    .start,
                                                            crossAxisAlignment:
                                                                WrapCrossAlignment
                                                                    .start,
                                                            direction:
                                                                Axis.horizontal,
                                                            runAlignment:
                                                                WrapAlignment
                                                                    .start,
                                                            verticalDirection:
                                                                VerticalDirection
                                                                    .down,
                                                            clipBehavior:
                                                                Clip.none,
                                                            children:
                                                                List.generate(
                                                                    kinks.length,
                                                                    (kinksIndex) {
                                                              final kinksItem =
                                                                  kinks[
                                                                      kinksIndex];
                                                              return Container(
                                                                decoration:
                                                                    BoxDecoration(
                                                                  color: const Color(
                                                                      0x00FFFFFF),
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              24.0),
                                                                  border:
                                                                      Border.all(
                                                                    color: FlutterFlowTheme.of(
                                                                            context)
                                                                        .info,
                                                                  ),
                                                                ),
                                                                child: Padding(
                                                                  padding: const EdgeInsetsDirectional
                                                                      .fromSTEB(
                                                                          10.0,
                                                                          6.0,
                                                                          10.0,
                                                                          6.0),
                                                                  child: Text(
                                                                    kinksItem,
                                                                    style: FlutterFlowTheme.of(
                                                                            context)
                                                                        .bodyMedium
                                                                        .override(
                                                                          fontFamily:
                                                                              'BT Beau Sans',
                                                                          color: FlutterFlowTheme.of(context)
                                                                              .info,
                                                                          letterSpacing:
                                                                              0.0,
                                                                          fontWeight:
                                                                              FontWeight.w600,
                                                                          useGoogleFonts:
                                                                              false,
                                                                        ),
                                                                  ),
                                                                ),
                                                              );
                                                            }),
                                                          );
                                                        },
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                    Align(
                                      alignment: const AlignmentDirectional(1.0, -1.0),
                                      child: Padding(
                                        padding: const EdgeInsetsDirectional.fromSTEB(
                                            0.0, 10.0, 10.0, 0.0),
                                        child: Container(
                                          width: 40.0,
                                          height: 40.0,
                                          decoration: BoxDecoration(
                                            color: FlutterFlowTheme.of(context)
                                                .primaryBackground,
                                            shape: BoxShape.circle,
                                            border: Border.all(
                                              color: FlutterFlowTheme.of(context)
                                                  .secondaryText,
                                            ),
                                          ),
                                          child: Align(
                                            alignment:
                                                const AlignmentDirectional(0.0, 0.0),
                                            child: Padding(
                                              padding:
                                                  const EdgeInsetsDirectional.fromSTEB(
                                                      2.0, 2.0, 2.0, 2.0),
                                              child: Icon(
                                                Icons.close_rounded,
                                                color:
                                                    FlutterFlowTheme.of(context)
                                                        .secondaryText,
                                                size: 20.0,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  20.0, 0.0, 0.0, 0.0),
                              child: Column(
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  Align(
                                    alignment: const AlignmentDirectional(-1.0, 0.0),
                                    child: Padding(
                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                          0.0, 19.0, 0.0, 11.0),
                                      child: Text(
                                        'More about me',
                                        textAlign: TextAlign.center,
                                        style: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .override(
                                              fontFamily: 'BT Beau Sans',
                                              fontSize: 20.0,
                                              letterSpacing: 0.0,
                                              fontWeight: FontWeight.w600,
                                              useGoogleFonts: false,
                                            ),
                                      ),
                                    ),
                                  ),
                                  if (!getRemoteConfigBool('hideXpForAll') &&
                                      widget.profile!.hasExperienceLevel())
                                    Padding(
                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                          0.0, 0.0, 0.0, 4.0),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.max,
                                        children: [
                                          const FaIcon(
                                            FontAwesomeIcons.dotCircle,
                                            color: Color(0xFF4D535B),
                                            size: 16.0,
                                          ),
                                          Padding(
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(
                                                    0.0, 0.0, 0.0, 2.0),
                                            child: Text(
                                              valueOrDefault<String>(
                                                widget.profile?.experienceLevel,
                                                '-',
                                              ),
                                              textAlign: TextAlign.start,
                                              style: FlutterFlowTheme.of(context)
                                                  .bodyMedium
                                                  .override(
                                                    fontFamily: 'BT Beau Sans',
                                                    color: const Color(0xFF4D535B),
                                                    fontSize: 16.0,
                                                    letterSpacing: 0.0,
                                                    useGoogleFonts: false,
                                                  ),
                                            ),
                                          ),
                                        ].divide(const SizedBox(width: 4.0)),
                                      ),
                                    ),
                                  if (widget.profile?.hasHeight() ?? true)
                                    Padding(
                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                          0.0, 0.0, 0.0, 4.0),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.max,
                                        children: [
                                          const FaIcon(
                                            FontAwesomeIcons.ruler,
                                            color: Color(0xFF4D535B),
                                            size: 16.0,
                                          ),
                                          Padding(
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(
                                                    0.0, 0.0, 0.0, 2.0),
                                            child: Text(
                                              '${formatNumber(
                                                widget.profile!.height / 100,
                                                formatType: FormatType.custom,
                                                format: '#.##',
                                                locale: '',
                                              )}m',
                                              textAlign: TextAlign.start,
                                              style: FlutterFlowTheme.of(context)
                                                  .bodyMedium
                                                  .override(
                                                    fontFamily: 'BT Beau Sans',
                                                    color: const Color(0xFF4D535B),
                                                    fontSize: 16.0,
                                                    letterSpacing: 0.0,
                                                    useGoogleFonts: false,
                                                  ),
                                            ),
                                          ),
                                        ].divide(const SizedBox(width: 4.0)),
                                      ),
                                    ),
                                  if (widget.profile?.hasGender() ?? true)
                                    Padding(
                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                          0.0, 0.0, 0.0, 4.0),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.max,
                                        children: [
                                          const Icon(
                                            Icons.man_4,
                                            color: Color(0xFF4D535B),
                                            size: 20.0,
                                          ),
                                          Padding(
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(
                                                    0.0, 0.0, 0.0, 2.0),
                                            child: Text(
                                              valueOrDefault<String>(
                                                () {
                                                  if (widget.profile
                                                              ?.specificGender !=
                                                          null &&
                                                      widget.profile
                                                              ?.specificGender !=
                                                          '') {
                                                    return '${widget.profile!.alternativeG ? widget.profile?.displayG : widget.profile?.gender?.name} (${widget.profile?.specificGender})';
                                                  } else if (widget
                                                      .profile!.alternativeG) {
                                                    return widget
                                                        .profile?.displayG;
                                                  } else {
                                                    return widget
                                                        .profile?.gender?.name;
                                                  }
                                                }(),
                                                'Gender cannot be fetched',
                                              ),
                                              style: FlutterFlowTheme.of(context)
                                                  .bodyMedium
                                                  .override(
                                                    fontFamily: 'BT Beau Sans',
                                                    color: const Color(0xFF4D535B),
                                                    fontSize: 16.0,
                                                    letterSpacing: 0.0,
                                                    useGoogleFonts: false,
                                                  ),
                                            ),
                                          ),
                                        ].divide(const SizedBox(width: 4.0)),
                                      ),
                                    ),
                                  if (widget.profile?.hasRelationPreferences() ??
                                      true)
                                    Padding(
                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                          0.0, 0.0, 0.0, 4.0),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.max,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          const Icon(
                                            Icons.people,
                                            color: Color(0xFF4D535B),
                                            size: 20.0,
                                          ),
                                          Builder(
                                            builder: (context) {
                                              final relationPrefs = widget
                                                      .profile
                                                      ?.relationPreferences
                                                      .toList() ??
                                                  [];
        
                                              return Column(
                                                mainAxisSize: MainAxisSize.max,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: List.generate(
                                                    relationPrefs.length,
                                                    (relationPrefsIndex) {
                                                  final relationPrefsItem =
                                                      relationPrefs[
                                                          relationPrefsIndex];
                                                  return Padding(
                                                    padding: const EdgeInsetsDirectional
                                                        .fromSTEB(
                                                            0.0, 0.0, 0.0, 2.0),
                                                    child: Text(
                                                      relationPrefsItem,
                                                      textAlign: TextAlign.start,
                                                      style: FlutterFlowTheme.of(
                                                              context)
                                                          .bodyMedium
                                                          .override(
                                                            fontFamily:
                                                                'BT Beau Sans',
                                                            color:
                                                                const Color(0xFF4D535B),
                                                            fontSize: 16.0,
                                                            letterSpacing: 0.0,
                                                            useGoogleFonts: false,
                                                          ),
                                                    ),
                                                  );
                                                }),
                                              );
                                            },
                                          ),
                                        ].divide(const SizedBox(width: 4.0)),
                                      ),
                                    ),
                                  if (widget.profile?.job != null &&
                                      widget.profile?.job != '')
                                    Padding(
                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                          0.0, 0.0, 0.0, 4.0),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.max,
                                        children: [
                                          const Padding(
                                            padding:
                                                EdgeInsetsDirectional.fromSTEB(
                                                    0.0, 0.0, 2.0, 0.0),
                                            child: Icon(
                                              Icons.business_center_rounded,
                                              color: Color(0xFF4D535B),
                                              size: 18.0,
                                            ),
                                          ),
                                          Padding(
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(
                                                    0.0, 0.0, 0.0, 2.0),
                                            child: Text(
                                              valueOrDefault<String>(
                                                widget.profile?.job,
                                                'Prefer not to say',
                                              ),
                                              style: FlutterFlowTheme.of(context)
                                                  .bodyMedium
                                                  .override(
                                                    fontFamily: 'BT Beau Sans',
                                                    color: const Color(0xFF4D535B),
                                                    fontSize: 16.0,
                                                    letterSpacing: 0.0,
                                                    useGoogleFonts: false,
                                                  ),
                                            ),
                                          ),
                                        ].divide(const SizedBox(width: 4.0)),
                                      ),
                                    ),
                                  if (widget.profile?.hasEducation() ?? true)
                                    Padding(
                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                          0.0, 0.0, 0.0, 4.0),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.max,
                                        children: [
                                          const Padding(
                                            padding:
                                                EdgeInsetsDirectional.fromSTEB(
                                                    0.0, 0.0, 2.0, 0.0),
                                            child: FaIcon(
                                              FontAwesomeIcons.building,
                                              color: Color(0xFF4D535B),
                                              size: 18.0,
                                            ),
                                          ),
                                          Padding(
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(
                                                    0.0, 0.0, 0.0, 2.0),
                                            child: Text(
                                              valueOrDefault<String>(
                                                widget.profile?.education,
                                                'Prefer not to say',
                                              ),
                                              style: FlutterFlowTheme.of(context)
                                                  .bodyMedium
                                                  .override(
                                                    fontFamily: 'BT Beau Sans',
                                                    color: const Color(0xFF4D535B),
                                                    fontSize: 16.0,
                                                    letterSpacing: 0.0,
                                                    useGoogleFonts: false,
                                                  ),
                                            ),
                                          ),
                                        ].divide(const SizedBox(width: 4.0)),
                                      ),
                                    ),
                                  if (widget.profile?.findom ?? true)
                                    Padding(
                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                          0.0, 15.0, 0.0, 15.0),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.max,
                                        children: [
                                          Padding(
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(
                                                    5.0, 3.0, 5.0, 2.0),
                                            child: Text(
                                              'Findom',
                                              style: FlutterFlowTheme.of(context)
                                                  .bodyMedium
                                                  .override(
                                                    fontFamily: 'BT Beau Sans',
                                                    color: const Color(0xFF4D535B),
                                                    fontSize: 16.0,
                                                    letterSpacing: 0.0,
                                                    useGoogleFonts: false,
                                                  ),
                                            ),
                                          ),
                                          Align(
                                            alignment:
                                                const AlignmentDirectional(-1.0, 0.0),
                                            child: InkWell(
                                              splashColor: Colors.transparent,
                                              focusColor: Colors.transparent,
                                              hoverColor: Colors.transparent,
                                              highlightColor: Colors.transparent,
                                              onTap: () async {
                                                await showModalBottomSheet(
                                                  isScrollControlled: true,
                                                  backgroundColor:
                                                      Colors.transparent,
                                                  enableDrag: false,
                                                  context: context,
                                                  builder: (context) {
                                                    return Padding(
                                                      padding: MediaQuery
                                                          .viewInsetsOf(
                                                              context),
                                                      child:
                                                          InfoSheetScrollableWidget(
                                                        title: getRemoteConfigString(
                                                            'findom_info_title'),
                                                        body: getRemoteConfigString(
                                                            'findom_info_body'),
                                                      ),
                                                    );
                                                  },
                                                ).then((value) =>
                                                    safeSetState(() {}));
                                              },
                                              child: Text(
                                                getRemoteConfigString(
                                                    'findom_info_title'),
                                                style: FlutterFlowTheme.of(
                                                        context)
                                                    .bodyMedium
                                                    .override(
                                                      fontFamily: 'BT Beau Sans',
                                                      color: const Color(0xFF4F5865),
                                                      letterSpacing: 0.0,
                                                      fontWeight: FontWeight.w600,
                                                      decoration: TextDecoration
                                                          .underline,
                                                      useGoogleFonts: false,
                                                    ),
                                              ),
                                            ),
                                          ),
                                        ].divide(const SizedBox(width: 4.0)),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                            const Divider(
                              thickness: 1.0,
                              color: Color(0xFFD8DBDF),
                            ),
                            if (widget.profile?.hasBio() ?? true)
                              Align(
                                alignment: const AlignmentDirectional(-1.0, -1.0),
                                child: Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      20.0, 15.0, 20.0, 15.0),
                                  child: Text(
                                    valueOrDefault<String>(
                                      widget.profile?.bio,
                                      '.',
                                    ),
                                    style: FlutterFlowTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'BT Beau Sans',
                                          fontSize: 16.0,
                                          letterSpacing: 0.0,
                                          useGoogleFonts: false,
                                          lineHeight: 1.3,
                                        ),
                                  ),
                                ),
                              ),
                            if (widget.profile?.prompts != null &&
                                (widget.profile?.prompts)!.isNotEmpty)
                              Builder(
                                builder: (context) {
                                  final prompts =
                                      widget.profile?.prompts.toList() ?? [];
        
                                  return Column(
                                    mainAxisSize: MainAxisSize.max,
                                    children: List.generate(prompts.length,
                                        (promptsIndex) {
                                      final promptsItem = prompts[promptsIndex];
                                      return Visibility(
                                        visible: (promptsItem.answer != '') &&
                                            (promptsItem.answer != ''),
                                        child: Padding(
                                          padding: const EdgeInsetsDirectional.fromSTEB(
                                              20.0, 0.0, 0.0, 0.0),
                                          child: Column(
                                            mainAxisSize: MainAxisSize.max,
                                            children: [
                                              const Divider(
                                                thickness: 1.0,
                                                color: Color(0xFFD8DBDF),
                                              ),
                                              Align(
                                                alignment: const AlignmentDirectional(
                                                    -1.0, -1.0),
                                                child: Padding(
                                                  padding: const EdgeInsetsDirectional
                                                      .fromSTEB(
                                                          0.0, 15.0, 20.0, 8.0),
                                                  child: Text(
                                                    promptsItem.prompt,
                                                    style: FlutterFlowTheme.of(
                                                            context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          fontSize: 16.0,
                                                          letterSpacing: 0.0,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          useGoogleFonts: false,
                                                        ),
                                                  ),
                                                ),
                                              ),
                                              Align(
                                                alignment: const AlignmentDirectional(
                                                    -1.0, -1.0),
                                                child: Padding(
                                                  padding: const EdgeInsetsDirectional
                                                      .fromSTEB(
                                                          0.0, 0.0, 20.0, 15.0),
                                                  child: Text(
                                                    promptsItem.answer,
                                                    style: FlutterFlowTheme.of(
                                                            context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          fontSize: 16.0,
                                                          letterSpacing: 0.0,
                                                          useGoogleFonts: false,
                                                          lineHeight: 1.3,
                                                        ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      );
                                    }),
                                  );
                                },
                              ),
                            const Divider(
                              thickness: 1.0,
                              color: Color(0xFFD8DBDF),
                            ),
                            Align(
                              alignment: const AlignmentDirectional(-1.0, 0.0),
                              child: Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    20.0, 15.0, 0.0, 15.0),
                                child: Container(
                                  width: MediaQuery.sizeOf(context).width * 0.8,
                                  decoration: const BoxDecoration(),
                                  child: Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        0.0, 9.0, 0.0, 0.0),
                                    child: Builder(
                                      builder: (context) {
                                        final hobbiesList =
                                            widget.profile?.hobbies.toList() ??
                                                [];
        
                                        return Wrap(
                                          spacing: 10.0,
                                          runSpacing: 9.0,
                                          alignment: WrapAlignment.start,
                                          crossAxisAlignment:
                                              WrapCrossAlignment.start,
                                          direction: Axis.horizontal,
                                          runAlignment: WrapAlignment.start,
                                          verticalDirection:
                                              VerticalDirection.down,
                                          clipBehavior: Clip.none,
                                          children:
                                              List.generate(hobbiesList.length,
                                                  (hobbiesListIndex) {
                                            final hobbiesListItem =
                                                hobbiesList[hobbiesListIndex];
                                            return Container(
                                              decoration: BoxDecoration(
                                                color: const Color(0x00FFFFFF),
                                                borderRadius:
                                                    BorderRadius.circular(24.0),
                                                border: Border.all(
                                                  color: const Color(0xFFBFC1C5),
                                                ),
                                              ),
                                              child: Padding(
                                                padding: const EdgeInsetsDirectional
                                                    .fromSTEB(
                                                        10.0, 6.0, 10.0, 6.0),
                                                child: Text(
                                                  hobbiesListItem,
                                                  style:
                                                      FlutterFlowTheme.of(context)
                                                          .bodyMedium
                                                          .override(
                                                            fontFamily:
                                                                'BT Beau Sans',
                                                            color:
                                                                const Color(0xFF747E90),
                                                            letterSpacing: 0.0,
                                                            fontWeight:
                                                                FontWeight.w600,
                                                            useGoogleFonts: false,
                                                          ),
                                                ),
                                              ),
                                            );
                                          }),
                                        );
                                      },
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            if (widget.profile!.nPictures.length > 1)
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 15.0, 0.0, 0.0),
                                child: StreamBuilder<ImagesRecord>(
                                  stream: ImagesRecord.getDocument(
                                      widget.profile!.nPictures[1]),
                                  builder: (context, snapshot) {
                                    // Customize what your widget looks like when it's loading.
                                    if (!snapshot.hasData) {
                                      return Center(
                                        child: SizedBox(
                                          width: 50.0,
                                          height: 50.0,
                                          child: CircularProgressIndicator(
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                              FlutterFlowTheme.of(context)
                                                  .accent2,
                                            ),
                                          ),
                                        ),
                                      );
                                    }
        
                                    final containerImagesRecord = snapshot.data!;
        
                                    return Container(
                                      width:
                                          MediaQuery.sizeOf(context).width * 1.0,
                                      height: 375.0,
                                      decoration: BoxDecoration(
                                        color: FlutterFlowTheme.of(context)
                                            .secondaryBackground,
                                        borderRadius: BorderRadius.circular(0.0),
                                      ),
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(11.0),
                                        child: Image.network(
                                          containerImagesRecord.url,
                                          width:
                                              MediaQuery.sizeOf(context).width *
                                                  1.0,
                                          height: 375.0,
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            if (_model.moreAboutMe.isNotEmpty)
                              Column(
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  if (!_model.showMoreDetails)
                                    Column(
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        Align(
                                          alignment:
                                              const AlignmentDirectional(-1.0, 0.0),
                                          child: Padding(
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(
                                                    20.0, 15.0, 0.0, 0.0),
                                            child: Container(
                                              width: MediaQuery.sizeOf(context)
                                                      .width *
                                                  0.8,
                                              decoration: const BoxDecoration(),
                                              child: Padding(
                                                padding: const EdgeInsetsDirectional
                                                    .fromSTEB(0.0, 9.0, 0.0, 0.0),
                                                child: Builder(
                                                  builder: (context) {
                                                    final moreAboutMe1 = _model
                                                        .moreAboutMe
                                                        .take(3)
                                                        .toList()
                                                        .take(3)
                                                        .toList();
        
                                                    return Wrap(
                                                      spacing: 10.0,
                                                      runSpacing: 9.0,
                                                      alignment:
                                                          WrapAlignment.start,
                                                      crossAxisAlignment:
                                                          WrapCrossAlignment
                                                              .start,
                                                      direction: Axis.horizontal,
                                                      runAlignment:
                                                          WrapAlignment.start,
                                                      verticalDirection:
                                                          VerticalDirection.down,
                                                      clipBehavior: Clip.none,
                                                      children: List.generate(
                                                          moreAboutMe1.length,
                                                          (moreAboutMe1Index) {
                                                        final moreAboutMe1Item =
                                                            moreAboutMe1[
                                                                moreAboutMe1Index];
                                                        return Container(
                                                          decoration:
                                                              BoxDecoration(
                                                            color:
                                                                const Color(0x00FFFFFF),
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        24.0),
                                                            border: Border.all(
                                                              color: const Color(
                                                                  0xFFBFC1C5),
                                                            ),
                                                          ),
                                                          child: Padding(
                                                            padding:
                                                                const EdgeInsetsDirectional
                                                                    .fromSTEB(
                                                                        10.0,
                                                                        6.0,
                                                                        10.0,
                                                                        6.0),
                                                            child: Text(
                                                              moreAboutMe1Item,
                                                              style: FlutterFlowTheme
                                                                      .of(context)
                                                                  .bodyMedium
                                                                  .override(
                                                                    fontFamily:
                                                                        'BT Beau Sans',
                                                                    color: const Color(
                                                                        0xFF747E90),
                                                                    letterSpacing:
                                                                        0.0,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w600,
                                                                    useGoogleFonts:
                                                                        false,
                                                                  ),
                                                            ),
                                                          ),
                                                        );
                                                      }),
                                                    );
                                                  },
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                        if (_model.moreAboutMe.length > 3)
                                          Align(
                                            alignment:
                                                const AlignmentDirectional(-1.0, 0.0),
                                            child: Padding(
                                              padding:
                                                  const EdgeInsetsDirectional.fromSTEB(
                                                      20.0, 0.0, 0.0, 0.0),
                                              child: Container(
                                                width: MediaQuery.sizeOf(context)
                                                        .width *
                                                    0.8,
                                                decoration: const BoxDecoration(),
                                                child: Padding(
                                                  padding: const EdgeInsetsDirectional
                                                      .fromSTEB(
                                                          0.0, 9.0, 0.0, 0.0),
                                                  child: Wrap(
                                                    spacing: 10.0,
                                                    runSpacing: 9.0,
                                                    alignment:
                                                        WrapAlignment.start,
                                                    crossAxisAlignment:
                                                        WrapCrossAlignment.start,
                                                    direction: Axis.horizontal,
                                                    runAlignment:
                                                        WrapAlignment.start,
                                                    verticalDirection:
                                                        VerticalDirection.down,
                                                    clipBehavior: Clip.none,
                                                    children: [
                                                      Container(
                                                          decoration:
                                                              BoxDecoration(
                                                            color:
                                                                const Color(0xFFEEEFF1),
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        24.0),
                                                            border: Border.all(
                                                              color: const Color(
                                                                  0xFFBFC1C5),
                                                            ),
                                                          ),
                                                          child: Row(
                                                            mainAxisSize:
                                                                MainAxisSize.min,
                                                            children: [
                                                              Padding(
                                                                padding:
                                                                    const EdgeInsetsDirectional
                                                                        .fromSTEB(
                                                                            10.0,
                                                                            6.0,
                                                                            10.0,
                                                                            6.0),
                                                                child: Text(
                                                                  'Show more',
                                                                  style: FlutterFlowTheme.of(
                                                                          context)
                                                                      .bodyMedium
                                                                      .override(
                                                                        fontFamily:
                                                                            'BT Beau Sans',
                                                                        color: const Color(
                                                                            0xFF747E90),
                                                                        letterSpacing:
                                                                            0.0,
                                                                        fontWeight:
                                                                            FontWeight
                                                                                .w600,
                                                                        useGoogleFonts:
                                                                            false,
                                                                      ),
                                                                ),
                                                              ),
                                                              if (!_model
                                                                  .showMoreDetails)
                                                                const Padding(
                                                                  padding:
                                                                      EdgeInsetsDirectional
                                                                          .fromSTEB(
                                                                              0.0,
                                                                              0.0,
                                                                              10.0,
                                                                              0.0),
                                                                  child: FaIcon(
                                                                    FontAwesomeIcons
                                                                        .chevronDown,
                                                                    color: Color(
                                                                        0xFF747E90),
                                                                    size: 14.0,
                                                                  ),
                                                                ),
                                                              if (_model
                                                                  .showMoreDetails)
                                                                const Padding(
                                                                  padding:
                                                                      EdgeInsetsDirectional
                                                                          .fromSTEB(
                                                                              0.0,
                                                                              0.0,
                                                                              10.0,
                                                                              0.0),
                                                                  child: FaIcon(
                                                                    FontAwesomeIcons
                                                                        .chevronUp,
                                                                    color: Color(
                                                                        0xFF747E90),
                                                                    size: 14.0,
                                                                  ),
                                                                ),
                                                            ],
                                                          ),
                                                        ),
                                                      
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                      ],
                                    ),
                                  if (_model.showMoreDetails)
                                    Column(
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        Align(
                                          alignment:
                                              const AlignmentDirectional(-1.0, 0.0),
                                          child: Padding(
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(
                                                    20.0, 15.0, 0.0, 0.0),
                                            child: Container(
                                              width: MediaQuery.sizeOf(context)
                                                      .width *
                                                  0.8,
                                              decoration: const BoxDecoration(),
                                              child: Padding(
                                                padding: const EdgeInsetsDirectional
                                                    .fromSTEB(0.0, 9.0, 0.0, 0.0),
                                                child: Builder(
                                                  builder: (context) {
                                                    final moreAboutMe1 = _model
                                                        .moreAboutMe
                                                        .toList();
        
                                                    return Wrap(
                                                      spacing: 10.0,
                                                      runSpacing: 9.0,
                                                      alignment:
                                                          WrapAlignment.start,
                                                      crossAxisAlignment:
                                                          WrapCrossAlignment
                                                              .start,
                                                      direction: Axis.horizontal,
                                                      runAlignment:
                                                          WrapAlignment.start,
                                                      verticalDirection:
                                                          VerticalDirection.down,
                                                      clipBehavior: Clip.none,
                                                      children: List.generate(
                                                          moreAboutMe1.length,
                                                          (moreAboutMe1Index) {
                                                        final moreAboutMe1Item =
                                                            moreAboutMe1[
                                                                moreAboutMe1Index];
                                                        return Container(
                                                          decoration:
                                                              BoxDecoration(
                                                            color:
                                                                const Color(0x00FFFFFF),
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        24.0),
                                                            border: Border.all(
                                                              color: const Color(
                                                                  0xFFBFC1C5),
                                                            ),
                                                          ),
                                                          child: Padding(
                                                            padding:
                                                                const EdgeInsetsDirectional
                                                                    .fromSTEB(
                                                                        10.0,
                                                                        6.0,
                                                                        10.0,
                                                                        6.0),
                                                            child: Text(
                                                              moreAboutMe1Item,
                                                              style: FlutterFlowTheme
                                                                      .of(context)
                                                                  .bodyMedium
                                                                  .override(
                                                                    fontFamily:
                                                                        'BT Beau Sans',
                                                                    color: const Color(
                                                                        0xFF747E90),
                                                                    letterSpacing:
                                                                        0.0,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w600,
                                                                    useGoogleFonts:
                                                                        false,
                                                                  ),
                                                            ),
                                                          ),
                                                        );
                                                      }),
                                                    );
                                                  },
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                        if (_model.moreAboutMe.length > 3)
                                          Align(
                                            alignment:
                                                const AlignmentDirectional(-1.0, 0.0),
                                            child: Padding(
                                              padding:
                                                  const EdgeInsetsDirectional.fromSTEB(
                                                      20.0, 0.0, 0.0, 0.0),
                                              child: Container(
                                                width: MediaQuery.sizeOf(context)
                                                        .width *
                                                    0.8,
                                                decoration: const BoxDecoration(),
                                                child: Padding(
                                                  padding: const EdgeInsetsDirectional
                                                      .fromSTEB(
                                                          0.0, 9.0, 0.0, 0.0),
                                                  child: Wrap(
                                                    spacing: 10.0,
                                                    runSpacing: 9.0,
                                                    alignment:
                                                        WrapAlignment.start,
                                                    crossAxisAlignment:
                                                        WrapCrossAlignment.start,
                                                    direction: Axis.horizontal,
                                                    runAlignment:
                                                        WrapAlignment.start,
                                                    verticalDirection:
                                                        VerticalDirection.down,
                                                    clipBehavior: Clip.none,
                                                    children: [
                                                      InkWell(
                                                        splashColor:
                                                            Colors.transparent,
                                                        focusColor:
                                                            Colors.transparent,
                                                        hoverColor:
                                                            Colors.transparent,
                                                        highlightColor:
                                                            Colors.transparent,
                                                        onTap: () async {
                                                          _model.showMoreDetails =
                                                              !_model
                                                                  .showMoreDetails;
                                                          safeSetState(() {});
                                                        },
                                                        child: Container(
                                                          decoration:
                                                              BoxDecoration(
                                                            color:
                                                                const Color(0xFFEEEFF1),
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        24.0),
                                                            border: Border.all(
                                                              color: const Color(
                                                                  0xFFBFC1C5),
                                                            ),
                                                          ),
                                                          child: Row(
                                                            mainAxisSize:
                                                                MainAxisSize.min,
                                                            children: [
                                                              Padding(
                                                                padding:
                                                                    const EdgeInsetsDirectional
                                                                        .fromSTEB(
                                                                            10.0,
                                                                            6.0,
                                                                            10.0,
                                                                            6.0),
                                                                child: Text(
                                                                  'Show more',
                                                                  style: FlutterFlowTheme.of(
                                                                          context)
                                                                      .bodyMedium
                                                                      .override(
                                                                        fontFamily:
                                                                            'BT Beau Sans',
                                                                        color: const Color(
                                                                            0xFF747E90),
                                                                        letterSpacing:
                                                                            0.0,
                                                                        fontWeight:
                                                                            FontWeight
                                                                                .w600,
                                                                        useGoogleFonts:
                                                                            false,
                                                                      ),
                                                                ),
                                                              ),
                                                              if (!_model
                                                                  .showMoreDetails)
                                                                const Padding(
                                                                  padding:
                                                                      EdgeInsetsDirectional
                                                                          .fromSTEB(
                                                                              0.0,
                                                                              0.0,
                                                                              10.0,
                                                                              0.0),
                                                                  child: FaIcon(
                                                                    FontAwesomeIcons
                                                                        .chevronDown,
                                                                    color: Color(
                                                                        0xFF747E90),
                                                                    size: 14.0,
                                                                  ),
                                                                ),
                                                              if (_model
                                                                  .showMoreDetails)
                                                                const Padding(
                                                                  padding:
                                                                      EdgeInsetsDirectional
                                                                          .fromSTEB(
                                                                              0.0,
                                                                              0.0,
                                                                              10.0,
                                                                              0.0),
                                                                  child: FaIcon(
                                                                    FontAwesomeIcons
                                                                        .chevronUp,
                                                                    color: Color(
                                                                        0xFF747E90),
                                                                    size: 14.0,
                                                                  ),
                                                                ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                      ],
                                    ),
                                ],
                              ),
                            if (widget.profile!.nPictures.length > 2)
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 15.0, 0.0, 0.0),
                                child: StreamBuilder<ImagesRecord>(
                                  stream: ImagesRecord.getDocument(
                                      widget.profile!.nPictures[2]),
                                  builder: (context, snapshot) {
                                    // Customize what your widget looks like when it's loading.
                                    if (!snapshot.hasData) {
                                      return Center(
                                        child: SizedBox(
                                          width: 50.0,
                                          height: 50.0,
                                          child: CircularProgressIndicator(
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                              FlutterFlowTheme.of(context)
                                                  .accent2,
                                            ),
                                          ),
                                        ),
                                      );
                                    }
        
                                    final containerImagesRecord = snapshot.data!;
        
                                    return Container(
                                      width:
                                          MediaQuery.sizeOf(context).width * 1.0,
                                      height: 375.0,
                                      decoration: BoxDecoration(
                                        color: FlutterFlowTheme.of(context)
                                            .secondaryBackground,
                                        borderRadius: BorderRadius.circular(0.0),
                                      ),
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(11.0),
                                        child: Image.network(
                                          containerImagesRecord.url,
                                          width:
                                              MediaQuery.sizeOf(context).width *
                                                  1.0,
                                          height: 375.0,
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            if (widget.profile!.nPictures.length > 3)
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 15.0, 0.0, 0.0),
                                child: StreamBuilder<ImagesRecord>(
                                  stream: ImagesRecord.getDocument(
                                      widget.profile!.nPictures[3]),
                                  builder: (context, snapshot) {
                                    // Customize what your widget looks like when it's loading.
                                    if (!snapshot.hasData) {
                                      return Center(
                                        child: SizedBox(
                                          width: 50.0,
                                          height: 50.0,
                                          child: CircularProgressIndicator(
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                              FlutterFlowTheme.of(context)
                                                  .accent2,
                                            ),
                                          ),
                                        ),
                                      );
                                    }
        
                                    final containerImagesRecord = snapshot.data!;
        
                                    return Container(
                                      width:
                                          MediaQuery.sizeOf(context).width * 1.0,
                                      height: 375.0,
                                      decoration: BoxDecoration(
                                        color: FlutterFlowTheme.of(context)
                                            .secondaryBackground,
                                        borderRadius: BorderRadius.circular(0.0),
                                      ),
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(11.0),
                                        child: Image.network(
                                          containerImagesRecord.url,
                                          width:
                                              MediaQuery.sizeOf(context).width *
                                                  1.0,
                                          height: 375.0,
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            if (widget.profile!.nPictures.length > 4)
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 15.0, 0.0, 0.0),
                                child: StreamBuilder<ImagesRecord>(
                                  stream: ImagesRecord.getDocument(
                                      widget.profile!.nPictures[4]),
                                  builder: (context, snapshot) {
                                    // Customize what your widget looks like when it's loading.
                                    if (!snapshot.hasData) {
                                      return Center(
                                        child: SizedBox(
                                          width: 50.0,
                                          height: 50.0,
                                          child: CircularProgressIndicator(
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                              FlutterFlowTheme.of(context)
                                                  .accent2,
                                            ),
                                          ),
                                        ),
                                      );
                                    }
        
                                    final containerImagesRecord = snapshot.data!;
        
                                    return Container(
                                      width:
                                          MediaQuery.sizeOf(context).width * 1.0,
                                      height: 375.0,
                                      decoration: BoxDecoration(
                                        color: FlutterFlowTheme.of(context)
                                            .secondaryBackground,
                                        borderRadius: BorderRadius.circular(0.0),
                                      ),
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(11.0),
                                        child: Image.network(
                                          containerImagesRecord.url,
                                          width:
                                              MediaQuery.sizeOf(context).width *
                                                  1.0,
                                          height: 375.0,
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            if (widget.profile!.nPictures.length > 5)
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 15.0, 0.0, 0.0),
                                child: StreamBuilder<ImagesRecord>(
                                  stream: ImagesRecord.getDocument(
                                      widget.profile!.nPictures[5]),
                                  builder: (context, snapshot) {
                                    // Customize what your widget looks like when it's loading.
                                    if (!snapshot.hasData) {
                                      return Center(
                                        child: SizedBox(
                                          width: 50.0,
                                          height: 50.0,
                                          child: CircularProgressIndicator(
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                              FlutterFlowTheme.of(context)
                                                  .accent2,
                                            ),
                                          ),
                                        ),
                                      );
                                    }
        
                                    final containerImagesRecord = snapshot.data!;
        
                                    return Container(
                                      width:
                                          MediaQuery.sizeOf(context).width * 1.0,
                                      height: 375.0,
                                      decoration: BoxDecoration(
                                        color: FlutterFlowTheme.of(context)
                                            .secondaryBackground,
                                        borderRadius: BorderRadius.circular(0.0),
                                      ),
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(11.0),
                                        child: Image.network(
                                          containerImagesRecord.url,
                                          width:
                                              MediaQuery.sizeOf(context).width *
                                                  1.0,
                                          height: 375.0,
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            const Divider(
                              thickness: 1.0,
                              color: Color(0xFFD8DBDF),
                            ),
                          ],
                        ),
                      ),
                      const Align(
                        alignment: AlignmentDirectional(0.0, 0.0),
                        child: Stack(
                          children: [],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
        
        Positioned(
          top: 0,
              child: ClipPath(
                child: Container(
                  height: MediaQuery.of(context).size.height * 0.3,
                  width: MediaQuery.of(context).size.width,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.black87, Colors.transparent], 
                      begin: Alignment.topCenter, 
                      end: Alignment.bottomCenter,
                      tileMode: TileMode.decal)),
                  child: Padding(
          padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
          child: Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              child: Padding(padding: const EdgeInsets.fromLTRB(20, 30, 20, 60),
              child: RichText(
                            text: TextSpan(children: [
                              TextSpan(
                              text: getRemoteConfigString('tutorial_ownprofile_t1'),
                              style: FlutterFlowTheme.of(context).bodyMedium.override(
                                    color: FlutterFlowTheme.of(context).primaryBackground,
                                    fontSize: 15.0,
                                    letterSpacing: 0.0,
                                    useGoogleFonts: false,
                                  ),),]), textAlign: TextAlign.left,))),
          ),
        )
                ),
              ),
            ),
            SafeArea(
          child: Align(
                      alignment: Alignment.bottomLeft,
                      child: Padding(
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(0.0, 50.0, 30.0, 0.0),
                        child: FFButtonWidget(
                          onPressed: () {
                            logSkippedIntro(runtimeType.toString());
                            GoRouter.of(context).goNamed('Discovery');
                          },
                          text: 'Skip',
                          options: FFButtonOptions(
                            height: 40.0,
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                16.0, 0.0, 16.0, 0.0),
                            iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 0.0, 0.0, 0.0),
                            color: const Color(0x004B39EF),
                            textStyle:
                                FlutterFlowTheme.of(context).titleSmall.override(
                                      fontFamily: 'BT Beau Sans',
                                      color: Colors.white,
                                      fontSize: 10.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.normal,
                                      useGoogleFonts: false,
                                    ),
                            elevation: 0.0,
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                        ),
                      ),
                    ),
        ),
        ]
      ),
    );
  }
}