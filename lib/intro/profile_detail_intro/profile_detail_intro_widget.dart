import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import 'package:flutter/rendering.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/general/info_sheet_scrollable/info_sheet_scrollable_widget.dart';
import 'dart:async';
import 'package:rive/rive.dart' hide LinearGradient, Image;
import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'profile_detail_intro_model.dart';
import 'package:chyrpe/intro/intro_amplitude_commons.dart';
export 'profile_detail_intro_model.dart';

class ProfileDetailIntroWidget extends StatefulWidget {
  const ProfileDetailIntroWidget({
    super.key,
    required this.profile,
    required this.showLikeEtcFields,
    required this.isMatch,
    this.like,
    bool? showEverything,
    bool? fromLikesScreen,
    bool? evolvedUpgradeDetail,
  })  : showEverything = showEverything ?? true,
        fromLikesScreen = fromLikesScreen ?? false,
        evolvedUpgradeDetail = evolvedUpgradeDetail ?? false;

  final PublicProfileRecord? profile;
  final bool? showLikeEtcFields;
  final bool? isMatch;
  final DocumentReference? like;
  final bool showEverything;
  final bool fromLikesScreen;
  final bool evolvedUpgradeDetail;

  @override
  State<ProfileDetailIntroWidget> createState() =>
      _ProfileDetailIntroWidgetState();
}

class _ProfileDetailIntroWidgetState extends State<ProfileDetailIntroWidget> {
  late ProfileDetailIntroModel _model;
  final GlobalKey profileDetailIntroStarBtn = GlobalKey(); 

  late StateMachineController _riveController1;
  late StateMachineController _riveController2;
  late StateMachineController _riveController3;
  late ScrollController _scrollController;

  void _onInit1(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    art.addController(ctrl);
    _riveController1 = ctrl;
      _riveController1.isActive = true;
      }
     // Keep it inactive initially
  }

  void _onInit2(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    art.addController(ctrl);
    _riveController2 = ctrl;
      _riveController2.isActive = true;
      }
     // Keep it inactive initially
  }

    void _onInit3(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    art.addController(ctrl);
    _riveController3 = ctrl;
      _riveController3.isActive = true;
      }
     // Keep it inactive initially
  }
  
  List<RRect> _cutoutRects = [];


  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => ProfileDetailIntroModel());
    _scrollController = ScrollController();

    logPageVisitIntro(runtimeType.toString());

    // On page load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {

    Future.delayed(const Duration(milliseconds: 200), () {
      calculatePositionBtn();
      });

  });

  }

    int _currentCutoutStep = 0;

  void _advanceCutouts() {
  setState(() {
    _currentCutoutStep++;
    _calculateCutouts();
  });
}

var cardTextBottomPosition;
bool secondTextVisible = false;

  void calculatePositionBtn() {
    final RenderBox? renderBox = profileDetailIntroStarBtn.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      final Offset offset = renderBox.localToGlobal(Offset.zero);
      final double elementBottom = offset.dy + 50;
      setState(() {
        cardTextBottomPosition = elementBottom; // 10px below the bottom
      });
    }
  }

void _calculateCutouts() {
  List<RRect> rects = [];
  
  if (_currentCutoutStep >= 0 && profileDetailIntroStarBtn.currentContext != null) {
    // Only add profile picture cutout when step is 1
    final RenderBox profileBox = profileDetailIntroStarBtn.currentContext!.findRenderObject() as RenderBox;
    final Size profileSize = profileBox.size;
    final Offset profilePosition = profileBox.localToGlobal(Offset.zero);
    rects.add(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(
          profilePosition.dx,
          profilePosition.dy,
          profileSize.width,
          profileSize.height,
        ),
        const Radius.circular(100),
      ),
    );
    
  setState(() {
    _cutoutRects = rects;
  });
  }
}

  @override
  void dispose() {
    _model.dispose();
    _scrollController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return GestureDetector(
      onTap: () {
        if (_currentCutoutStep == 0) {
              _scrollController.animateTo(0, duration: const Duration(milliseconds: 5), curve: Curves.ease);
              Future.delayed(const Duration(milliseconds: 80), () {
                _advanceCutouts();
              });
              
              } else {
                context.pushReplacementNamed(
            'DiscoveryIntro3',
            extra: <String, dynamic>{
              kTransitionInfoKey: const TransitionInfo(
                hasTransition: true,
                transitionType: PageTransitionType.topToBottom,
                duration: Duration(milliseconds: 100),
              ),
            },);
      }
      },
      child: Stack(
        children: [
          Scaffold(
            key: scaffoldKey,
            backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
            body: Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 45.0),
              child: Container(
                width: MediaQuery.sizeOf(context).width * 1.0,
                height: double.infinity,
                decoration: BoxDecoration(
                  color: FlutterFlowTheme.of(context).secondaryBackground,
                ),
                child: SizedBox(
                  width: double.infinity,
                  height: double.infinity,
                  child: Stack(
                    children: [
                      SingleChildScrollView(
                        controller: _scrollController,
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            if (widget.showEverything)
                              Column(
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  SizedBox(
                                    width:
                                        MediaQuery.sizeOf(context).width * 1.0,
                                    height: MediaQuery.sizeOf(context).height *
                                        0.72,
                                    child: Stack(
                                      children: [
                                        ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(10.75),
                                          child: Image.network(
                                            widget.profile!.profilePhoto,
                                            width: MediaQuery.sizeOf(context)
                                                    .width *
                                                1.0,
                                            height: MediaQuery.sizeOf(context)
                                                    .height *
                                                0.72,
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                        Container(
                                          width: MediaQuery.sizeOf(context)
                                                  .width *
                                              1.0,
                                          height: MediaQuery.sizeOf(context)
                                                  .height *
                                              0.72,
                                          decoration: BoxDecoration(
                                            gradient: const LinearGradient(
                                              colors: [
                                                Colors.transparent,
                                                Color(0x4B000000),
                                                Colors.black
                                              ],
                                              stops: [0.0, 0.7, 1.0],
                                              begin: AlignmentDirectional(
                                                  0.0, -1.0),
                                              end: AlignmentDirectional(
                                                  0, 1.0),
                                            ),
                                            borderRadius:
                                                BorderRadius.circular(10.75),
                                          ),
                                        ),
                                        Align(
                                          alignment:
                                              const AlignmentDirectional(-1.0, 1.0),
                                          child: Card(
                                            clipBehavior:
                                                Clip.antiAliasWithSaveLayer,
                                            color: Colors.transparent,
                                            elevation: 0.0,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(12.0),
                                            ),
                                            child: Padding(
                                              padding: const EdgeInsetsDirectional
                                                  .fromSTEB(
                                                      17.0, 0.0, 0.0, 16.0),
                                              child: Column(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Padding(
                                                    padding:
                                                        const EdgeInsetsDirectional
                                                            .fromSTEB(0.0, 0.0,
                                                                0.0, 9.0),
                                                    child: Row(
                                                      mainAxisSize:
                                                          MainAxisSize.max,
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .center,
                                                      children: [
                                                        AutoSizeText(
                                                          valueOrDefault<
                                                              String>(
                                                            widget.profile
                                                                ?.publicName,
                                                            'name',
                                                          ),
                                                          minFontSize: 16.0,
                                                          style: FlutterFlowTheme
                                                                  .of(context)
                                                              .bodyMedium
                                                              .override(
                                                                fontFamily:
                                                                    'BT Beau Sans',
                                                                color: FlutterFlowTheme.of(
                                                                        context)
                                                                    .info,
                                                                fontSize: 32.0,
                                                                letterSpacing:
                                                                    0.0,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w600,
                                                                useGoogleFonts:
                                                                    false,
                                                              ),
                                                        ),
                                                        Padding(
                                                          padding:
                                                              const EdgeInsetsDirectional
                                                                  .fromSTEB(
                                                                      11.0,
                                                                      0.0,
                                                                      0.0,
                                                                      0.0),
                                                          child: Text(
                                                            valueOrDefault<
                                                                String>(
                                                              widget
                                                                  .profile?.age
                                                                  .toString(),
                                                              '20',
                                                            ),
                                                            style: FlutterFlowTheme
                                                                    .of(context)
                                                                .bodyMedium
                                                                .override(
                                                                  fontFamily:
                                                                      'BT Beau Sans',
                                                                  color: FlutterFlowTheme.of(
                                                                          context)
                                                                      .info,
                                                                  fontSize:
                                                                      24.0,
                                                                  letterSpacing:
                                                                      0.0,
                                                                  useGoogleFonts:
                                                                      false,
                                                                ),
                                                          ),
                                                        ),
                                                        if (widget.profile!
                                                                .supporterBadgeShown &&
                                                            getRemoteConfigBool(
                                                                'profile_supporter_star_active'))
                                                          Padding(
                                                            padding:
                                                                const EdgeInsetsDirectional
                                                                    .fromSTEB(
                                                                        12.0,
                                                                        0.0,
                                                                        0.0,
                                                                        0.0),
                                                            child: ClipRRect(
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          8.0),
                                                              child:
                                                                  Image.asset(
                                                                'assets/images/Supporter_Badge_Star.png',
                                                                width: 26.0,
                                                                height: 26.0,
                                                                fit: BoxFit
                                                                    .cover,
                                                              ),
                                                            ),
                                                          ),
                                                      ],
                                                    ),
                                                  ),
                                                  if (widget.profile!
                                                          .publicRoleShown &&
                                                      !getRemoteConfigBool(
                                                          'hideRoleForAll') &&
                                                      widget.profile!
                                                          .hasPublicRole())
                                                    Row(
                                                      mainAxisSize:
                                                          MainAxisSize.max,
                                                      children: [
                                                        Text(
                                                          widget.profile?.specificRole !=
                                                                      null &&
                                                                  widget.profile
                                                                          ?.specificRole !=
                                                                      ''
                                                              ? '${widget.profile?.publicRole} (${widget.profile?.specificRole})'
                                                              : widget.profile!
                                                                  .publicRole,
                                                          style: FlutterFlowTheme
                                                                  .of(context)
                                                              .bodyMedium
                                                              .override(
                                                                fontFamily:
                                                                    'BT Beau Sans',
                                                                color: FlutterFlowTheme.of(
                                                                        context)
                                                                    .info,
                                                                fontSize: 16.0,
                                                                letterSpacing:
                                                                    0.0,
                                                                useGoogleFonts:
                                                                    false,
                                                              ),
                                                        ),
                                                      ].divide(
                                                          const SizedBox(width: 4.0)),
                                                    ),
                                                  Align(
                                                    alignment:
                                                        const AlignmentDirectional(
                                                            -1.0, 0.0),
                                                    child: Builder(
                                                      builder: (context) {
                                                        if (!widget.profile!
                                                            .cityHidden) {
                                                          return Padding(
                                                            padding:
                                                                const EdgeInsetsDirectional
                                                                    .fromSTEB(
                                                                        0.0,
                                                                        0.0,
                                                                        0.0,
                                                                        2.0),
                                                            child: Text(
                                                              valueOrDefault<
                                                                  String>(
                                                                widget.profile
                                                                    ?.city,
                                                                'City',
                                                              ),
                                                              textAlign:
                                                                  TextAlign
                                                                      .start,
                                                              style: FlutterFlowTheme
                                                                      .of(context)
                                                                  .bodyMedium
                                                                  .override(
                                                                    fontFamily:
                                                                        'BT Beau Sans',
                                                                    color: FlutterFlowTheme.of(
                                                                            context)
                                                                        .info,
                                                                    fontSize:
                                                                        16.0,
                                                                    letterSpacing:
                                                                        0.0,
                                                                    useGoogleFonts:
                                                                        false,
                                                                  ),
                                                            ),
                                                          );
                                                        } else {
                                                          return Padding(
                                                            padding:
                                                                const EdgeInsetsDirectional
                                                                    .fromSTEB(
                                                                        0.0,
                                                                        0.0,
                                                                        0.0,
                                                                        2.0),
                                                            child: Text(
                                                              getRemoteConfigString(
                                                                  'profile_location_private'),
                                                              textAlign:
                                                                  TextAlign
                                                                      .start,
                                                              style: FlutterFlowTheme
                                                                      .of(context)
                                                                  .bodyMedium
                                                                  .override(
                                                                    fontFamily:
                                                                        'BT Beau Sans',
                                                                    color: FlutterFlowTheme.of(
                                                                            context)
                                                                        .info,
                                                                    fontSize:
                                                                        16.0,
                                                                    letterSpacing:
                                                                        0.0,
                                                                    useGoogleFonts:
                                                                        false,
                                                                  ),
                                                            ),
                                                          );
                                                        }
                                                      },
                                                    ),
                                                  ),
                                                  if (getRemoteConfigBool(
                                                      'showKinks'))
                                                    Align(
                                                      alignment:
                                                          const AlignmentDirectional(
                                                              -1.0, 0.0),
                                                      child: Container(
                                                        width:
                                                            MediaQuery.sizeOf(
                                                                        context)
                                                                    .width *
                                                                0.6,
                                                        decoration:
                                                            const BoxDecoration(),
                                                        child: Padding(
                                                          padding:
                                                              const EdgeInsetsDirectional
                                                                  .fromSTEB(
                                                                      0.0,
                                                                      9.0,
                                                                      0.0,
                                                                      0.0),
                                                          child: Builder(
                                                            builder: (context) {
                                                              final kinks = widget
                                                                      .profile
                                                                      ?.playPreferences
                                                                      .toList() ??
                                                                  [];
                                                        
                                                              return Wrap(
                                                                spacing: 10.0,
                                                                runSpacing: 9.0,
                                                                alignment:
                                                                    WrapAlignment
                                                                        .start,
                                                                crossAxisAlignment:
                                                                    WrapCrossAlignment
                                                                        .start,
                                                                direction: Axis
                                                                    .horizontal,
                                                                runAlignment:
                                                                    WrapAlignment
                                                                        .start,
                                                                verticalDirection:
                                                                    VerticalDirection
                                                                        .down,
                                                                clipBehavior:
                                                                    Clip.none,
                                                                children: List.generate(
                                                                    kinks
                                                                        .length,
                                                                    (kinksIndex) {
                                                                  final kinksItem =
                                                                      kinks[
                                                                          kinksIndex];
                                                                  return Container(
                                                                    decoration:
                                                                        BoxDecoration(
                                                                      color: const Color(
                                                                          0x00FFFFFF),
                                                                      borderRadius:
                                                                          BorderRadius.circular(
                                                                              24.0),
                                                                      border:
                                                                          Border
                                                                              .all(
                                                                        color: FlutterFlowTheme.of(context)
                                                                            .info,
                                                                      ),
                                                                    ),
                                                                    child:
                                                                        Padding(
                                                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                                                          10.0,
                                                                          6.0,
                                                                          10.0,
                                                                          6.0),
                                                                      child:
                                                                          Text(
                                                                        kinksItem,
                                                                        style: FlutterFlowTheme.of(context)
                                                                            .bodyMedium
                                                                            .override(
                                                                              fontFamily: 'BT Beau Sans',
                                                                              color: FlutterFlowTheme.of(context).info,
                                                                              letterSpacing: 0.0,
                                                                              fontWeight: FontWeight.w600,
                                                                              useGoogleFonts: false,
                                                                            ),
                                                                      ),
                                                                    ),
                                                                  );
                                                                }),
                                                              );
                                                            },
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                        Column(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Align(
                                              alignment: const AlignmentDirectional(
                                                  1.0, -1.0),
                                              child: Padding(
                                                padding: const EdgeInsetsDirectional
                                                    .fromSTEB(
                                                        0.0, 10.0, 10.0, 0.0),
                                                child: Container(
                                                  width: 40.0,
                                                  height: 40.0,
                                                  decoration: BoxDecoration(
                                                    color: FlutterFlowTheme.of(
                                                            context)
                                                        .primaryBackground,
                                                    shape: BoxShape.circle,
                                                    border: Border.all(
                                                      color:
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .secondaryText,
                                                    ),
                                                  ),
                                                  child: Align(
                                                    alignment:
                                                        const AlignmentDirectional(
                                                            0.0, 0.0),
                                                    child: Padding(
                                                      padding:
                                                          const EdgeInsetsDirectional
                                                              .fromSTEB(
                                                                  2.0,
                                                                  2.0,
                                                                  2.0,
                                                                  2.0),
                                                      child: Icon(
                                                        Icons.close_rounded,
                                                        color:
                                                            FlutterFlowTheme.of(
                                                                    context)
                                                                .secondaryText,
                                                        size: 20.0,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            
                                              Align(
                                                alignment: const AlignmentDirectional(
                                                    1.0, -1.0),
                                                child: Builder(
                                                  builder: (context) => Padding(
                                                    padding:
                                                        const EdgeInsetsDirectional
                                                            .fromSTEB(0.0, 10.0,
                                                                10.0, 0.0),
                                                    child: AuthUserStreamWidget(
                                                      builder: (context) =>
                                                          Container(
                                                            key: profileDetailIntroStarBtn,
                                                            width: 40.0,
                                                            height: 40.0,
                                                            decoration:
                                                                BoxDecoration(
                                                              color: FlutterFlowTheme
                                                                      .of(context)
                                                                  .primaryBackground,
                                                              shape:
                                                                  BoxShape.circle,
                                                              border: Border.all(
                                                                color: FlutterFlowTheme
                                                                        .of(context)
                                                                    .secondaryText,
                                                              ),
                                                            ),
                                                            child: Align(
                                                              alignment:
                                                                  const AlignmentDirectional(
                                                                      0.0, 0.0),
                                                              child: Padding(
                                                                padding:
                                                                    const EdgeInsetsDirectional
                                                                        .fromSTEB(
                                                                            2.0,
                                                                            2.0,
                                                                            2.0,
                                                                            2.0),
                                                                child: Icon(
                                                                  Icons
                                                                      .star_border_rounded,
                                                                  color: FlutterFlowTheme.of(
                                                                          context)
                                                                      .secondaryText,
                                                                  size: 28.0,
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        20.0, 0.0, 0.0, 0.0),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        Align(
                                          alignment:
                                              const AlignmentDirectional(-1.0, 0.0),
                                          child: Padding(
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(
                                                    0.0, 19.0, 0.0, 11.0),
                                            child: Text(
                                              'More about me',
                                              textAlign: TextAlign.center,
                                              style: FlutterFlowTheme.of(context)
                                                  .bodyMedium
                                                  .override(
                                                    fontFamily: 'BT Beau Sans',
                                                    fontSize: 20.0,
                                                    letterSpacing: 0.0,
                                                    fontWeight: FontWeight.w600,
                                                    useGoogleFonts: false,
                                                  ),
                                            ),
                                          ),
                                        ),
                                        if (!getRemoteConfigBool(
                                                'hideXpForAll') &&
                                            widget.profile!.hasExperienceLevel())
                                          Padding(
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(
                                                    0.0, 0.0, 0.0, 4.0),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.max,
                                              children: [
                                                const FaIcon(
                                                  FontAwesomeIcons.dotCircle,
                                                  color: Color(0xFF4D535B),
                                                  size: 16.0,
                                                ),
                                                Padding(
                                                  padding: const EdgeInsetsDirectional
                                                      .fromSTEB(
                                                          1.0, 0.0, 0.0, 2.0),
                                                  child: Text(
                                                    valueOrDefault<String>(
                                                      widget.profile
                                                          ?.experienceLevel,
                                                      '-',
                                                    ),
                                                    style: FlutterFlowTheme.of(
                                                            context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          color:
                                                              const Color(0xFF4D535B),
                                                          fontSize: 16.0,
                                                          letterSpacing: 0.0,
                                                          useGoogleFonts: false,
                                                        ),
                                                  ),
                                                ),
                                              ].divide(const SizedBox(width: 4.0)),
                                            ),
                                          ),
                                        if (widget.profile?.hasHeight() ?? true)
                                          Padding(
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(
                                                    0.0, 0.0, 0.0, 4.0),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.max,
                                              children: [
                                                const FaIcon(
                                                  FontAwesomeIcons.ruler,
                                                  color: Color(0xFF4D535B),
                                                  size: 16.0,
                                                ),
                                                Padding(
                                                  padding: const EdgeInsetsDirectional
                                                      .fromSTEB(
                                                          1.0, 0.0, 0.0, 2.0),
                                                  child: Text(
                                                    '${formatNumber(
                                                      widget.profile!.height /
                                                          100,
                                                      formatType:
                                                          FormatType.custom,
                                                      format: '#.##',
                                                      locale: '',
                                                    )}m',
                                                    style: FlutterFlowTheme.of(
                                                            context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          color:
                                                              const Color(0xFF4D535B),
                                                          fontSize: 16.0,
                                                          letterSpacing: 0.0,
                                                          useGoogleFonts: false,
                                                        ),
                                                  ),
                                                ),
                                              ].divide(const SizedBox(width: 4.0)),
                                            ),
                                          ),
                                        if (widget.profile?.hasGender() ?? true)
                                          Padding(
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(
                                                    0.0, 0.0, 0.0, 4.0),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.max,
                                              children: [
                                                const Icon(
                                                  Icons.man_4,
                                                  color: Color(0xFF4D535B),
                                                  size: 20.0,
                                                ),
                                                Padding(
                                                  padding: const EdgeInsetsDirectional
                                                      .fromSTEB(
                                                          0.0, 0.0, 0.0, 2.0),
                                                  child: Text(
                                                    valueOrDefault<String>(
                                                      () {
                                                        if (widget.profile
                                                                    ?.specificGender !=
                                                                null &&
                                                            widget.profile
                                                                    ?.specificGender !=
                                                                '') {
                                                          return '${widget.profile!.alternativeG ? widget.profile?.displayG : widget.profile?.gender?.name} (${widget.profile?.specificGender})';
                                                        } else if (widget
                                                            .profile!
                                                            .alternativeG) {
                                                          return widget
                                                              .profile?.displayG;
                                                        } else {
                                                          return widget.profile
                                                              ?.gender?.name;
                                                        }
                                                      }(),
                                                      'Gender cannot be fetched',
                                                    ),
                                                    textAlign: TextAlign.start,
                                                    style: FlutterFlowTheme.of(
                                                            context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          color:
                                                              const Color(0xFF4D535B),
                                                          fontSize: 16.0,
                                                          letterSpacing: 0.0,
                                                          useGoogleFonts: false,
                                                        ),
                                                  ),
                                                ),
                                              ].divide(const SizedBox(width: 4.0)),
                                            ),
                                          ),
                                        if (widget.profile
                                                ?.hasRelationPreferences() ??
                                            true)
                                          Padding(
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(
                                                    0.0, 0.0, 0.0, 4.0),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.max,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.start,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                const Icon(
                                                  Icons.people,
                                                  color: Color(0xFF4D535B),
                                                  size: 20.0,
                                                ),
                                                Builder(
                                                  builder: (context) {
                                                    final relationPrefs = widget
                                                            .profile
                                                            ?.relationPreferences
                                                            .toList() ??
                                                        [];
                      
                                                    return Column(
                                                      mainAxisSize:
                                                          MainAxisSize.max,
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: List.generate(
                                                          relationPrefs.length,
                                                          (relationPrefsIndex) {
                                                        final relationPrefsItem =
                                                            relationPrefs[
                                                                relationPrefsIndex];
                                                        return Padding(
                                                          padding:
                                                              const EdgeInsetsDirectional
                                                                  .fromSTEB(
                                                                      0.0,
                                                                      0.0,
                                                                      0.0,
                                                                      2.0),
                                                          child: Text(
                                                            relationPrefsItem,
                                                            style: FlutterFlowTheme
                                                                    .of(context)
                                                                .bodyMedium
                                                                .override(
                                                                  fontFamily:
                                                                      'BT Beau Sans',
                                                                  color: const Color(
                                                                      0xFF4D535B),
                                                                  fontSize: 16.0,
                                                                  letterSpacing:
                                                                      0.0,
                                                                  useGoogleFonts:
                                                                      false,
                                                                ),
                                                          ),
                                                        );
                                                      }),
                                                    );
                                                  },
                                                ),
                                              ].divide(const SizedBox(width: 4.0)),
                                            ),
                                          ),
                                        if (widget.profile?.hasJob() ?? true)
                                          Padding(
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(
                                                    0.0, 0.0, 0.0, 4.0),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.max,
                                              children: [
                                                const Icon(
                                                  Icons.business_center_rounded,
                                                  color: Color(0xFF4D535B),
                                                  size: 18.0,
                                                ),
                                                Padding(
                                                  padding: const EdgeInsetsDirectional
                                                      .fromSTEB(
                                                          0.0, 0.0, 0.0, 2.0),
                                                  child: Text(
                                                    valueOrDefault<String>(
                                                      widget.profile?.job,
                                                      'Prefer not to say',
                                                    ),
                                                    textAlign: TextAlign.start,
                                                    style: FlutterFlowTheme.of(
                                                            context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          color:
                                                              const Color(0xFF4D535B),
                                                          fontSize: 16.0,
                                                          letterSpacing: 0.0,
                                                          useGoogleFonts: false,
                                                        ),
                                                  ),
                                                ),
                                              ].divide(const SizedBox(width: 4.0)),
                                            ),
                                          ),
                                        if (widget.profile?.hasEducation() ??
                                            true)
                                          Padding(
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(
                                                    0.0, 0.0, 0.0, 4.0),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.max,
                                              children: [
                                                const Padding(
                                                  padding: EdgeInsetsDirectional
                                                      .fromSTEB(
                                                          0.0, 0.0, 2.0, 0.0),
                                                  child: FaIcon(
                                                    FontAwesomeIcons.building,
                                                    color: Color(0xFF4D535B),
                                                    size: 18.0,
                                                  ),
                                                ),
                                                Padding(
                                                  padding: const EdgeInsetsDirectional
                                                      .fromSTEB(
                                                          1.0, 0.0, 0.0, 2.0),
                                                  child: Text(
                                                    valueOrDefault<String>(
                                                      widget.profile?.education,
                                                      'Prefer not to say',
                                                    ),
                                                    textAlign: TextAlign.start,
                                                    style: FlutterFlowTheme.of(
                                                            context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          color:
                                                              const Color(0xFF4D535B),
                                                          fontSize: 16.0,
                                                          letterSpacing: 0.0,
                                                          useGoogleFonts: false,
                                                        ),
                                                  ),
                                                ),
                                              ].divide(const SizedBox(width: 4.0)),
                                            ),
                                          ),
                                        if (widget.profile?.findom ?? true)
                                          Padding(
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(
                                                    0.0, 15.0, 0.0, 15.0),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.max,
                                              children: [
                                                Padding(
                                                  padding: const EdgeInsetsDirectional
                                                      .fromSTEB(
                                                          5.0, 3.0, 5.0, 2.0),
                                                  child: Text(
                                                    'Findom',
                                                    style: FlutterFlowTheme.of(
                                                            context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          color:
                                                              const Color(0xFF4D535B),
                                                          fontSize: 16.0,
                                                          letterSpacing: 0.0,
                                                          useGoogleFonts: false,
                                                        ),
                                                  ),
                                                ),
                                                Align(
                                                  alignment: const AlignmentDirectional(
                                                      -1.0, 0.0),
                                                  child: InkWell(
                                                    splashColor:
                                                        Colors.transparent,
                                                    focusColor:
                                                        Colors.transparent,
                                                    hoverColor:
                                                        Colors.transparent,
                                                    highlightColor:
                                                        Colors.transparent,
                                                    onTap: () async {
                                                      await showModalBottomSheet(
                                                        isScrollControlled: true,
                                                        backgroundColor:
                                                            Colors.transparent,
                                                        enableDrag: false,
                                                        context: context,
                                                        builder: (context) {
                                                          return GestureDetector(
                                                            onTap: () =>
                                                                FocusScope.of(
                                                                        context)
                                                                    .unfocus(),
                                                            child: Padding(
                                                              padding: MediaQuery
                                                                  .viewInsetsOf(
                                                                      context),
                                                              child:
                                                                  InfoSheetScrollableWidget(
                                                                title: getRemoteConfigString(
                                                                    'findom_info_title'),
                                                                body: getRemoteConfigString(
                                                                    'findom_info_body'),
                                                              ),
                                                            ),
                                                          );
                                                        },
                                                      ).then((value) =>
                                                          safeSetState(() {}));
                                                    },
                                                    child: Text(
                                                      getRemoteConfigString(
                                                          'findom_info_title'),
                                                      style: FlutterFlowTheme.of(
                                                              context)
                                                          .bodyMedium
                                                          .override(
                                                            fontFamily:
                                                                'BT Beau Sans',
                                                            color:
                                                                const Color(0xFF4F5865),
                                                            letterSpacing: 0.0,
                                                            fontWeight:
                                                                FontWeight.w600,
                                                            decoration:
                                                                TextDecoration
                                                                    .underline,
                                                            useGoogleFonts: false,
                                                          ),
                                                    ),
                                                  ),
                                                ),
                                              ].divide(const SizedBox(width: 4.0)),
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                                  const Divider(
                                    thickness: 1.0,
                                    color: Color(0xFFD8DBDF),
                                  ),
                                  if (widget.profile?.hasBio() ?? true)
                                    Align(
                                      alignment: const AlignmentDirectional(-1.0, -1.0),
                                      child: Padding(
                                        padding: const EdgeInsetsDirectional.fromSTEB(
                                            20.0, 15.0, 20.0, 15.0),
                                        child: Text(
                                          valueOrDefault<String>(
                                            widget.profile?.bio,
                                            '.',
                                          ),
                                          style: FlutterFlowTheme.of(context)
                                              .bodyMedium
                                              .override(
                                                fontFamily: 'BT Beau Sans',
                                                fontSize: 16.0,
                                                letterSpacing: 0.0,
                                                useGoogleFonts: false,
                                                lineHeight: 1.3,
                                              ),
                                        ),
                                      ),
                                    ),
                                  if (widget.profile?.prompts != null &&
                                      (widget.profile?.prompts)!.isNotEmpty)
                                    Builder(
                                      builder: (context) {
                                        final prompts =
                                            widget.profile?.prompts.toList() ??
                                                [];
                      
                                        return Column(
                                          mainAxisSize: MainAxisSize.max,
                                          children: List.generate(prompts.length,
                                              (promptsIndex) {
                                            final promptsItem =
                                                prompts[promptsIndex];
                                            return Visibility(
                                              visible: (promptsItem.answer != '') &&
                                                  (promptsItem.answer != ''),
                                              child: Padding(
                                                padding: const EdgeInsetsDirectional
                                                    .fromSTEB(
                                                        20.0, 0.0, 0.0, 0.0),
                                                child: Column(
                                                  mainAxisSize: MainAxisSize.max,
                                                  children: [
                                                    const Divider(
                                                      thickness: 1.0,
                                                      color: Color(0xFFD8DBDF),
                                                    ),
                                                    Align(
                                                      alignment:
                                                          const AlignmentDirectional(
                                                              -1.0, -1.0),
                                                      child: Padding(
                                                        padding:
                                                            const EdgeInsetsDirectional
                                                                .fromSTEB(
                                                                    0.0,
                                                                    15.0,
                                                                    20.0,
                                                                    8.0),
                                                        child: Text(
                                                          promptsItem.prompt,
                                                          style: FlutterFlowTheme
                                                                  .of(context)
                                                              .bodyMedium
                                                              .override(
                                                                fontFamily:
                                                                    'BT Beau Sans',
                                                                fontSize: 16.0,
                                                                letterSpacing:
                                                                    0.0,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w600,
                                                                useGoogleFonts:
                                                                    false,
                                                              ),
                                                        ),
                                                      ),
                                                    ),
                                                    Align(
                                                      alignment:
                                                          const AlignmentDirectional(
                                                              -1.0, -1.0),
                                                      child: Padding(
                                                        padding:
                                                            const EdgeInsetsDirectional
                                                                .fromSTEB(
                                                                    0.0,
                                                                    0.0,
                                                                    20.0,
                                                                    15.0),
                                                        child: Text(
                                                          promptsItem.answer,
                                                          style: FlutterFlowTheme
                                                                  .of(context)
                                                              .bodyMedium
                                                              .override(
                                                                fontFamily:
                                                                    'BT Beau Sans',
                                                                fontSize: 16.0,
                                                                letterSpacing:
                                                                    0.0,
                                                                useGoogleFonts:
                                                                    false,
                                                                lineHeight: 1.3,
                                                              ),
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            );
                                          }),
                                        );
                                      },
                                    ),
                                  const Divider(
                                    thickness: 1.0,
                                    color: Color(0xFFD8DBDF),
                                  ),
                                  if (widget.profile!.hobbies.isNotEmpty)
                                    Align(
                                      alignment: const AlignmentDirectional(-1.0, 0.0),
                                      child: Padding(
                                        padding: const EdgeInsetsDirectional.fromSTEB(
                                            20.0, 15.0, 0.0, 15.0),
                                        child: Container(
                                          width:
                                              MediaQuery.sizeOf(context).width *
                                                  0.8,
                                          decoration: const BoxDecoration(),
                                          child: Padding(
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(
                                                    0.0, 9.0, 0.0, 0.0),
                                            child: Builder(
                                              builder: (context) {
                                                final hobbiesList = widget
                                                        .profile?.hobbies
                                                        .toList() ??
                                                    [];
                      
                                                return Wrap(
                                                  spacing: 10.0,
                                                  runSpacing: 9.0,
                                                  alignment: WrapAlignment.start,
                                                  crossAxisAlignment:
                                                      WrapCrossAlignment.start,
                                                  direction: Axis.horizontal,
                                                  runAlignment:
                                                      WrapAlignment.start,
                                                  verticalDirection:
                                                      VerticalDirection.down,
                                                  clipBehavior: Clip.none,
                                                  children: List.generate(
                                                      hobbiesList.length,
                                                      (hobbiesListIndex) {
                                                    final hobbiesListItem =
                                                        hobbiesList[
                                                            hobbiesListIndex];
                                                    return Container(
                                                      decoration: BoxDecoration(
                                                        color: const Color(0x00FFFFFF),
                                                        borderRadius:
                                                            BorderRadius.circular(
                                                                24.0),
                                                        border: Border.all(
                                                          color:
                                                              const Color(0xFFBFC1C5),
                                                        ),
                                                      ),
                                                      child: Padding(
                                                        padding:
                                                            const EdgeInsetsDirectional
                                                                .fromSTEB(
                                                                    10.0,
                                                                    6.0,
                                                                    10.0,
                                                                    6.0),
                                                        child: Text(
                                                          hobbiesListItem,
                                                          style:
                                                              FlutterFlowTheme.of(
                                                                      context)
                                                                  .bodyMedium
                                                                  .override(
                                                                    fontFamily:
                                                                        'BT Beau Sans',
                                                                    color: const Color(
                                                                        0xFF747E90),
                                                                    letterSpacing:
                                                                        0.0,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w600,
                                                                    useGoogleFonts:
                                                                        false,
                                                                  ),
                                                        ),
                                                      ),
                                                    );
                                                  }),
                                                );
                                              },
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  if (widget.profile!.nPictures.length >= 2)
                                    Padding(
                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                          0.0, 15.0, 0.0, 0.0),
                                      child: StreamBuilder<ImagesRecord>(
                                        stream: ImagesRecord.getDocument(
                                            widget.profile!.nPictures[1]),
                                        builder: (context, snapshot) {
                                          // Customize what your widget looks like when it's loading.
                                          if (!snapshot.hasData) {
                                            return Center(
                                              child: SizedBox(
                                                width: 50.0,
                                                height: 50.0,
                                                child: CircularProgressIndicator(
                                                  valueColor:
                                                      AlwaysStoppedAnimation<
                                                          Color>(
                                                    FlutterFlowTheme.of(context)
                                                        .accent2,
                                                  ),
                                                ),
                                              ),
                                            );
                                          }
                      
                                          final containerImagesRecord =
                                              snapshot.data!;
                      
                                          return Container(
                                            width:
                                                MediaQuery.sizeOf(context).width *
                                                    1.0,
                                            height: 375.0,
                                            decoration: BoxDecoration(
                                              color: FlutterFlowTheme.of(context)
                                                  .secondaryBackground,
                                              borderRadius:
                                                  BorderRadius.circular(0.0),
                                            ),
                                            child: ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(11.0),
                                              child: Image.network(
                                                containerImagesRecord.url,
                                                width: MediaQuery.sizeOf(context)
                                                        .width *
                                                    1.0,
                                                height: 375.0,
                                                fit: BoxFit.cover,
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  if (_model.moreAboutMe.isNotEmpty)
                                    Column(
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        if (!_model.showMoreDetails)
                                          Column(
                                            mainAxisSize: MainAxisSize.max,
                                            children: [
                                              Align(
                                                alignment: const AlignmentDirectional(
                                                    -1.0, 0.0),
                                                child: Padding(
                                                  padding: const EdgeInsetsDirectional
                                                      .fromSTEB(
                                                          20.0, 15.0, 0.0, 0.0),
                                                  child: Container(
                                                    width:
                                                        MediaQuery.sizeOf(context)
                                                                .width *
                                                            0.8,
                                                    decoration: const BoxDecoration(),
                                                    child: Padding(
                                                      padding:
                                                          const EdgeInsetsDirectional
                                                              .fromSTEB(0.0, 9.0,
                                                                  0.0, 0.0),
                                                      child: Builder(
                                                        builder: (context) {
                                                          final moreAboutMe1 =
                                                              _model.moreAboutMe
                                                                  .take(3)
                                                                  .toList()
                                                                  .take(3)
                                                                  .toList();
                      
                                                          return Wrap(
                                                            spacing: 10.0,
                                                            runSpacing: 9.0,
                                                            alignment:
                                                                WrapAlignment
                                                                    .start,
                                                            crossAxisAlignment:
                                                                WrapCrossAlignment
                                                                    .start,
                                                            direction:
                                                                Axis.horizontal,
                                                            runAlignment:
                                                                WrapAlignment
                                                                    .start,
                                                            verticalDirection:
                                                                VerticalDirection
                                                                    .down,
                                                            clipBehavior:
                                                                Clip.none,
                                                            children: List.generate(
                                                                moreAboutMe1
                                                                    .length,
                                                                (moreAboutMe1Index) {
                                                              final moreAboutMe1Item =
                                                                  moreAboutMe1[
                                                                      moreAboutMe1Index];
                                                              return Container(
                                                                decoration:
                                                                    BoxDecoration(
                                                                  color: const Color(
                                                                      0x00FFFFFF),
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              24.0),
                                                                  border:
                                                                      Border.all(
                                                                    color: const Color(
                                                                        0xFFBFC1C5),
                                                                  ),
                                                                ),
                                                                child: Padding(
                                                                  padding: const EdgeInsetsDirectional
                                                                      .fromSTEB(
                                                                          10.0,
                                                                          6.0,
                                                                          10.0,
                                                                          6.0),
                                                                  child: Text(
                                                                    moreAboutMe1Item,
                                                                    style: FlutterFlowTheme.of(
                                                                            context)
                                                                        .bodyMedium
                                                                        .override(
                                                                          fontFamily:
                                                                              'BT Beau Sans',
                                                                          color: const Color(
                                                                              0xFF747E90),
                                                                          letterSpacing:
                                                                              0.0,
                                                                          fontWeight:
                                                                              FontWeight.w600,
                                                                          useGoogleFonts:
                                                                              false,
                                                                        ),
                                                                  ),
                                                                ),
                                                              );
                                                            }),
                                                          );
                                                        },
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              if (_model.moreAboutMe.length > 3)
                                                Align(
                                                  alignment: const AlignmentDirectional(
                                                      -1.0, 0.0),
                                                  child: Padding(
                                                    padding: const EdgeInsetsDirectional
                                                        .fromSTEB(
                                                            20.0, 0.0, 0.0, 0.0),
                                                    child: Container(
                                                      width: MediaQuery.sizeOf(
                                                                  context)
                                                              .width *
                                                          0.8,
                                                      decoration: const BoxDecoration(),
                                                      child: Padding(
                                                        padding:
                                                            const EdgeInsetsDirectional
                                                                .fromSTEB(
                                                                    0.0,
                                                                    9.0,
                                                                    0.0,
                                                                    0.0),
                                                        child: Wrap(
                                                          spacing: 10.0,
                                                          runSpacing: 9.0,
                                                          alignment:
                                                              WrapAlignment.start,
                                                          crossAxisAlignment:
                                                              WrapCrossAlignment
                                                                  .start,
                                                          direction:
                                                              Axis.horizontal,
                                                          runAlignment:
                                                              WrapAlignment.start,
                                                          verticalDirection:
                                                              VerticalDirection
                                                                  .down,
                                                          clipBehavior: Clip.none,
                                                          children: [
                                                            InkWell(
                                                              splashColor: Colors
                                                                  .transparent,
                                                              focusColor: Colors
                                                                  .transparent,
                                                              hoverColor: Colors
                                                                  .transparent,
                                                              highlightColor:
                                                                  Colors
                                                                      .transparent,
                                                              onTap: () async {
                                                                _model.showMoreDetails =
                                                                    !_model
                                                                        .showMoreDetails;
                                                                safeSetState(
                                                                    () {});
                                                              },
                                                              child: Container(
                                                                decoration:
                                                                    BoxDecoration(
                                                                  color: const Color(
                                                                      0xFFEEEFF1),
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              24.0),
                                                                  border:
                                                                      Border.all(
                                                                    color: const Color(
                                                                        0xFFBFC1C5),
                                                                  ),
                                                                ),
                                                                child: Row(
                                                                  mainAxisSize:
                                                                      MainAxisSize
                                                                          .min,
                                                                  children: [
                                                                    Padding(
                                                                      padding: const EdgeInsetsDirectional
                                                                          .fromSTEB(
                                                                              10.0,
                                                                              6.0,
                                                                              10.0,
                                                                              6.0),
                                                                      child: Text(
                                                                        'Show more',
                                                                        style: FlutterFlowTheme.of(
                                                                                context)
                                                                            .bodyMedium
                                                                            .override(
                                                                              fontFamily:
                                                                                  'BT Beau Sans',
                                                                              color:
                                                                                  const Color(0xFF747E90),
                                                                              letterSpacing:
                                                                                  0.0,
                                                                              fontWeight:
                                                                                  FontWeight.w600,
                                                                              useGoogleFonts:
                                                                                  false,
                                                                            ),
                                                                      ),
                                                                    ),
                                                                    if (!_model
                                                                        .showMoreDetails)
                                                                      const Padding(
                                                                        padding: EdgeInsetsDirectional.fromSTEB(
                                                                            0.0,
                                                                            0.0,
                                                                            10.0,
                                                                            0.0),
                                                                        child:
                                                                            FaIcon(
                                                                          FontAwesomeIcons
                                                                              .chevronDown,
                                                                          color: Color(
                                                                              0xFF747E90),
                                                                          size:
                                                                              14.0,
                                                                        ),
                                                                      ),
                                                                    if (_model
                                                                        .showMoreDetails)
                                                                      const Padding(
                                                                        padding: EdgeInsetsDirectional.fromSTEB(
                                                                            0.0,
                                                                            0.0,
                                                                            10.0,
                                                                            0.0),
                                                                        child:
                                                                            FaIcon(
                                                                          FontAwesomeIcons
                                                                              .chevronUp,
                                                                          color: Color(
                                                                              0xFF747E90),
                                                                          size:
                                                                              14.0,
                                                                        ),
                                                                      ),
                                                                  ],
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                            ],
                                          ),
                                        if (_model.showMoreDetails)
                                          Column(
                                            mainAxisSize: MainAxisSize.max,
                                            children: [
                                              Align(
                                                alignment: const AlignmentDirectional(
                                                    -1.0, 0.0),
                                                child: Padding(
                                                  padding: const EdgeInsetsDirectional
                                                      .fromSTEB(
                                                          20.0, 15.0, 0.0, 0.0),
                                                  child: Container(
                                                    width:
                                                        MediaQuery.sizeOf(context)
                                                                .width *
                                                            0.8,
                                                    decoration: const BoxDecoration(),
                                                    child: Padding(
                                                      padding:
                                                          const EdgeInsetsDirectional
                                                              .fromSTEB(0.0, 9.0,
                                                                  0.0, 0.0),
                                                      child: Builder(
                                                        builder: (context) {
                                                          final moreAboutMe1 =
                                                              _model.moreAboutMe
                                                                  .toList();
                      
                                                          return Wrap(
                                                            spacing: 10.0,
                                                            runSpacing: 9.0,
                                                            alignment:
                                                                WrapAlignment
                                                                    .start,
                                                            crossAxisAlignment:
                                                                WrapCrossAlignment
                                                                    .start,
                                                            direction:
                                                                Axis.horizontal,
                                                            runAlignment:
                                                                WrapAlignment
                                                                    .start,
                                                            verticalDirection:
                                                                VerticalDirection
                                                                    .down,
                                                            clipBehavior:
                                                                Clip.none,
                                                            children: List.generate(
                                                                moreAboutMe1
                                                                    .length,
                                                                (moreAboutMe1Index) {
                                                              final moreAboutMe1Item =
                                                                  moreAboutMe1[
                                                                      moreAboutMe1Index];
                                                              return Container(
                                                                decoration:
                                                                    BoxDecoration(
                                                                  color: const Color(
                                                                      0x00FFFFFF),
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              24.0),
                                                                  border:
                                                                      Border.all(
                                                                    color: const Color(
                                                                        0xFFBFC1C5),
                                                                  ),
                                                                ),
                                                                child: Padding(
                                                                  padding: const EdgeInsetsDirectional
                                                                      .fromSTEB(
                                                                          10.0,
                                                                          6.0,
                                                                          10.0,
                                                                          6.0),
                                                                  child: Text(
                                                                    moreAboutMe1Item,
                                                                    style: FlutterFlowTheme.of(
                                                                            context)
                                                                        .bodyMedium
                                                                        .override(
                                                                          fontFamily:
                                                                              'BT Beau Sans',
                                                                          color: const Color(
                                                                              0xFF747E90),
                                                                          letterSpacing:
                                                                              0.0,
                                                                          fontWeight:
                                                                              FontWeight.w600,
                                                                          useGoogleFonts:
                                                                              false,
                                                                        ),
                                                                  ),
                                                                ),
                                                              );
                                                            }),
                                                          );
                                                        },
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              if (_model.moreAboutMe.length > 3)
                                                Align(
                                                  alignment: const AlignmentDirectional(
                                                      -1.0, 0.0),
                                                  child: Padding(
                                                    padding: const EdgeInsetsDirectional
                                                        .fromSTEB(
                                                            20.0, 0.0, 0.0, 0.0),
                                                    child: Container(
                                                      width: MediaQuery.sizeOf(
                                                                  context)
                                                              .width *
                                                          0.8,
                                                      decoration: const BoxDecoration(),
                                                      child: Padding(
                                                        padding:
                                                            const EdgeInsetsDirectional
                                                                .fromSTEB(
                                                                    0.0,
                                                                    9.0,
                                                                    0.0,
                                                                    0.0),
                                                        child: Wrap(
                                                          spacing: 10.0,
                                                          runSpacing: 9.0,
                                                          alignment:
                                                              WrapAlignment.start,
                                                          crossAxisAlignment:
                                                              WrapCrossAlignment
                                                                  .start,
                                                          direction:
                                                              Axis.horizontal,
                                                          runAlignment:
                                                              WrapAlignment.start,
                                                          verticalDirection:
                                                              VerticalDirection
                                                                  .down,
                                                          clipBehavior: Clip.none,
                                                          children: [
                                                            InkWell(
                                                              splashColor: Colors
                                                                  .transparent,
                                                              focusColor: Colors
                                                                  .transparent,
                                                              hoverColor: Colors
                                                                  .transparent,
                                                              highlightColor:
                                                                  Colors
                                                                      .transparent,
                                                              onTap: () async {
                                                                _model.showMoreDetails =
                                                                    !_model
                                                                        .showMoreDetails;
                                                                safeSetState(
                                                                    () {});
                                                              },
                                                              child: Container(
                                                                decoration:
                                                                    BoxDecoration(
                                                                  color: const Color(
                                                                      0xFFEEEFF1),
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              24.0),
                                                                  border:
                                                                      Border.all(
                                                                    color: const Color(
                                                                        0xFFBFC1C5),
                                                                  ),
                                                                ),
                                                                child: Row(
                                                                  mainAxisSize:
                                                                      MainAxisSize
                                                                          .min,
                                                                  children: [
                                                                    Padding(
                                                                      padding: const EdgeInsetsDirectional
                                                                          .fromSTEB(
                                                                              10.0,
                                                                              6.0,
                                                                              10.0,
                                                                              6.0),
                                                                      child: Text(
                                                                        'Show more',
                                                                        style: FlutterFlowTheme.of(
                                                                                context)
                                                                            .bodyMedium
                                                                            .override(
                                                                              fontFamily:
                                                                                  'BT Beau Sans',
                                                                              color:
                                                                                  const Color(0xFF747E90),
                                                                              letterSpacing:
                                                                                  0.0,
                                                                              fontWeight:
                                                                                  FontWeight.w600,
                                                                              useGoogleFonts:
                                                                                  false,
                                                                            ),
                                                                      ),
                                                                    ),
                                                                    if (!_model
                                                                        .showMoreDetails)
                                                                      const Padding(
                                                                        padding: EdgeInsetsDirectional.fromSTEB(
                                                                            0.0,
                                                                            0.0,
                                                                            10.0,
                                                                            0.0),
                                                                        child:
                                                                            FaIcon(
                                                                          FontAwesomeIcons
                                                                              .chevronDown,
                                                                          color: Color(
                                                                              0xFF747E90),
                                                                          size:
                                                                              14.0,
                                                                        ),
                                                                      ),
                                                                    if (_model
                                                                        .showMoreDetails)
                                                                      const Padding(
                                                                        padding: EdgeInsetsDirectional.fromSTEB(
                                                                            0.0,
                                                                            0.0,
                                                                            10.0,
                                                                            0.0),
                                                                        child:
                                                                            FaIcon(
                                                                          FontAwesomeIcons
                                                                              .chevronUp,
                                                                          color: Color(
                                                                              0xFF747E90),
                                                                          size:
                                                                              14.0,
                                                                        ),
                                                                      ),
                                                                  ],
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                            ],
                                          ),
                                      ],
                                    ),
                                  if (widget.profile!.nPictures.length >= 3)
                                    StreamBuilder<ImagesRecord>(
                                      stream: ImagesRecord.getDocument(
                                          widget.profile!.nPictures[2]),
                                      builder: (context, snapshot) {
                                        // Customize what your widget looks like when it's loading.
                                        if (!snapshot.hasData) {
                                          return Center(
                                            child: SizedBox(
                                              width: 50.0,
                                              height: 50.0,
                                              child: CircularProgressIndicator(
                                                valueColor:
                                                    AlwaysStoppedAnimation<Color>(
                                                  FlutterFlowTheme.of(context)
                                                      .accent2,
                                                ),
                                              ),
                                            ),
                                          );
                                        }
                      
                                        final containerImagesRecord =
                                            snapshot.data!;
                      
                                        return Container(
                                          decoration: const BoxDecoration(),
                                          child: Padding(
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(
                                                    0.0, 15.0, 0.0, 0.0),
                                            child: Container(
                                              width: MediaQuery.sizeOf(context)
                                                      .width *
                                                  1.0,
                                              height: 375.0,
                                              decoration: BoxDecoration(
                                                color:
                                                    FlutterFlowTheme.of(context)
                                                        .secondaryBackground,
                                                borderRadius:
                                                    BorderRadius.circular(0.0),
                                              ),
                                              child: ClipRRect(
                                                borderRadius:
                                                    BorderRadius.circular(11.0),
                                                child: Image.network(
                                                  containerImagesRecord.url,
                                                  width:
                                                      MediaQuery.sizeOf(context)
                                                              .width *
                                                          1.0,
                                                  height: 375.0,
                                                  fit: BoxFit.cover,
                                                ),
                                              ),
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                  if (widget.profile!.nPictures.length >= 4)
                                    StreamBuilder<ImagesRecord>(
                                      stream: ImagesRecord.getDocument(
                                          widget.profile!.nPictures[3]),
                                      builder: (context, snapshot) {
                                        // Customize what your widget looks like when it's loading.
                                        if (!snapshot.hasData) {
                                          return Center(
                                            child: SizedBox(
                                              width: 50.0,
                                              height: 50.0,
                                              child: CircularProgressIndicator(
                                                valueColor:
                                                    AlwaysStoppedAnimation<Color>(
                                                  FlutterFlowTheme.of(context)
                                                      .accent2,
                                                ),
                                              ),
                                            ),
                                          );
                                        }
                      
                                        final containerImagesRecord =
                                            snapshot.data!;
                      
                                        return Container(
                                          decoration: const BoxDecoration(),
                                          child: Visibility(
                                            visible: containerImagesRecord
                                                    .availableToAll ||
                                                (containerImagesRecord
                                                        .availableToMatches &&
                                                    widget.isMatch!) ||
                                                containerImagesRecord
                                                    .availableToSpecified
                                                    .contains(
                                                        currentUserReference),
                                            child: Padding(
                                              padding:
                                                  const EdgeInsetsDirectional.fromSTEB(
                                                      0.0, 15.0, 0.0, 0.0),
                                              child: Container(
                                                width: MediaQuery.sizeOf(context)
                                                        .width *
                                                    1.0,
                                                height: 375.0,
                                                decoration: BoxDecoration(
                                                  color:
                                                      FlutterFlowTheme.of(context)
                                                          .secondaryBackground,
                                                  borderRadius:
                                                      BorderRadius.circular(0.0),
                                                ),
                                                child: ClipRRect(
                                                  borderRadius:
                                                      BorderRadius.circular(11.0),
                                                  child: Image.network(
                                                    containerImagesRecord.url,
                                                    width:
                                                        MediaQuery.sizeOf(context)
                                                                .width *
                                                            1.0,
                                                    height: 375.0,
                                                    fit: BoxFit.cover,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                  if (widget.profile!.nPictures.length >= 5)
                                    StreamBuilder<ImagesRecord>(
                                      stream: ImagesRecord.getDocument(
                                          widget.profile!.nPictures[4]),
                                      builder: (context, snapshot) {
                                        // Customize what your widget looks like when it's loading.
                                        if (!snapshot.hasData) {
                                          return Center(
                                            child: SizedBox(
                                              width: 50.0,
                                              height: 50.0,
                                              child: CircularProgressIndicator(
                                                valueColor:
                                                    AlwaysStoppedAnimation<Color>(
                                                  FlutterFlowTheme.of(context)
                                                      .accent2,
                                                ),
                                              ),
                                            ),
                                          );
                                        }
                      
                                        final containerImagesRecord =
                                            snapshot.data!;
                      
                                        return Container(
                                          decoration: const BoxDecoration(),
                                          child: Visibility(
                                            visible: containerImagesRecord
                                                    .availableToAll ||
                                                (containerImagesRecord
                                                        .availableToMatches &&
                                                    widget.isMatch!) ||
                                                containerImagesRecord
                                                    .availableToSpecified
                                                    .contains(
                                                        currentUserReference),
                                            child: Padding(
                                              padding:
                                                  const EdgeInsetsDirectional.fromSTEB(
                                                      0.0, 15.0, 0.0, 0.0),
                                              child: Container(
                                                width: MediaQuery.sizeOf(context)
                                                        .width *
                                                    1.0,
                                                height: 375.0,
                                                decoration: BoxDecoration(
                                                  color:
                                                      FlutterFlowTheme.of(context)
                                                          .secondaryBackground,
                                                  borderRadius:
                                                      BorderRadius.circular(0.0),
                                                ),
                                                child: ClipRRect(
                                                  borderRadius:
                                                      BorderRadius.circular(11.0),
                                                  child: Image.network(
                                                    containerImagesRecord.url,
                                                    width:
                                                        MediaQuery.sizeOf(context)
                                                                .width *
                                                            1.0,
                                                    height: 375.0,
                                                    fit: BoxFit.cover,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                  if (widget.profile!.nPictures.length >= 6)
                                    StreamBuilder<ImagesRecord>(
                                      stream: ImagesRecord.getDocument(
                                          widget.profile!.nPictures[5]),
                                      builder: (context, snapshot) {
                                        // Customize what your widget looks like when it's loading.
                                        if (!snapshot.hasData) {
                                          return Center(
                                            child: SizedBox(
                                              width: 50.0,
                                              height: 50.0,
                                              child: CircularProgressIndicator(
                                                valueColor:
                                                    AlwaysStoppedAnimation<Color>(
                                                  FlutterFlowTheme.of(context)
                                                      .accent2,
                                                ),
                                              ),
                                            ),
                                          );
                                        }
                      
                                        final containerImagesRecord =
                                            snapshot.data!;
                      
                                        return Container(
                                          decoration: const BoxDecoration(),
                                          child: Visibility(
                                            visible: containerImagesRecord
                                                    .availableToAll ||
                                                (containerImagesRecord
                                                        .availableToMatches &&
                                                    widget.isMatch!) ||
                                                containerImagesRecord
                                                    .availableToSpecified
                                                    .contains(
                                                        currentUserReference),
                                            child: Padding(
                                              padding:
                                                  const EdgeInsetsDirectional.fromSTEB(
                                                      0.0, 15.0, 0.0, 0.0),
                                              child: Container(
                                                width: MediaQuery.sizeOf(context)
                                                        .width *
                                                    1.0,
                                                height: 375.0,
                                                decoration: BoxDecoration(
                                                  color:
                                                      FlutterFlowTheme.of(context)
                                                          .secondaryBackground,
                                                  borderRadius:
                                                      BorderRadius.circular(0.0),
                                                ),
                                                child: ClipRRect(
                                                  borderRadius:
                                                      BorderRadius.circular(11.0),
                                                  child: Image.network(
                                                    containerImagesRecord.url,
                                                    width:
                                                        MediaQuery.sizeOf(context)
                                                                .width *
                                                            1.0,
                                                    height: 375.0,
                                                    fit: BoxFit.cover,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                  const Divider(
                                    thickness: 1.0,
                                    color: Color(0xFFD8DBDF),
                                  ),
                                ],
                              ),
                          
                          ],  
                                      
                        ),
                      ),
                      
                    ],
                  ),
                ),
              ),
            ),
          ),
          _IgnorePointerWithSemantics(
            child: AnimatedOpacity(
              opacity: _currentCutoutStep >= 1 ? 1 : 0,
              duration: const Duration(milliseconds: 300),
              child: Stack(
                children: [
                  Positioned.fill(
                        child: ClipPath(
                          clipper: MultiCutoutClipper(_cutoutRects), // Mul
                          child: Container(
                            color: Colors.black.withOpacity(0.8),
                          ),
                        ),
                      ),
                  Positioned
                  (top: 0, left: 0, right: 0, child:  
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      ClipRect(
                                      child: Align(
                                        alignment: Alignment.topRight,
                                        child: SizedBox(
                                        height: MediaQuery.of(context).size.height * 0.2,
                                        width: MediaQuery.of(context).size.width * 0.45,
                                        child: RiveAnimation.asset(
                                                            'assets/rive_animations/fs_tutorial/12-3.riv',
                                                            fit: BoxFit.fill,
                                                            onInit: _onInit3,
                                                            alignment: Alignment.topRight
                                                          ),
                                                                  ),
                                      ),
                                    ),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(30, 0, 30, 0),
                        child: Container(
                          constraints: const BoxConstraints(maxWidth: 400),
                          child: RichText(
                                  textScaler: MediaQuery.of(context).textScaler,
                                  text: TextSpan(
                                    children: [
                                      TextSpan(
                                        text: getRemoteConfigString('tutorial_profiledetail_t1'),
                                        style:
                                            FlutterFlowTheme.of(context).bodyMedium.override(
                                                  fontFamily: 'BT Beau Sans',
                                                  color: Colors.white,
                                                  letterSpacing: 0.0,
                                                  useGoogleFonts: false,
                                                ),
                                      ),
                                      TextSpan(
                                        text: getRemoteConfigString('tutorial_profiledetail_t2'),
                                        style:
                                            FlutterFlowTheme.of(context).bodyMedium.override(
                                                  fontFamily: 'BT Beau Sans',
                                                  color: const Color(0xFFFF9EDE),
                                                  letterSpacing: 0.0,
                                                  useGoogleFonts: false,
                                                ),
                                      ),
                                      TextSpan(
                                        text: getRemoteConfigString('tutorial_profiledetail_t3'),
                                            
                                        style:
                                            FlutterFlowTheme.of(context).bodyMedium.override(
                                                  fontFamily: 'BT Beau Sans',
                                                  color: Colors.white,
                                                  letterSpacing: 0.0,
                                                  useGoogleFonts: false,
                                                ),
                                      ),
                                    ],
                                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                                          fontFamily: 'BT Beau Sans',
                                          color:
                                              FlutterFlowTheme.of(context).primaryBackground,
                                          letterSpacing: 0.0,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ),
                        ),
                      ),
                      ClipRect(
                    child: SizedBox(
                    width: MediaQuery.of(context).size.width * 0.33,
                    height: 50,
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                      child: RiveAnimation.asset(
                          'assets/rive_animations/fs_tutorial/12-2.riv',
                          fit: BoxFit.fitHeight,
                          onInit: _onInit2,
                          alignment: Alignment.centerRight
                        ),
                    ),
                              ),
                  ),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(30, 0, 30, 0),
                        child: Container(
                          constraints: const BoxConstraints(maxWidth: 400),
                          child: RichText(
                                  textScaler: MediaQuery.of(context).textScaler,
                                  text: TextSpan(
                                    children: [
                                      TextSpan(
                                        text: getRemoteConfigString('tutorial_profiledetail_t4'),
                                        style:
                                            FlutterFlowTheme.of(context).bodyMedium.override(
                                                  fontFamily: 'BT Beau Sans',
                                                  color: Colors.white,
                                                  letterSpacing: 0.0,
                                                  useGoogleFonts: false,
                                                ),
                                      ),
                                      TextSpan(
                                        text: getRemoteConfigString('tutorial_profiledetail_t5'),
                                        style:
                                            FlutterFlowTheme.of(context).bodyMedium.override(
                                                  fontFamily: 'BT Beau Sans',
                                                  color: const Color(0xFFFF9EDE),
                                                  letterSpacing: 0.0,
                                                  useGoogleFonts: false,
                                                ),
                                      ),
                                    ],
                                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                                          fontFamily: 'BT Beau Sans',
                                          color:
                                              FlutterFlowTheme.of(context).primaryBackground,
                                          letterSpacing: 0.0,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ),
                        ),
                      ),
                      ClipRect(
                    child: Align(
                      alignment: Alignment.bottomLeft,
                      child: SizedBox(
                      height: MediaQuery.of(context).size.height * 0.4,
                      width: MediaQuery.of(context).size.width * 0.75, 
                      child: RiveAnimation.asset(
                          'assets/rive_animations/fs_tutorial/12-1.riv',
                          fit: BoxFit.fill,
                          onInit: _onInit1,
                          alignment: Alignment.bottomLeft
                        ),
                                ),
                    ),
                  ),
                    ],
                  ),
                  ),

                  SafeArea(
          child: Align(
                      alignment: Alignment.bottomLeft,
                      child: Padding(
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(0.0, 50.0, 30.0, 0.0),
                        child: FFButtonWidget(
                          onPressed: () {
                            logSkippedIntro(runtimeType.toString());
                            GoRouter.of(context).goNamed('Discovery');
                          },
                          text: 'Skip',
                          options: FFButtonOptions(
                            height: 40.0,
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                16.0, 0.0, 16.0, 0.0),
                            iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 0.0, 0.0, 0.0),
                            color: const Color(0x004B39EF),
                            textStyle:
                                FlutterFlowTheme.of(context).titleSmall.override(
                                      fontFamily: 'BT Beau Sans',
                                      color: Colors.white,
                                      fontSize: 10.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.normal,
                                      useGoogleFonts: false,
                                    ),
                            elevation: 0.0,
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                        ),
                      ),
                    ),
        ),
                      
                ],
              ),
            ),
          ),
              ],
      ),
    );
  }
}

class MultiCutoutClipper extends CustomClipper<Path> {
  final List<RRect> cutoutRects; // List of rounded rectangles

  MultiCutoutClipper(this.cutoutRects);

  @override
  Path getClip(Size size) {
    Path path = Path();

    // Full screen rectangle
    path.addRect(Rect.fromLTWH(0, 0, size.width, size.height));

    // Subtract each cutout rectangle from the path
    for (final cutout in cutoutRects) {
      path = Path.combine(
        PathOperation.difference,
        path,
        Path()..addRRect(cutout),
      );
    }

    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return true; // Reclip if the cutouts change
  }
}


class _IgnorePointerWithSemantics extends SingleChildRenderObjectWidget {
  const _IgnorePointerWithSemantics({
    super.child,
  });

  @override
  _RenderIgnorePointerWithSemantics createRenderObject(BuildContext context) {
    return _RenderIgnorePointerWithSemantics();
  }
}

class _RenderIgnorePointerWithSemantics extends RenderProxyBox {
  _RenderIgnorePointerWithSemantics();

  @override
  bool hitTest(BoxHitTestResult result, { required Offset position }) => false;
}