import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
import '/discovery/waiting_list_overlays/local_for_global_matchers_new/local_for_global_matchers_new_widget.dart';
import '/subscription_sale_screens/divine/divine_header_component/divine_header_component_widget.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/profile_settings/profile_home_purchases_horizontal_slider_copy/profile_home_purchases_horizontal_slider_copy_widget.dart';
import '/single_purchases/boost_purchase_component/boost_purchase_component_widget.dart';
import '/single_purchases/boost_start_confirm_component/boost_start_confirm_component_widget.dart';
import '/single_purchases/boosts_profile_home_widget_active/boosts_profile_home_widget_active_widget.dart';
import '/single_purchases/boosts_profile_home_widget_copy/boosts_profile_home_widget_copy_widget.dart';
import '/single_purchases/rose_purchase_component/rose_purchase_component_widget.dart';
import '/single_purchases/roses_profile_home_widget/roses_profile_home_widget_widget.dart';
import 'package:rive/rive.dart' hide LinearGradient, Image;
import '/flutter_flow/custom_functions.dart' as functions;
import '/flutter_flow/revenue_cat_util.dart' as revenue_cat;
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'profile_home_intro_model.dart';
import 'package:chyrpe/intro/intro_amplitude_commons.dart';
export 'profile_home_intro_model.dart';

class ProfileHomeIntroWidget extends StatefulWidget {
  const ProfileHomeIntroWidget({super.key});

  @override
  State<ProfileHomeIntroWidget> createState() => _ProfileHomeIntroWidgetState();
}

class _ProfileHomeIntroWidgetState extends State<ProfileHomeIntroWidget> {
  late ProfileHomeIntroModel _model;
  final GlobalKey profileHomeIntroProfilePic = GlobalKey();
  final GlobalKey profileHomeIntroEditBtn =
      GlobalKey(); // Key for the profile picture

  List<RRect> _cutoutRects = [];

  final scaffoldKey = GlobalKey<ScaffoldState>();

  late StateMachineController _riveController1;
  late StateMachineController _riveController2;

  late StateMachineController _riveController3;
  late StateMachineController _riveController4;
  bool activate2 = false;

  void _onInit1(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    art.addController(ctrl);
    _riveController1 = ctrl;
      _riveController1.isActive = true;
      }
     // Keep it inactive initially
  }

  void _onInit2(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 

    art.addController(ctrl);
    _riveController2 = ctrl;
    _riveController2.isActive = _currentCutoutStep >= 1;

     // Keep it inactive initially
    }
  }

    void _onInit3(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    art.addController(ctrl);
    _riveController3 = ctrl;
      _riveController3.isActive = _currentCutoutStep >= 1;
     // Keep it inactive initially
  }
  }

  void _onInit4(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) { 
    art.addController(ctrl);
    _riveController4 = ctrl;
      _riveController4.isActive = _currentCutoutStep >= 1;
     // Keep it inactive initially
  }
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => ProfileHomeIntroModel());

    logPageVisitIntro(runtimeType.toString());

    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(milliseconds: 200), () {
        _calculateCutouts();
        calculatePositionImage();
        calculatePositionBtn();
      });

  
      Future.delayed(const Duration(milliseconds: 1000), () {
        setState(() {
          _advanceCutouts();
          secondTextVisible = true;
          _riveController2.isActive = true;
          Future.delayed(const Duration(milliseconds: 100), () {
            _riveController3.isActive = true;
          });
          Future.delayed(const Duration(milliseconds: 500), () {
            _riveController4.isActive = true;
          });
          });
      });
    });
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  int _currentCutoutStep = 0;

  void _advanceCutouts() {
    setState(() {
      _currentCutoutStep++;
      _calculateCutouts();
    });
  }

  double imageTextTopPosition = 0;
  double imageSidePosition = 0;
  double buttonTextTopPosition = 0;


double imageRightEnd = 0;
double imageAnimation1Start = 0;
double imageAnimation1Height = 0;

double imageX = 0;
double imageThirdEnd = 0;

  bool secondTextVisible = false;

  void calculatePositionImage() {
    final RenderBox? renderBox = profileHomeIntroProfilePic.currentContext
        ?.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      final Offset offset = renderBox.localToGlobal(Offset.zero);
      final double elementBottom = offset.dy + renderBox.size.height;
      final double elementSide = offset.dx;
      final double elementSideRight = offset.dx + renderBox.size.width;
      final double elementHeight = renderBox.size.height;
      setState(() {
        imageTextTopPosition = elementBottom + 10.0;
        imageSidePosition = elementSide;
        imageRightEnd = elementSideRight;
        imageAnimation1Start = offset.dy + 0.75 * elementHeight;
        imageAnimation1Height = 1.2 * elementHeight;
        imageX = offset.dx;
        imageThirdEnd = offset.dy + elementHeight + 7; // 10px below the bottom
      });
    }
  }

  void calculatePositionBtn() {
    final RenderBox? renderBox = profileHomeIntroEditBtn.currentContext
        ?.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      final Offset offset = renderBox.localToGlobal(Offset.zero);
      final double elementBottom = offset.dy + renderBox.size.height;
      setState(() {
        buttonTextTopPosition = elementBottom + 10.0; // 10px below the bottom
      });
    }
  }

  void _calculateCutouts() {
    List<RRect> rects = [];

    if (_currentCutoutStep >= 0 &&
        profileHomeIntroProfilePic.currentContext != null) {
      // Only add profile picture cutout when step is 1
      final RenderBox profileBox = profileHomeIntroProfilePic.currentContext!
          .findRenderObject() as RenderBox;
      final Size profileSize = profileBox.size;
      final Offset profilePosition = profileBox.localToGlobal(Offset.zero);
      rects.add(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(
            profilePosition.dx,
            profilePosition.dy,
            profileSize.width,
            profileSize.height,
          ),
          const Radius.circular(100),
        ),
      );
    }

    if (_currentCutoutStep >= 1 &&
        profileHomeIntroEditBtn.currentContext != null) {
      // Only add profile picture cutout when step is 1
      final RenderBox profileBox = profileHomeIntroEditBtn.currentContext!
          .findRenderObject() as RenderBox;
      final Size profileSize = profileBox.size;
      final Offset profilePosition = profileBox.localToGlobal(Offset.zero);
      rects.add(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(
            profilePosition.dx - 10,
            profilePosition.dy - 10,
            profileSize.width + 20,
            profileSize.height + 20,
          ),
          const Radius.circular(100),
        ),
      );
    }

    setState(() {
      _cutoutRects = rects;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(children: [
      AuthUserStreamWidget(
        builder: (context) => StreamBuilder<PublicProfileRecord>(
          stream: PublicProfileRecord.getDocument(
              currentUserDocument!.publicProfile!),
          builder: (context, snapshot) {
            // Customize what your widget looks like when it's loading.
            if (!snapshot.hasData) {
              return Scaffold(
                backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
                body: Center(
                  child: SizedBox(
                    width: 40.0,
                    height: 40.0,
                    child: SpinKitDualRing(
                      color: FlutterFlowTheme.of(context).accent2,
                      size: 40.0,
                    ),
                  ),
                ),
              );
            }

            final profileHomeIntroPublicProfileRecord = snapshot.data!;

            return GestureDetector(
              onTap: () => FocusScope.of(context).unfocus(),
              child: Scaffold(
                key: scaffoldKey,
                backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
                appBar: AppBar(
                  backgroundColor:
                      FlutterFlowTheme.of(context).primaryBackground,
                  automaticallyImplyLeading: false,
                  title: Row(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Expanded(
                        child: Stack(
                          alignment: const AlignmentDirectional(0.0, 0.0),
                          children: [
                            Align(
                              alignment: const AlignmentDirectional(1.0, 0.0),
                              child: FlutterFlowIconButton(
                                borderColor: const Color(0x004B39EF),
                                borderRadius: 20.0,
                                borderWidth: 1.0,
                                buttonSize: 40.0,
                                fillColor: const Color(0x004B39EF),
                                icon: Icon(
                                  FFIcons.kfilter,
                                  color:
                                      FlutterFlowTheme.of(context).primaryText,
                                  size: 24.0,
                                ),
                                onPressed: () async {
                                  FFAppState().heightPrefLow = valueOrDefault(
                                          currentUserDocument?.lowerHeightReq,
                                          0)
                                      .toDouble();
                                  FFAppState().heightPrefHigh = valueOrDefault(
                                          currentUserDocument?.upperHeightReq,
                                          0)
                                      .toDouble();
                                  FFAppState().agePrefLow = valueOrDefault(
                                      currentUserDocument?.lowerAgeReq, 0);
                                  FFAppState().agePrefHigh = valueOrDefault(
                                      currentUserDocument?.upperAgeReq, 0);
                                  FFAppState().locationPref = valueOrDefault(
                                      currentUserDocument?.distanceReq, 0.0);
                                  FFAppState().genderPref = valueOrDefault(
                                      currentUserDocument?.genderReq, '');
                                  FFAppState().kinksReq = (currentUserDocument
                                              ?.kinksToSeeReq
                                              .toList() ??
                                          [])
                                      .toList()
                                      .cast<String>();
                                  FFAppState().interestsReq =
                                      (currentUserDocument?.hobbiesToSeeReq
                                                  .toList() ??
                                              [])
                                          .toList()
                                          .cast<String>();
                                  FFAppState().update(() {});

                               
                                },
                              ),
                            ),
                            
                            Align(
                              alignment: const AlignmentDirectional(0.0, 0.0),
                              child: InkWell(
                                splashColor: Colors.transparent,
                                focusColor: Colors.transparent,
                                hoverColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                onTap: () async {
                                  
                                },
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(8.0),
                                  child: SvgPicture.asset(
                                    'assets/images/ColorfulLogoA.svg',
                                    width: 150.0,
                                    height: 28.0,
                                    fit: BoxFit.contain,
                                  ),
                                ),
                              ),
                            ),
                         
                          ],
                        ),
                      ),
                    ],
                  ),
                  actions: const [],
                  centerTitle: false,
                  elevation: 0.0,
                ),
                body: SafeArea(
                  top: true,
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Flexible(
                        child: Align(
                          alignment: const AlignmentDirectional(0.0, -1.0),
                          child: Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                12.0, 0.0, 12.0, 0.0),
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                Container(
                                  decoration: const BoxDecoration(),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    children: [
                                      Padding(
                                        padding:
                                            const EdgeInsetsDirectional.fromSTEB(
                                                0.0, 22.0, 0.0, 0.0),
                                        child: Column(
                                          mainAxisSize: MainAxisSize.max,
                                          children: [
                                            InkWell(
                                              splashColor:
                                                  Colors.transparent,
                                              focusColor:
                                                  Colors.transparent,
                                              hoverColor:
                                                  Colors.transparent,
                                              highlightColor:
                                                  Colors.transparent,
                                              onTap: () async {
                                                context.pushReplacementNamed(
                                                  'EditProfileIntro',
                                                  extra: <String, dynamic>{
                                                    kTransitionInfoKey:
                                                        const TransitionInfo(
                                                      hasTransition: true,
                                                      transitionType:
                                                          PageTransitionType
                                                              .bottomToTop,
                                                    ),
                                                  },
                                                );
                                              },
                                              child: Container(
                                                key:
                                                    profileHomeIntroProfilePic,
                                                width: 139.0,
                                                height: 139.0,
                                                clipBehavior:
                                                    Clip.antiAlias,
                                                decoration: const BoxDecoration(
                                                  shape: BoxShape.circle,
                                                ),
                                                child: Image.network(
                                                  profileHomeIntroPublicProfileRecord
                                                      .profilePhoto,
                                                  fit: BoxFit.cover,
                                                ),
                                              ),
                                            ),
                                            Padding(
                                              padding: const EdgeInsetsDirectional
                                                  .fromSTEB(
                                                      0.0, 27.0, 0.0, 0.0),
                                              child: Text(
                                                '${valueOrDefault(currentUserDocument?.name, '')}, ${profileHomeIntroPublicProfileRecord.age.toString()}',
                                                style: FlutterFlowTheme.of(
                                                        context)
                                                    .bodyMedium
                                                    .override(
                                                      fontFamily:
                                                          'BT Beau Sans',
                                                      fontSize: 24.0,
                                                      letterSpacing: 0.0,
                                                      useGoogleFonts: false,
                                                    ),
                                              ),
                                            ),
                                            Padding(
                                              padding: const EdgeInsetsDirectional
                                                  .fromSTEB(
                                                      0.0, 35.0, 0.0, 0.0),
                                              child: FFButtonWidget(
                                                key:
                                                    profileHomeIntroEditBtn,
                                                onPressed: () async {
                                                  context.pushReplacementNamed(
                                                      'EditProfileIntro');
                                                },
                                                text: 'Edit profile',
                                                icon: const FaIcon(
                                                  FontAwesomeIcons
                                                      .pencilAlt,
                                                  size: 14.0,
                                                ),
                                                options: FFButtonOptions(
                                                  height: 33.0,
                                                  padding:
                                                      const EdgeInsetsDirectional
                                                          .fromSTEB(
                                                              24.0,
                                                              0.0,
                                                              24.0,
                                                              0.0),
                                                  iconPadding:
                                                      const EdgeInsetsDirectional
                                                          .fromSTEB(
                                                              0.0,
                                                              0.0,
                                                              0.0,
                                                              0.0),
                                                  color: const Color(0xFFEEEFF1),
                                                  textStyle:
                                                      FlutterFlowTheme.of(
                                                              context)
                                                          .titleSmall
                                                          .override(
                                                            fontFamily:
                                                                'BT Beau Sans',
                                                            color: const Color(
                                                                0xFF262A36),
                                                            fontSize: 14.0,
                                                            letterSpacing:
                                                                0.0,
                                                            useGoogleFonts:
                                                                false,
                                                          ),
                                                  elevation: 0.0,
                                                  borderSide: const BorderSide(
                                                    color:
                                                        Colors.transparent,
                                                    width: 0.0,
                                                  ),
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          100.0),
                                                ),
                                              ),
                                            ),
                                            if ((currentUserDocument
                                                        ?.gender ==
                                                    Gender.Female) &&
                                                getRemoteConfigBool(
                                                    'ambassador_button_visible'))
                                              Padding(
                                                padding:
                                                    const EdgeInsetsDirectional
                                                        .fromSTEB(0.0, 10.0,
                                                            0.0, 0.0),
                                                child: FFButtonWidget(
                                                  onPressed: () async {
                                                    await launchURL(
                                                        getRemoteConfigString(
                                                            'ambassador_button_url'));
                                                  },
                                                  text: getRemoteConfigString(
                                                      'ambassador_button_title'),
                                                  options: FFButtonOptions(
                                                    height: 33.0,
                                                    padding:
                                                        const EdgeInsetsDirectional
                                                            .fromSTEB(
                                                                24.0,
                                                                0.0,
                                                                24.0,
                                                                0.0),
                                                    iconPadding:
                                                        const EdgeInsetsDirectional
                                                            .fromSTEB(
                                                                0.0,
                                                                0.0,
                                                                0.0,
                                                                0.0),
                                                    color:
                                                        const Color(0xFFEEEFF1),
                                                    textStyle:
                                                        FlutterFlowTheme.of(
                                                                context)
                                                            .titleSmall
                                                            .override(
                                                              fontFamily:
                                                                  'BT Beau Sans',
                                                              color: const Color(
                                                                  0xFF262A36),
                                                              fontSize:
                                                                  14.0,
                                                              letterSpacing:
                                                                  0.0,
                                                              useGoogleFonts:
                                                                  false,
                                                            ),
                                                    elevation: 0.0,
                                                    borderSide: const BorderSide(
                                                      color: Colors
                                                          .transparent,
                                                      width: 0.0,
                                                    ),
                                                    borderRadius:
                                                        BorderRadius
                                                            .circular(
                                                                100.0),
                                                  ),
                                                ),
                                              ),
                                            if ((currentUserDocument
                                                        ?.gender ==
                                                    Gender.Male) &&
                                                valueOrDefault<bool>(
                                                    currentUserDocument
                                                        ?.globalMatching,
                                                    false) &&
                                                valueOrDefault<bool>(
                                                    currentUserDocument
                                                        ?.onWaitingList,
                                                    false))
                                              Padding(
                                                padding:
                                                    const EdgeInsetsDirectional
                                                        .fromSTEB(0.0, 10.0,
                                                            0.0, 0.0),
                                                child: FFButtonWidget(
                                                  onPressed: () async {
                                                    await showModalBottomSheet(
                                                      isScrollControlled:
                                                          true,
                                                      backgroundColor:
                                                          Colors
                                                              .transparent,
                                                      useSafeArea: true,
                                                      context: context,
                                                      builder: (context) {
                                                        return GestureDetector(
                                                          onTap: () =>
                                                              FocusScope.of(
                                                                      context)
                                                                  .unfocus(),
                                                          child: Padding(
                                                            padding: MediaQuery
                                                                .viewInsetsOf(
                                                                    context),
                                                            child:
                                                                SizedBox(
                                                              height: MediaQuery.sizeOf(
                                                                          context)
                                                                      .height *
                                                                  1.0,
                                                              child:
                                                                  const LocalForGlobalMatchersNewWidget(),
                                                            ),
                                                          ),
                                                        );
                                                      },
                                                    ).then((value) =>
                                                        safeSetState(
                                                            () {}));
                                                  },
                                                  text: getRemoteConfigString(
                                                      'profile_home_local_wl_text'),
                                                  icon: const FaIcon(
                                                    FontAwesomeIcons.listUl,
                                                    size: 14.0,
                                                  ),
                                                  options: FFButtonOptions(
                                                    height: 33.0,
                                                    padding:
                                                        const EdgeInsetsDirectional
                                                            .fromSTEB(
                                                                24.0,
                                                                0.0,
                                                                24.0,
                                                                0.0),
                                                    iconPadding:
                                                        const EdgeInsetsDirectional
                                                            .fromSTEB(
                                                                0.0,
                                                                0.0,
                                                                0.0,
                                                                0.0),
                                                    color:
                                                        const Color(0xFFEEEFF1),
                                                    textStyle:
                                                        FlutterFlowTheme.of(
                                                                context)
                                                            .titleSmall
                                                            .override(
                                                              fontFamily:
                                                                  'BT Beau Sans',
                                                              color: const Color(
                                                                  0xFF262A36),
                                                              fontSize:
                                                                  14.0,
                                                              letterSpacing:
                                                                  0.0,
                                                              useGoogleFonts:
                                                                  false,
                                                            ),
                                                    elevation: 0.0,
                                                    borderSide: const BorderSide(
                                                      color: Colors
                                                          .transparent,
                                                      width: 0.0,
                                                    ),
                                                    borderRadius:
                                                        BorderRadius
                                                            .circular(
                                                                100.0),
                                                  ),
                                                ),
                                              ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                if (valueOrDefault<bool>(
                                        currentUserDocument?.signUpFinished,
                                        false) &&
                                    valueOrDefault<bool>(
                                        currentUserDocument?.verified, false))
                                  Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        0.0, 28.0, 0.0, 0.0),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        Builder(
                                          builder: (context) {
                                            if (false) {
                                              return wrapWithModel(
                                                model: _model
                                                    .boostsProfileHomeWidgetActiveModel,
                                                updateCallback: () =>
                                                    safeSetState(() {}),
                                                child:
                                                    const BoostsProfileHomeWidgetActiveWidget(),
                                              );
                                            } else {
                                              return wrapWithModel(
                                                model: _model
                                                    .boostsProfileHomeWidgetCopyModel,
                                                updateCallback: () =>
                                                    safeSetState(() {}),
                                                updateOnChange: true,
                                                child:
                                                    BoostsProfileHomeWidgetCopyWidget(
                                                  callback: () async {
                                                    if (valueOrDefault(
                                                            currentUserDocument
                                                                ?.boostsLeft,
                                                            0) >
                                                        0) {
                                                      await showModalBottomSheet(
                                                        isScrollControlled:
                                                            true,
                                                        backgroundColor:
                                                            Colors
                                                                .transparent,
                                                        enableDrag: false,
                                                        useSafeArea: true,
                                                        context: context,
                                                        builder: (context) {
                                                          return GestureDetector(
                                                            onTap: () =>
                                                                FocusScope.of(
                                                                        context)
                                                                    .unfocus(),
                                                            child: Padding(
                                                              padding: MediaQuery
                                                                  .viewInsetsOf(
                                                                      context),
                                                              child:
                                                                  const BoostStartConfirmComponentWidget(),
                                                            ),
                                                          );
                                                        },
                                                      ).then((value) =>
                                                          safeSetState(
                                                              () {}));
                                                    } else {
                                                      await showModalBottomSheet(
                                                        isScrollControlled:
                                                            true,
                                                        backgroundColor:
                                                            Colors
                                                                .transparent,
                                                        enableDrag: false,
                                                        useSafeArea: true,
                                                        context: context,
                                                        builder: (context) {
                                                          return GestureDetector(
                                                            onTap: () =>
                                                                FocusScope.of(
                                                                        context)
                                                                    .unfocus(),
                                                            child: Padding(
                                                              padding: MediaQuery
                                                                  .viewInsetsOf(
                                                                      context),
                                                              child:
                                                                  const BoostPurchaseComponentWidget(),
                                                            ),
                                                          );
                                                        },
                                                      ).then((value) =>
                                                          safeSetState(
                                                              () {}));
                                                    }
                                                  },
                                                ),
                                              );
                                            }
                                          },
                                        ),
                                        wrapWithModel(
                                          model: _model
                                              .rosesProfileHomeWidgetModel,
                                          updateCallback: () =>
                                              safeSetState(() {}),
                                          updateOnChange: true,
                                          child: RosesProfileHomeWidgetWidget(
                                            callback: () async {
                                              await showModalBottomSheet(
                                                isScrollControlled: true,
                                                backgroundColor:
                                                    Colors.transparent,
                                                enableDrag: false,
                                                context: context,
                                                builder: (context) {
                                                  return GestureDetector(
                                                    onTap: () =>
                                                        FocusScope.of(context)
                                                            .unfocus(),
                                                    child: Padding(
                                                      padding: MediaQuery
                                                          .viewInsetsOf(
                                                              context),
                                                      child:
                                                          const RosePurchaseComponentWidget(),
                                                    ),
                                                  );
                                                },
                                              ).then((value) =>
                                                  safeSetState(() {}));
                                            },
                                          ),
                                        ),
                                      ].divide(const SizedBox(height: 13.0)),
                                    ),
                                  ),
                                if (valueOrDefault<bool>(
                                    currentUserDocument?.signUpFinished,
                                    false))
                                  Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        0.0, 32.0, 0.0, 0.0),
                                    child: Builder(
                                      builder: (context) {
                                         if (((currentUserDocument?.gender ==
                                          Gender.Female) &&
                                      (valueOrDefault(
                                              currentUserDocument
                                                  ?.fiveTestGroup,
                                              0) >
                                          getRemoteConfigInt(
                                              'divine_legacy_t')))) {
                                    return wrapWithModel(
                                      model: _model.divineHeaderComponentModel,
                                      updateCallback: () => safeSetState(() {}),
                                      child: !(revenue_cat.activeEntitlementIds
                                          .contains('divine_access')) ? const DivineHeaderComponentWidget(
                                        clickable: true,
                                      ) : Container(),
                                    );
                                  } else
                                        if (valueOrDefault<bool>(
                                            currentUserDocument
                                                ?.purchasesTestingNewDesign1,
                                            false)) {
                                          return Align(
                                            alignment: const AlignmentDirectional(
                                                0.0, 0.0),
                                            child: Container(
                                              constraints: const BoxConstraints(
                                                maxHeight: 400.0,
                                              ),
                                              decoration: const BoxDecoration(),
                                              child: wrapWithModel(
                                                model: _model
                                                    .profileHomePurchasesHorizontalSliderCopyModel,
                                                updateCallback: () =>
                                                    safeSetState(() {}),
                                                updateOnChange: true,
                                                child:
                                                    ProfileHomePurchasesHorizontalSliderCopyWidget(
                                                  purchaseBoxesList:
                                                      functions.getHomePackagesList(
                                                          revenue_cat
                                                              .offerings!
                                                              .current!
                                                              .availablePackages
                                                              .map((e) => e
                                                                  .identifier)
                                                              .toList(),
                                                          revenue_cat
                                                              .activeEntitlementIds
                                                              .toList(),
                                                          currentUserDocument!
                                                              .gender!,
                                                          getRemoteConfigBool(
                                                              'profile_home_dual_upgrade_visible'),
                                                          getRemoteConfigBool(
                                                              'profile_home_lifetime_upgrade_visible')),
                                                ),
                                              ),
                                            ),
                                          );
                                        } else {
                                          return Visibility(
                                            visible: valueOrDefault<bool>(
                                                currentUserDocument
                                                    ?.signUpFinished,
                                                false),
                                            child: Column(
                                              mainAxisSize: MainAxisSize.max,
                                              children: [
                                                if ((currentUserDocument?.gender ==
                                                        Gender.Male) &&
                                                    !(revenue_cat.activeEntitlementIds
                                                            .contains(
                                                                'paid_standard_lifetime') ||
                                                        revenue_cat
                                                            .activeEntitlementIds
                                                            .contains(
                                                                'paid_standard_1w')) &&
                                                    (revenue_cat
                                                            .offerings!
                                                            .current!
                                                            .availablePackages
                                                            .contains(revenue_cat
                                                                .offerings!
                                                                .current!
                                                                .getPackage(
                                                                    'chyrpe_standard_lifetime')) &&
                                                        revenue_cat
                                                            .offerings!
                                                            .current!
                                                            .availablePackages
                                                            .contains(revenue_cat.offerings!.current!.getPackage('chyrpe_standard_1w'))) &&
                                                    getRemoteConfigBool('profile_home_dual_upgrade_visible'))
                                                  Stack(
                                                    children: [
                                                      Container(
                                                        width:
                                                            MediaQuery.sizeOf(
                                                                        context)
                                                                    .width *
                                                                1.0,
                                                        height: 100.0,
                                                        decoration:
                                                            BoxDecoration(
                                                          gradient:
                                                              const LinearGradient(
                                                            colors: [
                                                              Color(
                                                                  0xFF91B2FB),
                                                              Color(
                                                                  0xFFFF9EDE)
                                                            ],
                                                            stops: [0.0, 1.0],
                                                            begin:
                                                                AlignmentDirectional(
                                                                    1.0, 0.0),
                                                            end:
                                                                AlignmentDirectional(
                                                                    -1.0, 0),
                                                          ),
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(
                                                                      9.0),
                                                        ),
                                                      ),
                                                      ClipRRect(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(
                                                                    9.0),
                                                        child: Container(
                                                          decoration:
                                                              BoxDecoration(
                                                            gradient:
                                                                LinearGradient(
                                                              colors: [
                                                                const Color(
                                                                    0x00FFFFFF),
                                                                FlutterFlowTheme.of(
                                                                        context)
                                                                    .info
                                                              ],
                                                              stops: const [
                                                                0.0,
                                                                0.4
                                                              ],
                                                              begin:
                                                                  const AlignmentDirectional(
                                                                      0.0,
                                                                      -1.0),
                                                              end:
                                                                  const AlignmentDirectional(
                                                                      0, 1.0),
                                                            ),
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        9.0),
                                                            border:
                                                                Border.all(
                                                              color: const Color(
                                                                  0xFFBFC1C5),
                                                              width: 1.0,
                                                            ),
                                                          ),
                                                          child: Padding(
                                                            padding:
                                                                const EdgeInsetsDirectional
                                                                    .fromSTEB(
                                                                        23.0,
                                                                        11.0,
                                                                        23.0,
                                                                        20.0),
                                                            child: Column(
                                                              mainAxisSize:
                                                                  MainAxisSize
                                                                      .max,
                                                              children: [
                                                                Row(
                                                                  mainAxisSize:
                                                                      MainAxisSize
                                                                          .max,
                                                                  mainAxisAlignment:
                                                                      MainAxisAlignment
                                                                          .spaceBetween,
                                                                  children: [
                                                                    Row(
                                                                      mainAxisSize:
                                                                          MainAxisSize.max,
                                                                      children: [
                                                                        Align(
                                                                          alignment:
                                                                              const AlignmentDirectional(0.0, 0.0),
                                                                          child:
                                                                              Row(
                                                                            mainAxisSize: MainAxisSize.max,
                                                                            mainAxisAlignment: MainAxisAlignment.center,
                                                                            children: [
                                                                              Text(
                                                                                'chyrpe',
                                                                                style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                      fontFamily: 'BT Beau Sans',
                                                                                      fontSize: 28.0,
                                                                                      letterSpacing: 0.0,
                                                                                      fontWeight: FontWeight.w600,
                                                                                      useGoogleFonts: false,
                                                                                    ),
                                                                              ),
                                                                              Padding(
                                                                                padding: const EdgeInsetsDirectional.fromSTEB(5.0, 0.0, 0.0, 0.0),
                                                                                child: Container(
                                                                                  width: 70.0,
                                                                                  height: 19.0,
                                                                                  decoration: BoxDecoration(
                                                                                    gradient: const LinearGradient(
                                                                                      colors: [
                                                                                        Color(0xFF80A6FA),
                                                                                        Color(0xFFFF67CB)
                                                                                      ],
                                                                                      stops: [0.0, 1.0],
                                                                                      begin: AlignmentDirectional(1.0, 0.0),
                                                                                      end: AlignmentDirectional(-1.0, 0),
                                                                                    ),
                                                                                    borderRadius: BorderRadius.circular(100.0),
                                                                                  ),
                                                                                  child: Align(
                                                                                    alignment: const AlignmentDirectional(0.0, 0.0),
                                                                                    child: Text(
                                                                                      'STANDARD',
                                                                                      textAlign: TextAlign.center,
                                                                                      style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                            fontFamily: 'BT Beau Sans',
                                                                                            color: FlutterFlowTheme.of(context).info,
                                                                                            fontSize: 10.0,
                                                                                            letterSpacing: 0.0,
                                                                                            fontWeight: FontWeight.normal,
                                                                                            useGoogleFonts: false,
                                                                                          ),
                                                                                    ),
                                                                                  ),
                                                                                ),
                                                                              ),
                                                                            ],
                                                                          ),
                                                                        ),
                                                                      ],
                                                                    ),
                                                                    InkWell(
                                                                      splashColor:
                                                                          Colors.transparent,
                                                                      focusColor:
                                                                          Colors.transparent,
                                                                      hoverColor:
                                                                          Colors.transparent,
                                                                      highlightColor:
                                                                          Colors.transparent,
                                                                      onTap:
                                                                          () async {
                                                                      },
                                                                      child:
                                                                          Container(
                                                                        width:
                                                                            100.0,
                                                                        height:
                                                                            100.0,
                                                                        constraints:
                                                                            const BoxConstraints(
                                                                          minWidth:
                                                                              128.0,
                                                                          minHeight:
                                                                              45.0,
                                                                          maxWidth:
                                                                              128.0,
                                                                          maxHeight:
                                                                              45.0,
                                                                        ),
                                                                        decoration:
                                                                            BoxDecoration(
                                                                          gradient:
                                                                              const LinearGradient(
                                                                            colors: [
                                                                              Color(0xFF80A6FA),
                                                                              Color(0xFFFF67CB)
                                                                            ],
                                                                            stops: [
                                                                              0.0,
                                                                              1.0
                                                                            ],
                                                                            begin: AlignmentDirectional(1.0, 0.0),
                                                                            end: AlignmentDirectional(-1.0, 0),
                                                                          ),
                                                                          borderRadius:
                                                                              BorderRadius.circular(100.0),
                                                                        ),
                                                                        child:
                                                                            Align(
                                                                          alignment:
                                                                              const AlignmentDirectional(0.0, 0.0),
                                                                          child:
                                                                              Text(
                                                                            getRemoteConfigString('freepaid_cohort_profilehomewidg_bttn'),
                                                                            style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                  fontFamily: 'BT Beau Sans',
                                                                                  color: FlutterFlowTheme.of(context).info,
                                                                                  fontSize: 16.0,
                                                                                  letterSpacing: 0.0,
                                                                                  fontWeight: FontWeight.bold,
                                                                                  useGoogleFonts: false,
                                                                                ),
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ],
                                                                ),
                                                                Align(
                                                                  alignment:
                                                                      const AlignmentDirectional(
                                                                          -1.0,
                                                                          1.0),
                                                                  child:
                                                                      Padding(
                                                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                                                        0.0,
                                                                        14.0,
                                                                        0.0,
                                                                        0.0),
                                                                    child:
                                                                        Text(
                                                                      getRemoteConfigString(
                                                                          'freepaid_cohort_profilehomewidg_desc'),
                                                                      style: FlutterFlowTheme.of(context)
                                                                          .bodyMedium
                                                                          .override(
                                                                            fontFamily: 'BT Beau Sans',
                                                                            fontSize: 16.0,
                                                                            letterSpacing: 0.0,
                                                                            useGoogleFonts: false,
                                                                          ),
                                                                    ),
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                
                                                if (revenue_cat
                                                        .activeEntitlementIds
                                                        .contains(
                                                            'paid_standard_1w') &&
                                                    (currentUserDocument
                                                            ?.gender ==
                                                        Gender.Male) &&
                                                    revenue_cat
                                                        .offerings!
                                                        .current!
                                                        .availablePackages
                                                        .contains(revenue_cat
                                                            .offerings!
                                                            .current!
                                                            .getPackage(
                                                                'chyrpe_standard_lifetime')) &&
                                                    getRemoteConfigBool(
                                                        'profile_home_lifetime_upgrade_visible') &&
                                                    !revenue_cat
                                                        .activeEntitlementIds
                                                        .contains(
                                                            'paid_standard_lifetime'))
                                                  Stack(
                                                    children: [
                                                      Container(
                                                        width:
                                                            MediaQuery.sizeOf(
                                                                        context)
                                                                    .width *
                                                                1.0,
                                                        height: 100.0,
                                                        decoration:
                                                            BoxDecoration(
                                                          gradient:
                                                              const LinearGradient(
                                                            colors: [
                                                              Color(
                                                                  0xFF91B2FB),
                                                              Color(
                                                                  0xFFFF9EDE)
                                                            ],
                                                            stops: [0.0, 1.0],
                                                            begin:
                                                                AlignmentDirectional(
                                                                    1.0, 0.0),
                                                            end:
                                                                AlignmentDirectional(
                                                                    -1.0, 0),
                                                          ),
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(
                                                                      9.0),
                                                        ),
                                                      ),
                                                      ClipRRect(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(
                                                                    9.0),
                                                        child: Container(
                                                          decoration:
                                                              BoxDecoration(
                                                            gradient:
                                                                LinearGradient(
                                                              colors: [
                                                                const Color(
                                                                    0x00FFFFFF),
                                                                FlutterFlowTheme.of(
                                                                        context)
                                                                    .info
                                                              ],
                                                              stops: const [
                                                                0.0,
                                                                0.4
                                                              ],
                                                              begin:
                                                                  const AlignmentDirectional(
                                                                      0.0,
                                                                      -1.0),
                                                              end:
                                                                  const AlignmentDirectional(
                                                                      0, 1.0),
                                                            ),
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        9.0),
                                                            border:
                                                                Border.all(
                                                              color: const Color(
                                                                  0xFFBFC1C5),
                                                              width: 1.0,
                                                            ),
                                                          ),
                                                          child: Padding(
                                                            padding:
                                                                const EdgeInsetsDirectional
                                                                    .fromSTEB(
                                                                        23.0,
                                                                        11.0,
                                                                        23.0,
                                                                        20.0),
                                                            child: Column(
                                                              mainAxisSize:
                                                                  MainAxisSize
                                                                      .max,
                                                              children: [
                                                                Row(
                                                                  mainAxisSize:
                                                                      MainAxisSize
                                                                          .max,
                                                                  mainAxisAlignment:
                                                                      MainAxisAlignment
                                                                          .spaceBetween,
                                                                  children: [
                                                                    Row(
                                                                      mainAxisSize:
                                                                          MainAxisSize.max,
                                                                      children: [
                                                                        Align(
                                                                          alignment:
                                                                              const AlignmentDirectional(0.0, 0.0),
                                                                          child:
                                                                              Row(
                                                                            mainAxisSize: MainAxisSize.max,
                                                                            mainAxisAlignment: MainAxisAlignment.center,
                                                                            children: [
                                                                              Text(
                                                                                'chyrpe',
                                                                                style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                      fontFamily: 'BT Beau Sans',
                                                                                      fontSize: 28.0,
                                                                                      letterSpacing: 0.0,
                                                                                      fontWeight: FontWeight.w600,
                                                                                      useGoogleFonts: false,
                                                                                    ),
                                                                              ),
                                                                              Padding(
                                                                                padding: const EdgeInsetsDirectional.fromSTEB(5.0, 0.0, 0.0, 0.0),
                                                                                child: Container(
                                                                                  width: 60.0,
                                                                                  height: 19.0,
                                                                                  decoration: BoxDecoration(
                                                                                    gradient: const LinearGradient(
                                                                                      colors: [
                                                                                        Color(0xFF80A6FA),
                                                                                        Color(0xFFFF67CB)
                                                                                      ],
                                                                                      stops: [0.0, 1.0],
                                                                                      begin: AlignmentDirectional(1.0, 0.0),
                                                                                      end: AlignmentDirectional(-1.0, 0),
                                                                                    ),
                                                                                    borderRadius: BorderRadius.circular(100.0),
                                                                                  ),
                                                                                  child: Align(
                                                                                    alignment: const AlignmentDirectional(0.0, 0.0),
                                                                                    child: Text(
                                                                                      'LIFETIME',
                                                                                      textAlign: TextAlign.center,
                                                                                      style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                            fontFamily: 'BT Beau Sans',
                                                                                            color: FlutterFlowTheme.of(context).info,
                                                                                            fontSize: 10.0,
                                                                                            letterSpacing: 0.0,
                                                                                            fontWeight: FontWeight.normal,
                                                                                            useGoogleFonts: false,
                                                                                          ),
                                                                                    ),
                                                                                  ),
                                                                                ),
                                                                              ),
                                                                            ],
                                                                          ),
                                                                        ),
                                                                      ],
                                                                    ),
                                                                    InkWell(
                                                                      splashColor:
                                                                          Colors.transparent,
                                                                      focusColor:
                                                                          Colors.transparent,
                                                                      hoverColor:
                                                                          Colors.transparent,
                                                                      highlightColor:
                                                                          Colors.transparent,
                                                                      onTap:
                                                                          () async {
                                                                        if (valueOrDefault<bool>(
                                                                            currentUserDocument?.purchasesTestingNewDesign1,
                                                                            false)) {
                                                                        }
                                                                      },
                                                                      child:
                                                                          Container(
                                                                        width:
                                                                            100.0,
                                                                        height:
                                                                            100.0,
                                                                        constraints:
                                                                            const BoxConstraints(
                                                                          minWidth:
                                                                              128.0,
                                                                          minHeight:
                                                                              45.0,
                                                                          maxWidth:
                                                                              128.0,
                                                                          maxHeight:
                                                                              45.0,
                                                                        ),
                                                                        decoration:
                                                                            BoxDecoration(
                                                                          gradient:
                                                                              const LinearGradient(
                                                                            colors: [
                                                                              Color(0xFF80A6FA),
                                                                              Color(0xFFFF67CB)
                                                                            ],
                                                                            stops: [
                                                                              0.0,
                                                                              1.0
                                                                            ],
                                                                            begin: AlignmentDirectional(1.0, 0.0),
                                                                            end: AlignmentDirectional(-1.0, 0),
                                                                          ),
                                                                          borderRadius:
                                                                              BorderRadius.circular(100.0),
                                                                        ),
                                                                        child:
                                                                            Align(
                                                                          alignment:
                                                                              const AlignmentDirectional(0.0, 0.0),
                                                                          child:
                                                                              Text(
                                                                            'Upgrade',
                                                                            style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                  fontFamily: 'BT Beau Sans',
                                                                                  color: FlutterFlowTheme.of(context).info,
                                                                                  fontSize: 16.0,
                                                                                  letterSpacing: 0.0,
                                                                                  fontWeight: FontWeight.bold,
                                                                                  useGoogleFonts: false,
                                                                                ),
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ],
                                                                ),
                                                                Align(
                                                                  alignment:
                                                                      const AlignmentDirectional(
                                                                          -1.0,
                                                                          1.0),
                                                                  child:
                                                                      Padding(
                                                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                                                        0.0,
                                                                        14.0,
                                                                        0.0,
                                                                        0.0),
                                                                    child:
                                                                        Text(
                                                                      getRemoteConfigString(
                                                                          'profile_home_lifetime_upgrade_description'),
                                                                      style: FlutterFlowTheme.of(context)
                                                                          .bodyMedium
                                                                          .override(
                                                                            fontFamily: 'BT Beau Sans',
                                                                            fontSize: 16.0,
                                                                            letterSpacing: 0.0,
                                                                            useGoogleFonts: false,
                                                                          ),
                                                                    ),
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                if (!(revenue_cat
                                                        .activeEntitlementIds
                                                        .contains(
                                                            'plus_access') ||
                                                    revenue_cat
                                                        .activeEntitlementIds
                                                        .contains(
                                                            'evolved_access')))
                                                  Container(
                                                    decoration: BoxDecoration(
                                                      gradient:
                                                          LinearGradient(
                                                        colors: [
                                                          const Color(0xFFBFC1C5),
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .info
                                                        ],
                                                        stops: const [0.0, 0.4],
                                                        begin:
                                                            const AlignmentDirectional(
                                                                0.0, -1.0),
                                                        end:
                                                            const AlignmentDirectional(
                                                                0, 1.0),
                                                      ),
                                                      borderRadius:
                                                          BorderRadius
                                                              .circular(9.0),
                                                      border: Border.all(
                                                        color:
                                                            const Color(0xFF575757),
                                                        width: 1.0,
                                                      ),
                                                    ),
                                                    child: Padding(
                                                      padding:
                                                          const EdgeInsetsDirectional
                                                              .fromSTEB(
                                                                  23.0,
                                                                  11.0,
                                                                  23.0,
                                                                  20.0),
                                                      child: Column(
                                                        mainAxisSize:
                                                            MainAxisSize.max,
                                                        children: [
                                                          Row(
                                                            mainAxisSize:
                                                                MainAxisSize
                                                                    .max,
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .spaceBetween,
                                                            children: [
                                                              Row(
                                                                mainAxisSize:
                                                                    MainAxisSize
                                                                        .max,
                                                                children: [
                                                                  Align(
                                                                    alignment:
                                                                        const AlignmentDirectional(
                                                                            0.0,
                                                                            0.0),
                                                                    child:
                                                                        Row(
                                                                      mainAxisSize:
                                                                          MainAxisSize.max,
                                                                      mainAxisAlignment:
                                                                          MainAxisAlignment.center,
                                                                      children: [
                                                                        Text(
                                                                          'chyrpe',
                                                                          style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                fontFamily: 'BT Beau Sans',
                                                                                fontSize: 28.0,
                                                                                letterSpacing: 0.0,
                                                                                fontWeight: FontWeight.w600,
                                                                                useGoogleFonts: false,
                                                                              ),
                                                                        ),
                                                                        Padding(
                                                                          padding: const EdgeInsetsDirectional.fromSTEB(
                                                                              5.0,
                                                                              0.0,
                                                                              0.0,
                                                                              0.0),
                                                                          child:
                                                                              Container(
                                                                            width: 50.0,
                                                                            height: 19.0,
                                                                            decoration: BoxDecoration(
                                                                              gradient: const LinearGradient(
                                                                                colors: [
                                                                                  Color(0xFF575757),
                                                                                  Color(0xFFB0B0B0)
                                                                                ],
                                                                                stops: [
                                                                                  0.0,
                                                                                  1.0
                                                                                ],
                                                                                begin: AlignmentDirectional(1.0, 0.0),
                                                                                end: AlignmentDirectional(-1.0, 0),
                                                                              ),
                                                                              borderRadius: BorderRadius.circular(100.0),
                                                                            ),
                                                                            child: Align(
                                                                              alignment: const AlignmentDirectional(0.0, 0.0),
                                                                              child: Text(
                                                                                'PLUS',
                                                                                textAlign: TextAlign.center,
                                                                                style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                      fontFamily: 'BT Beau Sans',
                                                                                      color: FlutterFlowTheme.of(context).info,
                                                                                      fontSize: 10.0,
                                                                                      letterSpacing: 0.0,
                                                                                      fontWeight: FontWeight.normal,
                                                                                      useGoogleFonts: false,
                                                                                    ),
                                                                              ),
                                                                            ),
                                                                          ),
                                                                        ),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                              InkWell(
                                                                splashColor:
                                                                    Colors
                                                                        .transparent,
                                                                focusColor: Colors
                                                                    .transparent,
                                                                hoverColor: Colors
                                                                    .transparent,
                                                                highlightColor:
                                                                    Colors
                                                                        .transparent,
                                                                onTap:
                                                                    () async {
                                                                  if (valueOrDefault<
                                                                          bool>(
                                                                      currentUserDocument
                                                                          ?.purchasesTestingNewDesign1,
                                                                      false)) {
                                                                    
                                                                  }
                                                                },
                                                                child:
                                                                    Container(
                                                                  width:
                                                                      100.0,
                                                                  height:
                                                                      100.0,
                                                                  constraints:
                                                                      const BoxConstraints(
                                                                    minWidth:
                                                                        128.0,
                                                                    minHeight:
                                                                        45.0,
                                                                    maxWidth:
                                                                        128.0,
                                                                    maxHeight:
                                                                        45.0,
                                                                  ),
                                                                  decoration:
                                                                      BoxDecoration(
                                                                    gradient:
                                                                        const LinearGradient(
                                                                      colors: [
                                                                        Color(
                                                                            0xFF575757),
                                                                        Color(
                                                                            0xFF949494)
                                                                      ],
                                                                      stops: [
                                                                        0.0,
                                                                        1.0
                                                                      ],
                                                                      begin: AlignmentDirectional(
                                                                          -1.0,
                                                                          0.0),
                                                                      end: AlignmentDirectional(
                                                                          1.0,
                                                                          0),
                                                                    ),
                                                                    borderRadius:
                                                                        BorderRadius.circular(
                                                                            100.0),
                                                                  ),
                                                                  child:
                                                                      Align(
                                                                    alignment:
                                                                        const AlignmentDirectional(
                                                                            0.0,
                                                                            0.0),
                                                                    child:
                                                                        Text(
                                                                      'Upgrade',
                                                                      style: FlutterFlowTheme.of(context)
                                                                          .bodyMedium
                                                                          .override(
                                                                            fontFamily: 'BT Beau Sans',
                                                                            color: FlutterFlowTheme.of(context).info,
                                                                            fontSize: 16.0,
                                                                            letterSpacing: 0.0,
                                                                            fontWeight: FontWeight.bold,
                                                                            useGoogleFonts: false,
                                                                          ),
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                          Padding(
                                                            padding:
                                                                const EdgeInsetsDirectional
                                                                    .fromSTEB(
                                                                        0.0,
                                                                        14.0,
                                                                        0.0,
                                                                        0.0),
                                                            child: Column(
                                                              mainAxisSize:
                                                                  MainAxisSize
                                                                      .max,
                                                              children: [
                                                                if (currentUserDocument
                                                                        ?.gender ==
                                                                    Gender
                                                                        .Male)
                                                                  Row(
                                                                    mainAxisSize:
                                                                        MainAxisSize
                                                                            .max,
                                                                    children: [
                                                                      Text(
                                                                        '+',
                                                                        style: FlutterFlowTheme.of(context)
                                                                            .bodyMedium
                                                                            .override(
                                                                              fontFamily: 'BT Beau Sans',
                                                                              color: const Color(0xFF575757),
                                                                              fontSize: 18.0,
                                                                              letterSpacing: 0.0,
                                                                              fontWeight: FontWeight.w600,
                                                                              useGoogleFonts: false,
                                                                            ),
                                                                      ),
                                                                      Padding(
                                                                        padding: const EdgeInsetsDirectional.fromSTEB(
                                                                            10.0,
                                                                            0.0,
                                                                            0.0,
                                                                            0.0),
                                                                        child:
                                                                            Text(
                                                                          'See profile feedback',
                                                                          style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                fontFamily: 'BT Beau Sans',
                                                                                fontSize: 16.0,
                                                                                letterSpacing: 0.0,
                                                                                useGoogleFonts: false,
                                                                              ),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                Padding(
                                                                  padding: const EdgeInsetsDirectional
                                                                      .fromSTEB(
                                                                          0.0,
                                                                          18.0,
                                                                          0.0,
                                                                          0.0),
                                                                  child: Row(
                                                                    mainAxisSize:
                                                                        MainAxisSize
                                                                            .max,
                                                                    children: [
                                                                      Text(
                                                                        '+',
                                                                        style: FlutterFlowTheme.of(context)
                                                                            .bodyMedium
                                                                            .override(
                                                                              fontFamily: 'BT Beau Sans',
                                                                              color: const Color(0xFF575757),
                                                                              fontSize: 18.0,
                                                                              letterSpacing: 0.0,
                                                                              fontWeight: FontWeight.w600,
                                                                              useGoogleFonts: false,
                                                                            ),
                                                                      ),
                                                                      Padding(
                                                                        padding: const EdgeInsetsDirectional.fromSTEB(
                                                                            10.0,
                                                                            0.0,
                                                                            0.0,
                                                                            0.0),
                                                                        child:
                                                                            Text(
                                                                          'Hide pictures',
                                                                          style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                fontFamily: 'BT Beau Sans',
                                                                                fontSize: 16.0,
                                                                                letterSpacing: 0.0,
                                                                                useGoogleFonts: false,
                                                                              ),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                                Padding(
                                                                  padding: const EdgeInsetsDirectional
                                                                      .fromSTEB(
                                                                          0.0,
                                                                          18.0,
                                                                          0.0,
                                                                          0.0),
                                                                  child: Row(
                                                                    mainAxisSize:
                                                                        MainAxisSize
                                                                            .max,
                                                                    children: [
                                                                      Text(
                                                                        '+',
                                                                        style: FlutterFlowTheme.of(context)
                                                                            .bodyMedium
                                                                            .override(
                                                                              fontFamily: 'BT Beau Sans',
                                                                              color: const Color(0xFF575757),
                                                                              fontSize: 18.0,
                                                                              letterSpacing: 0.0,
                                                                              fontWeight: FontWeight.w600,
                                                                              useGoogleFonts: false,
                                                                            ),
                                                                      ),
                                                                      Padding(
                                                                        padding: const EdgeInsetsDirectional.fromSTEB(
                                                                            10.0,
                                                                            0.0,
                                                                            0.0,
                                                                            0.0),
                                                                        child:
                                                                            Text(
                                                                          'Stay invisible',
                                                                          style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                fontFamily: 'BT Beau Sans',
                                                                                fontSize: 16.0,
                                                                                letterSpacing: 0.0,
                                                                                useGoogleFonts: false,
                                                                              ),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                                Padding(
                                                                  padding: const EdgeInsetsDirectional
                                                                      .fromSTEB(
                                                                          0.0,
                                                                          23.0,
                                                                          0.0,
                                                                          0.0),
                                                                  child: Row(
                                                                    mainAxisSize:
                                                                        MainAxisSize
                                                                            .max,
                                                                    children: [
                                                                      InkWell(
                                                                        splashColor:
                                                                            Colors.transparent,
                                                                        focusColor:
                                                                            Colors.transparent,
                                                                        hoverColor:
                                                                            Colors.transparent,
                                                                        highlightColor:
                                                                            Colors.transparent,
                                                                        onTap:
                                                                            () async {
                                                                         
                                                                        },
                                                                        child:
                                                                            Text(
                                                                          'see all advantages',
                                                                          style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                fontFamily: 'BT Beau Sans',
                                                                                color: const Color(0xFF575757),
                                                                                fontSize: 16.0,
                                                                                letterSpacing: 0.0,
                                                                                fontWeight: FontWeight.w600,
                                                                                useGoogleFonts: false,
                                                                              ),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                if (!(revenue_cat
                                                        .activeEntitlementIds
                                                        .contains(
                                                            'gold_access') ||
                                                    revenue_cat
                                                        .activeEntitlementIds
                                                        .contains(
                                                            'evolved_access')))
                                                  Container(
                                                    decoration: BoxDecoration(
                                                      gradient:
                                                          LinearGradient(
                                                        colors: [
                                                          const Color(0xFFFEDA73),
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .info
                                                        ],
                                                        stops: const [0.0, 0.4],
                                                        begin:
                                                            const AlignmentDirectional(
                                                                0.0, -1.0),
                                                        end:
                                                            const AlignmentDirectional(
                                                                0, 1.0),
                                                      ),
                                                      borderRadius:
                                                          BorderRadius
                                                              .circular(9.0),
                                                      border: Border.all(
                                                        color:
                                                            const Color(0xFFBE9F04),
                                                        width: 1.0,
                                                      ),
                                                    ),
                                                    child: Padding(
                                                      padding:
                                                          const EdgeInsetsDirectional
                                                              .fromSTEB(
                                                                  23.0,
                                                                  11.0,
                                                                  23.0,
                                                                  20.0),
                                                      child: Column(
                                                        mainAxisSize:
                                                            MainAxisSize.max,
                                                        children: [
                                                          Row(
                                                            mainAxisSize:
                                                                MainAxisSize
                                                                    .max,
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .spaceBetween,
                                                            children: [
                                                              Row(
                                                                mainAxisSize:
                                                                    MainAxisSize
                                                                        .max,
                                                                children: [
                                                                  Align(
                                                                    alignment:
                                                                        const AlignmentDirectional(
                                                                            0.0,
                                                                            0.0),
                                                                    child:
                                                                        Row(
                                                                      mainAxisSize:
                                                                          MainAxisSize.max,
                                                                      mainAxisAlignment:
                                                                          MainAxisAlignment.center,
                                                                      children: [
                                                                        Text(
                                                                          'chyrpe',
                                                                          style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                fontFamily: 'BT Beau Sans',
                                                                                fontSize: 28.0,
                                                                                letterSpacing: 0.0,
                                                                                fontWeight: FontWeight.w600,
                                                                                useGoogleFonts: false,
                                                                              ),
                                                                        ),
                                                                        Padding(
                                                                          padding: const EdgeInsetsDirectional.fromSTEB(
                                                                              5.0,
                                                                              0.0,
                                                                              0.0,
                                                                              0.0),
                                                                          child:
                                                                              Container(
                                                                            width: 55.0,
                                                                            height: 19.0,
                                                                            decoration: BoxDecoration(
                                                                              gradient: const LinearGradient(
                                                                                colors: [
                                                                                  Color(0xFFFFCC10),
                                                                                  Color(0xFFBE9F04)
                                                                                ],
                                                                                stops: [
                                                                                  0.0,
                                                                                  1.0
                                                                                ],
                                                                                begin: AlignmentDirectional(1.0, 0.0),
                                                                                end: AlignmentDirectional(-1.0, 0),
                                                                              ),
                                                                              borderRadius: BorderRadius.circular(100.0),
                                                                            ),
                                                                            child: Align(
                                                                              alignment: const AlignmentDirectional(0.0, 0.0),
                                                                              child: Text(
                                                                                'GOLD',
                                                                                textAlign: TextAlign.center,
                                                                                style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                      fontFamily: 'BT Beau Sans',
                                                                                      color: FlutterFlowTheme.of(context).info,
                                                                                      fontSize: 10.0,
                                                                                      letterSpacing: 0.0,
                                                                                      fontWeight: FontWeight.normal,
                                                                                      useGoogleFonts: false,
                                                                                    ),
                                                                              ),
                                                                            ),
                                                                          ),
                                                                        ),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                              InkWell(
                                                                splashColor:
                                                                    Colors
                                                                        .transparent,
                                                                focusColor: Colors
                                                                    .transparent,
                                                                hoverColor: Colors
                                                                    .transparent,
                                                                highlightColor:
                                                                    Colors
                                                                        .transparent,
                                                                onTap:
                                                                    () async {
                                                                  
                                                                     
                                                                  
                                                                },
                                                                child:
                                                                    Container(
                                                                  width:
                                                                      100.0,
                                                                  height:
                                                                      100.0,
                                                                  constraints:
                                                                      const BoxConstraints(
                                                                    minWidth:
                                                                        128.0,
                                                                    minHeight:
                                                                        45.0,
                                                                    maxWidth:
                                                                        128.0,
                                                                    maxHeight:
                                                                        45.0,
                                                                  ),
                                                                  decoration:
                                                                      BoxDecoration(
                                                                    gradient:
                                                                        const LinearGradient(
                                                                      colors: [
                                                                        Color(
                                                                            0xFFBE9F04),
                                                                        Color(
                                                                            0xFFFFB800)
                                                                      ],
                                                                      stops: [
                                                                        0.0,
                                                                        1.0
                                                                      ],
                                                                      begin: AlignmentDirectional(
                                                                          -1.0,
                                                                          0.0),
                                                                      end: AlignmentDirectional(
                                                                          1.0,
                                                                          0),
                                                                    ),
                                                                    borderRadius:
                                                                        BorderRadius.circular(
                                                                            100.0),
                                                                  ),
                                                                  child:
                                                                      Align(
                                                                    alignment:
                                                                        const AlignmentDirectional(
                                                                            0.0,
                                                                            0.0),
                                                                    child:
                                                                        Text(
                                                                      'Upgrade',
                                                                      style: FlutterFlowTheme.of(context)
                                                                          .bodyMedium
                                                                          .override(
                                                                            fontFamily: 'BT Beau Sans',
                                                                            color: FlutterFlowTheme.of(context).info,
                                                                            fontSize: 16.0,
                                                                            letterSpacing: 0.0,
                                                                            fontWeight: FontWeight.bold,
                                                                            useGoogleFonts: false,
                                                                          ),
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                          Padding(
                                                            padding:
                                                                const EdgeInsetsDirectional
                                                                    .fromSTEB(
                                                                        0.0,
                                                                        14.0,
                                                                        0.0,
                                                                        0.0),
                                                            child: Column(
                                                              mainAxisSize:
                                                                  MainAxisSize
                                                                      .max,
                                                              children: [
                                                                Row(
                                                                  mainAxisSize:
                                                                      MainAxisSize
                                                                          .max,
                                                                  children: [
                                                                    Text(
                                                                      '+',
                                                                      style: FlutterFlowTheme.of(context)
                                                                          .bodyMedium
                                                                          .override(
                                                                            fontFamily: 'BT Beau Sans',
                                                                            color: const Color(0xFFBE9F04),
                                                                            fontSize: 18.0,
                                                                            letterSpacing: 0.0,
                                                                            fontWeight: FontWeight.w600,
                                                                            useGoogleFonts: false,
                                                                          ),
                                                                    ),
                                                                    Padding(
                                                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                                                          10.0,
                                                                          0.0,
                                                                          0.0,
                                                                          0.0),
                                                                      child:
                                                                          Text(
                                                                        'Get extra likes',
                                                                        style: FlutterFlowTheme.of(context)
                                                                            .bodyMedium
                                                                            .override(
                                                                              fontFamily: 'BT Beau Sans',
                                                                              fontSize: 16.0,
                                                                              letterSpacing: 0.0,
                                                                              useGoogleFonts: false,
                                                                            ),
                                                                      ),
                                                                    ),
                                                                  ],
                                                                ),
                                                                Padding(
                                                                  padding: const EdgeInsetsDirectional
                                                                      .fromSTEB(
                                                                          0.0,
                                                                          18.0,
                                                                          0.0,
                                                                          0.0),
                                                                  child: Row(
                                                                    mainAxisSize:
                                                                        MainAxisSize
                                                                            .max,
                                                                    children: [
                                                                      Text(
                                                                        '+',
                                                                        style: FlutterFlowTheme.of(context)
                                                                            .bodyMedium
                                                                            .override(
                                                                              fontFamily: 'BT Beau Sans',
                                                                              color: const Color(0xFFBE9F04),
                                                                              fontSize: 18.0,
                                                                              letterSpacing: 0.0,
                                                                              fontWeight: FontWeight.w600,
                                                                              useGoogleFonts: false,
                                                                            ),
                                                                      ),
                                                                      Padding(
                                                                        padding: const EdgeInsetsDirectional.fromSTEB(
                                                                            10.0,
                                                                            0.0,
                                                                            0.0,
                                                                            0.0),
                                                                        child:
                                                                            Text(
                                                                          'See who likes you',
                                                                          style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                fontFamily: 'BT Beau Sans',
                                                                                fontSize: 16.0,
                                                                                letterSpacing: 0.0,
                                                                                useGoogleFonts: false,
                                                                              ),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                                Padding(
                                                                  padding: const EdgeInsetsDirectional
                                                                      .fromSTEB(
                                                                          0.0,
                                                                          18.0,
                                                                          0.0,
                                                                          0.0),
                                                                  child: Row(
                                                                    mainAxisSize:
                                                                        MainAxisSize
                                                                            .max,
                                                                    children: [
                                                                      Text(
                                                                        '+',
                                                                        style: FlutterFlowTheme.of(context)
                                                                            .bodyMedium
                                                                            .override(
                                                                              fontFamily: 'BT Beau Sans',
                                                                              color: const Color(0xFFBE9F04),
                                                                              fontSize: 18.0,
                                                                              letterSpacing: 0.0,
                                                                              fontWeight: FontWeight.w600,
                                                                              useGoogleFonts: false,
                                                                            ),
                                                                      ),
                                                                      Padding(
                                                                        padding: const EdgeInsetsDirectional.fromSTEB(
                                                                            10.0,
                                                                            0.0,
                                                                            0.0,
                                                                            0.0),
                                                                        child:
                                                                            Text(
                                                                          '2 evolved likes weekly',
                                                                          style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                fontFamily: 'BT Beau Sans',
                                                                                fontSize: 16.0,
                                                                                letterSpacing: 0.0,
                                                                                useGoogleFonts: false,
                                                                              ),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                                Padding(
                                                                  padding: const EdgeInsetsDirectional
                                                                      .fromSTEB(
                                                                          0.0,
                                                                          23.0,
                                                                          0.0,
                                                                          0.0),
                                                                  child: Row(
                                                                    mainAxisSize:
                                                                        MainAxisSize
                                                                            .max,
                                                                    children: [
                                                                      InkWell(
                                                                        splashColor:
                                                                            Colors.transparent,
                                                                        focusColor:
                                                                            Colors.transparent,
                                                                        hoverColor:
                                                                            Colors.transparent,
                                                                        highlightColor:
                                                                            Colors.transparent,
                                                                        onTap:
                                                                            () async {
                                                                          
                                                                        },
                                                                        child:
                                                                            Text(
                                                                          'see all advantages',
                                                                          style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                fontFamily: 'BT Beau Sans',
                                                                                color: const Color(0xFFBE9F04),
                                                                                fontSize: 16.0,
                                                                                letterSpacing: 0.0,
                                                                                fontWeight: FontWeight.w600,
                                                                                useGoogleFonts: false,
                                                                              ),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                if (!revenue_cat
                                                    .activeEntitlementIds
                                                    .contains(
                                                        'evolved_access'))
                                                  Padding(
                                                    padding:
                                                        const EdgeInsetsDirectional
                                                            .fromSTEB(
                                                                0.0,
                                                                0.0,
                                                                0.0,
                                                                30.0),
                                                    child: Container(
                                                      decoration:
                                                          BoxDecoration(
                                                        gradient:
                                                            LinearGradient(
                                                          colors: [
                                                            const Color(0xFFDBAEFF),
                                                            FlutterFlowTheme.of(
                                                                    context)
                                                                .info
                                                          ],
                                                          stops: const [0.0, 0.4],
                                                          begin:
                                                              const AlignmentDirectional(
                                                                  0.0, -1.0),
                                                          end:
                                                              const AlignmentDirectional(
                                                                  0, 1.0),
                                                        ),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(
                                                                    9.0),
                                                        border: Border.all(
                                                          color: const Color(
                                                              0xFF7611C5),
                                                          width: 1.0,
                                                        ),
                                                      ),
                                                      child: Padding(
                                                        padding:
                                                            const EdgeInsetsDirectional
                                                                .fromSTEB(
                                                                    23.0,
                                                                    11.0,
                                                                    23.0,
                                                                    20.0),
                                                        child: Column(
                                                          mainAxisSize:
                                                              MainAxisSize
                                                                  .max,
                                                          children: [
                                                            Row(
                                                              mainAxisSize:
                                                                  MainAxisSize
                                                                      .max,
                                                              mainAxisAlignment:
                                                                  MainAxisAlignment
                                                                      .spaceBetween,
                                                              children: [
                                                                Row(
                                                                  mainAxisSize:
                                                                      MainAxisSize
                                                                          .max,
                                                                  children: [
                                                                    Text(
                                                                      'chyrpe',
                                                                      style: FlutterFlowTheme.of(context)
                                                                          .bodyMedium
                                                                          .override(
                                                                            fontFamily: 'BT Beau Sans',
                                                                            fontSize: 28.0,
                                                                            letterSpacing: 0.0,
                                                                            fontWeight: FontWeight.w600,
                                                                            useGoogleFonts: false,
                                                                          ),
                                                                    ),
                                                                    Padding(
                                                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                                                          3.0,
                                                                          0.0,
                                                                          0.0,
                                                                          0.0),
                                                                      child:
                                                                          Container(
                                                                        constraints:
                                                                            const BoxConstraints(
                                                                          minWidth:
                                                                              59.0,
                                                                          minHeight:
                                                                              19.0,
                                                                          maxWidth:
                                                                              59.0,
                                                                          maxHeight:
                                                                              19.0,
                                                                        ),
                                                                        decoration:
                                                                            BoxDecoration(
                                                                          gradient:
                                                                              const LinearGradient(
                                                                            colors: [
                                                                              Color(0xFFA12CFD),
                                                                              Color(0xFFFF6C3E)
                                                                            ],
                                                                            stops: [
                                                                              0.0,
                                                                              1.0
                                                                            ],
                                                                            begin: AlignmentDirectional(-0.17, 1.0),
                                                                            end: AlignmentDirectional(0.17, -1.0),
                                                                          ),
                                                                          borderRadius:
                                                                              BorderRadius.circular(100.0),
                                                                        ),
                                                                        alignment: const AlignmentDirectional(
                                                                            0.0,
                                                                            0.0),
                                                                        child:
                                                                            Text(
                                                                          'EVOLVED',
                                                                          style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                fontFamily: 'BT Beau Sans',
                                                                                color: FlutterFlowTheme.of(context).info,
                                                                                fontSize: 10.0,
                                                                                letterSpacing: 0.0,
                                                                                fontWeight: FontWeight.w500,
                                                                                useGoogleFonts: false,
                                                                              ),
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ],
                                                                ),
                                                                InkWell(
                                                                  splashColor:
                                                                      Colors
                                                                          .transparent,
                                                                  focusColor:
                                                                      Colors
                                                                          .transparent,
                                                                  hoverColor:
                                                                      Colors
                                                                          .transparent,
                                                                  highlightColor:
                                                                      Colors
                                                                          .transparent,
                                                                  onTap:
                                                                      () async {
                                                                    
                                                                  },
                                                                  child:
                                                                      Container(
                                                                    width:
                                                                        100.0,
                                                                    height:
                                                                        100.0,
                                                                    constraints:
                                                                        const BoxConstraints(
                                                                      minWidth:
                                                                          128.0,
                                                                      minHeight:
                                                                          45.0,
                                                                      maxWidth:
                                                                          128.0,
                                                                      maxHeight:
                                                                          45.0,
                                                                    ),
                                                                    decoration:
                                                                        BoxDecoration(
                                                                      gradient:
                                                                          const LinearGradient(
                                                                        colors: [
                                                                          Color(0xFFA12CFD),
                                                                          Color(0xFFFF6C3E)
                                                                        ],
                                                                        stops: [
                                                                          0.0,
                                                                          1.0
                                                                        ],
                                                                        begin: AlignmentDirectional(
                                                                            -1.0,
                                                                            0.0),
                                                                        end: AlignmentDirectional(
                                                                            1.0,
                                                                            0),
                                                                      ),
                                                                      borderRadius:
                                                                          BorderRadius.circular(100.0),
                                                                    ),
                                                                    child:
                                                                        Align(
                                                                      alignment: const AlignmentDirectional(
                                                                          0.0,
                                                                          0.0),
                                                                      child:
                                                                          Text(
                                                                        'Upgrade',
                                                                        style: FlutterFlowTheme.of(context)
                                                                            .bodyMedium
                                                                            .override(
                                                                              fontFamily: 'BT Beau Sans',
                                                                              color: FlutterFlowTheme.of(context).info,
                                                                              fontSize: 16.0,
                                                                              letterSpacing: 0.0,
                                                                              fontWeight: FontWeight.bold,
                                                                              useGoogleFonts: false,
                                                                            ),
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                            Padding(
                                                              padding:
                                                                  const EdgeInsetsDirectional
                                                                      .fromSTEB(
                                                                          0.0,
                                                                          14.0,
                                                                          0.0,
                                                                          0.0),
                                                              child: Column(
                                                                mainAxisSize:
                                                                    MainAxisSize
                                                                        .max,
                                                                children: [
                                                                  Row(
                                                                    mainAxisSize:
                                                                        MainAxisSize
                                                                            .max,
                                                                    children: [
                                                                      Text(
                                                                        '+',
                                                                        style: FlutterFlowTheme.of(context)
                                                                            .bodyMedium
                                                                            .override(
                                                                              fontFamily: 'BT Beau Sans',
                                                                              color: const Color(0xFF7611C5),
                                                                              fontSize: 18.0,
                                                                              letterSpacing: 0.0,
                                                                              fontWeight: FontWeight.w600,
                                                                              useGoogleFonts: false,
                                                                            ),
                                                                      ),
                                                                      Padding(
                                                                        padding: const EdgeInsetsDirectional.fromSTEB(
                                                                            10.0,
                                                                            0.0,
                                                                            0.0,
                                                                            0.0),
                                                                        child:
                                                                            Text(
                                                                          'Like twice as much',
                                                                          style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                fontFamily: 'BT Beau Sans',
                                                                                fontSize: 16.0,
                                                                                letterSpacing: 0.0,
                                                                                useGoogleFonts: false,
                                                                              ),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                  Padding(
                                                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                                                        0.0,
                                                                        18.0,
                                                                        0.0,
                                                                        0.0),
                                                                    child:
                                                                        Row(
                                                                      mainAxisSize:
                                                                          MainAxisSize.max,
                                                                      children: [
                                                                        Text(
                                                                          '+',
                                                                          style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                fontFamily: 'BT Beau Sans',
                                                                                color: const Color(0xFF7611C5),
                                                                                fontSize: 18.0,
                                                                                letterSpacing: 0.0,
                                                                                fontWeight: FontWeight.w600,
                                                                                useGoogleFonts: false,
                                                                              ),
                                                                        ),
                                                                        Padding(
                                                                          padding: const EdgeInsetsDirectional.fromSTEB(
                                                                              10.0,
                                                                              0.0,
                                                                              0.0,
                                                                              0.0),
                                                                          child:
                                                                              Text(
                                                                            'See who likes you',
                                                                            style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                  fontFamily: 'BT Beau Sans',
                                                                                  fontSize: 16.0,
                                                                                  letterSpacing: 0.0,
                                                                                  useGoogleFonts: false,
                                                                                ),
                                                                          ),
                                                                        ),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                  if (currentUserDocument
                                                                          ?.gender ==
                                                                      Gender
                                                                          .Male)
                                                                    Padding(
                                                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                                                          0.0,
                                                                          18.0,
                                                                          0.0,
                                                                          0.0),
                                                                      child:
                                                                          Row(
                                                                        mainAxisSize:
                                                                            MainAxisSize.max,
                                                                        children: [
                                                                          Text(
                                                                            '+',
                                                                            style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                  fontFamily: 'BT Beau Sans',
                                                                                  color: const Color(0xFF7611C5),
                                                                                  fontSize: 18.0,
                                                                                  letterSpacing: 0.0,
                                                                                  fontWeight: FontWeight.w600,
                                                                                  useGoogleFonts: false,
                                                                                ),
                                                                          ),
                                                                          Padding(
                                                                            padding: const EdgeInsetsDirectional.fromSTEB(10.0, 0.0, 0.0, 0.0),
                                                                            child: Text(
                                                                              'See profile feedback',
                                                                              style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                    fontFamily: 'BT Beau Sans',
                                                                                    fontSize: 16.0,
                                                                                    letterSpacing: 0.0,
                                                                                    useGoogleFonts: false,
                                                                                  ),
                                                                            ),
                                                                          ),
                                                                        ],
                                                                      ),
                                                                    ),
                                                                  Padding(
                                                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                                                        0.0,
                                                                        18.0,
                                                                        0.0,
                                                                        0.0),
                                                                    child:
                                                                        Row(
                                                                      mainAxisSize:
                                                                          MainAxisSize.max,
                                                                      children: [
                                                                        Text(
                                                                          '+',
                                                                          style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                fontFamily: 'BT Beau Sans',
                                                                                color: const Color(0xFF7611C5),
                                                                                fontSize: 18.0,
                                                                                letterSpacing: 0.0,
                                                                                fontWeight: FontWeight.w600,
                                                                                useGoogleFonts: false,
                                                                              ),
                                                                        ),
                                                                        Padding(
                                                                          padding: const EdgeInsetsDirectional.fromSTEB(
                                                                              10.0,
                                                                              0.0,
                                                                              0.0,
                                                                              0.0),
                                                                          child:
                                                                              Text(
                                                                            'Stay invisible',
                                                                            style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                  fontFamily: 'BT Beau Sans',
                                                                                  fontSize: 16.0,
                                                                                  letterSpacing: 0.0,
                                                                                  useGoogleFonts: false,
                                                                                ),
                                                                          ),
                                                                        ),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                  Padding(
                                                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                                                        0.0,
                                                                        18.0,
                                                                        0.0,
                                                                        0.0),
                                                                    child:
                                                                        Row(
                                                                      mainAxisSize:
                                                                          MainAxisSize.max,
                                                                      children: [
                                                                        Text(
                                                                          '+',
                                                                          style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                fontFamily: 'BT Beau Sans',
                                                                                color: const Color(0xFF7611C5),
                                                                                fontSize: 18.0,
                                                                                letterSpacing: 0.0,
                                                                                fontWeight: FontWeight.w600,
                                                                                useGoogleFonts: false,
                                                                              ),
                                                                        ),
                                                                        Padding(
                                                                          padding: const EdgeInsetsDirectional.fromSTEB(
                                                                              10.0,
                                                                              0.0,
                                                                              0.0,
                                                                              0.0),
                                                                          child:
                                                                              Text(
                                                                            '4 evolved likes weekly',
                                                                            style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                  fontFamily: 'BT Beau Sans',
                                                                                  fontSize: 16.0,
                                                                                  letterSpacing: 0.0,
                                                                                  useGoogleFonts: false,
                                                                                ),
                                                                          ),
                                                                        ),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                  Padding(
                                                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                                                        0.0,
                                                                        23.0,
                                                                        0.0,
                                                                        0.0),
                                                                    child:
                                                                        Row(
                                                                      mainAxisSize:
                                                                          MainAxisSize.max,
                                                                      children: [
                                                                        InkWell(
                                                                          splashColor:
                                                                              Colors.transparent,
                                                                          focusColor:
                                                                              Colors.transparent,
                                                                          hoverColor:
                                                                              Colors.transparent,
                                                                          highlightColor:
                                                                              Colors.transparent,
                                                                          onTap:
                                                                              () async {
                                                                            
                                                                          },
                                                                          child:
                                                                              Text(
                                                                            'see all advantages',
                                                                            style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                  fontFamily: 'BT Beau Sans',
                                                                                  color: const Color(0xFF7611C5),
                                                                                  fontSize: 16.0,
                                                                                  letterSpacing: 0.0,
                                                                                  fontWeight: FontWeight.w600,
                                                                                  useGoogleFonts: false,
                                                                                ),
                                                                          ),
                                                                        ),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                              ].divide(
                                                  const SizedBox(height: 20.0)
                                                  ),
                                            ),
                                          );
                                        }
                                      },
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      Container(
                        width: MediaQuery.sizeOf(context).width * 1.0,
                        height: 70.0,
                        decoration: const BoxDecoration(
                          color: Colors.white,
                        ),
                        child: Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              20.0, 0.0, 20.0, 0.0),
                          child: Row(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Align(
                                alignment: const AlignmentDirectional(0.0, 0.0),
                                child: FlutterFlowIconButton(
                                  borderColor: Colors.transparent,
                                  borderRadius: 50.0,
                                  buttonSize: 60.0,
                                  fillColor: Colors.white,
                                  icon: const Icon(
                                    FFIcons.kcards3,
                                    color: Color(0xFFD8DBE0),
                                    size: 39.0,
                                  ),
                                  onPressed: () {
                                    print('IconButton pressed ...');
                                  },
                                ),
                              ),
                              FlutterFlowIconButton(
                                borderColor: Colors.transparent,
                                borderRadius: 100.0,
                                buttonSize: 60.0,
                                fillColor: Colors.white,
                                icon: const Icon(
                                  FFIcons.ksolarSystem,
                                  color: Color(0xFFD8DBE0),
                                  size: 33.0,
                                ),
                                onPressed: () {
                                  print('IconButton pressed ...');
                                },
                              ),
                              FlutterFlowIconButton(
                                borderColor: Colors.transparent,
                                borderRadius: 100.0,
                                buttonSize: 60.0,
                                fillColor: Colors.white,
                                icon: const Icon(
                                  FFIcons.ksparkles1,
                                  color: Color(0xFFD8DBE0),
                                  size: 32.0,
                                ),
                                onPressed: () {
                                  print('IconButton pressed ...');
                                },
                              ),
                              FlutterFlowIconButton(
                                borderColor: Colors.transparent,
                                borderRadius: 100.0,
                                buttonSize: 60.0,
                                fillColor: Colors.white,
                                icon: const Icon(
                                  FFIcons.kchat,
                                  color: Color(0xFFD8DBE0),
                                  size: 30.0,
                                ),
                                onPressed: () {
                                  print('IconButton pressed ...');
                                },
                              ),
                              FlutterFlowIconButton(
                                borderColor: Colors.transparent,
                                borderRadius: 100.0,
                                buttonSize: 60.0,
                                fillColor: Colors.white,
                                icon: const Icon(
                                  FFIcons.kprofileFilled,
                                  color: Color(0xFF747E90),
                                  size: 32.0,
                                ),
                                onPressed: () {
                                  print('IconButton pressed ...');
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
      GestureDetector(
        onTap: () {
          
        },
        child: Stack(
          children: [
            Positioned.fill(
              child: ClipPath(
                clipper: MultiCutoutClipper(_cutoutRects), // Multiple cutouts
                child: Container(
                  color: Colors.black.withOpacity(0.8),
                ),
              ),
            ),
            Positioned(
          top: MediaQuery.of(context).size.width > 600 ? imageAnimation1Start + 25 : imageAnimation1Start,
          left: 0,
          child: IntrinsicHeight(
            child: SizedBox(
              width:  imageRightEnd,
              child: RiveAnimation.asset(
                    'assets/rive_animations/fs_tutorial/4-1.riv',
                    fit: BoxFit.fitWidth,
                    onInit: _onInit1,
                  ),
            ),
          ),
        ),
            Positioned(
          left: imageX + 5,
          right: imageX - 15,
          top:  imageThirdEnd,
          child: IntrinsicHeight(
                child: SizedBox(
                  width: imageSidePosition,
                  child: RiveAnimation.asset(
                        'assets/rive_animations/fs_tutorial/5-2.riv',
                        fit: BoxFit.fitWidth,
                        onInit: _onInit2,
                      ),
                ),
              ),
        ),
            Positioned(
                top: imageTextTopPosition + 50,
                right: imageX - 44,
                child: SizedBox(
                  width: 25,
                  height: 80,
                  child: RiveAnimation.asset(
                        'assets/rive_animations/fs_tutorial/5-4.riv',
                        fit: BoxFit.fill,
                        onInit: _onInit3,
                      ),
                ),
              ),
            Positioned(
          top: buttonTextTopPosition + 60,
          right: MediaQuery.of(context).size.width > 600 ? (MediaQuery.of(context).size.width)/6 : 0,
          bottom: 0,
          child: ClipRRect(
            child: SizedBox(
              height: MediaQuery.of(context).size.height - buttonTextTopPosition - 60,
              width: MediaQuery.of(context).size.width * 0.8,
              child: RiveAnimation.asset(
                    'assets/rive_animations/fs_tutorial/4-4.riv',
                    fit: BoxFit.fitWidth,
                    alignment: Alignment.center,
                    onInit: _onInit4,
                  ),
            ),
          ),
        ),
            Positioned(
            top: imageTextTopPosition, // Set the vertical position dynamically
            left: 0, // Aligns with the horizontal center
            right: 0, // Aligns with the horizontal center
            child: Align(
              alignment: Alignment.topCenter, // Ensures horizontal centering
              child: Padding(
                padding: const EdgeInsets.fromLTRB(20, 10, 20, 0),
                child: RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: getRemoteConfigString('tutorial_profilehome_t1'),
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              color:
                                  FlutterFlowTheme.of(context).primaryBackground,
                              fontSize: 15.0,
                              letterSpacing: 0.0,
                              fontWeight: FontWeight.w500,
                              useGoogleFonts: false,
                            ),
                      ),
                    ],
                  ),
                  textAlign: TextAlign.center, // Ensures text is center-aligned
                ),
              ),
            )
            ),
            Positioned(
            top: buttonTextTopPosition,
            left: 0,
            right: 0,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(40, 10, 40, 0),
              child: AnimatedOpacity(
                opacity: _currentCutoutStep >= 1 ? 1 : 0,
                duration: const Duration(milliseconds: 300),
                child: RichText(
                  text: TextSpan(children: [
                    TextSpan(
                      text: getRemoteConfigString('tutorial_profilehome_t2'),                          
                      style: FlutterFlowTheme.of(context).bodyMedium.override(
                            color: FlutterFlowTheme.of(context).primaryBackground,
                            fontSize: 15.0,
                            letterSpacing: 0.0,
                            fontWeight: FontWeight.w500,
                            useGoogleFonts: false,
                          ),
                    ),
                  ]),
                  textAlign: TextAlign.center,
                ),
              ),
            )),
            SafeArea(
          child: Align(
                      alignment: Alignment.bottomLeft,
                      child: Padding(
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(0.0, 50.0, 30.0, 0.0),
                        child: FFButtonWidget(
                          onPressed: () {
                            logSkippedIntro(runtimeType.toString());
                            GoRouter.of(context).goNamed('Discovery');
                          },
                          text: 'Skip',
                          options: FFButtonOptions(
                            height: 40.0,
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                16.0, 0.0, 16.0, 0.0),
                            iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 0.0, 0.0, 0.0),
                            color: const Color(0x004B39EF),
                            textStyle:
                                FlutterFlowTheme.of(context).titleSmall.override(
                                      fontFamily: 'BT Beau Sans',
                                      color: Colors.white,
                                      fontSize: 10.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.normal,
                                      useGoogleFonts: false,
                                    ),
                            elevation: 0.0,
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                        ),
                      ),
                    ),
        ),
        
          ],
        ),
      ),
      
      
      
      
    ]);
  }
}

class MultiCutoutClipper extends CustomClipper<Path> {
  final List<RRect> cutoutRects; // List of rounded rectangles

  MultiCutoutClipper(this.cutoutRects);

  @override
  Path getClip(Size size) {
    Path path = Path();

    // Full screen rectangle
    path.addRect(Rect.fromLTWH(0, 0, size.width, size.height));

    // Subtract each cutout rectangle from the path
    for (final cutout in cutoutRects) {
      path = Path.combine(
        PathOperation.difference,
        path,
        Path()..addRRect(cutout),
      );
    }

    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return true; // Reclip if the cutouts change
  }
}
