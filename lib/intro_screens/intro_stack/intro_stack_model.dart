import '/flutter_flow/flutter_flow_util.dart';
import '/intro/intro_start_new/intro_start_new_widget.dart';
import '/intro_screens/intro1/intro1_widget.dart';
import '/intro_screens/intro2/intro2_widget.dart';
import '/intro_screens/intro3/intro3_widget.dart';
import '/intro_screens/intro4/intro4_widget.dart';
import '/intro_screens/intro5/intro5_widget.dart';
import 'intro_stack_widget.dart' show IntroStackWidget;
import 'package:flutter/material.dart';
import 'package:flutter_card_swiper/flutter_card_swiper.dart';

class IntroStackModel extends FlutterFlowModel<IntroStackWidget> {
  ///  Local state fields for this component.
  /// 
  
  late IntroStartNewModel introStartNewModel;


  bool introStartDone = false;

  int swipeableStackCounter = 0;

  ///  State fields for stateful widgets in this component.

  // State field(s) for SwipeableStack widget.
  late CardSwiperController swipeableStackController;
  // Model for Intro1 component.
  late Intro1Model intro1Model;
  // Model for Intro2 component.
  late Intro2Model intro2Model;
  // Model for Intro3 component.
  late Intro3Model intro3Model;
  // Model for Intro4 component.
  late Intro4Model intro4Model;
  // Model for Intro5 component.
  late Intro5Model intro5Model;

  /// Initialization and disposal methods.

  @override
  void initState(BuildContext context) {
    introStartNewModel = createModel(context, () => IntroStartNewModel());
    swipeableStackController = CardSwiperController();
    intro1Model = createModel(context, () => Intro1Model());
    intro2Model = createModel(context, () => Intro2Model());
    intro3Model = createModel(context, () => Intro3Model());
    intro4Model = createModel(context, () => Intro4Model());
    intro5Model = createModel(context, () => Intro5Model());
  }

  @override
  void dispose() {
    introStartNewModel.dispose();
    intro1Model.dispose();
    intro2Model.dispose();
    intro3Model.dispose();
    intro4Model.dispose();
    intro5Model.dispose();
  }

  /// Action blocks are added here.

  /// Additional helper methods are added here.
}
