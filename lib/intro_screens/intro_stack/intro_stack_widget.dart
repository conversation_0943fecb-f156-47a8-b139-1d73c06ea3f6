import '/auth/firebase_auth/auth_util.dart';
import '/backend/schema/enums/enums.dart';
import '/discovery/popup/matching_info_popup_l_a/matching_info_popup_l_a_widget.dart';
import '/flutter_flow/flutter_flow_swipeable_stack.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/intro_screens/intro1/intro1_widget.dart';
import '/intro_screens/intro2/intro2_widget.dart';
import '/intro_screens/intro3/intro3_widget.dart';
import '/intro_screens/intro4/intro4_widget.dart';
import '/intro_screens/intro5/intro5_widget.dart';
import 'package:flutter/material.dart';
import '/intro/intro_start_new/intro_start_new_widget.dart';
import 'package:provider/provider.dart';
import 'intro_stack_model.dart';
export 'intro_stack_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';

class IntroStackWidget extends StatefulWidget {
  const IntroStackWidget({
    super.key,
    this.introStartDone,
  });

  final bool? introStartDone;

  @override
  State<IntroStackWidget> createState() => _IntroStackWidgetState();
}

class _IntroStackWidgetState extends State<IntroStackWidget> {
  late IntroStackModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => IntroStackModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return Builder(
      builder: (context) {
        if ((currentUserDocument?.gender == Gender.Female) &&
            getRemoteConfigBool('discovery_show_women_new_intro')) {
          return wrapWithModel(
            model: _model.introStartNewModel,
            updateCallback: () => safeSetState(() {}),
            child: const IntroStartNewWidget(),
          );
        }
        else {
        return Stack(
          children: [
            FlutterFlowSwipeableStack(
              onSwipeFn: (index) async {
                setState(() {
                  _model.swipeableStackCounter = _model.swipeableStackCounter + 1;
                });
                if (_model.swipeableStackCounter >= 5) {
                  setState(() {
                    FFAppState().introCompleted = true;
                  });
                }
              },
              onLeftSwipe: (index) {},
              onRightSwipe: (index) {},
              onUpSwipe: (index) {},
              onDownSwipe: (index) {},
              itemBuilder: (context, index) {
                return [
                  () => Builder(
                        builder: (context) => wrapWithModel(
                          model: _model.intro1Model,
                          updateCallback: () => setState(() {}),
                          child: Intro1Widget(
                            action: () async {
                              analytics.logEvent('Started Introduction');
                              _model.swipeableStackController.swipeLeft();
                            },
                            skipAction: () async {
                              FFAppState().update(() {
                                FFAppState().introCompleted = true;
                                analytics.logEvent('Completed Introduction', eventProperties: {'Skipped Introduction': true});
                              });
                              FFAppState().update(() {});
                              if (currentUserDocument?.gender == Gender.Male) {
                                await showDialog(
                                  barrierDismissible: false,
                                  context: context,
                                  builder: (dialogContext) {
                                    return Dialog(
                                      elevation: 0,
                                      insetPadding: EdgeInsets.zero,
                                      backgroundColor: Colors.transparent,
                                      alignment: const AlignmentDirectional(0.0, 0.0)
                                          .resolve(Directionality.of(context)),
                                      child: const MatchingInfoPopupLAWidget(),
                                    );
                                  },
                                );
                              }
                            },
                          ),
                        ),
                      ),
                  () => Builder(
                        builder: (context) => wrapWithModel(
                          model: _model.intro2Model,
                          updateCallback: () => safeSetState(() {}),
                          child: Intro2Widget(
                            action: () async {
                              _model.swipeableStackController.swipeRight();
                            },
                            skipAction: () async {
                              FFAppState().introCompleted = true;
                              FFAppState().update(() {});
                              if (currentUserDocument?.gender == Gender.Male) {
                                await showDialog(
                                  barrierDismissible: false,
                                  context: context,
                                  builder: (dialogContext) {
                                    return Dialog(
                                      elevation: 0,
                                      insetPadding: EdgeInsets.zero,
                                      backgroundColor: Colors.transparent,
                                      alignment: const AlignmentDirectional(0.0, 0.0)
                                          .resolve(Directionality.of(context)),
                                      child: const MatchingInfoPopupLAWidget(),
                                    );
                                  },
                                );
                              }
                            },
                          ),
                        ),
                      ),
                  () => Builder(
                        builder: (context) => wrapWithModel(
                          model: _model.intro3Model,
                          updateCallback: () => safeSetState(() {}),
                          child: Intro3Widget(
                            action: () async {},
                            skipAction: () async {
                              FFAppState().introCompleted = true;
                              FFAppState().update(() {});
                              if (currentUserDocument?.gender == Gender.Male) {
                                await showDialog(
                                  barrierDismissible: false,
                                  context: context,
                                  builder: (dialogContext) {
                                    return Dialog(
                                      elevation: 0,
                                      insetPadding: EdgeInsets.zero,
                                      backgroundColor: Colors.transparent,
                                      alignment: const AlignmentDirectional(0.0, 0.0)
                                          .resolve(Directionality.of(context)),
                                      child: const MatchingInfoPopupLAWidget(),
                                    );
                                  },
                                );
                              }
                            },
                          ),
                        ),
                      ),
                  () => Builder(
                        builder: (context) => wrapWithModel(
                          model: _model.intro4Model,
                          updateCallback: () => safeSetState(() {}),
                          child: Intro4Widget(
                            action: () async {},
                            skipAction: () async {
                              FFAppState().introCompleted = true;
                              FFAppState().update(() {});
                              if (currentUserDocument?.gender == Gender.Male) {
                                await showDialog(
                                  barrierDismissible: false,
                                  context: context,
                                  builder: (dialogContext) {
                                    return Dialog(
                                      elevation: 0,
                                      insetPadding: EdgeInsets.zero,
                                      backgroundColor: Colors.transparent,
                                      alignment: const AlignmentDirectional(0.0, 0.0)
                                          .resolve(Directionality.of(context)),
                                      child: const MatchingInfoPopupLAWidget(),
                                    );
                                  },
                                );
                              }
                            },
                          ),
                        ),
                      ),
                  () => Builder(
                        builder: (context) => wrapWithModel(
                          model: _model.intro5Model,
                          updateCallback: () => safeSetState(() {}),
                          child: Intro5Widget(
                            action: () async {
                              FFAppState().introCompleted = true;
                              FFAppState().update(() {});
                              if (currentUserDocument?.gender == Gender.Male) {
                                await showDialog(
                                  barrierDismissible: false,
                                  context: context,
                                  builder: (dialogContext) {
                                    return Dialog(
                                      elevation: 0,
                                      insetPadding: EdgeInsets.zero,
                                      backgroundColor: Colors.transparent,
                                      alignment: const AlignmentDirectional(0.0, 0.0)
                                          .resolve(Directionality.of(context)),
                                      child: const MatchingInfoPopupLAWidget(),
                                    );
                                  },
                                ).then((value) => setState(() {}));
                              }
                            },
                          ),
                        ),
                      ),
                  () => Builder(
                        builder: (context) => wrapWithModel(
                          model: _model.intro2Model,
                          updateCallback: () => setState(() {}),
                          child: Intro2Widget(
                            action: () async {
                              _model.swipeableStackController.swipeRight();
                            },
                            skipAction: () async {
                              FFAppState().update(() {
                                FFAppState().introCompleted = true;
                                analytics.logEvent('Completed Introduction', eventProperties: {'Skipped Introduction': true});
                              });
                              if (currentUserDocument?.gender == Gender.Male) {
                                await showDialog(
                                  barrierDismissible: false,
                                  context: context,
                                  builder: (dialogContext) {
                                    return Dialog(
                                      elevation: 0,
                                      insetPadding: EdgeInsets.zero,
                                      backgroundColor: Colors.transparent,
                                      alignment: const AlignmentDirectional(0.0, 0.0)
                                          .resolve(Directionality.of(context)),
                                      child: const MatchingInfoPopupLAWidget(),
                                    );
                                  },
                                ).then((value) => setState(() {}));
                              }
                            },
                          ),
                        ),
                      ),
                  () => Builder(
                        builder: (context) => wrapWithModel(
                          model: _model.intro3Model,
                          updateCallback: () => setState(() {}),
                          child: Intro3Widget(
                            action: () async {},
                            skipAction: () async {
                              FFAppState().update(() {
                                FFAppState().introCompleted = true;
                                analytics.logEvent('Completed Introduction', eventProperties: {'Skipped Introduction': true});
                              });
                              if (currentUserDocument?.gender == Gender.Male) {
                                await showDialog(
                                  barrierDismissible: false,
                                  context: context,
                                  builder: (dialogContext) {
                                    return Dialog(
                                      elevation: 0,
                                      insetPadding: EdgeInsets.zero,
                                      backgroundColor: Colors.transparent,
                                      alignment: const AlignmentDirectional(0.0, 0.0)
                                          .resolve(Directionality.of(context)),
                                      child: const MatchingInfoPopupLAWidget(),
                                    );
                                  },
                                ).then((value) => setState(() {}));
                              }
                            },
                          ),
                        ),
                      ),
                  () => Builder(
                        builder: (context) => wrapWithModel(
                          model: _model.intro4Model,
                          updateCallback: () => setState(() {}),
                          child: Intro4Widget(
                            action: () async {},
                            skipAction: () async {
                              FFAppState().update(() {
                                FFAppState().introCompleted = true;
                                analytics.logEvent('Completed Introduction', eventProperties: {'Skipped Introduction': true});
                              });
                              if (currentUserDocument?.gender == Gender.Male) {
                                await showDialog(
                                  barrierDismissible: false,
                                  context: context,
                                  builder: (dialogContext) {
                                    return Dialog(
                                      elevation: 0,
                                      insetPadding: EdgeInsets.zero,
                                      backgroundColor: Colors.transparent,
                                      alignment: const AlignmentDirectional(0.0, 0.0)
                                          .resolve(Directionality.of(context)),
                                      child: const MatchingInfoPopupLAWidget(),
                                    );
                                  },
                                ).then((value) => setState(() {}));
                              }
                            },
                          ),
                        ),
                      ),
                  () => Builder(
                        builder: (context) => wrapWithModel(
                          model: _model.intro5Model,
                          updateCallback: () => setState(() {}),
                          child: Intro5Widget(
                            action: () async {
                              FFAppState().update(() {
                                FFAppState().introCompleted = true;
                                analytics.logEvent('Completed Introduction', eventProperties: {'Skipped Introduction': false});
                              });
                              if (currentUserDocument?.gender == Gender.Male) {
                                await showDialog(
                                  barrierDismissible: false,
                                  context: context,
                                  builder: (dialogContext) {
                                    return Dialog(
                                      elevation: 0,
                                      insetPadding: EdgeInsets.zero,
                                      backgroundColor: Colors.transparent,
                                      alignment: const AlignmentDirectional(0.0, 0.0)
                                          .resolve(Directionality.of(context)),
                                      child: const MatchingInfoPopupLAWidget(),
                                    );
                                  },
                                ).then((value) => setState(() {}));
                              }
                            },
                          ),
                        ),
                      ),
                ][index]();
              },
              itemCount: 5,
              controller: _model.swipeableStackController,
              loop: false,
              cardDisplayCount: 3,
              scale: 0.9,
            ),
          ],
        );
        }
      }
    );
  }
}
