import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/skip_button/skip_button_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'intro_start_model.dart';
export 'intro_start_model.dart';

class IntroStartWidget extends StatefulWidget {
  const IntroStartWidget({
    super.key,
    required this.action,
    required this.skipAction,
  });

  final Future Function()? action;
  final Future Function()? skipAction;

  @override
  State<IntroStartWidget> createState() => _IntroStartWidgetState();
}

class _IntroStartWidgetState extends State<IntroStartWidget> {
  late IntroStartModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => IntroStartModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return Align(
      alignment: const AlignmentDirectional(0.0, 0.0),
      child: Padding(
        padding: const EdgeInsetsDirectional.fromSTEB(20.0, 0.0, 20.0, 0.0),
        child: Container(
          width: MediaQuery.sizeOf(context).width * 11.0,
          height: MediaQuery.sizeOf(context).height * 0.964,
          decoration: BoxDecoration(
            color: Colors.black,
            borderRadius: BorderRadius.circular(10.75),
          ),
          alignment: const AlignmentDirectional(0.0, 0.0),
          child: Align(
            alignment: const AlignmentDirectional(0.0, 0.0),
            child: Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(40.0, 0.0, 40.0, 0.0),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8.0),
                    child: SvgPicture.asset(
                      'assets/images/WhiteCirclesOnly.svg',
                      width: 44.0,
                      height: 44.0,
                      fit: BoxFit.contain,
                    ),
                  ),
                  Align(
                    alignment: const AlignmentDirectional(0.0, 0.0),
                    child: Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(0.0, 34.0, 0.0, 0.0),
                      child: Text(
                        'Let\'s get you started',
                        textAlign: TextAlign.center,
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'BT Beau Sans',
                              color: FlutterFlowTheme.of(context).info,
                              fontSize: 28.0,
                              fontWeight: FontWeight.w500,
                              useGoogleFonts: false,
                            ),
                      ),
                    ),
                  ),
                  Padding(
                    padding:
                        const EdgeInsetsDirectional.fromSTEB(0.0, 34.0, 0.0, 0.0),
                    child: Text(
                      'Some things you should know',
                      style: FlutterFlowTheme.of(context).bodyMedium.override(
                            fontFamily: 'BT Beau Sans',
                            color: FlutterFlowTheme.of(context).info,
                            fontSize: 18.0,
                            fontWeight: FontWeight.normal,
                            useGoogleFonts: false,
                          ),
                    ),
                  ),
                  Padding(
                    padding:
                        const EdgeInsetsDirectional.fromSTEB(0.0, 29.0, 0.0, 0.0),
                    child: wrapWithModel(
                      model: _model.gradientButtonModel,
                      updateCallback: () => setState(() {}),
                      updateOnChange: true,
                      child: GradientButtonWidget(
                        title: 'START TUTORIAL',
                        action: () async {
                          await widget.action?.call();
                        },
                      ),
                    ),
                  ),
                  Padding(
                    padding:
                        const EdgeInsetsDirectional.fromSTEB(0.0, 34.0, 0.0, 0.0),
                    child: InkWell(
                      splashColor: Colors.transparent,
                      focusColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      onTap: () async {
                        FFAppState().update(() {
                          FFAppState().introCompleted = true;
                        });
                      },
                      child: wrapWithModel(
                        model: _model.skipButtonModel,
                        updateCallback: () => setState(() {}),
                        child: SkipButtonWidget(
                          action: () async {
                            await widget.skipAction?.call();
                          },
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
