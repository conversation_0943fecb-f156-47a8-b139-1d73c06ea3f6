import 'package:chyrpe/general/gradient_button/gradient_button_widget.dart';
import 'package:flutter/material.dart';

import '../../general/gradient_button_disabled/gradient_button_disabled_widget.dart';

class WelcomeRulesNextButton extends StatelessWidget {
  const WelcomeRulesNextButton({
    super.key,
    required this.checkState,
    required this.label,
    this.action,
  });
  final List<bool> checkState;
  final String label;
   final Future Function()? action;

  @override
  Widget build(BuildContext context) {
    return checkState.any((v) => v == false)
        ? GradientButtonDisabledWidget(
            title: label,
            action: () async {},
          )
        : GradientButtonWidget(
            title: label,
            action: action,
          );
  }
}
