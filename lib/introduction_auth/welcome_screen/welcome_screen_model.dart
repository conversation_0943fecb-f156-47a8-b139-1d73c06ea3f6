import 'package:carousel_slider/carousel_controller.dart';
import 'package:chyrpe/introduction_auth/welcome_screen/welcome_screen_widget.dart';

import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button_inverse/gradient_button_inverse_widget.dart';
import 'package:flutter/material.dart';

class WelcomeScreenModel extends FlutterFlowModel<WelcomeScreenWidget> {
  ///  State fields for stateful widgets in this page.

  final unfocusNode = FocusNode();

  /// Initializes CarouselController and other state variables.
  CarouselController? carouselController;

  @override
  void initState(BuildContext context) {
    carouselController = CarouselController();
  }

  @override
  void dispose() {
    unfocusNode.dispose();
  }

  /// Action blocks are added here.

  /// Additional helper methods are added here.
}
