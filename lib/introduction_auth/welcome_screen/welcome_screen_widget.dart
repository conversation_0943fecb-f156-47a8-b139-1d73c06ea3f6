import 'package:carousel_slider/carousel_slider.dart';
import 'package:chyrpe/auth/firebase_auth/auth_util.dart';
import 'package:chyrpe/backend/backend.dart';
import 'package:chyrpe/flutter_flow/flutter_flow_model.dart';
import 'package:chyrpe/flutter_flow/flutter_flow_theme.dart';
import 'package:chyrpe/flutter_flow/flutter_flow_util.dart';
import 'package:chyrpe/flutter_flow/flutter_flow_widgets.dart';
import 'package:chyrpe/introduction_auth/welcome_screen/welcome_screen_model.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'dart:math' as math;

import 'package:url_launcher/url_launcher.dart';

class WelcomeScreenWidget extends StatefulWidget {
  const WelcomeScreenWidget({super.key});

  @override
  State<WelcomeScreenWidget> createState() => _WelcomeScreenWidgetState();
}

class _WelcomeScreenWidgetState extends State<WelcomeScreenWidget> {
  late WelcomeScreenModel _model;
  int _current = 0;
  double carouselPosition = 0.0;
  bool _isLoadingApple = false;
  bool _isLoadingGoogle = false;
  bool _isLoadingPhone = false;

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => WelcomeScreenModel());
  }

  @override
  Widget build(BuildContext context) {
    final controller = _model.carouselController ?? CarouselController();
    List<CardIntro> listOfIntro =
        CardIntro.fromJsonList(getRemoteConfigString('introduction_cards'));
    final shouldShowAppleButton = (isiOS && getRemoteConfigBool('welcome_screen_show_apple') && !isAndroid);

    return Scaffold(
      body: Stack(
        children: [
          ShaderMask(
            shaderCallback: (bounds) {
              return const LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.white,
                  Colors.transparent,
                ],
              ).createShader(Rect.fromLTWH(0, 0, bounds.width, bounds.height));
            },
            blendMode: BlendMode.dstIn,
            child: Container(
              height: MediaQuery.of(context).size.height / 3,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [Color(0xFF67B0E5), Color(0xFFF49BD1)],
                  begin: AlignmentDirectional(1, 0),
                  end: AlignmentDirectional(-1, 0),
                ),
              ),
            ),
          ),
          LayoutBuilder(
            builder: (context, constraints) {
              return Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  SizedBox(height: constraints.maxHeight * 0.05),
                  CarouselSlider(
                    carouselController: controller,
                    items: listOfIntro.map((item) {
                      return AnimatedAlign(
                        duration: const Duration(milliseconds: 300),
                        alignment: Alignment(
                          0,
                          _calculateAlignmentY(carouselPosition % 4,
                              listOfIntro.indexOf(item), listOfIntro.length),
                        ),
                        child: CarousselItemIntro(
                          item.imagePath,
                          constraints.maxHeight,
                        ),
                      );
                    }).toList(),
                    options: CarouselOptions(
                      autoPlay: true,
                      autoPlayInterval: const Duration(seconds: 5),
                      autoPlayAnimationDuration: const Duration(milliseconds: 1500),
                      height: constraints.maxHeight * 0.35,
                      initialPage: 0,
                      enableInfiniteScroll: true,
                      viewportFraction: 0.5,
                      onPageChanged: (index, reason) {
                        setState(() {
                          _current = index;
                        });
                      },
                      onScrolled: (index) {
                        setState(() {
                          carouselPosition = (index ?? 10000) - 10000;
                        });
                      },
                    ),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    listOfIntro[_current].title,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.w500,
                      fontFamily: 'Lora',
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 24.0),
                    child: Text(
                      listOfIntro[_current].description,
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontSize: 14,
                        fontFamily: 'BT Beau Sans',
                        fontWeight: FontWeight.w300,
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: listOfIntro.asMap().entries.map((e) {
                        return GestureDetector(
                          onTap: () => controller.animateToPage(e.key),
                          child: Container(
                            width: 5.0,
                            height: 5.0,
                            margin: EdgeInsets.symmetric(
                                vertical: 8.0, horizontal: 4.0),
                            decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: (Theme.of(context).brightness ==
                                            Brightness.dark
                                        ? Colors.white
                                        : Colors.black)
                                    .withOpacity(_current == e.key ? 0.9 : 0.4)),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                  SizedBox(height: 8),
                  Flexible(child: Container()),
                  if (shouldShowAppleButton)
                    Container(
                    margin: const EdgeInsets.fromLTRB(17.0, 0, 17, 10),
                    width: double.infinity,
                    height: 49,
                    child: FFButtonWidget(
                
                      text:
                        getRemoteConfigString('welcome_screen2_apple_button_title'),
                  
                      onPressed: () async => await appleButtonFunction(),
                     options: FFButtonOptions(
                            height: 40.0,
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                15.0, 0.0, 15.0, 0.0),
                            iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 0.0, 0.0, 0.0),
                            color: Color(0xFFB99CE4),
                            textStyle: FlutterFlowTheme.of(context)
                                .titleSmall
                                .override(
                                  fontFamily: 'BT Beau Sans',
                                  color:
                                      FlutterFlowTheme.of(context).info,
                                  fontSize: 13.0,
                                  letterSpacing: 0.0,
                                  fontWeight: FontWeight.w500,
                                  useGoogleFonts: false,
                                ),
                            elevation: 0.0,
                            borderSide: const BorderSide(
                              color: Color(0x0057636C),
                              width: 0.0,
                            ),
                            borderRadius: BorderRadius.circular(100.0),
                          
                      ),
                    ),
                  ),
                if (getRemoteConfigBool('welcome_screen_show_google'))
                    Container(
                      margin: const EdgeInsets.fromLTRB(17.0, 0, 17, 10),
                      width: double.infinity,
                      height: 49,
                      child: FFButtonWidget(
text: getRemoteConfigString('welcome_screen2_google_title'),
                         
                        onPressed: () async => await googleButtonFunction(),
                        options: FFButtonOptions(
                            height: 40.0,
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                15.0, 0.0, 15.0, 0.0),
                            iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 0.0, 0.0, 0.0),
                            color: shouldShowAppleButton ? Colors.white : Color(0xFFB99CE4),
                            textStyle: FlutterFlowTheme.of(context)
                                .titleSmall
                                .override(
                                  fontFamily: 'BT Beau Sans',
                                  color:
                                      shouldShowAppleButton ? FlutterFlowTheme.of(context).secondaryText : Colors.white,
                                  fontSize: 13.0,
                                  letterSpacing: 0.0,
                                  fontWeight: FontWeight.w500,
                                  useGoogleFonts: false,
                                ),
                            elevation: 0.0,
                            borderSide: const BorderSide(
                              color: Color(0x0057636C),
                              width: 0.0,
                            ),
                            borderRadius: BorderRadius.circular(100.0),
                          
                      ),
                      ),
                    ),
                  if (isAndroid
                      ? getRemoteConfigBool(
                          'welcome_screen_show_phone_login_android')
                      : getRemoteConfigBool('welcome_screen_show_phone_button'))
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 17.0),
                      width: double.infinity,
                      height: 49,
                      child: FFButtonWidget(
                       text: getRemoteConfigString('welcome_screen2_phone_button_title'),
                        onPressed: () async => await phoneButtonFunction(),
                        options: FFButtonOptions(
                            height: 40.0,
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                15.0, 0.0, 15.0, 0.0),
                            iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 0.0, 0.0, 0.0),
                            color: Colors.white,
                            textStyle: FlutterFlowTheme.of(context)
                                .titleSmall
                                .override(
                                  fontFamily: 'BT Beau Sans',
                                  color:
                                      FlutterFlowTheme.of(context).secondaryText,
                                  fontSize: 13.0,
                                  letterSpacing: 0.0,
                                  fontWeight: FontWeight.w500,
                                  useGoogleFonts: false,
                                ),
                            elevation: 0.0,
                            borderSide: const BorderSide(
                              color: Color(0x0057636C),
                              width: 0.0,
                            ),
                            borderRadius: BorderRadius.circular(100.0),
                          
                      ),
                      ),
                    ),
                  Expanded(child: Container()),
                  Padding(
                    padding: const EdgeInsets.fromLTRB(0,8.0,0,0),
                    child: Center(
                      child: RichText(
                        textAlign: TextAlign.center,
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: 'By tapping “Continue”, you agree to\n',
                              style: TextStyle(
                                fontFamily: 'BT Beau Sans',
                                fontSize: 10,
                                fontWeight: FontWeight.w400,
                                color: Colors.black,
                              ),
                            ),
                            TextSpan(
                              text: 'Our Terms & Privacy Policy',
                              style: TextStyle(
                                color: Colors.black,
                                decoration: TextDecoration.underline,
                                fontFamily: 'BT Beau Sans',
                                fontSize: 10,
                                fontWeight: FontWeight.w400,
                              ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () async {
                                  await launchURL(
                                      'https://www.chyrpe.com/legal/overview');
                                },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 40),
                ],
              );
            }
          ),
        ],
      ),
    );
  }

  double _calculateAlignmentY(double position, int index, int totalItems) {
    final diff = (position - index).abs();
    final wrappedDiff = (totalItems - diff).abs();
    final minDistance = math.min(diff, wrappedDiff);
    return 1 - minDistance.clamp(0, 1);
  }

  Widget CarousselItemIntro(String imagePath, double maxHeight) {
    return Image.network(
      imagePath,
      width: maxHeight > 800 ? 153 : 100,
      height: maxHeight > 800 ? 211 : 137,
      fit: BoxFit.cover,
    );
  }

  Future<void> appleButtonFunction() async {

    setState(() {
      _isLoadingApple = true;
    });

    GoRouter.of(context).prepareAuthEvent();
    final user = await authManager.signInWithApple(context);
    if (user == null) {
      return;
    }

    if (!(valueOrDefault(currentUserDocument?.refCode, '') != '')) {
      await currentUserReference!.update(createUsersRecordData(
        refCode: FFAppState().deepLinkReferrer,
      ));
    }

    setState(() {
      _isLoadingApple = false;
    });

    context.goNamedAuth('Discovery', context.mounted);
  }

  Future<void> phoneButtonFunction() async {

    setState(() {
      _isLoadingPhone = true;
    });

    context.pushNamed('PhoneNumber');

    setState(() {
      _isLoadingPhone = false;
    });


  }

  Future<void> googleButtonFunction() async {

    setState(() {
      _isLoadingGoogle = true;
    });

    GoRouter.of(context).prepareAuthEvent();
    final user = await authManager.signInWithGoogle(context);
    if (user == null) {
      return;
    }
    if (!(valueOrDefault(currentUserDocument?.refCode, '') != '')) {
      await currentUserReference!.update(createUsersRecordData(
        refCode: FFAppState().deepLinkReferrer,
      ));
    }

    setState(() {
      _isLoadingGoogle = false;
    });


    context.goNamedAuth('Discovery', context.mounted);
  }
}

class CardIntro {
  final String imagePath;
  final String title;
  final String description;

  CardIntro(this.imagePath, this.title, this.description);

  factory CardIntro.fromMap(Map<String, dynamic> map) {
    return CardIntro(
      map['image_url'] as String,
      map['title'] as String,
      map['description'] as String,
    );
  }

  static List<CardIntro> fromJsonList(String mapList) {
    return (jsonDecode(mapList) as List<dynamic>)
        .map((map) => CardIntro.fromMap(map as Map<String, dynamic>))
        .toList();
  }
}
