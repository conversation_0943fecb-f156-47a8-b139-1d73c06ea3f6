import 'package:equatable/equatable.dart';

class LanguageModel extends Equatable {
  final String? english;
  final String? french;
  final String? german;
  final String? spanish;

  const LanguageModel({
    this.english,
    this.french,
    this.german,
    this.spanish,
  });

  factory LanguageModel.fromJson(Map<String, dynamic> json) => LanguageModel(
        english: json['english'] as String?,
        french: json['french'] as String?,
        german: json['german'] as String?,
        spanish: json['spanish'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'english': english,
        'french': french,
        'german': german,
        'spanish': spanish,
      };

  @override
  bool get stringify => true;

  @override
  List<Object?> get props => [english, french, german, spanish];
}
