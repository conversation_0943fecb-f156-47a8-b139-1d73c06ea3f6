import 'dart:async';

import 'package:appsflyer_sdk/appsflyer_sdk.dart';
import 'package:blitzllama_flutter/blitzllama_flutter.dart';
import 'package:chyrpe/blitzllamaConfig.dart';
import 'package:provider/provider.dart';
import 'package:flutter/material.dart';

import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_web_plugins/url_strategy.dart';
import 'auth/firebase_auth/firebase_user_provider.dart';
import 'auth/firebase_auth/auth_util.dart';

import 'backend/firebase/firebase_config.dart';
import 'flutter_flow/flutter_flow_theme.dart';
import 'flutter_flow/flutter_flow_util.dart';
import 'flutter_flow/internationalization.dart';
import 'flutter_flow/firebase_app_check_util.dart';
import 'index.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'flutter_flow/revenue_cat_util.dart' as revenue_cat;
import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'appsFlyerConfig.dart';
import 'amplitudeConfig.dart';
import 'package:flutter_branch_sdk/flutter_branch_sdk.dart';

import 'package:likes/likes.dart';
import 'package:verification/verification.dart';
import 'package:hobbies/hobbies.dart';

import '/backend/backend.dart'; // Import your Firestore backend methods
// Import for currentUserReference
import '/discovery/discovery/utils/daily_like_limit.dart';


void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  GoRouter.optionURLReflectsImperativeAPIs = true;
  usePathUrlStrategy();
  await initFirebase();

  const String flavor = String.fromEnvironment('FLUTTER_APP_FLAVOR');

  final appState = FFAppState(); // Initialize FFAppState
  await appState.initializePersistedState();

  await revenue_cat.initialize(
    flavor == 'dev' ? "appl_ZFhOnGzBbCBeZnRhqdUSXDDWHHa" : "appl_FlblOfjUDfggpWBuzCwPEqdscdD",
    flavor == 'dev' ? "goog_DnGrsVVPTsoysQhLAIAxojJySwn" : "goog_qMlXtfajPuCFmXABgCClqzLpHtJ",
    loadDataAfterLaunch: false,
  );

  await initializeFirebaseRemoteConfig();
  await initializeFirebaseAppCheck();

  if (flavor == 'prod') {
    analytics.init('65dbac83dec4e06df231ca34a83e4757');
    analytics.trackingSessionEvents(true);
  }

  final attStatus = await AppTrackingTransparency.requestTrackingAuthorization();


   appsflyerSdk.onDeepLinking((DeepLinkResult dp) {
      switch (dp.status) {
        case Status.FOUND:
          var deepLink = dp.deepLink;
          if (deepLink?.deepLinkValue == "refer") {
              FFAppState().deepLinkReferrer = deepLink?.getStringValue('deep_link_sub1') ?? 'default';
          }

          break;
        case Status.NOT_FOUND:

          break;
        case Status.ERROR:

          break;
        case Status.PARSE_ERROR:

          break;
      }
    });

  await appsflyerSdk.initSdk(
    registerConversionDataCallback: true,
    registerOnAppOpenAttributionCallback: true,
    registerOnDeepLinkingCallback: true
);


try {
await FlutterBranchSdk.init(enableLogging: false, disableTracking: false);

StreamSubscription<Map> streamSubscription = FlutterBranchSdk.listSession().listen((data)  {
    print(data);
    if (data.containsKey("+clicked_branch_link") &&
        data["+clicked_branch_link"] == true) {
        //Link clicked. Add logic to get link data
        try {
        String deepLinkPath = data["\$deeplink_path"];
        List<String> splitDeepLinks = deepLinkPath.split('refer/');
        if (splitDeepLinks.isNotEmpty) {
          FFAppState().deepLinkReferrer = splitDeepLinks.last;
        } else {
          FFAppState().deepLinkReferrer = 'default';
        }
        } catch(error) {}
    }
  }, onError: (error) {
  });

  try {
  await LikeLimitConfigService.instance.initialize(currentLikeLimitsId: ((currentUserDocument?.hasCurrentLikeLimitsId() ?? false) && currentUserDocument?.currentLikeLimitsId != '') ? currentUserDocument?.currentLikeLimitsId ?? 'default' : 'default').timeout(Duration(seconds: 5));
  } catch(e) {}

} catch(error) {}


  runApp(ChangeNotifierProvider(
    create: (context) => appState,
    child: const MyApp(),
  ));
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  State<MyApp> createState() => _MyAppState();

  static _MyAppState of(BuildContext context) =>
      context.findAncestorStateOfType<_MyAppState>()!;
}

class _MyAppState extends State<MyApp> {
  Locale? _locale;
  ThemeMode _themeMode = ThemeMode.system;

  late Stream<BaseAuthUser> userStream;

  late AppStateNotifier _appStateNotifier;
  late GoRouter _router;

  final authUserSub = authenticatedUserStream.listen((user) {
    revenue_cat.login(user?.uid);
  });
  
  // final fcmTokenSub = fcmTokenUserStream.listen((_) {});

  @override
  void initState() {
    super.initState();

    initializeBlitzllama();
    _appStateNotifier = AppStateNotifier.instance;
    _router = createRouter(_appStateNotifier);
    userStream = chyrpeFirebaseUserStream()
      ..listen((user) => _appStateNotifier.update(user));
    jwtTokenStream.listen((_) {});
    Future.delayed(
      const Duration(milliseconds: 1000),
      () => _appStateNotifier.stopShowingSplashImage(),
    );
  }

  @override
  void dispose() {
    authUserSub.cancel();
    // fcmTokenSub.cancel();
    super.dispose();
  }

  void setLocale(String language) {
    setState(() => _locale = createLocale(language));
  }

  void setThemeMode(ThemeMode mode) => setState(() {
        _themeMode = mode;
      });

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'Chyrpe',
      localizationsDelegates: const [
        FFLocalizationsDelegate(),
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      locale: _locale,
      supportedLocales: const [
        Locale('en'),
      ],
      theme: ThemeData(
        brightness: Brightness.light,
        useMaterial3: false,
        extensions: [
        LikeThemeExtension.day,
        HobbyThemeExtension.day(),
        VerificationThemeExtension.day()
     ],
      ),
      themeMode: _themeMode,
      routerConfig: _router,
      builder: (_, child) => MediaQuery(
        data: MediaQuery.of(context).copyWith(
          textScaler: MediaQuery.of(context).textScaler.clamp(
                maxScaleFactor: 1.0,
              ),
        ),
        child: child!,
      ),
    );
  }
}

class NavBarPage extends StatefulWidget {
  const NavBarPage({super.key, this.initialPage, this.page});
  

  final String? initialPage;
  final Widget? page;

  @override
  _NavBarPageState createState() => _NavBarPageState();
}

/// This is the private State class that goes with NavBarPage.
class _NavBarPageState extends State<NavBarPage> {
  String _currentPageName = 'Discovery';
  late Widget? _currentPage;
  var _selectedPageIndex;
  late List<Widget> _pages;
  late PageController _pageController;

  final List<String> pageNames = [
  'Discovery',
  'MiscScreen',
  'EvolvedTab',
  'Chat',
  'ProfileHome',
];

  @override
  void initState() {
    super.initState();
  _currentPageName = widget.initialPage ?? _currentPageName;
  _currentPage = widget.page;

  // Set _selectedPageIndex based on _currentPageName
  _selectedPageIndex = pageNames.indexOf(_currentPageName);

  // Ensure _selectedPageIndex is valid
  if (_selectedPageIndex == -1) {
    _selectedPageIndex = 0; // Default to the first page if not found
  }

  _pages = [
    const DiscoveryWidget(),
    const MiscScreenWidget(),
    const EvolvedTabWidget(),
    const ChatWidget(),
    const ProfileHomeWidget(),
  ];

  _pageController = PageController(initialPage: _selectedPageIndex);
  }

  @override
  Widget build(BuildContext context) {
    final tabs = {
      'Discovery': const DiscoveryWidget(),
      'MiscScreen': const MiscScreenWidget(),
      'EvolvedTab': const EvolvedTabWidget(),
      'Chat': const ChatWidget(),
      'ProfileHome': const ProfileHomeWidget(),
    };
    final currentIndex = tabs.keys.toList().indexOf(_currentPageName);

    return Scaffold(
      body: PageView(
      controller: _pageController,
      physics: const NeverScrollableScrollPhysics(),
      children: _pages,
    ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedPageIndex,
        onTap: (i) => setState(() {
          if (i == 0) {
            FFAppState().navToP1 = true;
          }
          _selectedPageIndex = i;
          _currentPageName = pageNames[i];
          _pageController.animateToPage(
            i,
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
        );
          if (GoRouter.of(context).routerDelegate.currentConfiguration.matches.first.matchedLocation.toLowerCase().substring(1) != pageNames[i].toLowerCase()) {
            GoRouter.of(context).replaceNamed(pageNames[i]);
          }
        }),
        backgroundColor: valueOrDefault(currentUserDocument!.boostedUntil, DateTime(1970, 1, 1))  >
                                        getCurrentTimestamp ? const Color(0xFF923DCB) : FlutterFlowTheme.of(context).info,
        selectedItemColor: valueOrDefault(currentUserDocument!.boostedUntil, DateTime(1970, 1, 1))  >
                                        getCurrentTimestamp ? FlutterFlowTheme.of(context).info : const Color(0xFF747E90),
        unselectedItemColor: const Color(0xFFD8DBE0),
        showSelectedLabels: false,
        showUnselectedLabels: false,
        type: BottomNavigationBarType.fixed,
        items: <BottomNavigationBarItem>[
          const BottomNavigationBarItem(
            icon: Icon(
              FFIcons.kcards3,
              size: 39.0,
            ),
            label: '',
            tooltip: '',
          ),
          const BottomNavigationBarItem(
            icon: Icon(
              FFIcons.ksolarSystem,
              size: 33.0,
            ),
            label: 'Home',
            tooltip: '',
          ),
          BottomNavigationBarItem(
  icon: Stack(
    children: [
      const Icon(
        FFIcons.ksparkles1,
        size: 32.0,
      ),
      StreamBuilder<List<LikesRecord>>(
        stream: queryLikesRecord(
          queryBuilder: (likesRecord) => likesRecord
              .where('likedUser', isEqualTo: currentUserReference)
              .where('mutual', isEqualTo: false)
              .where('unmatched', isEqualTo: false)
              .limit(1),
        ),
        builder: (context, snapshot) {
          bool showBadge = snapshot.hasData && snapshot.data!.isNotEmpty;

          return showBadge
              ? Positioned(
                  top: 0,
                  right: 0,
                  child: Container(
                    width: 8.0,
                    height: 8.0,
                    decoration: const BoxDecoration(
                      color: Color.fromARGB(255, 255, 184, 0), // Badge color
                      shape: BoxShape.circle,
                    ),
                  ),
                )
              : const SizedBox.shrink(); // No badge if condition not met
        },
      ),
    ],
  ),
  activeIcon: SvgPicture.asset(
    'assets/images/gradient_evolved_icon.svg', // Path to your SVG asset
    width: 39.0, // Adjust size accordingly
  ),
  label: 'Home',
  tooltip: '',
),
          BottomNavigationBarItem(
  icon: Stack(
    children: [
      const Icon(
        FFIcons.kchat,
        size: 30.0,
      ),
      StreamBuilder<List<LikesRecord>>(
        stream: queryLikesRecord(
          queryBuilder: (likesRecord) => likesRecord
              .where('mutual', isEqualTo: true)
              .where('unmatched', isEqualTo: false)
              .where('recentMessageNotSeenBy', arrayContains: currentUserReference) // Filter by recentMessageSender
              .limit(1), // We only need 1 record to decide if the badge should show
        ),
        builder: (context, snapshot) {
          bool showBadge = snapshot.hasData && snapshot.data!.isNotEmpty;

          return showBadge
              ? Positioned(
                  top: 0,
                  right: 0,
                  child: Container(
                    width: 8.0,
                    height: 8.0,
                    decoration: const BoxDecoration(
                      color: Colors.red, // Badge color
                      shape: BoxShape.circle,
                    ),
                  ),
                )
              : const SizedBox.shrink(); // No badge if condition not met
        },
      ),
    ],
  ),
  activeIcon: const Icon(
    FFIcons.kchatFilled,
    size: 30.0,
  ),
  label: 'Home',
  tooltip: '',
),
          const BottomNavigationBarItem(
            icon: Icon(
              FFIcons.kprofile,
              size: 32.0,
            ),
            activeIcon: Icon(
              FFIcons.kprofileFilled,
              size: 32.0,
            ),
            label: '',
            tooltip: '',
          )
        ],
      ),
    );
  }
}
