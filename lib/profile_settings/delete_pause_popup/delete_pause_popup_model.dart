import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/backend/schema/enums/enums.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/gradient_button_flex_flex_font/gradient_button_flex_flex_font_widget.dart';
import 'delete_pause_popup_widget.dart' show DeletePausePopupWidget;
import 'package:flutter/material.dart';

class DeletePausePopupModel extends FlutterFlowModel<DeletePausePopupWidget> {
  ///  Local state fields for this component.

  String? selectedReason;

  bool deleteInProgress = false;

  PauseDeleteScreenOptions? screen = PauseDeleteScreenOptions.entry;

  String? textfieldReason;

  ///  State fields for stateful widgets in this component.

  final formKey = GlobalKey<FormState>();
  // Model for GradientButton component.
  late GradientButtonModel gradientButtonModel;
  // Stores action output result for [Cloud Function - changePausedMode] action in GradientButton widget.
  ChangePausedModeCloudFunctionCallResponse? changePausedMode1;
  // Stores action output result for [Cloud Function - deleteOwnAccount] action in Button widget.
  DeleteOwnAccountCloudFunctionCallResponse? cloudFunctione24;
  // Model for GradientButtonFlexFlexFont component.
  late GradientButtonFlexFlexFontModel gradientButtonFlexFlexFontModel1;
  // Stores action output result for [Cloud Function - changePausedMode] action in GradientButtonFlexFlexFont widget.
  ChangePausedModeCloudFunctionCallResponse? changePausedMode1Copy;
  // Model for GradientButtonFlexFlexFont component.
  late GradientButtonFlexFlexFontModel gradientButtonFlexFlexFontModel2;
  // Model for GradientButtonFlexFlexFont component.
  late GradientButtonFlexFlexFontModel gradientButtonFlexFlexFontModel3;
  // Stores action output result for [Cloud Function - resetOwnDislikes] action in GradientButtonFlexFlexFont widget.
  ResetOwnDislikesCloudFunctionCallResponse? resetOwnDislikesCf;
  // State field(s) for TextField widget.
  FocusNode? textFieldFocusNode;
  TextEditingController? textController;
  String? Function(BuildContext, String?)? textControllerValidator;
  String? _textControllerValidator(BuildContext context, String? val) {
    if (val == null || val.isEmpty) {
      return 'Type your message...';
    }

    if (val.length < 2) {
      return 'Write min. 2 characters';
    }
    if (val.length > 500) {
      return 'Write max. 500 characters';
    }

    return null;
  }

  // Model for GradientButtonFlexFlexFont component.
  late GradientButtonFlexFlexFontModel gradientButtonFlexFlexFontModel4;

  @override
  void initState(BuildContext context) {
    gradientButtonModel = createModel(context, () => GradientButtonModel());
    gradientButtonFlexFlexFontModel1 =
        createModel(context, () => GradientButtonFlexFlexFontModel());
    gradientButtonFlexFlexFontModel2 =
        createModel(context, () => GradientButtonFlexFlexFontModel());
    gradientButtonFlexFlexFontModel3 =
        createModel(context, () => GradientButtonFlexFlexFontModel());
    textControllerValidator = _textControllerValidator;
    gradientButtonFlexFlexFontModel4 =
        createModel(context, () => GradientButtonFlexFlexFontModel());
  }

  @override
  void dispose() {
    gradientButtonModel.dispose();
    gradientButtonFlexFlexFontModel1.dispose();
    gradientButtonFlexFlexFontModel2.dispose();
    gradientButtonFlexFlexFontModel3.dispose();
    textFieldFocusNode?.dispose();
    textController?.dispose();

    gradientButtonFlexFlexFontModel4.dispose();
  }
}