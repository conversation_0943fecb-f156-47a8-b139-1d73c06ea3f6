import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/backend/schema/enums/enums.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/general/gradient_button_flex_flex_font/gradient_button_flex_flex_font_widget.dart';
import 'dart:async';
import '/flutter_flow/custom_functions.dart' as functions;
import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'delete_pause_popup_model.dart';
export 'delete_pause_popup_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';

class DeletePausePopupWidget extends StatefulWidget {
  const DeletePausePopupWidget({super.key});

  @override
  State<DeletePausePopupWidget> createState() => _DeletePausePopupWidgetState();
}

class _DeletePausePopupWidgetState extends State<DeletePausePopupWidget> {
  late DeletePausePopupModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => DeletePausePopupModel());

    _model.textController ??= TextEditingController();
    _model.textFieldFocusNode ??= FocusNode();
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Builder(
            builder: (context) {
                if (_model.screen ==
                    PauseDeleteScreenOptions.finalChoice) {
                  return Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 23.0),
                      child: Text(
                        getRemoteConfigString('delete_p2_1'),
                        textAlign: TextAlign.center,
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'BT Beau Sans',
                              fontSize: 16.0,
                              letterSpacing: 0.0,
                              fontWeight: FontWeight.w500,
                              useGoogleFonts: false,
                            ),
                      ),
                    ),
                    Align(
                      alignment: const AlignmentDirectional(0.0, 0.0),
                      child: Padding(
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(30.0, 0.0, 30.0, 0.0),
                        child: Container(
                          width: MediaQuery.sizeOf(context).width * 1.0,
                          constraints: BoxConstraints(
                            maxWidth: 400.0,
                            maxHeight: MediaQuery.sizeOf(context).height * 0.5,
                          ),
                          decoration: BoxDecoration(
                            color: FlutterFlowTheme.of(context).primaryBackground,
                            borderRadius: BorderRadius.circular(10.0),
                          ),
                          child: Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                15.0, 30.0, 15.0, 25.0),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Text(
                                  getRemoteConfigString('delete_p1_pause_title'),
                                  style: FlutterFlowTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        fontSize: 14.0,
                                        letterSpacing: 0.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                ),
                                Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      0.0, 20.0, 0.0, 0.0),
                                  child: Text(
                                    getRemoteConfigString(
                                        'delete_p1_pause_explanation'),
                                    textAlign: TextAlign.center,
                                    style: FlutterFlowTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'BT Beau Sans',
                                          fontSize: 12.0,
                                          letterSpacing: 0.0,
                                          fontWeight: FontWeight.normal,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ),
                                Container(
                                  width: 100.0,
                                  decoration: const BoxDecoration(),
                                  child: Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        0.0, 25.0, 0.0, 0.0),
                                    child: wrapWithModel(
                                      model: _model.gradientButtonModel,
                                      updateCallback: () => safeSetState(() {}),
                                      updateOnChange: true,
                                      child: GradientButtonFlexFlexFontWidget(
                                        height: 40,
                                        fontSize: 14,
                                        title: getRemoteConfigString(
                                            'delete_p1_pause_btn'),
                                        action: () async {
                                          analytics.logEvent('Paused Profile from Delete Pause Final Choice Widget', eventProperties: {"deleteReason": _model.selectedReason});
                                          await currentUserReference!.update({
                                            ...createUsersRecordData(
                                              paused: true,
                                            ),
                                            ...mapToFirestore(
                                              {
                                                'matchingSuggestions':
                                                    FieldValue.delete(),
                                              },
                                            ),
                                          });
                                          unawaited(
                                            () async {
                                              try {
                                                final result =
                                                    await FirebaseFunctions
                                                            .instanceFor(
                                                                region:
                                                                    'europe-west2')
                                                        .httpsCallable(
                                                            'changePausedMode')
                                                        .call({});
                                                _model.changePausedMode1 =
                                                    ChangePausedModeCloudFunctionCallResponse(
                                                  succeeded: true,
                                                );
                                              } on FirebaseFunctionsException catch (error) {
                                                _model.changePausedMode1 =
                                                    ChangePausedModeCloudFunctionCallResponse(
                                                  errorCode: error.code,
                                                  succeeded: false,
                                                );
                                              }
                                            }(),
                                          );
                                          Navigator.pop(context);
      
                                          safeSetState(() {});
                                        },
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    Align(
                      alignment: const AlignmentDirectional(0.0, 0.0),
                      child: SingleChildScrollView(
                        child: Padding(
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(30.0, 15.0, 30.0, 0.0),
                          child: Container(
                            width: MediaQuery.sizeOf(context).width * 1.0,
                            constraints: BoxConstraints(
                              maxWidth: 400.0,
                              maxHeight: MediaQuery.sizeOf(context).height * 0.75,
                            ),
                            decoration: BoxDecoration(
                              color: FlutterFlowTheme.of(context).primaryBackground,
                              borderRadius: BorderRadius.circular(10.0),
                            ),
                            child: Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                15.0, 30.0, 15.0, 25.0),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Text(
                                    getRemoteConfigString('delete_p2_delete_title'),
                                    style: FlutterFlowTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'BT Beau Sans',
                                          fontSize: 14.0,
                                          letterSpacing: 0.0,
                                          fontWeight: FontWeight.w500,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        0.0, 20.0, 0.0, 0.0),
                                    child: Text(
                                      getRemoteConfigString(
                                          'delete_p2_delete_explanation'),
                                      textAlign: TextAlign.center,
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            fontFamily: 'BT Beau Sans',
                                            fontSize: 12.0,
                                            letterSpacing: 0.0,
                                            fontWeight: FontWeight.normal,
                                            useGoogleFonts: false,
                                          ),
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        0.0, 25.0, 0.0, 0.0),
                                    child: FFButtonWidget(
                                      onPressed: () async {
                                        _model.deleteInProgress = true;
                                        safeSetState(() {});
                                        try {
                                          final result =
                                              await FirebaseFunctions.instanceFor(
                                                      region: 'europe-west2')
                                              .httpsCallable('deleteOwnAccount')
                                              .call({
                                            "deleteReason":
                                                _model.selectedReason,
                                            "deleteReasonTextfield":
                                                _model.textfieldReason,
                                          });
                                          _model.cloudFunctione24 =
                                              DeleteOwnAccountCloudFunctionCallResponse(
                                            succeeded: true,
                                          );
                                        } on FirebaseFunctionsException catch (error) {
                                          _model.cloudFunctione24 =
                                              DeleteOwnAccountCloudFunctionCallResponse(
                                            errorCode: error.code,
                                            succeeded: false,
                                          );
                                        }
                        
                                        GoRouter.of(context).prepareAuthEvent();
                                        await authManager.signOut();
                                        GoRouter.of(context)
                                            .clearRedirectLocation();
                        
                                        context.goNamedAuth(
                                            'WelcomeScreen', context.mounted);
                        
                                        safeSetState(() {});
                                      },
                                      text: getRemoteConfigString(
                                          'delete_p2_delete_btn'),
                                      options: FFButtonOptions(
                                        width: 100.0,
                                        height: 40.0,
                                        padding: const EdgeInsetsDirectional.fromSTEB(
                                            16.0, 0.0, 16.0, 0.0),
                                        iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                            0.0, 0.0, 0.0, 0.0),
                                        color: FlutterFlowTheme.of(context)
                                            .secondaryText,
                                        textStyle: FlutterFlowTheme.of(context)
                                            .titleSmall
                                            .override(
                                              fontFamily: 'BT Beau Sans',
                                              color: Colors.white,
                                              letterSpacing: 0.0,
                                              useGoogleFonts: false,
                                              fontSize: 12.0
                                            ),
                                        elevation: 0.0,
                                        borderRadius: BorderRadius.circular(100.0),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                );
                  } else if (_model.screen ==
                    PauseDeleteScreenOptions.thanksPause) {
                  return Align(
                    alignment: const AlignmentDirectional(0.0, 0.0),
                    child: Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(30.0, 0.0, 30.0, 0.0),
                      child: Container(
                        width: MediaQuery.sizeOf(context).width * 1.0,
                        constraints: const BoxConstraints(
                          maxWidth: 400.0,
                        ),
                        decoration: BoxDecoration(
                          color: FlutterFlowTheme.of(context).primaryBackground,
                          borderRadius: BorderRadius.circular(10.0),
                        ),
                        child: Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              0.0, 0.0, 0.0, 30.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Row(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  FlutterFlowIconButton(
                                    borderColor: Colors.transparent,
                                    borderRadius: 8.0,
                                    buttonSize: 40.0,
                                    fillColor: FlutterFlowTheme.of(context)
                                        .primaryBackground,
                                    icon: Icon(
                                      Icons.close_rounded,
                                      color: FlutterFlowTheme.of(context)
                                          .primaryText,
                                      size: 16.0,
                                    ),
                                    onPressed: () async {
                                      analytics.logEvent('Cancelled Delete/Pause from Thanks Pause Widget', eventProperties: {"deleteReason": _model.selectedReason});
                                      Navigator.pop(context);
                                    },
                                  ),
                                ],
                              ),
                              // ClipRRect(
                              //   borderRadius: BorderRadius.circular(0.0),
                              //   child: Image.network(
                              //     getRemoteConfigString('delete_thanks_pause_img'),
                              //     width: MediaQuery.sizeOf(context).width * 1.0,
                              //     height: 150.0,
                              //     fit: BoxFit.contain,
                              //   ),
                              // ),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    10.0, 30.0, 10.0, 0.0),
                                child: Text(
                                  getRemoteConfigString(
                                      'delete_thanks_pause_title'),
                                  style: FlutterFlowTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        fontSize: 14.0,
                                        letterSpacing: 0.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    20.0, 14.0, 20.0, 0.0),
                                child: Text(
                                  getRemoteConfigString(
                                      'delete_thanks_pause_explanation'),
                                  textAlign: TextAlign.center,
                                  style: FlutterFlowTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        fontSize: 13.0,
                                        letterSpacing: 0.0,
                                        fontWeight: FontWeight.normal,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 30.0, 0.0, 0.0),
                                child: Container(
                                  width: 110.0,
                                  decoration: const BoxDecoration(),
                                  child: wrapWithModel(
                                    model:
                                        _model.gradientButtonFlexFlexFontModel1,
                                    updateCallback: () => safeSetState(() {}),
                                    child: GradientButtonFlexFlexFontWidget(
                                      title: getRemoteConfigString(
                                          'delete_thanks_pause_btn'),
                                      height: 45.0,
                                      fontSize: 14.0,
                                      action: () async {
                                        analytics.logEvent('Paused Profile from Thanks Pause Widget', eventProperties: {"deleteReason": _model.selectedReason});
                                        await currentUserReference!.update({
                                          ...createUsersRecordData(
                                            paused: true,
                                          ),
                                          ...mapToFirestore(
                                            {
                                              'matchingSuggestions':
                                                  FieldValue.delete(),
                                            },
                                          ),
                                        });
                                        unawaited(
                                          () async {
                                            try {
                                              final result =
                                                  await FirebaseFunctions
                                                          .instanceFor(
                                                              region:
                                                                  'europe-west2')
                                                      .httpsCallable(
                                                          'changePausedMode')
                                                      .call({});
                                              _model.changePausedMode1Copy =
                                                  ChangePausedModeCloudFunctionCallResponse(
                                                succeeded: true,
                                              );
                                            } on FirebaseFunctionsException catch (error) {
                                              _model.changePausedMode1Copy =
                                                  ChangePausedModeCloudFunctionCallResponse(
                                                errorCode: error.code,
                                                succeeded: false,
                                              );
                                            }
                                          }(),
                                        );
                                        Navigator.pop(context);

                                        safeSetState(() {});
                                      },
                                    ),
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 20.0, 0.0, 0.0),
                                child: FFButtonWidget(
                                  onPressed: () async {
                                   _model.screen =
                                        PauseDeleteScreenOptions.finalChoice;
                                    safeSetState(() {});
                                  },
                                  text: getRemoteConfigString(
                                      'delete_thanks_pause_no'),
                                  options: FFButtonOptions(
                                    height: 45.0,
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        16.0, 0.0, 16.0, 0.0),
                                    iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                        0.0, 0.0, 0.0, 0.0),
                                    color: const Color(0x004B39EF),
                                    textStyle: FlutterFlowTheme.of(context)
                                        .titleSmall
                                        .override(
                                          fontFamily: 'BT Beau Sans',
                                          color: FlutterFlowTheme.of(context)
                                              .primaryText,
                                          fontSize: 14.0,
                                          letterSpacing: 0.0,
                                          useGoogleFonts: false,
                                        ),
                                    elevation: 0.0,
                                    borderRadius: BorderRadius.circular(10.0),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                } else if (_model.screen ==
                    PauseDeleteScreenOptions.pause) {
                  return Align(
                    alignment: const AlignmentDirectional(0.0, 0.0),
                    child: Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(30.0, 0.0, 30.0, 0.0),
                      child: Container(
                        width: MediaQuery.sizeOf(context).width * 1.0,
                        constraints: const BoxConstraints(
                          maxWidth: 400.0,
                        ),
                        decoration: BoxDecoration(
                          color: FlutterFlowTheme.of(context).primaryBackground,
                          borderRadius: BorderRadius.circular(10.0),
                        ),
                        child: Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              0.0, 0.0, 0.0, 30.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Row(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  FlutterFlowIconButton(
                                    borderColor: Colors.transparent,
                                    borderRadius: 8.0,
                                    buttonSize: 40.0,
                                    fillColor: FlutterFlowTheme.of(context)
                                        .primaryBackground,
                                    icon: Icon(
                                      Icons.close_rounded,
                                      color: FlutterFlowTheme.of(context)
                                          .primaryText,
                                      size: 16.0,
                                    ),
                                    onPressed: () async {
                                      analytics.logEvent('Cancelled Delete/Pause from Pause Suggestion Widget', eventProperties: {"deleteReason": _model.selectedReason});
                                      Navigator.pop(context);
                                    },
                                  ),
                                ],
                              ),
                              // ClipRRect(
                              //   borderRadius: BorderRadius.circular(0.0),
                              //   child: Image.network(
                              //     getRemoteConfigString('delete_pause_img'),
                              //     width: MediaQuery.sizeOf(context).width * 1.0,
                              //     height: 150.0,
                              //     fit: BoxFit.contain,
                              //   ),
                              // ),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    10.0, 30.0, 10.0, 0.0),
                                child: Text(
                                  getRemoteConfigString('delete_pause_title'),
                                  style: FlutterFlowTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        fontSize: 14.0,
                                        letterSpacing: 0.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    20.0, 14.0, 20.0, 0.0),
                                child: Text(
                                  getRemoteConfigString(
                                      'delete_pause_explanation'),
                                  textAlign: TextAlign.center,
                                  style: FlutterFlowTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        fontSize: 13.0,
                                        letterSpacing: 0.0,
                                        fontWeight: FontWeight.normal,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 30.0, 0.0, 0.0),
                                child: Container(
                                  width: 110.0,
                                  decoration: const BoxDecoration(),
                                  child: wrapWithModel(
                                    model:
                                        _model.gradientButtonFlexFlexFontModel2,
                                    updateCallback: () => safeSetState(() {}),
                                    child: GradientButtonFlexFlexFontWidget(
                                      title: getRemoteConfigString(
                                          'delete_pause_btn'),
                                      height: 45.0,
                                      fontSize: 14.0,
                                      action: () async {
                                        analytics.logEvent('Paused Profile from Pause Suggestion Widget', eventProperties: {"deleteReason": _model.selectedReason});
                                        await currentUserReference!.update({
                                          ...createUsersRecordData(
                                            paused: true,
                                          ),
                                          ...mapToFirestore(
                                            {
                                              'matchingSuggestions':
                                                  FieldValue.delete(),
                                            },
                                          ),
                                        });
                                        unawaited(
                                          () async {
                                            try {
                                              final result =
                                                  await FirebaseFunctions
                                                          .instanceFor(
                                                              region:
                                                                  'europe-west2')
                                                      .httpsCallable(
                                                          'changePausedMode')
                                                      .call({});
                                              _model.changePausedMode1Copy =
                                                  ChangePausedModeCloudFunctionCallResponse(
                                                succeeded: true,
                                              );
                                            } on FirebaseFunctionsException catch (error) {
                                              _model.changePausedMode1Copy =
                                                  ChangePausedModeCloudFunctionCallResponse(
                                                errorCode: error.code,
                                                succeeded: false,
                                              );
                                            }
                                          }(),
                                        );
                                        Navigator.pop(context);

                                        safeSetState(() {});
                                      },
                                    ),
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 20.0, 0.0, 0.0),
                                child: FFButtonWidget(
                                  onPressed: () async {
                                    _model.screen =
                                        PauseDeleteScreenOptions.finalChoice;
                                    safeSetState(() {});
                                  },
                                  text:
                                      getRemoteConfigString('delete_pause_no'),
                                  options: FFButtonOptions(
                                    height: 45.0,
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        16.0, 0.0, 16.0, 0.0),
                                    iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                        0.0, 0.0, 0.0, 0.0),
                                    color: const Color(0x004B39EF),
                                    textStyle: FlutterFlowTheme.of(context)
                                        .titleSmall
                                        .override(
                                          fontFamily: 'BT Beau Sans',
                                          color: FlutterFlowTheme.of(context)
                                              .primaryText,
                                          fontSize: 14.0,
                                          letterSpacing: 0.0,
                                          useGoogleFonts: false,
                                        ),
                                    elevation: 0.0,
                                    borderRadius: BorderRadius.circular(10.0),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                } else if (_model.screen ==
                    PauseDeleteScreenOptions.freshChapter) {
                  return Align(
                    alignment: const AlignmentDirectional(0.0, 0.0),
                    child: Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(30.0, 0.0, 30.0, 0.0),
                      child: Container(
                        width: MediaQuery.sizeOf(context).width * 1.0,
                        constraints: const BoxConstraints(
                          maxWidth: 400.0,
                        ),
                        decoration: BoxDecoration(
                          color: FlutterFlowTheme.of(context).primaryBackground,
                          borderRadius: BorderRadius.circular(10.0),
                        ),
                        child: Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              0.0, 0.0, 0.0, 30.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Row(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  FlutterFlowIconButton(
                                    borderColor: Colors.transparent,
                                    borderRadius: 8.0,
                                    buttonSize: 40.0,
                                    fillColor: FlutterFlowTheme.of(context)
                                        .primaryBackground,
                                    icon: Icon(
                                      Icons.close_rounded,
                                      color: FlutterFlowTheme.of(context)
                                          .primaryText,
                                      size: 16.0,
                                    ),
                                    onPressed: () async {
                                      analytics.logEvent('Cancelled Delete/Pause from Fresh Chapter Widget', eventProperties: {"deleteReason": _model.selectedReason});
                                      Navigator.pop(context);
                                    },
                                  ),
                                ],
                              ),
                              // ClipRRect(
                              //   borderRadius: BorderRadius.circular(0.0),
                              //   child: Image.network(
                              //     getRemoteConfigString('delete_newchapter_img'),
                              //     width: MediaQuery.sizeOf(context).width * 1.0,
                              //     height: 150.0,
                              //     fit: BoxFit.contain,
                              //   ),
                              // ),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    10.0, 30.0, 10.0, 0.0),
                                child: Text(
                                  getRemoteConfigString(
                                      'delete_newchapter_title'),
                                  style: FlutterFlowTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        fontSize: 14.0,
                                        letterSpacing: 0.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    20.0, 14.0, 20.0, 0.0),
                                child: Text(
                                  getRemoteConfigString(
                                      'delete_newchapter_explanation'),
                                  textAlign: TextAlign.center,
                                  style: FlutterFlowTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        fontSize: 13.0,
                                        letterSpacing: 0.0,
                                        fontWeight: FontWeight.normal,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 30.0, 0.0, 0.0),
                                child: Container(
                                  width: 110.0,
                                  decoration: const BoxDecoration(),
                                  child: Builder(
                                    builder: (context) => wrapWithModel(
                                      model: _model
                                          .gradientButtonFlexFlexFontModel3,
                                      updateCallback: () => safeSetState(() {}),
                                      child: GradientButtonFlexFlexFontWidget(
                                        title: getRemoteConfigString(
                                            'delete_newchapter_btn'),
                                        height: 45.0,
                                        fontSize: 14.0,
                                        action: () async {
                                          analytics.logEvent('Selected Fresh Chapter from Delete Widget', eventProperties: {"deleteReason": _model.selectedReason});
                                          var shouldSetState = false;
                                          try {
                                            final result =
                                                await FirebaseFunctions
                                                        .instanceFor(
                                                            region:
                                                                'europe-west2')
                                                    .httpsCallable(
                                                        'resetOwnDislikes')
                                                    .call({});
                                            _model.resetOwnDislikesCf =
                                                ResetOwnDislikesCloudFunctionCallResponse(
                                              succeeded: true,
                                            );
                                          } on FirebaseFunctionsException catch (error) {
                                            _model.resetOwnDislikesCf =
                                                ResetOwnDislikesCloudFunctionCallResponse(
                                              errorCode: error.code,
                                              succeeded: false,
                                            );
                                          }

                                          shouldSetState = true;
                                          if (_model
                                              .resetOwnDislikesCf!.succeeded!) {
                                            context.goNamed('TempLoaderScreen');

                                            if (shouldSetState) {
                                              safeSetState(() {});
                                            }
                                            return;
                                          } else {
                                            await showDialog(
                                              context: context,
                                              builder: (dialogContext) {
                                                return Dialog(
                                                  elevation: 0,
                                                  insetPadding: EdgeInsets.zero,
                                                  backgroundColor:
                                                      Colors.transparent,
                                                  alignment:
                                                      const AlignmentDirectional(
                                                              0.0, 0.0)
                                                          .resolve(
                                                              Directionality.of(
                                                                  context)),
                                                  child: const GeneralPopupWidget(
                                                    alertTitle:
                                                        'Something went wrong',
                                                    alertText:
                                                        'Please try again <NAME_EMAIL>',
                                                  ),
                                                );
                                              },
                                            );

                                            if (shouldSetState) {
                                              safeSetState(() {});
                                            }
                                            return;
                                          }

                                          if (shouldSetState) {
                                            safeSetState(() {});
                                          }
                                        },
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 20.0, 0.0, 0.0),
                                child: FFButtonWidget(
                                  onPressed: () async {
                                    _model.screen =
                                        PauseDeleteScreenOptions.finalChoice;
                                    safeSetState(() {});
                                  },
                                  text: getRemoteConfigString(
                                      'delete_newchapter_no'),
                                  options: FFButtonOptions(
                                    height: 45.0,
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        16.0, 0.0, 16.0, 0.0),
                                    iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                        0.0, 0.0, 0.0, 0.0),
                                    color: const Color(0x004B39EF),
                                    textStyle: FlutterFlowTheme.of(context)
                                        .titleSmall
                                        .override(
                                          fontFamily: 'BT Beau Sans',
                                          color: FlutterFlowTheme.of(context)
                                              .primaryText,
                                          fontSize: 14.0,
                                          letterSpacing: 0.0,
                                          useGoogleFonts: false,
                                        ),
                                    elevation: 0.0,
                                    borderRadius: BorderRadius.circular(10.0),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                } else if (_model.screen ==
                    PauseDeleteScreenOptions.thanksTextfield) {
                  return Align(
                    alignment: const AlignmentDirectional(0.0, 0.0),
                    child: Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(30.0, 0.0, 30.0, 0.0),
                      child: Container(
                        width: MediaQuery.sizeOf(context).width * 1.0,
                        constraints: const BoxConstraints(
                          maxWidth: 400.0,
                        ),
                        decoration: BoxDecoration(
                          color: FlutterFlowTheme.of(context).primaryBackground,
                          borderRadius: BorderRadius.circular(10.0),
                        ),
                        child: Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              0.0, 0.0, 0.0, 30.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Row(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  FlutterFlowIconButton(
                                    borderColor: Colors.transparent,
                                    borderRadius: 8.0,
                                    buttonSize: 40.0,
                                    fillColor: FlutterFlowTheme.of(context)
                                        .primaryBackground,
                                    icon: Icon(
                                      Icons.close_rounded,
                                      color: FlutterFlowTheme.of(context)
                                          .primaryText,
                                      size: 16.0,
                                    ),
                                    onPressed: () async {
                                      analytics.logEvent('Cancelled Delete/Pause from Own Feedback Widget', eventProperties: {"deleteReason": _model.selectedReason});
                                      Navigator.pop(context);
                                    },
                                  ),
                                ],
                              ),
                              Text(
                                valueOrDefault<String>(
                                  functions.getValueForKeyFromDict(
                                      _model.selectedReason!,
                                      getRemoteConfigString(
                                          'delete_reasons_textfield_titles')),
                                  'What is your feedback?',
                                ),
                                style: FlutterFlowTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'BT Beau Sans',
                                      fontSize: 14.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.w500,
                                      useGoogleFonts: false,
                                    ),
                              ),
                              Flexible(
                                child: Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      0.0, 39.0, 0.0, 0.0),
                                  child: Stack(
                                    children: [
                                      Padding(
                                        padding: const EdgeInsetsDirectional.fromSTEB(
                                            35.0, 0.0, 35.0, 0.0),
                                        child: Container(
                                          width:
                                              MediaQuery.sizeOf(context).width *
                                                  1.0,
                                          constraints: const BoxConstraints(
                                            minHeight: 51.0,
                                            maxHeight: 200.0,
                                          ),
                                          decoration: BoxDecoration(
                                            color: FlutterFlowTheme.of(context)
                                                .secondaryBackground,
                                            borderRadius:
                                                BorderRadius.circular(11.0),
                                            border: Border.all(
                                              color: const Color(0xFFE9EAEE),
                                              width: 1.0,
                                            ),
                                          ),
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            children: [
                                              Flexible(
                                                child: Form(
                                                  key: _model.formKey,
                                                  autovalidateMode:
                                                      AutovalidateMode.disabled,
                                                  child: Padding(
                                                    padding:
                                                        const EdgeInsetsDirectional
                                                            .fromSTEB(10.0, 0.0,
                                                                10.0, 0.0),
                                                    child: TextFormField(
                                                      controller:
                                                          _model.textController,
                                                      focusNode: _model
                                                          .textFieldFocusNode,
                                                      autofocus: false,
                                                      obscureText: false,
                                                      decoration:
                                                          InputDecoration(
                                                        hintText:
                                                            getRemoteConfigString(
                                                                'delete_textfield_preview'),
                                                        hintStyle:
                                                            FlutterFlowTheme.of(
                                                                    context)
                                                                .labelMedium
                                                                .override(
                                                                  fontFamily:
                                                                      'BT Beau Sans',
                                                                  fontSize:
                                                                      13.0,
                                                                  letterSpacing:
                                                                      0.0,
                                                                  useGoogleFonts:
                                                                      false,
                                                                ),
                                                        enabledBorder:
                                                            InputBorder.none,
                                                        focusedBorder:
                                                            InputBorder.none,
                                                        errorBorder:
                                                            InputBorder.none,
                                                        focusedErrorBorder:
                                                            InputBorder.none,
                                                      ),
                                                      style: FlutterFlowTheme
                                                              .of(context)
                                                          .bodyMedium
                                                          .override(
                                                            fontFamily:
                                                                'BT Beau Sans',
                                                            fontSize: 13.0,
                                                            letterSpacing: 0.0,
                                                            useGoogleFonts:
                                                                false,
                                                          ),
                                                      maxLines: 5,
                                                      minLines: 1,
                                                      maxLength: 1000,
                                                      maxLengthEnforcement:
                                                          MaxLengthEnforcement
                                                              .enforced,
                                                      buildCounter: (context,
                                                              {required currentLength,
                                                              required isFocused,
                                                              maxLength}) =>
                                                          null,
                                                      validator: _model
                                                          .textControllerValidator
                                                          .asValidator(context),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 30.0, 0.0, 0.0),
                                child: Container(
                                  width: 110.0,
                                  decoration: const BoxDecoration(),
                                  child: wrapWithModel(
                                    model:
                                        _model.gradientButtonFlexFlexFontModel4,
                                    updateCallback: () => safeSetState(() {}),
                                    child: GradientButtonFlexFlexFontWidget(
                                      title: getRemoteConfigString(
                                          'delete_textfield_next'),
                                      height: 45.0,
                                      fontSize: 13.0,
                                      action: () async {
                                        analytics.logEvent('Added own Feedback on Delete Pause Widget', eventProperties: {"deleteReason": _model.selectedReason});
                                        _model.textfieldReason =
                                            _model.textController.text;
                                        safeSetState(() {});
                                        _model.screen = PauseDeleteScreenOptions
                                            .thanksPause;
                                        safeSetState(() {});
                                      },
                                    ),
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 20.0, 0.0, 0.0),
                                child: FFButtonWidget(
                                  onPressed: () async {
                                    analytics.logEvent('Jumped over own Feedback on Delete Pause Widget', eventProperties: {"deleteReason": _model.selectedReason});
                                    _model.screen =
                                        PauseDeleteScreenOptions.thanksPause;
                                    safeSetState(() {});
                                  },
                                  text: getRemoteConfigString(
                                      'delete_textfield_skip'),
                                  options: FFButtonOptions(
                                    height: 45.0,
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        16.0, 0.0, 16.0, 0.0),
                                    iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                        0.0, 0.0, 0.0, 0.0),
                                    color: const Color(0x004B39EF),
                                    textStyle: FlutterFlowTheme.of(context)
                                        .titleSmall
                                        .override(
                                          fontFamily: 'BT Beau Sans',
                                          color: FlutterFlowTheme.of(context)
                                              .primaryText,
                                          fontSize: 14.0,
                                          letterSpacing: 0.0,
                                          useGoogleFonts: false,
                                        ),
                                    elevation: 0.0,
                                    borderRadius: BorderRadius.circular(10.0),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                } else {
                  return Align(
                  alignment: const AlignmentDirectional(0.0, 0.0),
                  child: Padding(
                    padding: const EdgeInsetsDirectional.fromSTEB(30.0, 0.0, 30.0, 0.0),
                    child: Container(
                      width: MediaQuery.sizeOf(context).width * 1.0,
                      constraints: BoxConstraints(
                        maxWidth: 400.0,
                        maxHeight:  MediaQuery.sizeOf(context).height > 700 ? MediaQuery.sizeOf(context).height * 0.7 : MediaQuery.sizeOf(context).height - 150,
                      ),
                      decoration: BoxDecoration(
                        color: FlutterFlowTheme.of(context).primaryBackground,
                        borderRadius: BorderRadius.circular(10.0),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              FlutterFlowIconButton(
                                borderRadius: 8.0,
                                buttonSize: 40.0,
                                fillColor: FlutterFlowTheme.of(context)
                                    .primaryBackground,
                                icon: Icon(
                                  Icons.close_rounded,
                                  color: FlutterFlowTheme.of(context).primaryText,
                                  size: 16.0,
                                ),
                                onPressed: () async {
                                  analytics.logEvent('Cancelled Delete/Pause from Delete Pause Widget', eventProperties: {"deleteReason": _model.selectedReason});
                                  Navigator.pop(context);
                                },
                              ),
                            ],
                          ),
                          // ClipRRect(
                          //   borderRadius: BorderRadius.circular(0.0),
                          //   child: Image.network(
                          //     getRemoteConfigString("delete_p1_img"),
                          //     width: MediaQuery.sizeOf(context).width * 1.0,
                          //     height: 150.0,
                          //     fit: BoxFit.contain,
                          //   ),
                          // ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 30.0, 0.0, 0.0),
                            child: Text(
                              getRemoteConfigString('delete_p1_q'),
                              style: FlutterFlowTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'BT Beau Sans',
                                    fontSize: 18.0,
                                    letterSpacing: 0.0,
                                    fontWeight: FontWeight.w500,
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ),
                          Flexible(
                            child: Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  20.0, 30.0, 20.0, 0.0),
                              child: Builder(
                                builder: (context) {
                                  final reasonOptions = functions
                                      .getStringListFromJson(
                                          getRemoteConfigString(
                                              'delete_reason_list'))
                                      .toList()..shuffle();
      
                                  return ListView.builder(
                                    padding: const EdgeInsets.fromLTRB(
                                      0,
                                      0,
                                      0,
                                      40.0,
                                    ),
                                    shrinkWrap: true,
                                    scrollDirection: Axis.vertical,
                                    itemCount: reasonOptions.length,
                                    itemBuilder: (context, reasonOptionsIndex) {
                                      final reasonOptionsItem =
                                          reasonOptions[reasonOptionsIndex];
                                      return Column(
                                        mainAxisSize: MainAxisSize.max,
                                        children: [
                                          InkWell(
                                            splashColor: Colors.transparent,
                                            focusColor: Colors.transparent,
                                            hoverColor: Colors.transparent,
                                            highlightColor: Colors.transparent,
                                            onTap: () async {
                                              _model.selectedReason =
                                                  reasonOptionsItem;
                                              analytics.logEvent('Selected Reason from Delete Pause Widget', eventProperties: {"deleteReason": _model.selectedReason});
                                              if (functions
                                                    .getStringListFromJson(
                                                        getRemoteConfigString(
                                                            'delete_reasons_nav_pause'))
                                                    .contains(
                                                        reasonOptionsItem)) {
                                                  _model.screen =
                                                      PauseDeleteScreenOptions
                                                          .pause;
                                                  safeSetState(() {});
                                                } else if (functions
                                                    .getStringListFromJson(
                                                        getRemoteConfigString(
                                                            'delete_reasons_nav_newchapter'))
                                                    .contains(
                                                        reasonOptionsItem)) {
                                                  _model.screen =
                                                      PauseDeleteScreenOptions
                                                          .freshChapter;
                                                  safeSetState(() {});
                                                } else if (functions
                                                    .getStringListFromJson(
                                                        getRemoteConfigString(
                                                            'delete_reasons_nav_thanks_pause'))
                                                    .contains(
                                                        reasonOptionsItem)) {
                                                  _model.screen =
                                                      PauseDeleteScreenOptions
                                                          .thanksPause;
                                                  safeSetState(() {});
                                                } else if (functions
                                                    .getStringListFromJson(
                                                        getRemoteConfigString(
                                                            'delete_reasons_nav_textfield'))
                                                    .contains(
                                                        reasonOptionsItem)) {
                                                  _model.screen =
                                                      PauseDeleteScreenOptions
                                                          .thanksTextfield;
                                                  safeSetState(() {});
                                                } else {
                                                  _model.screen =
                                                      PauseDeleteScreenOptions
                                                          .finalChoice;
                                                  safeSetState(() {});
                                                }
                                              safeSetState(() {});
                                            },
                                            child: Row(
                                              mainAxisSize: MainAxisSize.max,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.spaceBetween,
                                              children: [
                                                Padding(
                                                  padding: const EdgeInsetsDirectional
                                                      .fromSTEB(
                                                          0.0, 15.0, 0.0, 15.0),
                                                  child: Text(
                                                    reasonOptionsItem,
                                                    style: FlutterFlowTheme.of(
                                                            context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          letterSpacing: 0.0,
                                                          useGoogleFonts: false,
                                                        ),
                                                  ),
                                                ),
                                                Container(
                                                  width: 15.0,
                                                  height: 15.0,
                                                  decoration: const BoxDecoration(
                                                    color: Color(0xFFD9D9D9),
                                                    shape: BoxShape.circle,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          Divider(
                                            thickness: 1.0,
                                            color: FlutterFlowTheme.of(context)
                                                .alternate,
                                          ),
                                        ],
                                      );
                                    },
                                  );
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
                }
              },
            ),
           if (!_model.deleteInProgress &&
                !(_model.textFieldFocusNode?.hasFocus ?? false))
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(0.0, 25.0, 0.0, 0.0),
              child: FFButtonWidget(
                onPressed: () async {
                  analytics.logEvent('Cancelled Delete/Pause from Bottom Delete Pause Widget', eventProperties: {"deleteReason": _model.selectedReason});
                  Navigator.pop(context);
                },
                text: 'Cancel',
                options: FFButtonOptions(
                  height: 45.0,
                  padding: const EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                  iconPadding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                  color: const Color(0x004B39EF),
                  textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                        fontFamily: 'BT Beau Sans',
                        color: FlutterFlowTheme.of(context).primaryText,
                        letterSpacing: 0.0,
                        useGoogleFonts: false,
                        fontSize: 15
                      ),
                  elevation: 0.0,
                  borderRadius: BorderRadius.circular(10.0),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
