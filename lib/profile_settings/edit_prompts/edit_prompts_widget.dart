import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/components/prompt_filled_in_widget.dart';
import '/components/prompt_n_e_widget.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/done_button_blue/done_button_blue_widget.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'edit_prompts_model.dart';
export 'edit_prompts_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';

class EditPromptsWidget extends StatefulWidget {
  const EditPromptsWidget({super.key});

  @override
  State<EditPromptsWidget> createState() => _EditPromptsWidgetState();
}

class _EditPromptsWidgetState extends State<EditPromptsWidget> {
  late EditPromptsModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => EditPromptsModel());
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return AuthUserStreamWidget(
      builder: (context) => StreamBuilder<PublicProfileRecord>(
        stream: PublicProfileRecord.getDocument(
            currentUserDocument!.publicProfile!),
        builder: (context, snapshot) {
          // Customize what your widget looks like when it's loading.
          if (!snapshot.hasData) {
            return Scaffold(
              backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
              body: Center(
                child: SizedBox(
                  width: 50.0,
                  height: 50.0,
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      FlutterFlowTheme.of(context).accent2,
                    ),
                  ),
                ),
              ),
            );
          }
          final editPromptsPublicProfileRecord = snapshot.data!;
          return GestureDetector(
            onTap: () => _model.unfocusNode.canRequestFocus
                ? FocusScope.of(context).requestFocus(_model.unfocusNode)
                : FocusScope.of(context).unfocus(),
            child: Scaffold(
              key: scaffoldKey,
              backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
              appBar: AppBar(
                backgroundColor: FlutterFlowTheme.of(context).info,
                automaticallyImplyLeading: false,
                title: Align(
                  alignment: const AlignmentDirectional(0.0, 0.0),
                  child: Stack(
                    alignment: const AlignmentDirectional(0.0, 0.0),
                    children: [
                      Text(
                        'Edit Prompts',
                        style: FlutterFlowTheme.of(context)
                            .headlineMedium
                            .override(
                              fontFamily: 'BT Beau Sans',
                              color: const Color(0xFF262A36),
                              fontSize: 20.0,
                              fontWeight: FontWeight.bold,
                              useGoogleFonts: false,
                            ),
                      ),
                      Align(
                        alignment: const AlignmentDirectional(1.0, 0.0),
                        child: Container(
                          height: 40.0,
                          decoration: const BoxDecoration(),
                          child: wrapWithModel(
                            model: _model.doneButtonBlueModel,
                            updateCallback: () => setState(() {}),
                            child: DoneButtonBlueWidget(
                              action: () async {
                                analytics.setUserProperties({"Prompts": snapshot.data?.prompts.length ?? 0});
                                context.safePop();
                              },
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                actions: const [],
                centerTitle: false,
                elevation: 1.0,
              ),
              body: SafeArea(
                top: true,
                child: Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(6.0, 20.0, 6.0, 0.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Column(
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 0.0, 0.0, 19.0),
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                if ((editPromptsPublicProfileRecord
                                            .hasPrompts() ==
                                        false) ||
                                    (editPromptsPublicProfileRecord
                                            .prompts.isEmpty))
                                  wrapWithModel(
                                    model: _model.promptNEModel1,
                                    updateCallback: () => setState(() {}),
                                    child: const PromptNEWidget(),
                                  ),
                                if ((editPromptsPublicProfileRecord
                                            .hasPrompts() ==
                                        true) &&
                                    (editPromptsPublicProfileRecord
                                            .prompts.isNotEmpty))
                                  wrapWithModel(
                                    model: _model.promptFilledInModel1,
                                    updateCallback: () => setState(() {}),
                                    updateOnChange: true,
                                    child: PromptFilledInWidget(
                                      promptNo: 1,
                                      publicProfile:
                                          editPromptsPublicProfileRecord,
                                    ),
                                  ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 0.0, 0.0, 19.0),
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                if ((editPromptsPublicProfileRecord
                                            .hasPrompts() ==
                                        false) ||
                                    (editPromptsPublicProfileRecord
                                            .prompts.length <=
                                        1))
                                  wrapWithModel(
                                    model: _model.promptNEModel2,
                                    updateCallback: () => setState(() {}),
                                    child: const PromptNEWidget(),
                                  ),
                                if ((editPromptsPublicProfileRecord
                                            .hasPrompts() ==
                                        true) &&
                                    (editPromptsPublicProfileRecord
                                            .prompts.length >=
                                        2))
                                  wrapWithModel(
                                    model: _model.promptFilledInModel2,
                                    updateCallback: () => setState(() {}),
                                    updateOnChange: true,
                                    child: PromptFilledInWidget(
                                      promptNo: 2,
                                      publicProfile:
                                          editPromptsPublicProfileRecord,
                                    ),
                                  ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 0.0, 0.0, 19.0),
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                if ((editPromptsPublicProfileRecord
                                            .hasPrompts() ==
                                        false) ||
                                    (editPromptsPublicProfileRecord
                                            .prompts.length <=
                                        2))
                                  wrapWithModel(
                                    model: _model.promptNEModel3,
                                    updateCallback: () => setState(() {}),
                                    child: const PromptNEWidget(),
                                  ),
                                if ((editPromptsPublicProfileRecord
                                            .hasPrompts() ==
                                        true) &&
                                    (editPromptsPublicProfileRecord
                                            .prompts.length >=
                                        3))
                                  wrapWithModel(
                                    model: _model.promptFilledInModel3,
                                    updateCallback: () => setState(() {}),
                                    updateOnChange: true,
                                    child: PromptFilledInWidget(
                                      promptNo: 3,
                                      publicProfile:
                                          editPromptsPublicProfileRecord,
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
