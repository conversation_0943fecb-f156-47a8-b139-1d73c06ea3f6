import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/flutter_flow/flutter_flow_radio_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/form_field_controller.dart';
import '/general/gradient_button_small/gradient_button_small_widget.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'findom_question_settings_model.dart';
export 'findom_question_settings_model.dart';

class FindomQuestionSettingsWidget extends StatefulWidget {
  const FindomQuestionSettingsWidget({super.key});

  @override
  State<FindomQuestionSettingsWidget> createState() =>
      _FindomQuestionSettingsWidgetState();
}

class _FindomQuestionSettingsWidgetState
    extends State<FindomQuestionSettingsWidget> {
  late FindomQuestionSettingsModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => FindomQuestionSettingsModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: const AlignmentDirectional(0.0, 0.0),
      child: Padding(
        padding: const EdgeInsetsDirectional.fromSTEB(20.0, 0.0, 20.0, 0.0),
        child: Container(
          width: MediaQuery.sizeOf(context).width * 1.0,
          height: 430.0,
          constraints: const BoxConstraints(
            maxWidth: 400.0,
          ),
          decoration: BoxDecoration(
            color: FlutterFlowTheme.of(context).secondaryBackground,
            borderRadius: BorderRadius.circular(12.0),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(0.0, 40.0, 0.0, 0.0),
                child: Text(
                  'Are you a Findomme?',
                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                        fontFamily: 'BT Beau Sans',
                        fontSize: 20.0,
                        letterSpacing: 0.0,
                        fontWeight: FontWeight.bold,
                        useGoogleFonts: false,
                      ),
                ),
              ),
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(33.0, 11.0, 33.0, 0.0),
                child: Text(
                  'We allow Findommes and other professionals on the app, but you have to declare it if you are one. No declaration can lead to a ban for life.',
                  textAlign: TextAlign.center,
                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                        fontFamily: 'BT Beau Sans',
                        fontSize: 16.0,
                        letterSpacing: 0.0,
                        useGoogleFonts: false,
                        lineHeight: 1.5,
                      ),
                ),
              ),
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(0.0, 45.0, 0.0, 0.0),
                child: AuthUserStreamWidget(
                  builder: (context) => FlutterFlowRadioButton(
                    options: ['Yes', 'No'].toList(),
                    onChanged: (val) => setState(() {}),
                    controller: _model.radioButtonValueController ??=
                        FormFieldController<String>(valueOrDefault<bool>(
                                currentUserDocument?.findom, false)
                            ? 'Yes'
                            : 'No'),
                    optionHeight: 32.0,
                    textStyle:
                        FlutterFlowTheme.of(context).labelMedium.override(
                              fontFamily: 'BT Beau Sans',
                              letterSpacing: 0.0,
                              useGoogleFonts: false,
                            ),
                    textPadding:
                        const EdgeInsetsDirectional.fromSTEB(15.0, 0.0, 0.0, 0.0),
                    buttonPosition: RadioButtonPosition.left,
                    direction: Axis.vertical,
                    radioButtonColor: FlutterFlowTheme.of(context).accent2,
                    inactiveRadioButtonColor:
                        FlutterFlowTheme.of(context).secondaryText,
                    toggleable: false,
                    horizontalAlignment: WrapAlignment.start,
                    verticalAlignment: WrapCrossAlignment.start,
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(52.0, 45.0, 52.0, 0.0),
                child: wrapWithModel(
                  model: _model.gradientButtonSmallModel,
                  updateCallback: () => setState(() {}),
                  child: GradientButtonSmallWidget(
                    title: 'Next',
                    action: () async {
                      await currentUserReference!.update(createUsersRecordData(
                        findom: _model.radioButtonValue == 'Yes' ? true : false,
                      ));

                      await currentUserDocument!.publicProfile!
                          .update(createPublicProfileRecordData(
                        findom: _model.radioButtonValue == 'Yes' ? true : false,
                      ));
                      try {
                        final result = await FirebaseFunctions.instanceFor(
                                region: 'europe-west1')
                            .httpsCallable('changeProfessionalStatus')
                            .call({
                          "newStatus":
                              _model.radioButtonValue == 'Yes' ? true : false,
                        });
                        _model.changeProfessionalStatusCf =
                            ChangeProfessionalStatusCloudFunctionCallResponse(
                          succeeded: true,
                        );
                      } on FirebaseFunctionsException catch (error) {
                        _model.changeProfessionalStatusCf =
                            ChangeProfessionalStatusCloudFunctionCallResponse(
                          errorCode: error.code,
                          succeeded: false,
                        );
                      }

                      Navigator.pop(context);

                      safeSetState(() {});
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
