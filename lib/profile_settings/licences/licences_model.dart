import 'package:flutter/foundation.dart';

import '/flutter_flow/flutter_flow_util.dart';
import '/general/done_button_blue/done_button_blue_widget.dart';
import 'licences_widget.dart' show LicencesWidget;
import 'package:flutter/material.dart';

class LicencesModel extends FlutterFlowModel<LicencesWidget> {
  ///  State fields for stateful widgets in this page.

  final unfocusNode = FocusNode();
  // Model for DoneButtonBlue component.
  late DoneButtonBlueModel doneButtonBlueModel;

  @override
  void initState(BuildContext context) {
    doneButtonBlueModel = createModel(context, () => DoneButtonBlueModel());
  }

  @override
  void dispose() {
    unfocusNode.dispose();
    doneButtonBlueModel.dispose();
  }
}

class License {
  String title;
  String text;

  License(this.title, this.text);
}

Future<List<License>> loadLicenses() async =>
      LicenseRegistry.licenses.asyncMap<License>((license) async {
        final title = license.packages.join('\n');
        final text = license.paragraphs
            .map<String>((paragraph) => paragraph.text)
            .join('\n\n');

        return License(title, text);
      }).toList();