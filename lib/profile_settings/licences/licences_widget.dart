import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/done_button_blue/done_button_blue_widget.dart';
import 'package:flutter/material.dart';
import 'licences_model.dart';
export 'licences_model.dart';

class LicencesWidget extends StatefulWidget {
  const LicencesWidget({super.key, List<License>? licenses});

  @override
  State<LicencesWidget> createState() => _LicencesWidgetState();
}

class _LicencesWidgetState extends State<LicencesWidget> {
  late LicencesModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => LicencesModel());
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _model.unfocusNode.canRequestFocus
          ? FocusScope.of(context).requestFocus(_model.unfocusNode)
          : FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
        appBar: AppBar(
          backgroundColor: FlutterFlowTheme.of(context).info,
          automaticallyImplyLeading: false,
          title: Align(
            alignment: const AlignmentDirectional(0.0, 0.0),
            child: Stack(
              alignment: const AlignmentDirectional(0.0, 0.0),
              children: [
                Text(
                  'Licences',
                  style: FlutterFlowTheme.of(context).headlineMedium.override(
                        fontFamily: 'Inter',
                        color: const Color(0xFF262A36),
                        fontSize: 20.0,
                        letterSpacing: 0.0,
                        fontWeight: FontWeight.bold,
                      ),
                ),
                Align(
                  alignment: const AlignmentDirectional(1.0, 0.0),
                  child: Container(
                    width: 80.0,
                    height: 50.0,
                    decoration: const BoxDecoration(),
                    child: Align(
                      alignment: const AlignmentDirectional(1.0, 0.0),
                      child: wrapWithModel(
                        model: _model.doneButtonBlueModel,
                        updateCallback: () => setState(() {}),
                        child: DoneButtonBlueWidget(
                          action: () async {
                            context.safePop();
                          },
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          actions: const [],
          centerTitle: false,
          elevation: 0.0,
        ),
        body: FutureBuilder<List<License>>(
          future: loadLicenses(),
          builder: (context, snapshot) {
            final licenses = snapshot.data;

            switch (snapshot.connectionState) {
              case ConnectionState.waiting:
                return const Center(child: CircularProgressIndicator());
              default:
                if (snapshot.hasError) {
                  return const Center(child: Text('Some error occurred!'));
                } else {
                  return ListView.builder(
  itemCount: licenses!.length, // Number of items in your list
  itemBuilder: (context, index) {
    final item = licenses[index];
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            item.title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500, // Medium weight
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 4), // Space between title and text
          Text(
            item.text,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400, // Regular weight
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  },
);
                }
            }
          },
        ),
      ),
    );
  } 
}