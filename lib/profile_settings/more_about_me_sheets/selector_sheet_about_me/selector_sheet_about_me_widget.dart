import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/done_button_grey/done_button_grey_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:provider/provider.dart';
import 'selector_sheet_about_me_model.dart';
export 'selector_sheet_about_me_model.dart';

class SelectorSheetAboutMeWidget extends StatefulWidget {
  const SelectorSheetAboutMeWidget({
    super.key,
    required this.options,
    this.initialChoice,
    required this.title,
    required this.mandatoryChoice,
  });

  final List<String>? options;
  final String? initialChoice;
  final String? title;
  final bool? mandatoryChoice;

  @override
  State<SelectorSheetAboutMeWidget> createState() =>
      _SelectorSheetAboutMeWidgetState();
}

class _SelectorSheetAboutMeWidgetState
    extends State<SelectorSheetAboutMeWidget> {
  late SelectorSheetAboutMeModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => SelectorSheetAboutMeModel());

    // On component load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      setState(() {
        _model.result =
            widget.initialChoice != null && widget.initialChoice != ''
                ? widget.initialChoice
                : null;
      });
    });
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return Material(
      color: Colors.transparent,
      elevation: 5.0,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(0.0),
          bottomRight: Radius.circular(0.0),
          topLeft: Radius.circular(16.0),
          topRight: Radius.circular(16.0),
        ),
      ),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: FlutterFlowTheme.of(context).primaryBackground,
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(0.0),
            bottomRight: Radius.circular(0.0),
            topLeft: Radius.circular(16.0),
            topRight: Radius.circular(16.0),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(0.0, 12.0, 0.0, 0.0),
              child: Container(
                width: 50.0,
                height: 4.0,
                decoration: BoxDecoration(
                  color: FlutterFlowTheme.of(context).alternate,
                  borderRadius: BorderRadius.circular(8.0),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(17.0, 0.0, 17.0, 0.0),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    splashColor: Colors.transparent,
                    focusColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    onTap: () async {
                      Navigator.pop(context);
                    },
                    child: Icon(
                      Icons.close_sharp,
                      color: FlutterFlowTheme.of(context).secondaryText,
                      size: 30.0,
                    ),
                  ),
                  wrapWithModel(
                    model: _model.doneButtonGreyModel,
                    updateCallback: () => setState(() {}),
                    updateOnChange: true,
                    child: DoneButtonGreyWidget(
                      action: () async {
                        await currentUserDocument!.publicProfile!.update({
                          ...mapToFirestore(
                            {
                              'moreAboutMe': FieldValue.arrayRemove([
                                getMoreAboutMeFirestoreData(
                                  createMoreAboutMeStruct(
                                    type: widget.title,
                                    option: widget.initialChoice,
                                    clearUnsetFields: false,
                                  ),
                                  true,
                                )
                              ]),
                            },
                          ),
                        });
                        if (_model.result != null && _model.result != '') {
                          await currentUserDocument!.publicProfile!.update({
                            ...mapToFirestore(
                              {
                                'moreAboutMe': FieldValue.arrayUnion([
                                  getMoreAboutMeFirestoreData(
                                    createMoreAboutMeStruct(
                                      type: widget.title,
                                      option: _model.result,
                                      clearUnsetFields: false,
                                    ),
                                    true,
                                  )
                                ]),
                              },
                            ),
                          });
                        }
                        Navigator.pop(context);
                      },
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(17.0, 0.0, 17.0, 0.0),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  Padding(
                    padding:
                        const EdgeInsetsDirectional.fromSTEB(0.0, 16.0, 17.0, 0.0),
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Column(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Text(
                              valueOrDefault<String>(
                                widget.title,
                                'Please make a choice',
                              ),
                              style: FlutterFlowTheme.of(context)
                                  .headlineSmall
                                  .override(
                                    fontFamily: 'BT Beau Sans',
                                    fontSize: 28.0,
                                    fontWeight: FontWeight.bold,
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Align(
              alignment: const AlignmentDirectional(-1.0, -1.0),
              child: Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(17.0, 18.0, 17.0, 18.0),
                child: Builder(
                  builder: (context) {
                    final options = widget.options!.toList();
                    return Wrap(
                      spacing: 0.0,
                      runSpacing: 0.0,
                      alignment: WrapAlignment.start,
                      crossAxisAlignment: WrapCrossAlignment.start,
                      direction: Axis.horizontal,
                      runAlignment: WrapAlignment.start,
                      verticalDirection: VerticalDirection.down,
                      clipBehavior: Clip.none,
                      children: List.generate(options.length, (optionsIndex) {
                        final optionsItem = options[optionsIndex];
                        return Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              0.0, 0.0, 15.0, 14.0),
                          child: InkWell(
                            splashColor: Colors.transparent,
                            focusColor: Colors.transparent,
                            hoverColor: Colors.transparent,
                            highlightColor: Colors.transparent,
                            onTap: () async {
                              if (_model.result != null &&
                                  _model.result != '') {
                                if (optionsItem == _model.result) {
                                  setState(() {
                                    _model.result = null;
                                  });
                                } else {
                                  setState(() {
                                    _model.result = optionsItem;
                                  });
                                }
                              } else {
                                setState(() {
                                  _model.result = optionsItem;
                                });
                              }
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                color: const Color(0x00FFFFFF),
                                borderRadius: BorderRadius.circular(24.0),
                                border: Border.all(
                                  color: optionsItem == _model.result
                                      ? const Color(0xFFAE34E8)
                                      : const Color(0xFFB9BFC8),
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    10.0, 6.0, 10.0, 6.0),
                                child: Text(
                                  optionsItem,
                                  style: FlutterFlowTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        color: optionsItem == _model.result
                                            ? FlutterFlowTheme.of(context)
                                                .primaryText
                                            : FlutterFlowTheme.of(context)
                                                .secondaryText,
                                        fontWeight: FontWeight.w600,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                            ),
                          ),
                        );
                      }),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
