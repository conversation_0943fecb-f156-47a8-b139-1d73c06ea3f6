import '/discounts/profile_home_discount_builder/profile_home_discount_builder_widget.dart';
import '/discovery/mandatory_profile_editing/mpe_profile_home_notice/mpe_profile_home_notice_widget.dart';
import '/subscription_sale_screens/divine/divine_header_component/divine_header_component_widget.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/profile_settings/profile_home_purchases_horizontal_slider_copy/profile_home_purchases_horizontal_slider_copy_widget.dart';
import '/single_purchases/boosts_profile_home_widget_active/boosts_profile_home_widget_active_widget.dart';
import '/single_purchases/boosts_profile_home_widget_copy/boosts_profile_home_widget_copy_widget.dart';
import '/single_purchases/roses_profile_home_widget/roses_profile_home_widget_widget.dart';
import 'profile_home_widget.dart' show ProfileHomeWidget;
import 'package:flutter/material.dart';

class ProfileHomeModel extends FlutterFlowModel<ProfileHomeWidget> {
  ///  State fields for stateful widgets in this page.

  final unfocusNode = FocusNode();
  // Model for BoostsProfileHomeWidgetActive component.
  late BoostsProfileHomeWidgetActiveModel boostsProfileHomeWidgetActiveModel;
  // Model for BoostsProfileHomeWidgetCopy component.
  late BoostsProfileHomeWidgetCopyModel boostsProfileHomeWidgetCopyModel;
  // Model for RosesProfileHomeWidget component.
  late RosesProfileHomeWidgetModel rosesProfileHomeWidgetModel;
  // Model for ProfileHomeDiscountBuilder component.
  late ProfileHomeDiscountBuilderModel profileHomeDiscountBuilderModel;
  // Model for ProfileHomePurchasesHorizontalSliderCopy component.
  late ProfileHomePurchasesHorizontalSliderCopyModel
      profileHomePurchasesHorizontalSliderCopyModel;
  // Model for DivineHeaderComponent component.
  late DivineHeaderComponentModel divineHeaderComponentModel;

  late MpeProfileHomeNoticeModel mpeProfileHomeNoticeModel;


  @override
  void initState(BuildContext context) {
    boostsProfileHomeWidgetActiveModel =
        createModel(context, () => BoostsProfileHomeWidgetActiveModel());
    boostsProfileHomeWidgetCopyModel =
        createModel(context, () => BoostsProfileHomeWidgetCopyModel());
    divineHeaderComponentModel =
        createModel(context, () => DivineHeaderComponentModel());
    rosesProfileHomeWidgetModel =
        createModel(context, () => RosesProfileHomeWidgetModel());
    profileHomeDiscountBuilderModel =
        createModel(context, () => ProfileHomeDiscountBuilderModel());
    profileHomePurchasesHorizontalSliderCopyModel = createModel(
        context, () => ProfileHomePurchasesHorizontalSliderCopyModel());
    mpeProfileHomeNoticeModel = MpeProfileHomeNoticeModel();
  }

  @override
  void dispose() {
    boostsProfileHomeWidgetActiveModel.dispose();
    boostsProfileHomeWidgetCopyModel.dispose();
    divineHeaderComponentModel.dispose();
    rosesProfileHomeWidgetModel.dispose();
    profileHomeDiscountBuilderModel.dispose();
    profileHomePurchasesHorizontalSliderCopyModel.dispose();
    unfocusNode.dispose();
    mpeProfileHomeNoticeModel.dispose();
  }

  /// Action blocks are added here.

  /// Additional helper methods are added here.
}
