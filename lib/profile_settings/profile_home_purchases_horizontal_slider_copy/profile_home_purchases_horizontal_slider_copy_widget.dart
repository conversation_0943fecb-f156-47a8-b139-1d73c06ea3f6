import '/auth/firebase_auth/auth_util.dart';
import '/backend/schema/enums/enums.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button_evolve_flex/gradient_button_evolve_flex_widget.dart';
import '/general/gradient_button_flex/gradient_button_flex_widget.dart';
import '/general/gradient_button_gold_flex/gradient_button_gold_flex_widget.dart';
import '/general/gradient_button_silver_flex/gradient_button_silver_flex_widget.dart';
import '/flutter_flow/revenue_cat_util.dart' as revenue_cat;
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'profile_home_purchases_horizontal_slider_copy_model.dart';
export 'profile_home_purchases_horizontal_slider_copy_model.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:chyrpe/amplitudeConfig.dart';


class ProfileHomePurchasesHorizontalSliderCopyWidget extends StatefulWidget {
  const ProfileHomePurchasesHorizontalSliderCopyWidget({
    super.key,
    required this.purchaseBoxesList,
  });

  final List<PurchaseTypesHome>? purchaseBoxesList;

  @override
  State<ProfileHomePurchasesHorizontalSliderCopyWidget> createState() =>
      _ProfileHomePurchasesHorizontalSliderCopyWidgetState();
}

class _ProfileHomePurchasesHorizontalSliderCopyWidgetState
    extends State<ProfileHomePurchasesHorizontalSliderCopyWidget> {
  late ProfileHomePurchasesHorizontalSliderCopyModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(
        context, () => ProfileHomePurchasesHorizontalSliderCopyModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: widget.purchaseBoxesList != null &&
          (widget.purchaseBoxesList)!.isNotEmpty,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            child: Builder(
              builder: (context) {
                final purchesBoxesList = widget.purchaseBoxesList!.toList();
      
                return SizedBox(
                  width: MediaQuery.sizeOf(context).width * 1.0,
                  child: CarouselSlider.builder(
                    itemCount: purchesBoxesList.length,
                    itemBuilder: (context, purchesBoxesListIndex, _) {
                      final purchesBoxesListItem =
                          purchesBoxesList[purchesBoxesListIndex];
                      return Builder(
                        builder: (context) {
                          if (purchesBoxesListItem ==
                              PurchaseTypesHome.Standard) {
                            return Visibility(
                              visible: (currentUserDocument?.gender ==
                                      Gender.Male) &&
                                  !(revenue_cat.activeEntitlementIds
                                          .contains('paid_standard_lifetime') ||
                                      revenue_cat.activeEntitlementIds
                                          .contains('paid_standard_1w')) &&
                                  (revenue_cat.offerings!.current!.availablePackages
                                          .contains(revenue_cat.offerings!.current!
                                              .getPackage(
                                                  'chyrpe_standard_lifetime')) &&
                                      revenue_cat
                                          .offerings!.current!.availablePackages
                                          .contains(revenue_cat.offerings!.current!
                                              .getPackage('chyrpe_standard_1w'))) &&
                                  getRemoteConfigBool('profile_home_dual_upgrade_visible'),
                              child: AuthUserStreamWidget(
                                builder: (context) => Stack(
                                  children: [
                                    Container(
                                      width:
                                          MediaQuery.sizeOf(context).width * 1.0,
                                      height:
                                          MediaQuery.sizeOf(context).height * 1.0,
                                      decoration: BoxDecoration(
                                        gradient: const LinearGradient(
                                          colors: [
                                            Color(0xFF91B2FB),
                                            Color(0xFFFF9EDE)
                                          ],
                                          stops: [0.0, 1.0],
                                          begin: AlignmentDirectional(1.0, 0.0),
                                          end: AlignmentDirectional(-1.0, 0),
                                        ),
                                        borderRadius: BorderRadius.circular(9.0),
                                      ),
                                    ),
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(9.0),
                                      child: Container(
                                        decoration: BoxDecoration(
                                          gradient: LinearGradient(
                                            colors: [
                                              const Color(0x00FFFFFF),
                                              FlutterFlowTheme.of(context).info
                                            ],
                                            stops: const [0.0, 0.4],
                                            begin:
                                                const AlignmentDirectional(0.0, -1.0),
                                            end: const AlignmentDirectional(0, 1.0),
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(9.0),
                                          border: Border.all(
                                            color: const Color(0xFFBFC1C5),
                                            width: 1.0,
                                          ),
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsetsDirectional.fromSTEB(
                                              23.0, 11.0, 23.0, 20.0),
                                          child: Column(
                                            mainAxisSize: MainAxisSize.max,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceEvenly,
                                            children: [
                                              Row(
                                                mainAxisSize: MainAxisSize.max,
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  Align(
                                                    alignment:
                                                        const AlignmentDirectional(
                                                            0.0, 0.0),
                                                    child: Row(
                                                      mainAxisSize:
                                                          MainAxisSize.max,
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .center,
                                                      children: [
                                                        Text(
                                                          'chyrpe',
                                                          style: FlutterFlowTheme
                                                                  .of(context)
                                                              .bodyMedium
                                                              .override(
                                                                fontFamily:
                                                                    'BT Beau Sans',
                                                                fontSize: 28.0,
                                                                letterSpacing:
                                                                    0.0,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w600,
                                                                useGoogleFonts:
                                                                    false,
                                                              ),
                                                        ),
                                                        Padding(
                                                          padding:
                                                              const EdgeInsetsDirectional
                                                                  .fromSTEB(
                                                                      5.0,
                                                                      0.0,
                                                                      0.0,
                                                                      0.0),
                                                          child: Container(
                                                            width: 70.0,
                                                            height: 19.0,
                                                            decoration:
                                                                BoxDecoration(
                                                              gradient:
                                                                  const LinearGradient(
                                                                colors: [
                                                                  Color(
                                                                      0xFF80A6FA),
                                                                  Color(
                                                                      0xFFFF67CB)
                                                                ],
                                                                stops: [0.0, 1.0],
                                                                begin:
                                                                    AlignmentDirectional(
                                                                        1.0, 0.0),
                                                                end:
                                                                    AlignmentDirectional(
                                                                        -1.0, 0),
                                                              ),
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          100.0),
                                                            ),
                                                            child: Align(
                                                              alignment:
                                                                  const AlignmentDirectional(
                                                                      0.0, 0.0),
                                                              child: Text(
                                                                'STANDARD',
                                                                textAlign:
                                                                    TextAlign
                                                                        .center,
                                                                style: FlutterFlowTheme
                                                                        .of(context)
                                                                    .bodyMedium
                                                                    .override(
                                                                      fontFamily:
                                                                          'BT Beau Sans',
                                                                      color: FlutterFlowTheme.of(
                                                                              context)
                                                                          .info,
                                                                      fontSize:
                                                                          10.0,
                                                                      letterSpacing:
                                                                          0.0,
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .normal,
                                                                      useGoogleFonts:
                                                                          false,
                                                                    ),
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              Align(
                                                alignment: const AlignmentDirectional(
                                                    0.0, 0.0),
                                                child: Padding(
                                                  padding: const EdgeInsetsDirectional
                                                      .fromSTEB(
                                                          0.0, 8.0, 0.0, 8.0),
                                                  child: Text(
                                                    getRemoteConfigString(
                                                        'purchases_standard_new_tagline'),
                                                    textAlign: TextAlign.center,
                                                    style: FlutterFlowTheme.of(
                                                            context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          fontSize: 16.0,
                                                          letterSpacing: 0.0,
                                                          useGoogleFonts: false,
                                                        ),
                                                  ),
                                                ),
                                              ),
                                              Container(
                                                width: 220.0,
                                                decoration: const BoxDecoration(),
                                                child: GradientButtonFlexWidget(
                                                  key: Key(
                                                      'Keybht_${purchesBoxesListIndex}_of_${purchesBoxesList.length}'),
                                                  title: getRemoteConfigString(
                                                      'purchases_standard_new_btn'),
                                                  height: 45.0,
                                                  action: () async {
                                                    if (valueOrDefault<bool>(
                                                        currentUserDocument
                                                            ?.purchasesTestingNewDesign1,
                                                        false)) {
                                                          try {
                       analytics.logEvent('Navigated to Standard from Profile Home');
                       } catch(e) {}
                                                      context.pushNamed(
                                                        'ChyrpeStandardNewNew',
                                                        extra: <String, dynamic>{
                                                          kTransitionInfoKey:
                                                              const TransitionInfo(
                                                            hasTransition: true,
                                                            transitionType:
                                                                PageTransitionType
                                                                    .bottomToTop,
                                                          ),
                                                        },
                                                      );
                                                    } else {
                                                      try {
                       analytics.logEvent('Navigated to Standard from Profile Home');
                       } catch(e) {}
                                                      context.pushNamed(
                                                        'ChyrpeStandardNew',
                                                        extra: <String, dynamic>{
                                                          kTransitionInfoKey:
                                                              const TransitionInfo(
                                                            hasTransition: true,
                                                            transitionType:
                                                                PageTransitionType
                                                                    .bottomToTop,
                                                          ),
                                                        },
                                                      );
                                                    }
                                                  },
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          } else if (purchesBoxesListItem ==
                              PurchaseTypesHome.StandardLifetime) {
                            return Visibility(
                              visible: (currentUserDocument?.gender ==
                                      Gender.Male) &&
                                  !(revenue_cat.activeEntitlementIds
                                          .contains('paid_standard_lifetime')) &&
                                  (revenue_cat.offerings!.current!.availablePackages
                                          .contains(revenue_cat.offerings!.current!
                                              .getPackage(
                                                  'chyrpe_standard_lifetime')) &&
                                      revenue_cat
                                          .offerings!.current!.availablePackages
                                          .contains(revenue_cat.offerings!.current!
                                              .getPackage('chyrpe_standard_lifetime'))) &&
                                  getRemoteConfigBool('profile_home_dual_upgrade_visible'),
                              child: AuthUserStreamWidget(
                                builder: (context) => Stack(
                                  children: [
                                    Container(
                                      width:
                                          MediaQuery.sizeOf(context).width * 1.0,
                                      height:
                                          MediaQuery.sizeOf(context).height * 1.0,
                                      decoration: BoxDecoration(
                                        gradient: const LinearGradient(
                                          colors: [
                                            Color(0xFF91B2FB),
                                            Color(0xFFFF9EDE)
                                          ],
                                          stops: [0.0, 1.0],
                                          begin: AlignmentDirectional(1.0, 0.0),
                                          end: AlignmentDirectional(-1.0, 0),
                                        ),
                                        borderRadius: BorderRadius.circular(9.0),
                                      ),
                                    ),
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(9.0),
                                      child: Container(
                                        decoration: BoxDecoration(
                                          gradient: LinearGradient(
                                            colors: [
                                              const Color(0x00FFFFFF),
                                              FlutterFlowTheme.of(context).info
                                            ],
                                            stops: const [0.0, 0.4],
                                            begin:
                                                const AlignmentDirectional(0.0, -1.0),
                                            end: const AlignmentDirectional(0, 1.0),
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(9.0),
                                          border: Border.all(
                                            color: const Color(0xFFBFC1C5),
                                            width: 1.0,
                                          ),
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsetsDirectional.fromSTEB(
                                              23.0, 11.0, 23.0, 20.0),
                                          child: Column(
                                            mainAxisSize: MainAxisSize.max,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceEvenly,
                                            children: [
                                              Row(
                                                mainAxisSize: MainAxisSize.max,
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  Align(
                                                    alignment:
                                                        const AlignmentDirectional(
                                                            0.0, 0.0),
                                                    child: Row(
                                                      mainAxisSize:
                                                          MainAxisSize.max,
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .center,
                                                      children: [
                                                        Text(
                                                          'chyrpe',
                                                          style: FlutterFlowTheme
                                                                  .of(context)
                                                              .bodyMedium
                                                              .override(
                                                                fontFamily:
                                                                    'BT Beau Sans',
                                                                fontSize: 28.0,
                                                                letterSpacing:
                                                                    0.0,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w600,
                                                                useGoogleFonts:
                                                                    false,
                                                              ),
                                                        ),
                                                        Padding(
                                                          padding:
                                                              const EdgeInsetsDirectional
                                                                  .fromSTEB(
                                                                      5.0,
                                                                      0.0,
                                                                      0.0,
                                                                      0.0),
                                                          child: Container(
                                                            width: 70.0,
                                                            height: 19.0,
                                                            decoration:
                                                                BoxDecoration(
                                                              gradient:
                                                                  const LinearGradient(
                                                                colors: [
                                                                  Color(
                                                                      0xFF80A6FA),
                                                                  Color(
                                                                      0xFFFF67CB)
                                                                ],
                                                                stops: [0.0, 1.0],
                                                                begin:
                                                                    AlignmentDirectional(
                                                                        1.0, 0.0),
                                                                end:
                                                                    AlignmentDirectional(
                                                                        -1.0, 0),
                                                              ),
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          100.0),
                                                            ),
                                                            child: Align(
                                                              alignment:
                                                                  const AlignmentDirectional(
                                                                      0.0, 0.0),
                                                              child: Text(
                                                                'LIFETIME',
                                                                textAlign:
                                                                    TextAlign
                                                                        .center,
                                                                style: FlutterFlowTheme
                                                                        .of(context)
                                                                    .bodyMedium
                                                                    .override(
                                                                      fontFamily:
                                                                          'BT Beau Sans',
                                                                      color: FlutterFlowTheme.of(
                                                                              context)
                                                                          .info,
                                                                      fontSize:
                                                                          10.0,
                                                                      letterSpacing:
                                                                          0.0,
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .normal,
                                                                      useGoogleFonts:
                                                                          false,
                                                                    ),
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              Align(
                                                alignment: const AlignmentDirectional(
                                                    0.0, 0.0),
                                                child: Padding(
                                                  padding: const EdgeInsetsDirectional
                                                      .fromSTEB(
                                                          0.0, 8.0, 0.0, 8.0),
                                                  child: Text(
                                                    getRemoteConfigString(
                                                        'purchases_standard_ltu_new_tagline'),
                                                    textAlign: TextAlign.center,
                                                    style: FlutterFlowTheme.of(
                                                            context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          fontSize: 16.0,
                                                          letterSpacing: 0.0,
                                                          useGoogleFonts: false,
                                                        ),
                                                  ),
                                                ),
                                              ),
                                              Container(
                                                width: 220.0,
                                                decoration: const BoxDecoration(
                                                  color: Colors.transparent,
                                                ),
                                                child: GradientButtonFlexWidget(
                                                  key: Key(
                                                      'Keyeou_${purchesBoxesListIndex}_of_${purchesBoxesList.length}'),
                                                  title: getRemoteConfigString(
                                                      'purchases_standard_new_btn'),
                                                  height: 45.0,
                                                  action: () async {
                                                    if (valueOrDefault<bool>(
                                                        currentUserDocument
                                                            ?.purchasesTestingNewDesign1,
                                                        false)) {
                                                      context.pushNamed(
                                                        'StandardLifetimeUpgradeNew',
                                                        extra: <String, dynamic>{
                                                          kTransitionInfoKey:
                                                              const TransitionInfo(
                                                            hasTransition: true,
                                                            transitionType:
                                                                PageTransitionType
                                                                    .bottomToTop,
                                                          ),
                                                        },
                                                      );
                                                    } else {
                                                      context.pushNamed(
                                                        'StandardLifetimeUpgrade',
                                                        extra: <String, dynamic>{
                                                          kTransitionInfoKey:
                                                              const TransitionInfo(
                                                            hasTransition: true,
                                                            transitionType:
                                                                PageTransitionType
                                                                    .bottomToTop,
                                                          ),
                                                        },
                                                      );
                                                    }
                                                  },
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          } else if (purchesBoxesListItem ==
                              PurchaseTypesHome.Plus) {
                            return Visibility(
                              visible: !(revenue_cat.activeEntitlementIds
                                      .contains('plus_access') ||
                                  revenue_cat.activeEntitlementIds
                                      .contains('evolved_access')),
                              child: Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      const Color(0xFFBFC1C5),
                                      FlutterFlowTheme.of(context).info
                                    ],
                                    stops: const [0.0, 0.4],
                                    begin: const AlignmentDirectional(0.0, -1.0),
                                    end: const AlignmentDirectional(0, 1.0),
                                  ),
                                  borderRadius: BorderRadius.circular(9.0),
                                  border: Border.all(
                                    color: const Color(0xFF575757),
                                    width: 1.0,
                                  ),
                                ),
                                child: Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      23.0, 11.0, 23.0, 20.0),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceEvenly,
                                    children: [
                                      Align(
                                        alignment: const AlignmentDirectional(0.0, 0.0),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.max,
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Text(
                                              'chyrpe',
                                              style: FlutterFlowTheme.of(context)
                                                  .bodyMedium
                                                  .override(
                                                    fontFamily: 'BT Beau Sans',
                                                    fontSize: 28.0,
                                                    letterSpacing: 0.0,
                                                    fontWeight: FontWeight.w600,
                                                    useGoogleFonts: false,
                                                  ),
                                            ),
                                            Padding(
                                              padding:
                                                  const EdgeInsetsDirectional.fromSTEB(
                                                      5.0, 0.0, 0.0, 0.0),
                                              child: Container(
                                                width: 50.0,
                                                height: 19.0,
                                                decoration: BoxDecoration(
                                                  gradient: const LinearGradient(
                                                    colors: [
                                                      Color(0xFF575757),
                                                      Color(0xFFB0B0B0)
                                                    ],
                                                    stops: [0.0, 1.0],
                                                    begin: AlignmentDirectional(
                                                        1.0, 0.0),
                                                    end: AlignmentDirectional(
                                                        -1.0, 0),
                                                  ),
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          100.0),
                                                ),
                                                child: Align(
                                                  alignment: const AlignmentDirectional(
                                                      0.0, 0.0),
                                                  child: Text(
                                                    'PLUS',
                                                    textAlign: TextAlign.center,
                                                    style: FlutterFlowTheme.of(
                                                            context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          color:
                                                              FlutterFlowTheme.of(
                                                                      context)
                                                                  .info,
                                                          fontSize: 10.0,
                                                          letterSpacing: 0.0,
                                                          fontWeight:
                                                              FontWeight.normal,
                                                          useGoogleFonts: false,
                                                        ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Align(
                                        alignment: const AlignmentDirectional(0.0, 0.0),
                                        child: Padding(
                                          padding: const EdgeInsetsDirectional.fromSTEB(
                                              0.0, 8.0, 0.0, 8.0),
                                          child: Text(
                                            getRemoteConfigString(
                                                'purchases_plus_new_tagline'),
                                            textAlign: TextAlign.center,
                                            style: FlutterFlowTheme.of(context)
                                                .bodyMedium
                                                .override(
                                                  fontFamily: 'BT Beau Sans',
                                                  fontSize: 16.0,
                                                  letterSpacing: 0.0,
                                                  useGoogleFonts: false,
                                                ),
                                          ),
                                        ),
                                      ),
                                      Container(
                                        width: 220.0,
                                        decoration: const BoxDecoration(
                                          color: Color(0x00F0F2F4),
                                        ),
                                        child: GradientButtonSilverFlexWidget(
                                          key: Key(
                                              'Keyw0f_${purchesBoxesListIndex}_of_${purchesBoxesList.length}'),
                                          title: getRemoteConfigString(
                                              'purchases_plus_btn'),
                                          height: 45.0,
                                          action: () async {
                                            if (valueOrDefault<bool>(
                                                currentUserDocument
                                                    ?.purchasesTestingNewDesign1,
                                                false)) {
                                                  try {
                       analytics.logEvent('Navigated to Plus from Profile Home');
                       } catch(e) {}
                                              context.pushNamed(
                                                'PlusSubscriptionNew',
                                                extra: <String, dynamic>{
                                                  kTransitionInfoKey:
                                                      const TransitionInfo(
                                                    hasTransition: true,
                                                    transitionType:
                                                        PageTransitionType
                                                            .bottomToTop,
                                                  ),
                                                },
                                              );
                                            } else {
                                              try {
                       analytics.logEvent('Navigated to Plus from Profile Home');
                       } catch(e) {}
                                              context.pushNamed(
                                                'PlusSubscription',
                                                extra: <String, dynamic>{
                                                  kTransitionInfoKey:
                                                      const TransitionInfo(
                                                    hasTransition: true,
                                                    transitionType:
                                                        PageTransitionType
                                                            .bottomToTop,
                                                  ),
                                                },
                                              );
                                            }
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          } else if (purchesBoxesListItem ==
                              PurchaseTypesHome.Gold) {
                            return Visibility(
                              visible: !(revenue_cat.activeEntitlementIds
                                      .contains('gold_access') ||
                                  revenue_cat.activeEntitlementIds
                                      .contains('evolved_access')),
                              child: Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      const Color(0xFFFEDA73),
                                      FlutterFlowTheme.of(context).info
                                    ],
                                    stops: const [0.0, 0.4],
                                    begin: const AlignmentDirectional(0.0, -1.0),
                                    end: const AlignmentDirectional(0, 1.0),
                                  ),
                                  borderRadius: BorderRadius.circular(9.0),
                                  border: Border.all(
                                    color: const Color(0xFFBE9F04),
                                    width: 1.0,
                                  ),
                                ),
                                child: Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      23.0, 11.0, 23.0, 20.0),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceEvenly,
                                    children: [
                                      Align(
                                        alignment: const AlignmentDirectional(0.0, 0.0),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.max,
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Text(
                                              'chyrpe',
                                              style: FlutterFlowTheme.of(context)
                                                  .bodyMedium
                                                  .override(
                                                    fontFamily: 'BT Beau Sans',
                                                    fontSize: 28.0,
                                                    letterSpacing: 0.0,
                                                    fontWeight: FontWeight.w600,
                                                    useGoogleFonts: false,
                                                  ),
                                            ),
                                            Padding(
                                              padding:
                                                  const EdgeInsetsDirectional.fromSTEB(
                                                      5.0, 0.0, 0.0, 0.0),
                                              child: Container(
                                                width: 55.0,
                                                height: 19.0,
                                                decoration: BoxDecoration(
                                                  gradient: const LinearGradient(
                                                    colors: [
                                                      Color(0xFFFFCC10),
                                                      Color(0xFFBE9F04)
                                                    ],
                                                    stops: [0.0, 1.0],
                                                    begin: AlignmentDirectional(
                                                        1.0, 0.0),
                                                    end: AlignmentDirectional(
                                                        -1.0, 0),
                                                  ),
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          100.0),
                                                ),
                                                child: Align(
                                                  alignment: const AlignmentDirectional(
                                                      0.0, 0.0),
                                                  child: Text(
                                                    'GOLD',
                                                    textAlign: TextAlign.center,
                                                    style: FlutterFlowTheme.of(
                                                            context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          color:
                                                              FlutterFlowTheme.of(
                                                                      context)
                                                                  .info,
                                                          fontSize: 10.0,
                                                          letterSpacing: 0.0,
                                                          fontWeight:
                                                              FontWeight.normal,
                                                          useGoogleFonts: false,
                                                        ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Align(
                                        alignment: const AlignmentDirectional(0.0, 0.0),
                                        child: Padding(
                                          padding: const EdgeInsetsDirectional.fromSTEB(
                                              0.0, 8.0, 0.0, 8.0),
                                          child: Text(
                                            getRemoteConfigString(
                                                'purchases_gold_new_tagline'),
                                            textAlign: TextAlign.center,
                                            style: FlutterFlowTheme.of(context)
                                                .bodyMedium
                                                .override(
                                                  fontFamily: 'BT Beau Sans',
                                                  fontSize: 16.0,
                                                  letterSpacing: 0.0,
                                                  useGoogleFonts: false,
                                                ),
                                          ),
                                        ),
                                      ),
                                      Container(
                                        width: 220.0,
                                        decoration: const BoxDecoration(
                                          color: Colors.transparent,
                                        ),
                                        child: GradientButtonGoldFlexWidget(
                                          key: Key(
                                              'Keyg4q_${purchesBoxesListIndex}_of_${purchesBoxesList.length}'),
                                          title: getRemoteConfigString(
                                              'purchases_gold_btn'),
                                          height: 45.0,
                                          action: () async {
                                            if (valueOrDefault<bool>(
                                                currentUserDocument
                                                    ?.purchasesTestingNewDesign1,
                                                false)) {
                                                   try {
                       analytics.logEvent('Navigated to Gold from Profile Home');
                       } catch(e) {}
                                              context.pushNamed(
                                                'GoldSubscriptionNew',
                                                extra: <String, dynamic>{
                                                  kTransitionInfoKey:
                                                      const TransitionInfo(
                                                    hasTransition: true,
                                                    transitionType:
                                                        PageTransitionType
                                                            .bottomToTop,
                                                  ),
                                                },
                                              );
                                            } else {
                                               try {
                       analytics.logEvent('Navigated to Gold from Profile Home');
                       } catch(e) {}
                                              context.pushNamed(
                                                'GoldSubscription',
                                                extra: <String, dynamic>{
                                                  kTransitionInfoKey:
                                                      const TransitionInfo(
                                                    hasTransition: true,
                                                    transitionType:
                                                        PageTransitionType
                                                            .bottomToTop,
                                                  ),
                                                },
                                              );
                                            }
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          } else if (purchesBoxesListItem ==
                              PurchaseTypesHome.Evolved) {
                            return Visibility(
                              visible: !revenue_cat.activeEntitlementIds
                                  .contains('evolved_access'),
                              child: Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      const Color(0xFFDBAEFF),
                                      FlutterFlowTheme.of(context).info
                                    ],
                                    stops: const [0.0, 0.4],
                                    begin: const AlignmentDirectional(0.0, -1.0),
                                    end: const AlignmentDirectional(0, 1.0),
                                  ),
                                  borderRadius: BorderRadius.circular(9.0),
                                  border: Border.all(
                                    color: const Color(0xFF7611C5),
                                    width: 1.0,
                                  ),
                                ),
                                child: Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      23.0, 11.0, 23.0, 20.0),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceEvenly,
                                    children: [
                                      Row(
                                        mainAxisSize: MainAxisSize.max,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            'chyrpe',
                                            style: FlutterFlowTheme.of(context)
                                                .bodyMedium
                                                .override(
                                                  fontFamily: 'BT Beau Sans',
                                                  fontSize: 28.0,
                                                  letterSpacing: 0.0,
                                                  fontWeight: FontWeight.w600,
                                                  useGoogleFonts: false,
                                                ),
                                          ),
                                          Padding(
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(
                                                    3.0, 0.0, 0.0, 0.0),
                                            child: Container(
                                              constraints: const BoxConstraints(
                                                minWidth: 59.0,
                                                minHeight: 19.0,
                                                maxWidth: 59.0,
                                                maxHeight: 19.0,
                                              ),
                                              decoration: BoxDecoration(
                                                gradient: const LinearGradient(
                                                  colors: [
                                                    Color(0xFFA12CFD),
                                                    Color(0xFFFF6C3E)
                                                  ],
                                                  stops: [0.0, 1.0],
                                                  begin: AlignmentDirectional(
                                                      -0.17, 1.0),
                                                  end: AlignmentDirectional(
                                                      0.17, -1.0),
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(100.0),
                                              ),
                                              alignment:
                                                  const AlignmentDirectional(0.0, 0.0),
                                              child: Text(
                                                'EVOLVED',
                                                style: FlutterFlowTheme.of(
                                                        context)
                                                    .bodyMedium
                                                    .override(
                                                      fontFamily: 'BT Beau Sans',
                                                      color: FlutterFlowTheme.of(
                                                              context)
                                                          .info,
                                                      fontSize: 10.0,
                                                      letterSpacing: 0.0,
                                                      fontWeight: FontWeight.w500,
                                                      useGoogleFonts: false,
                                                    ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      Align(
                                        alignment: const AlignmentDirectional(0.0, 0.0),
                                        child: Padding(
                                          padding: const EdgeInsetsDirectional.fromSTEB(
                                              0.0, 8.0, 0.0, 8.0),
                                          child: Text(
                                            getRemoteConfigString(
                                                'purchases_evolved_new_tagline'),
                                            textAlign: TextAlign.center,
                                            style: FlutterFlowTheme.of(context)
                                                .bodyMedium
                                                .override(
                                                  fontFamily: 'BT Beau Sans',
                                                  fontSize: 16.0,
                                                  letterSpacing: 0.0,
                                                  useGoogleFonts: false,
                                                ),
                                          ),
                                        ),
                                      ),
                                      Container(
                                        width: 220.0,
                                        decoration: const BoxDecoration(
                                          color: Colors.transparent,
                                        ),
                                        child: GradientButtonEvolveFlexWidget(
                                          key: Key(
                                              'Keyrs0_${purchesBoxesListIndex}_of_${purchesBoxesList.length}'),
                                          title: getRemoteConfigString(
                                              'purchases_evolved_btn'),
                                          height: 45.0,
                                          action: () async {
                                            if (valueOrDefault<bool>(
                                                currentUserDocument
                                                    ?.purchasesTestingNewDesign1,
                                                false)) {
                                                  try {
                       analytics.logEvent('Navigated to Evolved from Profile Home');
                       } catch(e) {}
                                              context.pushNamed(
                                                'EvolvedSubscriptionNew',
                                                extra: <String, dynamic>{
                                                  kTransitionInfoKey:
                                                      const TransitionInfo(
                                                    hasTransition: true,
                                                    transitionType:
                                                        PageTransitionType
                                                            .bottomToTop,
                                                  ),
                                                },
                                              );
                                            } else {
                                              try {
                       analytics.logEvent('Navigated to Evolved from Profile Home');
                       } catch(e) {}
                                              context.pushNamed(
                                                'EvolvedSubscription',
                                                extra: <String, dynamic>{
                                                  kTransitionInfoKey:
                                                      const TransitionInfo(
                                                    hasTransition: true,
                                                    transitionType:
                                                        PageTransitionType
                                                            .bottomToTop,
                                                  ),
                                                },
                                              );
                                            }
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          } else {
                            return Container(
                              width: 100.0,
                              height: 100.0,
                              decoration: const BoxDecoration(
                                color: Colors.white,
                              ),
                            );
                          }
                        },
                      );
                    },
                    carouselController: _model.carouselController ??=
                        CarouselController(),
                    options: CarouselOptions(
                      initialPage: max(0, min(1, purchesBoxesList.length - 1)),
                      viewportFraction: 1.0,
                      disableCenter: true,
                      enlargeCenterPage: true,
                      enlargeFactor: 1.0,
                      enableInfiniteScroll: true,
                      scrollDirection: Axis.horizontal,
                      autoPlay: true,
                      autoPlayAnimationDuration: const Duration(milliseconds: 800),
                      autoPlayInterval: const Duration(milliseconds: (800 + 4000)),
                      autoPlayCurve: Curves.linear,
                      pauseAutoPlayInFiniteScroll: true,
                      onPageChanged: (index, reason) {
              setState(() {
                // Update the active index on page change
                _model.carouselCurrentIndex = index;
              });
                      }
                    ),
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 16), // Add some space between the carousel and the indicator
      
      // Here is your SmoothPageIndicator
      AnimatedSmoothIndicator(
        activeIndex: _model.carouselCurrentIndex, // Tracks the active carousel index
        count: widget.purchaseBoxesList!.toList().length, // Number of pages in the carousel
        effect: const WormEffect(
          dotHeight: 6.0,
          dotWidth: 6.0,
          activeDotColor: Colors.black54,
          dotColor: Colors.black38,
        ),
       onDotClicked: (index) {
          // Animate the carousel to the clicked dot
          _model.carouselController?.animateToPage(index);
          setState(() {
            _model.carouselCurrentIndex = index; // Update the index when a dot is clicked
          });
        },
      ),
        ],
      ),
    );
  }
}
