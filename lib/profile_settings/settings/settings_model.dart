import '/backend/backend.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/subscription_sale_screens/divine/divine_header_component/divine_header_component_widget.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/done_button_blue/done_button_blue_widget.dart';
import 'settings_widget.dart' show SettingsWidget;
import 'package:flutter/material.dart';
import '/components/matching_mode_component_widget.dart';

class SettingsModel extends FlutterFlowModel<SettingsWidget> {
  ///  Local state fields for this page.

  String genderPref = 'Men';

  List<String> genderOpt = ['Women', 'Men'];
  void addToGenderOpt(String item) => genderOpt.add(item);
  void removeFromGenderOpt(String item) => genderOpt.remove(item);
  void removeAtIndexFromGenderOpt(int index) => genderOpt.removeAt(index);
  void insertAtIndexInGenderOpt(int index, String item) =>
      genderOpt.insert(index, item);
  void updateGenderOptAtIndex(int index, Function(String) updateFn) =>
      genderOpt[index] = updateFn(genderOpt[index]);

  double distancePref = 50.1;

  List<String> kinksPref = [''];
  void addToKinksPref(String item) => kinksPref.add(item);
  void removeFromKinksPref(String item) => kinksPref.remove(item);
  void removeAtIndexFromKinksPref(int index) => kinksPref.removeAt(index);
  void insertAtIndexInKinksPref(int index, String item) =>
      kinksPref.insert(index, item);
  void updateKinksPrefAtIndex(int index, Function(String) updateFn) =>
      kinksPref[index] = updateFn(kinksPref[index]);

  ///  State fields for stateful widgets in this page.

  final unfocusNode = FocusNode();
  final formKey = GlobalKey<FormState>();
  // Stores action output result for [Cloud Function - changeMatchingStatus] action in Container widget.
  ChangeMatchingStatusCloudFunctionCallResponse? changeMatchingStatus;
  // Stores action output result for [Cloud Function - changeMatchingStatus] action in Container widget.
  ChangeMatchingStatusCloudFunctionCallResponse? changeMatchingStatus5;
  // Stores action output result for [Cloud Function - changeMatchingStatus] action in Container widget.
  ChangeMatchingStatusCloudFunctionCallResponse? changeMatchingStatus3;
  // Stores action output result for [Cloud Function - changeMatchingStatus] action in Container widget.
  ChangeMatchingStatusCloudFunctionCallResponse? changeMatchingStatus2;
  // State field(s) for SupporterBadgeSwitch widget.
  bool? supporterBadgeSwitchValue;
  // Model for DivineHeaderComponent component.
  late DivineHeaderComponentModel divineHeaderComponentModel;
  // State field(s) for RoleSwitch widget.
  bool? roleSwitchValue;
  // Stores action output result for [Firestore Query - Query a collection] action in Row widget.
  OptionsForSelectorsRecord? kinksAvailable;
  // Stores action output result for [Firestore Query - Query a collection] action in Row widget.
  OptionsForSelectorsRecord? hobbiesAvailable;
  // State field(s) for IncognitoSwitch widget.
  bool? incognitoSwitchValue;
  bool? switchValueProfessionalOut;
  bool? switchValueProfessional;
  
  bool? kinkInterested;
  bool? kinksOthersVisible;
  bool? kinksOwnVisible;

  // Stores action output result for [Cloud Function - changeIncognitoMode] action in IncognitoSwitch widget.
  ChangeIncognitoModeCloudFunctionCallResponse? changeIncognitoCf;
  // Stores action output result for [Cloud Function - changeIncognitoMode] action in IncognitoSwitch widget.
  ChangeIncognitoModeCloudFunctionCallResponse? changeIncognitoOff;
  // State field(s) for Switch widget.
  bool? switchValue1;
  // Stores action output result for [Cloud Function - changePausedMode] action in Switch widget.
  ChangePausedModeCloudFunctionCallResponse? changePausedMode1;
  // Stores action output result for [Cloud Function - changePausedMode] action in Switch widget.
  ChangePausedModeCloudFunctionCallResponse? changePausedOff1;
  // Stores action output result for [Cloud Function - generateMatches] action in Switch widget.
  GenerateMatchesCloudFunctionCallResponse? cloudFunctionuws;
  // Stores action output result for [Custom Action - fetchAndCompareContacts] action in Row widget.
  List<ContactForBlockingStruct>? contacts;
  late MatchingModeComponentModel matchingModeComponentModel;
  // Stores action output result for [Cloud Function - generateMatches] action in Row widget.
  GenerateMatchesCloudFunctionCallResponse? matches;
  // State field(s) for Switch widget.
  bool? switchValue2;
  // State field(s) for Switch widget.
  bool? switchValue3;
   // Stores action output result for [Cloud Function - syncAfterOneTimePurchase] action in Container widget.
  SyncAfterOneTimePurchaseCloudFunctionCallResponse? syncAfter1TPcf;
  // State field(s) for Switch widget.
  bool? switchValue4;
  // Stores action output result for [Cloud Function - changePausedMode] action in Switch widget.
  ChangePausedModeCloudFunctionCallResponse? changePausedMode2;
  // Stores action output result for [Cloud Function - changePausedMode] action in Switch widget.
  ChangePausedModeCloudFunctionCallResponse? changePausedModeOff2;
  // Stores action output result for [Cloud Function - generateMatches] action in Switch widget.
  GenerateMatchesCloudFunctionCallResponse? cloudFunction7z1;
  // Model for DoneButtonBlue component.
  late DoneButtonBlueModel doneButtonBlueModel;
  // State field(s) for Switch widget.
  bool? switchValue5;

  /// Initialization and disposal methods.

  @override
  void initState(BuildContext context) {
     divineHeaderComponentModel =
        createModel(context, () => DivineHeaderComponentModel());
    doneButtonBlueModel = createModel(context, () => DoneButtonBlueModel());
    matchingModeComponentModel =
        createModel(context, () => MatchingModeComponentModel());
  }

  @override
  void dispose() {
    unfocusNode.dispose();
     divineHeaderComponentModel.dispose();
    doneButtonBlueModel.dispose();
  }

  /// Action blocks are added here.

  /// Additional helper methods are added here.
}
