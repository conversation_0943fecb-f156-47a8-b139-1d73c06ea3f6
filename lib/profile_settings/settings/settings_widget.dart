import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/backend/schema/enums/enums.dart';
import '/components/general_popup_widget.dart';
import '/subscription_sale_screens/divine/divine_header_component/divine_header_component_widget.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/done_button_blue/done_button_blue_widget.dart';
import '/general/info_sheet_scrollable/info_sheet_scrollable_widget.dart';
import '/profile_settings/delete_pause_popup/delete_pause_popup_widget.dart';
import '/profile_settings/findom_question_settings/findom_question_settings_widget.dart';
import '/profile_settings/switch_free_info_popup/switch_free_info_popup_widget.dart';
import '/setup_p2/contacts/contact_sheet/contact_sheet_widget.dart';
import '/custom_code/actions/index.dart' as actions;
import '/flutter_flow/custom_functions.dart' as functions;
import '/flutter_flow/permissions_util.dart';
import '/flutter_flow/revenue_cat_util.dart' as revenue_cat;
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'settings_model.dart';
export 'settings_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';

class SettingsWidget extends StatefulWidget {
  const SettingsWidget({super.key});

  @override
  State<SettingsWidget> createState() => _SettingsWidgetState();
}

class _SettingsWidgetState extends State<SettingsWidget> {
  late SettingsModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => SettingsModel());
    logFirebaseEvent('screen_view', parameters: {'screen_name': 'Settings'});

    // On page load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      FFAppState().update(() {
        FFAppState().heightPrefLow =
            valueOrDefault(currentUserDocument?.lowerHeightReq, 80).toDouble();
        FFAppState().heightPrefHigh =
            valueOrDefault(currentUserDocument?.upperHeightReq, 220).toDouble();
        FFAppState().agePrefLow =
            valueOrDefault(currentUserDocument?.lowerAgeReq, 0);
        FFAppState().agePrefHigh =
            valueOrDefault(currentUserDocument?.upperAgeReq, 0);
        FFAppState().locationPref = valueOrDefault<double>(
          valueOrDefault(currentUserDocument?.distanceReq, 0.0),
          80.0,
        );
        FFAppState().genderPref =
            valueOrDefault(currentUserDocument?.genderReq, '');
        FFAppState().kinksReq =
            (currentUserDocument?.kinksToSeeReq.toList() ?? [])
                .toList()
                .cast<String>();
        FFAppState().interestsReq =
            (currentUserDocument?.hobbiesToSeeReq.toList() ?? [])
                .toList()
                .cast<String>();

        _model.kinkInterested = currentUserDocument?.kinkInterested;
        _model.kinksOthersVisible = currentUserDocument?.kinksOthersVisible;
        _model.kinksOwnVisible = currentUserDocument?.kinksOwnVisible;

        safeSetState(() {});
      });
    });

    _model.supporterBadgeSwitchValue =
        valueOrDefault<bool>(currentUserDocument?.supporterBadgeShown, false);
    _model.roleSwitchValue =
        valueOrDefault<bool>(currentUserDocument?.roleMatching, false);
    _model.incognitoSwitchValue =
        valueOrDefault<bool>(currentUserDocument?.incognito, false);
    _model.switchValue1 =
        valueOrDefault<bool>(currentUserDocument?.paused, false);
    _model.switchValue2 =
        valueOrDefault<bool>(currentUserDocument?.pnMa, false);
    _model.switchValue3 =
        valueOrDefault<bool>(currentUserDocument?.pnMe, false);
    _model.switchValue4 =
        valueOrDefault<bool>(currentUserDocument?.paused, false);
    _model.switchValue5 =
        valueOrDefault<bool>(currentUserDocument?.fullPaused, false);
    _model.switchValueProfessionalOut = false;
    _model.switchValueProfessional =
        valueOrDefault<bool>(currentUserDocument?.professionalsReq, false);
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  void changeKinkInterest(newValue) async {
    var updateDict = {'kinkInterested': newValue};

    _model.kinkInterested = newValue;

    safeSetState(() {});

    if (!newValue) {
      updateDict['kinksOthersVisible'] = false;
      updateDict['kinksOwnVisible'] = false;
      updateDict['kinks'] = [];

      currentUserDocument?.publicProfile?.update({
        'kinks': [],
      });
    }

    currentUserReference!.update(updateDict);

    final result = await FirebaseFunctions.instanceFor(region: 'europe-west1')
        .httpsCallable('changeKinkStatus')
        .call({});
  }

  void changeOthersKinkVisible(newValue) {
    currentUserReference!.update({'kinksOthersVisible': newValue});

    _model.kinksOthersVisible = newValue;
    safeSetState(() {});
  }

  void changeOwnKinkEnabled(newValue) async {
    var updateDict = {'kinksOwnVisible': newValue};

    _model.kinksOwnVisible = newValue;

    safeSetState(() {});

    if (!newValue) {
      updateDict['kinks'] = [];

      currentUserDocument?.publicProfile?.update({
        'kinks': [],
      });
    }

    currentUserReference!.update(updateDict);

    final result = await FirebaseFunctions.instanceFor(region: 'europe-west1')
        .httpsCallable('changeKinkStatus')
        .call({});
  
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return GestureDetector(
      onTap: () => _model.unfocusNode.canRequestFocus
          ? FocusScope.of(context).requestFocus(_model.unfocusNode)
          : FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
        appBar: AppBar(
          backgroundColor: FlutterFlowTheme.of(context).info,
          automaticallyImplyLeading: false,
          title: Align(
            alignment: const AlignmentDirectional(0.0, 0.0),
            child: Stack(
              alignment: const AlignmentDirectional(0.0, 0.0),
              children: [
                Text(
                  'Settings',
                  style: FlutterFlowTheme.of(context).headlineMedium.override(
                        fontFamily: 'Inter',
                        color: const Color(0xFF262A36),
                        fontSize: 20.0,
                        fontWeight: FontWeight.bold,
                      ),
                ),
                Align(
                  alignment: const AlignmentDirectional(1.0, 0.0),
                  child: Container(
                    width: 80.0,
                    height: 50.0,
                    decoration: const BoxDecoration(),
                    child: Align(
                      alignment: const AlignmentDirectional(1.0, 0.0),
                      child: wrapWithModel(
                        model: _model.doneButtonBlueModel,
                        updateCallback: () => setState(() {}),
                        child: DoneButtonBlueWidget(
                          action: () async {
                            await currentUserReference!.update({
                              ...createUsersRecordData(
                                lowerAgeReq: FFAppState().agePrefLow,
                                upperAgeReq: FFAppState().agePrefHigh,
                                lowerHeightReq:
                                    FFAppState().heightPrefLow.toInt(),
                                upperHeightReq:
                                    FFAppState().heightPrefHigh.toInt(),
                                distanceReq: FFAppState().locationPref,
                                genderReq: FFAppState().genderPref,
                              ),
                              ...mapToFirestore(
                                {
                                  'matchingSuggestions': FieldValue.delete(),
                                },
                              ),
                            });
                            if (valueOrDefault<bool>(
                                currentUserDocument?.signUpFinished, false)) {
                              context.goNamed(
                                'TempLoaderScreen',
                                extra: <String, dynamic>{
                                  kTransitionInfoKey: const TransitionInfo(
                                    hasTransition: true,
                                    transitionType:
                                        PageTransitionType.topToBottom,
                                    duration: Duration(milliseconds: 200),
                                  ),
                                },
                              );
                            } else {
                              context.safePop();
                            }
                          },
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          actions: const [],
          centerTitle: false,
          elevation: 0.0,
        ),
        body: SafeArea(
          top: true,
          child: Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(13.0, 0.0, 13.0, 0.0),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  Padding(
                    padding: const EdgeInsetsDirectional.fromSTEB(
                        0.0, 0.0, 0.0, 18.0),
                    child: Offstage(
                      offstage: (!(valueOrDefault<bool>(
                              currentUserDocument?.signUpFinished, false)) ||
                          ((currentUserDocument?.gender == Gender.Female) &&
                              (valueOrDefault(
                                      currentUserDocument?.fiveTestGroup, 0) >
                                  getRemoteConfigInt('divine_legacy_t')))),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          if (!revenue_cat.activeEntitlementIds
                                  .contains('evolved_access') ||
                              (revenue_cat.activeEntitlementIds
                                      .contains('evolved_access') ||
                                  (currentUserDocument?.gender ==
                                          Gender.Female) &&
                                      revenue_cat.activeEntitlementIds
                                          .contains('divine_access')))
                            Align(
                              alignment: const AlignmentDirectional(0.0, 0.0),
                              child: InkWell(
                                splashColor: Colors.transparent,
                                focusColor: Colors.transparent,
                                hoverColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                onTap: () async {
                                  if (valueOrDefault<bool>(
                                      currentUserDocument
                                          ?.purchasesTestingNewDesign1,
                                      false)) {
                                    try {
                                      analytics.logEvent(
                                          'Navigated to Evolved from Settings');
                                    } catch (e) {}
                                    context.pushNamed(
                                      'EvolvedSubscriptionNew',
                                      extra: <String, dynamic>{
                                        kTransitionInfoKey:
                                            const TransitionInfo(
                                          hasTransition: true,
                                          transitionType:
                                              PageTransitionType.bottomToTop,
                                        ),
                                      },
                                    );
                                  } else {
                                    if (valueOrDefault<bool>(
                                        currentUserDocument
                                            ?.purchasesTestingNewDesign1,
                                        false)) {
                                      try {
                                        analytics.logEvent(
                                            'Navigated to Evolved from Settings');
                                      } catch (e) {}
                                      context.pushNamed(
                                        'EvolvedSubscriptionNew',
                                        extra: <String, dynamic>{
                                          kTransitionInfoKey:
                                              const TransitionInfo(
                                            hasTransition: true,
                                            transitionType:
                                                PageTransitionType.bottomToTop,
                                          ),
                                        },
                                      );
                                    } else {
                                      try {
                                        analytics.logEvent(
                                            'Navigated to Evolved from Settings');
                                      } catch (e) {}
                                      context.pushNamed(
                                        'EvolvedSubscription',
                                        extra: <String, dynamic>{
                                          kTransitionInfoKey:
                                              const TransitionInfo(
                                            hasTransition: true,
                                            transitionType:
                                                PageTransitionType.bottomToTop,
                                          ),
                                        },
                                      );
                                    }
                                  }
                                },
                                child: Container(
                                  width: MediaQuery.sizeOf(context).width * 1.0,
                                  decoration: BoxDecoration(
                                    color: FlutterFlowTheme.of(context)
                                        .primaryBackground,
                                    borderRadius: BorderRadius.circular(8.0),
                                  ),
                                  child: Align(
                                    alignment:
                                        const AlignmentDirectional(0.0, 0.0),
                                    child: Padding(
                                      padding:
                                          const EdgeInsetsDirectional.fromSTEB(
                                              10.0, 10.0, 10.0, 10.0),
                                      child: Column(
                                        mainAxisSize: MainAxisSize.max,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Row(
                                            mainAxisSize: MainAxisSize.max,
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Text(
                                                'chyrpe',
                                                style:
                                                    FlutterFlowTheme.of(context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          fontSize: 28.0,
                                                          letterSpacing: 0.0,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          useGoogleFonts: false,
                                                        ),
                                              ),
                                              Padding(
                                                padding:
                                                    const EdgeInsetsDirectional
                                                        .fromSTEB(
                                                        3.0, 0.0, 0.0, 0.0),
                                                child: Container(
                                                  constraints:
                                                      const BoxConstraints(
                                                    minWidth: 59.0,
                                                    minHeight: 19.0,
                                                    maxWidth: 59.0,
                                                    maxHeight: 19.0,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    gradient:
                                                        const LinearGradient(
                                                      colors: [
                                                        Color(0xFFA12CFD),
                                                        Color(0xFFFF6C3E)
                                                      ],
                                                      stops: [0.0, 1.0],
                                                      begin:
                                                          AlignmentDirectional(
                                                              -0.17, 1.0),
                                                      end: AlignmentDirectional(
                                                          0.17, -1.0),
                                                    ),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            100.0),
                                                  ),
                                                  alignment:
                                                      const AlignmentDirectional(
                                                          0.0, 0.0),
                                                  child: Text(
                                                    'EVOLVED',
                                                    style: FlutterFlowTheme.of(
                                                            context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          color: FlutterFlowTheme
                                                                  .of(context)
                                                              .info,
                                                          fontSize: 10.0,
                                                          letterSpacing: 0.0,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                          useGoogleFonts: false,
                                                        ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                          Align(
                                            alignment:
                                                const AlignmentDirectional(
                                                    0.0, 0.0),
                                            child: Text(
                                              'All features and tools to find the right one',
                                              textAlign: TextAlign.center,
                                              style: FlutterFlowTheme.of(
                                                      context)
                                                  .bodyMedium
                                                  .override(
                                                    fontFamily: 'BT Beau Sans',
                                                    color:
                                                        const Color(0xFF505965),
                                                    fontSize: 15.0,
                                                    letterSpacing: 0.0,
                                                    useGoogleFonts: false,
                                                  ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          if (!(revenue_cat.activeEntitlementIds
                                  .contains('gold_access') ||
                              revenue_cat.activeEntitlementIds
                                  .contains('evolved_access') ||
                              (revenue_cat.activeEntitlementIds
                                      .contains('evolved_access') ||
                                  (currentUserDocument?.gender ==
                                          Gender.Female) &&
                                      revenue_cat.activeEntitlementIds
                                          .contains('divine_access'))))
                            Align(
                              alignment: const AlignmentDirectional(0.0, 0.0),
                              child: Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 11.0, 0.0, 0.0),
                                child: InkWell(
                                  splashColor: Colors.transparent,
                                  focusColor: Colors.transparent,
                                  hoverColor: Colors.transparent,
                                  highlightColor: Colors.transparent,
                                  onTap: () async {
                                    if (valueOrDefault<bool>(
                                        currentUserDocument
                                            ?.purchasesTestingNewDesign1,
                                        false)) {
                                      try {
                                        analytics.logEvent(
                                            'Navigated to Gold from Settings');
                                      } catch (e) {}
                                      context.pushNamed(
                                        'GoldSubscriptionNew',
                                        extra: <String, dynamic>{
                                          kTransitionInfoKey:
                                              const TransitionInfo(
                                            hasTransition: true,
                                            transitionType:
                                                PageTransitionType.bottomToTop,
                                          ),
                                        },
                                      );
                                    } else {
                                      try {
                                        analytics.logEvent(
                                            'Navigated to Gold from Settings');
                                      } catch (e) {}
                                      context.pushNamed(
                                        'GoldSubscription',
                                        extra: <String, dynamic>{
                                          kTransitionInfoKey:
                                              const TransitionInfo(
                                            hasTransition: true,
                                            transitionType:
                                                PageTransitionType.bottomToTop,
                                          ),
                                        },
                                      );
                                    }
                                  },
                                  child: Container(
                                    width:
                                        MediaQuery.sizeOf(context).width * 1.0,
                                    decoration: BoxDecoration(
                                      color: FlutterFlowTheme.of(context)
                                          .primaryBackground,
                                      borderRadius: BorderRadius.circular(8.0),
                                    ),
                                    child: Align(
                                      alignment:
                                          const AlignmentDirectional(0.0, 0.0),
                                      child: Padding(
                                        padding: const EdgeInsetsDirectional
                                            .fromSTEB(10.0, 10.0, 10.0, 10.0),
                                        child: Column(
                                          mainAxisSize: MainAxisSize.max,
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Row(
                                              mainAxisSize: MainAxisSize.max,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                Text(
                                                  'chyrpe',
                                                  style: FlutterFlowTheme.of(
                                                          context)
                                                      .bodyMedium
                                                      .override(
                                                        fontFamily:
                                                            'BT Beau Sans',
                                                        fontSize: 28.0,
                                                        letterSpacing: 0.0,
                                                        fontWeight:
                                                            FontWeight.w600,
                                                        useGoogleFonts: false,
                                                      ),
                                                ),
                                                Padding(
                                                  padding:
                                                      const EdgeInsetsDirectional
                                                          .fromSTEB(
                                                          5.0, 0.0, 0.0, 0.0),
                                                  child: Container(
                                                    width: 55.0,
                                                    height: 19.0,
                                                    decoration: BoxDecoration(
                                                      gradient:
                                                          const LinearGradient(
                                                        colors: [
                                                          Color(0xFFFFCC10),
                                                          Color(0xFFBE9F04)
                                                        ],
                                                        stops: [0.0, 1.0],
                                                        begin:
                                                            AlignmentDirectional(
                                                                1.0, 0.0),
                                                        end:
                                                            AlignmentDirectional(
                                                                -1.0, 0),
                                                      ),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              100.0),
                                                    ),
                                                    child: Align(
                                                      alignment:
                                                          const AlignmentDirectional(
                                                              0.0, 0.0),
                                                      child: Text(
                                                        'GOLD',
                                                        textAlign:
                                                            TextAlign.center,
                                                        style:
                                                            FlutterFlowTheme.of(
                                                                    context)
                                                                .bodyMedium
                                                                .override(
                                                                  fontFamily:
                                                                      'BT Beau Sans',
                                                                  color: FlutterFlowTheme.of(
                                                                          context)
                                                                      .info,
                                                                  fontSize:
                                                                      10.0,
                                                                  letterSpacing:
                                                                      0.0,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .normal,
                                                                  useGoogleFonts:
                                                                      false,
                                                                ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                            Align(
                                              alignment:
                                                  const AlignmentDirectional(
                                                      0.0, 0.0),
                                              child: Text(
                                                'See who likes you and send extra likes',
                                                textAlign: TextAlign.center,
                                                style:
                                                    FlutterFlowTheme.of(context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          color: const Color(
                                                              0xFF505965),
                                                          fontSize: 15.0,
                                                          letterSpacing: 0.0,
                                                          useGoogleFonts: false,
                                                        ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          if (!(revenue_cat.activeEntitlementIds
                                      .contains('plus_access') ||
                                  revenue_cat.activeEntitlementIds
                                      .contains('evolved_access')) ||
                              (revenue_cat.activeEntitlementIds
                                      .contains('evolved_access') ||
                                  (currentUserDocument?.gender ==
                                          Gender.Female) &&
                                      revenue_cat.activeEntitlementIds
                                          .contains('divine_access')))
                            Align(
                              alignment: const AlignmentDirectional(0.0, 0.0),
                              child: Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 11.0, 0.0, 0.0),
                                child: InkWell(
                                  splashColor: Colors.transparent,
                                  focusColor: Colors.transparent,
                                  hoverColor: Colors.transparent,
                                  highlightColor: Colors.transparent,
                                  onTap: () async {
                                    if (valueOrDefault<bool>(
                                        currentUserDocument
                                            ?.purchasesTestingNewDesign1,
                                        false)) {
                                      try {
                                        analytics.logEvent(
                                            'Navigated to Plus from Settings');
                                      } catch (e) {}
                                      context.pushNamed(
                                        'PlusSubscriptionNew',
                                        extra: <String, dynamic>{
                                          kTransitionInfoKey:
                                              const TransitionInfo(
                                            hasTransition: true,
                                            transitionType:
                                                PageTransitionType.bottomToTop,
                                          ),
                                        },
                                      );
                                    } else {
                                      try {
                                        analytics.logEvent(
                                            'Navigated to Plus from Settings');
                                      } catch (e) {}
                                      context.pushNamed(
                                        'PlusSubscription',
                                        extra: <String, dynamic>{
                                          kTransitionInfoKey:
                                              const TransitionInfo(
                                            hasTransition: true,
                                            transitionType:
                                                PageTransitionType.bottomToTop,
                                          ),
                                        },
                                      );
                                    }
                                  },
                                  child: Container(
                                    width:
                                        MediaQuery.sizeOf(context).width * 1.0,
                                    decoration: BoxDecoration(
                                      color: FlutterFlowTheme.of(context)
                                          .primaryBackground,
                                      borderRadius: BorderRadius.circular(8.0),
                                    ),
                                    child: Align(
                                      alignment:
                                          const AlignmentDirectional(0.0, 0.0),
                                      child: Padding(
                                        padding: const EdgeInsetsDirectional
                                            .fromSTEB(10.0, 10.0, 10.0, 10.0),
                                        child: Column(
                                          mainAxisSize: MainAxisSize.max,
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Align(
                                              alignment:
                                                  const AlignmentDirectional(
                                                      0.0, 0.0),
                                              child: Row(
                                                mainAxisSize: MainAxisSize.max,
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  Text(
                                                    'chyrpe',
                                                    style: FlutterFlowTheme.of(
                                                            context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          fontSize: 28.0,
                                                          letterSpacing: 0.0,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          useGoogleFonts: false,
                                                        ),
                                                  ),
                                                  Padding(
                                                    padding:
                                                        const EdgeInsetsDirectional
                                                            .fromSTEB(
                                                            5.0, 0.0, 0.0, 0.0),
                                                    child: Container(
                                                      width: 50.0,
                                                      height: 19.0,
                                                      decoration: BoxDecoration(
                                                        gradient:
                                                            const LinearGradient(
                                                          colors: [
                                                            Color(0xFF575757),
                                                            Color(0xFFB0B0B0)
                                                          ],
                                                          stops: [0.0, 1.0],
                                                          begin:
                                                              AlignmentDirectional(
                                                                  1.0, 0.0),
                                                          end:
                                                              AlignmentDirectional(
                                                                  -1.0, 0),
                                                        ),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(
                                                                    100.0),
                                                      ),
                                                      child: Align(
                                                        alignment:
                                                            const AlignmentDirectional(
                                                                0.0, 0.0),
                                                        child: Text(
                                                          'PLUS',
                                                          textAlign:
                                                              TextAlign.center,
                                                          style: FlutterFlowTheme
                                                                  .of(context)
                                                              .bodyMedium
                                                              .override(
                                                                fontFamily:
                                                                    'BT Beau Sans',
                                                                color: FlutterFlowTheme.of(
                                                                        context)
                                                                    .info,
                                                                fontSize: 10.0,
                                                                letterSpacing:
                                                                    0.0,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .normal,
                                                                useGoogleFonts:
                                                                    false,
                                                              ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Align(
                                              alignment:
                                                  const AlignmentDirectional(
                                                      0.0, 0.0),
                                              child: Text(
                                                'Stay private and see profile feedback',
                                                textAlign: TextAlign.center,
                                                style:
                                                    FlutterFlowTheme.of(context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          color: const Color(
                                                              0xFF505965),
                                                          fontSize: 15.0,
                                                          letterSpacing: 0.0,
                                                          useGoogleFonts: false,
                                                        ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                  if ((currentUserDocument?.gender == Gender.Female) &&
                      (valueOrDefault<bool>(
                          currentUserDocument?.signUpFinished, false)) &&
                      (valueOrDefault(currentUserDocument?.fiveTestGroup, 0) >
                          getRemoteConfigInt('divine_legacy_t')) &&
                      !revenue_cat.activeEntitlementIds
                          .contains('divine_access'))
                    Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(
                          0.0, 0.0, 0.0, 18.0),
                      child: AuthUserStreamWidget(
                        builder: (context) => wrapWithModel(
                          model: _model.divineHeaderComponentModel,
                          updateCallback: () => safeSetState(() {}),
                          child: const DivineHeaderComponentWidget(
                            clickable: true,
                          ),
                        ),
                      ),
                    ),
                  Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Form(
                        key: _model.formKey,
                        autovalidateMode: AutovalidateMode.disabled,
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 0.0, 0.0, 25.0),
                              child: Column(
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  Align(
                                    alignment:
                                        const AlignmentDirectional(-1.0, -1.0),
                                    child: Padding(
                                      padding:
                                          const EdgeInsetsDirectional.fromSTEB(
                                              0.0, 0.0, 0.0, 9.0),
                                      child: Text(
                                        'Account Settings',
                                        style: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .override(
                                              fontFamily: 'BT Beau Sans',
                                              fontSize: 16.0,
                                              fontWeight: FontWeight.w600,
                                              useGoogleFonts: false,
                                            ),
                                      ),
                                    ),
                                  ),
                                  Column(
                                    mainAxisSize: MainAxisSize.max,
                                    children: [
                                      if (currentPhoneNumber != '')
                                        Padding(
                                          padding: const EdgeInsetsDirectional
                                              .fromSTEB(0.0, 0.0, 0.0, 5.0),
                                          child: AuthUserStreamWidget(
                                            builder: (context) => InkWell(
                                              splashColor: Colors.transparent,
                                              focusColor: Colors.transparent,
                                              hoverColor: Colors.transparent,
                                              highlightColor:
                                                  Colors.transparent,
                                              onTap: () async {
                                                context.pushNamed(
                                                    'UpdatePhoneNumber');
                                              },
                                              child: Container(
                                                height: 45.0,
                                                decoration: BoxDecoration(
                                                  color: FlutterFlowTheme.of(
                                                          context)
                                                      .primaryBackground,
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          10.0),
                                                ),
                                                child: Padding(
                                                  padding:
                                                      const EdgeInsetsDirectional
                                                          .fromSTEB(
                                                          16.0, 0.0, 0.0, 0.0),
                                                  child: Row(
                                                    mainAxisSize:
                                                        MainAxisSize.max,
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      Text(
                                                        'Phone number',
                                                        style: FlutterFlowTheme
                                                                .of(context)
                                                            .bodyMedium
                                                            .override(
                                                              fontFamily:
                                                                  'BT Beau Sans',
                                                              fontSize: 16.0,
                                                              letterSpacing:
                                                                  0.0,
                                                              useGoogleFonts:
                                                                  false,
                                                            ),
                                                      ),
                                                      Flexible(
                                                        child: Padding(
                                                          padding:
                                                              const EdgeInsetsDirectional
                                                                  .fromSTEB(
                                                                  0.0,
                                                                  0.0,
                                                                  10.0,
                                                                  0.0),
                                                          child: Text(
                                                            currentPhoneNumber,
                                                            style: FlutterFlowTheme
                                                                    .of(context)
                                                                .bodyMedium
                                                                .override(
                                                                  fontFamily:
                                                                      'BT Beau Sans',
                                                                  color: const Color(
                                                                      0xFF4F5865),
                                                                  fontSize:
                                                                      16.0,
                                                                  letterSpacing:
                                                                      0.0,
                                                                  useGoogleFonts:
                                                                      false,
                                                                ),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      if (currentPhoneNumber != '')
                                        Padding(
                                          padding: const EdgeInsetsDirectional
                                              .fromSTEB(0.0, 0.0, 0.0, 5.0),
                                          child: AuthUserStreamWidget(
                                            builder: (context) => InkWell(
                                              splashColor: Colors.transparent,
                                              focusColor: Colors.transparent,
                                              hoverColor: Colors.transparent,
                                              highlightColor:
                                                  Colors.transparent,
                                              onTap: () async {
                                                context.pushNamed(
                                                    'EmailAddressChange');
                                              },
                                              child: Container(
                                                height: 45.0,
                                                decoration: BoxDecoration(
                                                  color: FlutterFlowTheme.of(
                                                          context)
                                                      .primaryBackground,
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          10.0),
                                                ),
                                                child: Padding(
                                                  padding:
                                                      const EdgeInsetsDirectional
                                                          .fromSTEB(
                                                          16.0, 0.0, 0.0, 0.0),
                                                  child: Row(
                                                    mainAxisSize:
                                                        MainAxisSize.max,
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      Text(
                                                        'Email address',
                                                        style: FlutterFlowTheme
                                                                .of(context)
                                                            .bodyMedium
                                                            .override(
                                                              fontFamily:
                                                                  'BT Beau Sans',
                                                              fontSize: 16.0,
                                                              letterSpacing:
                                                                  0.0,
                                                              useGoogleFonts:
                                                                  false,
                                                            ),
                                                      ),
                                                      Flexible(
                                                        child: Padding(
                                                          padding:
                                                              const EdgeInsetsDirectional
                                                                  .fromSTEB(
                                                                  10.0,
                                                                  0.0,
                                                                  10.0,
                                                                  0.0),
                                                          child: Text(
                                                            currentUserEmail
                                                                .maybeHandleOverflow(
                                                              maxChars: 20,
                                                              replacement: '…',
                                                            ),
                                                            maxLines: 1,
                                                            style: FlutterFlowTheme
                                                                    .of(context)
                                                                .bodyMedium
                                                                .override(
                                                                  fontFamily:
                                                                      'BT Beau Sans',
                                                                  color: const Color(
                                                                      0xFF4F5865),
                                                                  fontSize:
                                                                      16.0,
                                                                  letterSpacing:
                                                                      0.0,
                                                                  useGoogleFonts:
                                                                      false,
                                                                ),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                  if (currentUserDocument?.gender ==
                                      Gender.Female)
                                    AuthUserStreamWidget(
                                      builder: (context) => Column(
                                        mainAxisSize: MainAxisSize.max,
                                        children: [
                                          Padding(
                                            padding: const EdgeInsetsDirectional
                                                .fromSTEB(0.0, 0.0, 0.0, 5.0),
                                            child: Container(
                                              height: 45.0,
                                              decoration: BoxDecoration(
                                                color:
                                                    FlutterFlowTheme.of(context)
                                                        .primaryBackground,
                                                borderRadius:
                                                    BorderRadius.circular(10.0),
                                              ),
                                              child: Builder(
                                                builder: (context) => Padding(
                                                  padding:
                                                      const EdgeInsetsDirectional
                                                          .fromSTEB(
                                                          16.0, 0.0, 0.0, 0.0),
                                                  child: InkWell(
                                                    splashColor:
                                                        Colors.transparent,
                                                    focusColor:
                                                        Colors.transparent,
                                                    hoverColor:
                                                        Colors.transparent,
                                                    highlightColor:
                                                        Colors.transparent,
                                                    onTap: () async {
                                                      await showDialog(
                                                        context: context,
                                                        builder:
                                                            (dialogContext) {
                                                          return Dialog(
                                                            elevation: 0,
                                                            insetPadding:
                                                                EdgeInsets.zero,
                                                            backgroundColor:
                                                                Colors
                                                                    .transparent,
                                                            alignment: const AlignmentDirectional(
                                                                    0.0, 0.0)
                                                                .resolve(
                                                                    Directionality.of(
                                                                        context)),
                                                            child:
                                                                GestureDetector(
                                                              onTap: () => _model
                                                                      .unfocusNode
                                                                      .canRequestFocus
                                                                  ? FocusScope.of(
                                                                          context)
                                                                      .requestFocus(
                                                                          _model
                                                                              .unfocusNode)
                                                                  : FocusScope.of(
                                                                          context)
                                                                      .unfocus(),
                                                              child:
                                                                  const FindomQuestionSettingsWidget(),
                                                            ),
                                                          );
                                                        },
                                                      ).then((value) =>
                                                          setState(() {}));
                                                    },
                                                    child: Row(
                                                      mainAxisSize:
                                                          MainAxisSize.max,
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceBetween,
                                                      children: [
                                                        Text(
                                                          'Findomme',
                                                          style: FlutterFlowTheme
                                                                  .of(context)
                                                              .bodyMedium
                                                              .override(
                                                                fontFamily:
                                                                    'BT Beau Sans',
                                                                fontSize: 16.0,
                                                                useGoogleFonts:
                                                                    false,
                                                              ),
                                                        ),
                                                        Padding(
                                                          padding:
                                                              const EdgeInsetsDirectional
                                                                  .fromSTEB(
                                                                  0.0,
                                                                  0.0,
                                                                  16.0,
                                                                  0.0),
                                                          child: Row(
                                                            mainAxisSize:
                                                                MainAxisSize
                                                                    .max,
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .end,
                                                            children: [
                                                              Builder(
                                                                builder:
                                                                    (context) =>
                                                                        InkWell(
                                                                  splashColor:
                                                                      Colors
                                                                          .transparent,
                                                                  focusColor: Colors
                                                                      .transparent,
                                                                  hoverColor: Colors
                                                                      .transparent,
                                                                  highlightColor:
                                                                      Colors
                                                                          .transparent,
                                                                  onTap:
                                                                      () async {
                                                                    await showDialog(
                                                                      context:
                                                                          context,
                                                                      builder:
                                                                          (dialogContext) {
                                                                        return Dialog(
                                                                          elevation:
                                                                              0,
                                                                          insetPadding:
                                                                              EdgeInsets.zero,
                                                                          backgroundColor:
                                                                              Colors.transparent,
                                                                          alignment:
                                                                              const AlignmentDirectional(0.0, 0.0).resolve(Directionality.of(context)),
                                                                          child:
                                                                              GestureDetector(
                                                                            onTap: () => _model.unfocusNode.canRequestFocus
                                                                                ? FocusScope.of(context).requestFocus(_model.unfocusNode)
                                                                                : FocusScope.of(context).unfocus(),
                                                                            child:
                                                                                const FindomQuestionSettingsWidget(),
                                                                          ),
                                                                        );
                                                                      },
                                                                    ).then((value) =>
                                                                        setState(
                                                                            () {}));
                                                                  },
                                                                  child: Row(
                                                                    mainAxisSize:
                                                                        MainAxisSize
                                                                            .max,
                                                                    mainAxisAlignment:
                                                                        MainAxisAlignment
                                                                            .end,
                                                                    children: [
                                                                      Padding(
                                                                        padding: const EdgeInsetsDirectional
                                                                            .fromSTEB(
                                                                            0.0,
                                                                            0.0,
                                                                            10.0,
                                                                            0.0),
                                                                        child:
                                                                            Text(
                                                                          valueOrDefault<bool>(currentUserDocument?.findom, false)
                                                                              ? 'Yes'
                                                                              : 'No',
                                                                          style: FlutterFlowTheme.of(context)
                                                                              .bodyMedium
                                                                              .override(
                                                                                fontFamily: 'BT Beau Sans',
                                                                                color: const Color(0xFF4F5865),
                                                                                fontSize: 16.0,
                                                                                useGoogleFonts: false,
                                                                              ),
                                                                        ),
                                                                      ),
                                                                      const FaIcon(
                                                                        FontAwesomeIcons
                                                                            .angleRight,
                                                                        color: Color(
                                                                            0xFFB9BFC8),
                                                                        size:
                                                                            18.0,
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  Padding(
                                    padding:
                                        const EdgeInsetsDirectional.fromSTEB(
                                            0.0, 9.0, 0.0, 0.0),
                                    child: Container(
                                      height: 45.0,
                                      decoration: BoxDecoration(
                                        color: FlutterFlowTheme.of(context)
                                            .primaryBackground,
                                        borderRadius:
                                            BorderRadius.circular(10.0),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsetsDirectional
                                            .fromSTEB(16.0, 0.0, 16.0, 0.0),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.max,
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text(
                                              'Use imperial units',
                                              style:
                                                  FlutterFlowTheme.of(context)
                                                      .bodyMedium
                                                      .override(
                                                        fontFamily:
                                                            'BT Beau Sans',
                                                        fontSize: 16.0,
                                                        useGoogleFonts: false,
                                                      ),
                                            ),
                                            Builder(
                                              builder: (context) =>
                                                  AuthUserStreamWidget(
                                                builder: (context) =>
                                                    Switch.adaptive(
                                                  value: valueOrDefault<bool>(
                                                      currentUserDocument
                                                          ?.imperialUnits,
                                                      false),
                                                  onChanged: (newValue) async {
                                                    if (newValue) {
                                                      await currentUserReference!
                                                          .update(
                                                        createUsersRecordData(
                                                          imperialUnits: true,
                                                        ),
                                                      );
                                                      setState(() {});
                                                    } else {
                                                      await currentUserReference!
                                                          .update(
                                                              createUsersRecordData(
                                                        imperialUnits: false,
                                                      ));
                                                    }
                                                  },
                                                  activeColor:
                                                      const Color(0xFFAE34E8),
                                                  activeTrackColor:
                                                      FlutterFlowTheme.of(
                                                              context)
                                                          .accent1,
                                                  inactiveTrackColor:
                                                      FlutterFlowTheme.of(
                                                              context)
                                                          .alternate,
                                                  inactiveThumbColor:
                                                      FlutterFlowTheme.of(
                                                              context)
                                                          .secondaryText,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  // Padding(
                  //   padding: EdgeInsetsDirectional.fromSTEB(
                  //       0.0, 0.0, 0.0, 5.0),
                  //   child: AuthUserStreamWidget(
                  //     builder: (context) => StreamBuilder<
                  //         List<OpenRegionsRecord>>(
                  //       stream: queryOpenRegionsRecord(
                  //         queryBuilder: (openRegionsRecord) =>
                  //             openRegionsRecord.where(
                  //           'name',
                  //           isEqualTo: valueOrDefault(
                  //               currentUserDocument?.wlRegion,
                  //               ''),
                  //         ),
                  //         singleRecord: true,
                  //       ),
                  //       builder: (context, snapshot) {
                  //         // Customize what your widget looks like when it's loading.
                  //         if (!snapshot.hasData) {
                  //           return Center(
                  //             child: SizedBox(
                  //               width: 50.0,
                  //               height: 50.0,
                  //               child:
                  //                   CircularProgressIndicator(
                  //                 valueColor:
                  //                     AlwaysStoppedAnimation<
                  //                         Color>(
                  //                   FlutterFlowTheme.of(context)
                  //                       .accent2,
                  //                 ),
                  //               ),
                  //             ),
                  //           );
                  //         }
                  //         List<OpenRegionsRecord>
                  //             containerOpenRegionsRecordList =
                  //             snapshot.data!;
                  //         final containerOpenRegionsRecord =
                  //             containerOpenRegionsRecordList
                  //                     .isNotEmpty
                  //                 ? containerOpenRegionsRecordList
                  //                     .first
                  //                 : null;

                  //         return Container(
                  //           width: double.infinity,
                  //           height: 60.0,
                  //           decoration: BoxDecoration(
                  //             color:
                  //                 FlutterFlowTheme.of(context)
                  //                     .primaryBackground,
                  //             borderRadius:
                  //                 BorderRadius.circular(10.0),
                  //           ),
                  //           child: Stack(
                  //             children: [
                  //               Padding(
                  //                 padding: EdgeInsetsDirectional
                  //                     .fromSTEB(16.0, 15.0,
                  //                         16.0, 15.0),
                  //                 child: Column(
                  //                   mainAxisSize:
                  //                       MainAxisSize.max,
                  //                   children: [
                  //                     Builder(
                  //                         builder: (context) {
                  //                       if ((currentUserDocument
                  //                                   ?.gender ==
                  //                               Gender.Male) &&
                  //                           valueOrDefault<
                  //                                   bool>(
                  //                               currentUserDocument
                  //                                   ?.signUpFinished,
                  //                               false) &&
                  //                           !revenue_cat
                  //                               .activeEntitlementIds
                  //                               .contains(
                  //                                   'paid_standard_1w') &&
                  //                           !revenue_cat
                  //                               .activeEntitlementIds
                  //                               .contains(
                  //                                   'paid_standard_lifetime') &&
                  //                           !revenue_cat
                  //                               .activeEntitlementIds
                  //                               .contains(
                  //                                   'gold_access') &&
                  //                           !revenue_cat
                  //                               .activeEntitlementIds
                  //                               .contains(
                  //                                   'evolved_access')) {
                  //                         return InkWell(
                  //                           splashColor: Colors
                  //                               .transparent,
                  //                           focusColor: Colors
                  //                               .transparent,
                  //                           hoverColor: Colors
                  //                               .transparent,
                  //                           highlightColor:
                  //                               Colors
                  //                                   .transparent,
                  //                           onTap: () async {
                  //                             analytics.logEvent(
                  //                                 'Navigated to Standard from Settings');
                  //                             await action_blocks
                  //                                 .goStandard(
                  //                                     context);
                  //                             safeSetState(
                  //                                 () {});
                  //                           },
                  //                           child: Row(
                  //                             mainAxisSize:
                  //                                 MainAxisSize
                  //                                     .max,
                  //                             mainAxisAlignment:
                  //                                 MainAxisAlignment
                  //                                     .spaceBetween,
                  //                             children: [
                  //                               Row(
                  //                                 mainAxisSize:
                  //                                     MainAxisSize
                  //                                         .max,
                  //                                 children: [
                  //                                   Text(
                  //                                     'Matching Mode',
                  //                                     style: FlutterFlowTheme.of(
                  //                                             context)
                  //                                         .bodyMedium
                  //                                         .override(
                  //                                           fontFamily:
                  //                                               'BT Beau Sans',
                  //                                           color:
                  //                                               FlutterFlowTheme.of(context).secondaryText,
                  //                                           fontSize:
                  //                                               16.0,
                  //                                           letterSpacing:
                  //                                               0.0,
                  //                                           useGoogleFonts:
                  //                                               false,
                  //                                         ),
                  //                                   ),
                  //                                   Padding(
                  //                                     padding: EdgeInsetsDirectional.fromSTEB(
                  //                                         3.0,
                  //                                         0.0,
                  //                                         0.0,
                  //                                         0.0),
                  //                                     child:
                  //                                         Icon(
                  //                                       Icons
                  //                                           .lock_outline_rounded,
                  //                                       color: FlutterFlowTheme.of(context)
                  //                                           .primaryText,
                  //                                       size:
                  //                                           24.0,
                  //                                     ),
                  //                                   ),
                  //                                 ],
                  //                               ),
                  //                               Builder(
                  //                                 builder:
                  //                                     (context) {
                  //                                   if (valueOrDefault<
                  //                                           bool>(
                  //                                       currentUserDocument
                  //                                           ?.localMatching,
                  //                                       false)) {
                  //                                     return Row(
                  //                                       mainAxisSize:
                  //                                           MainAxisSize.max,
                  //                                       children: [
                  //                                         Container(
                  //                                           width:
                  //                                               153.0,
                  //                                           height:
                  //                                               30.0,
                  //                                           decoration:
                  //                                               BoxDecoration(
                  //                                             color: FlutterFlowTheme.of(context).secondaryBackground,
                  //                                             borderRadius: BorderRadius.circular(24.0),
                  //                                           ),
                  //                                           child:
                  //                                               Stack(
                  //                                             alignment: AlignmentDirectional(0.0, 0.0),
                  //                                             children: [
                  //                                               Row(
                  //                                                 mainAxisSize: MainAxisSize.max,
                  //                                                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //                                                 children: [
                  //                                                   Container(
                  //                                                     width: 69.0,
                  //                                                     height: 34.0,
                  //                                                     decoration: BoxDecoration(
                  //                                                       borderRadius: BorderRadius.circular(100.0),
                  //                                                     ),
                  //                                                     child: Align(
                  //                                                       alignment: AlignmentDirectional(0.0, 0.0),
                  //                                                       child: Text(
                  //                                                         'GLOBAL',
                  //                                                         textAlign: TextAlign.center,
                  //                                                         style: FlutterFlowTheme.of(context).bodyMedium.override(
                  //                                                               fontFamily: 'BT Beau Sans',
                  //                                                               color: FlutterFlowTheme.of(context).primaryText,
                  //                                                               fontSize: 12.0,
                  //                                                               letterSpacing: 0.0,
                  //                                                               fontWeight: FontWeight.normal,
                  //                                                               useGoogleFonts: false,
                  //                                                             ),
                  //                                                       ),
                  //                                                     ),
                  //                                                   ),
                  //                                                   Container(
                  //                                                     width: 84.0,
                  //                                                     height: 34.0,
                  //                                                     decoration: BoxDecoration(
                  //                                                       color: Color(0xFF99A0A5),
                  //                                                       borderRadius: BorderRadius.circular(100.0),
                  //                                                     ),
                  //                                                     child: Align(
                  //                                                       alignment: AlignmentDirectional(0.0, 0.0),
                  //                                                       child: Text(
                  //                                                         'REGIONAL',
                  //                                                         textAlign: TextAlign.center,
                  //                                                         style: FlutterFlowTheme.of(context).bodyMedium.override(
                  //                                                               fontFamily: 'BT Beau Sans',
                  //                                                               color: FlutterFlowTheme.of(context).info,
                  //                                                               fontSize: 12.0,
                  //                                                               letterSpacing: 0.0,
                  //                                                               fontWeight: FontWeight.normal,
                  //                                                               useGoogleFonts: false,
                  //                                                             ),
                  //                                                       ),
                  //                                                     ),
                  //                                                   ),
                  //                                                 ],
                  //                                               ),
                  //                                             ],
                  //                                           ),
                  //                                         ),
                  //                                       ],
                  //                                     );
                  //                                   } else {
                  //                                     return Container(
                  //                                       width:
                  //                                           153.0,
                  //                                       height:
                  //                                           30.0,
                  //                                       decoration:
                  //                                           BoxDecoration(
                  //                                         color:
                  //                                             FlutterFlowTheme.of(context).secondaryBackground,
                  //                                         borderRadius:
                  //                                             BorderRadius.circular(24.0),
                  //                                       ),
                  //                                       child:
                  //                                           Stack(
                  //                                         alignment: AlignmentDirectional(
                  //                                             0.0,
                  //                                             0.0),
                  //                                         children: [
                  //                                           Row(
                  //                                             mainAxisSize: MainAxisSize.max,
                  //                                             mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //                                             children: [
                  //                                               Container(
                  //                                                 width: 69.0,
                  //                                                 height: 34.0,
                  //                                                 decoration: BoxDecoration(
                  //                                                   color: Color(0xFF99A0A5),
                  //                                                   borderRadius: BorderRadius.circular(100.0),
                  //                                                 ),
                  //                                                 child: Align(
                  //                                                   alignment: AlignmentDirectional(0.0, 0.0),
                  //                                                   child: Text(
                  //                                                     'GLOBAL',
                  //                                                     textAlign: TextAlign.center,
                  //                                                     style: FlutterFlowTheme.of(context).bodyMedium.override(
                  //                                                           fontFamily: 'BT Beau Sans',
                  //                                                           color: FlutterFlowTheme.of(context).info,
                  //                                                           fontSize: 12.0,
                  //                                                           letterSpacing: 0.0,
                  //                                                           fontWeight: FontWeight.normal,
                  //                                                           useGoogleFonts: false,
                  //                                                         ),
                  //                                                   ),
                  //                                                 ),
                  //                                               ),
                  //                                               Container(
                  //                                                 width: 84.0,
                  //                                                 height: 34.0,
                  //                                                 decoration: BoxDecoration(
                  //                                                   borderRadius: BorderRadius.circular(100.0),
                  //                                                 ),
                  //                                                 child: Align(
                  //                                                   alignment: AlignmentDirectional(0.0, 0.0),
                  //                                                   child: Text(
                  //                                                     'REGIONAL',
                  //                                                     textAlign: TextAlign.center,
                  //                                                     style: FlutterFlowTheme.of(context).bodyMedium.override(
                  //                                                           fontFamily: 'BT Beau Sans',
                  //                                                           color: FlutterFlowTheme.of(context).secondaryText,
                  //                                                           fontSize: 12.0,
                  //                                                           letterSpacing: 0.0,
                  //                                                           fontWeight: FontWeight.normal,
                  //                                                           useGoogleFonts: false,
                  //                                                         ),
                  //                                                   ),
                  //                                                 ),
                  //                                               ),
                  //                                             ],
                  //                                           ),
                  //                                         ],
                  //                                       ),
                  //                                     );
                  //                                   }
                  //                                 },
                  //                               ),
                  //                             ],
                  //                           ),
                  //                         );
                  //                       } else {
                  //                         return Row(
                  //                             mainAxisSize:
                  //                                 MainAxisSize
                  //                                     .max,
                  //                             mainAxisAlignment:
                  //                                 MainAxisAlignment
                  //                                     .spaceBetween,
                  //                             children: [
                  //                               Align(
                  //                                 alignment:
                  //                                     AlignmentDirectional(
                  //                                         -1.0,
                  //                                         0.0),
                  //                                 child: Text(
                  //                                   'Matching Mode',
                  //                                   style: FlutterFlowTheme.of(
                  //                                           context)
                  //                                       .bodyMedium
                  //                                       .override(
                  //                                         fontFamily:
                  //                                             'BT Beau Sans',
                  //                                         fontSize:
                  //                                             16.0,
                  //                                         letterSpacing:
                  //                                             0.0,
                  //                                         useGoogleFonts:
                  //                                             false,
                  //                                       ),
                  //                                 ),
                  //                               ),
                  //                               Builder(
                  //                                 builder:
                  //                                     (context) {
                  //                                   if (valueOrDefault<bool>(
                  //                                           currentUserDocument
                  //                                               ?.noGlobalForLocal,
                  //                                           false) ||
                  //                                       ((currentUserDocument?.gender == Gender.Male) &&
                  //                                           getRemoteConfigBool(
                  //                                               'noGlobalMatchingForLocalMatchersM') &&
                  //                                           valueOrDefault<bool>(
                  //                                               currentUserDocument
                  //                                                   ?.localWlAdmitted,
                  //                                               false)) ||
                  //                                       ((currentUserDocument?.gender == Gender.Female) &&
                  //                                           getRemoteConfigBool(
                  //                                               'noGlobalMatchingForLocalMatchersW') &&
                  //                                           (containerOpenRegionsRecord !=
                  //                                               null))) {
                  //                                     return Row(
                  //                                       mainAxisSize:
                  //                                           MainAxisSize.max,
                  //                                       children: [
                  //                                         Container(
                  //                                           width:
                  //                                               153.0,
                  //                                           height:
                  //                                               30.0,
                  //                                           decoration:
                  //                                               BoxDecoration(
                  //                                             color: FlutterFlowTheme.of(context).secondaryBackground,
                  //                                             borderRadius: BorderRadius.circular(24.0),
                  //                                           ),
                  //                                           child:
                  //                                               Stack(
                  //                                             alignment: AlignmentDirectional(0.0, 0.0),
                  //                                             children: [
                  //                                               Row(
                  //                                                 mainAxisSize: MainAxisSize.max,
                  //                                                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //                                                 children: [
                  //                                                   Container(
                  //                                                     width: 69.0,
                  //                                                     height: 34.0,
                  //                                                     decoration: BoxDecoration(
                  //                                                       borderRadius: BorderRadius.circular(100.0),
                  //                                                     ),
                  //                                                     child: Align(
                  //                                                       alignment: AlignmentDirectional(0.0, 0.0),
                  //                                                       child: Text(
                  //                                                         'GLOBAL',
                  //                                                         textAlign: TextAlign.center,
                  //                                                         style: FlutterFlowTheme.of(context).bodyMedium.override(
                  //                                                               fontFamily: 'BT Beau Sans',
                  //                                                               color: FlutterFlowTheme.of(context).primaryText,
                  //                                                               fontSize: 12.0,
                  //                                                               letterSpacing: 0.0,
                  //                                                               fontWeight: FontWeight.normal,
                  //                                                               useGoogleFonts: false,
                  //                                                             ),
                  //                                                       ),
                  //                                                     ),
                  //                                                   ),
                  //                                                   Container(
                  //                                                     width: 84.0,
                  //                                                     height: 34.0,
                  //                                                     decoration: BoxDecoration(
                  //                                                       color: Color(0xFF99A0A5),
                  //                                                       borderRadius: BorderRadius.circular(100.0),
                  //                                                     ),
                  //                                                     child: Align(
                  //                                                       alignment: AlignmentDirectional(0.0, 0.0),
                  //                                                       child: Text(
                  //                                                         'REGIONAL',
                  //                                                         textAlign: TextAlign.center,
                  //                                                         style: FlutterFlowTheme.of(context).bodyMedium.override(
                  //                                                               fontFamily: 'BT Beau Sans',
                  //                                                               color: FlutterFlowTheme.of(context).info,
                  //                                                               fontSize: 12.0,
                  //                                                               letterSpacing: 0.0,
                  //                                                               fontWeight: FontWeight.normal,
                  //                                                               useGoogleFonts: false,
                  //                                                             ),
                  //                                                       ),
                  //                                                     ),
                  //                                                   ),
                  //                                                 ],
                  //                                               ),
                  //                                             ],
                  //                                           ),
                  //                                         ),
                  //                                         Padding(
                  //                                           padding: EdgeInsetsDirectional.fromSTEB(
                  //                                               5.0,
                  //                                               0.0,
                  //                                               0.0,
                  //                                               0.0),
                  //                                           child:
                  //                                               Icon(
                  //                                             Icons.info_outlined,
                  //                                             color: FlutterFlowTheme.of(context).secondaryText,
                  //                                             size: 24.0,
                  //                                           ),
                  //                                         ),
                  //                                       ],
                  //                                     );
                  //                                   } else if (currentUserDocument?.gender == Gender.Female
                  //                                       ? !(containerOpenRegionsRecord !=
                  //                                           null)
                  //                                       : (!valueOrDefault<bool>(currentUserDocument?.localWlAdmitted, false) &&
                  //                                           valueOrDefault<bool>(
                  //                                               currentUserDocument
                  //                                                   ?.globalMatching,
                  //                                               false))) {
                  //                                     return Container(
                  //                                       width:
                  //                                           153.0,
                  //                                       height:
                  //                                           30.0,
                  //                                       decoration:
                  //                                           BoxDecoration(
                  //                                         color:
                  //                                             FlutterFlowTheme.of(context).secondaryBackground,
                  //                                         borderRadius:
                  //                                             BorderRadius.circular(24.0),
                  //                                       ),
                  //                                       child:
                  //                                           Stack(
                  //                                         alignment: AlignmentDirectional(
                  //                                             0.0,
                  //                                             0.0),
                  //                                         children: [
                  //                                           Row(
                  //                                             mainAxisSize: MainAxisSize.max,
                  //                                             mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //                                             children: [
                  //                                               Container(
                  //                                                 width: 69.0,
                  //                                                 height: 34.0,
                  //                                                 decoration: BoxDecoration(
                  //                                                   color: Color(0xFF99A0A5),
                  //                                                   borderRadius: BorderRadius.circular(100.0),
                  //                                                 ),
                  //                                                 child: Align(
                  //                                                   alignment: AlignmentDirectional(0.0, 0.0),
                  //                                                   child: Text(
                  //                                                     'GLOBAL',
                  //                                                     textAlign: TextAlign.center,
                  //                                                     style: FlutterFlowTheme.of(context).bodyMedium.override(
                  //                                                           fontFamily: 'BT Beau Sans',
                  //                                                           color: FlutterFlowTheme.of(context).info,
                  //                                                           fontSize: 12.0,
                  //                                                           letterSpacing: 0.0,
                  //                                                           fontWeight: FontWeight.normal,
                  //                                                           useGoogleFonts: false,
                  //                                                         ),
                  //                                                   ),
                  //                                                 ),
                  //                                               ),
                  //                                               Container(
                  //                                                 width: 84.0,
                  //                                                 height: 34.0,
                  //                                                 decoration: BoxDecoration(
                  //                                                   borderRadius: BorderRadius.circular(100.0),
                  //                                                 ),
                  //                                                 child: Align(
                  //                                                   alignment: AlignmentDirectional(0.0, 0.0),
                  //                                                   child: Text(
                  //                                                     'REGIONAL',
                  //                                                     textAlign: TextAlign.center,
                  //                                                     style: FlutterFlowTheme.of(context).bodyMedium.override(
                  //                                                           fontFamily: 'BT Beau Sans',
                  //                                                           color: FlutterFlowTheme.of(context).secondaryText,
                  //                                                           fontSize: 12.0,
                  //                                                           letterSpacing: 0.0,
                  //                                                           fontWeight: FontWeight.normal,
                  //                                                           useGoogleFonts: false,
                  //                                                         ),
                  //                                                   ),
                  //                                                 ),
                  //                                               ),
                  //                                             ],
                  //                                           ),
                  //                                         ],
                  //                                       ),
                  //                                     );
                  //                                   } else if (currentUserDocument?.gender == Gender.Female
                  //                                       ? ((containerOpenRegionsRecord != null) &&
                  //                                           valueOrDefault<bool>(
                  //                                               currentUserDocument
                  //                                                   ?.localMatching,
                  //                                               false))
                  //                                       : (valueOrDefault<bool>(currentUserDocument?.localWlAdmitted, false) &&
                  //                                           valueOrDefault<bool>(
                  //                                               currentUserDocument
                  //                                                   ?.localMatching,
                  //                                               false))) {
                  //                                     return Container(
                  //                                       width:
                  //                                           153.0,
                  //                                       height:
                  //                                           30.0,
                  //                                       decoration:
                  //                                           BoxDecoration(
                  //                                         color:
                  //                                             FlutterFlowTheme.of(context).secondaryBackground,
                  //                                         borderRadius:
                  //                                             BorderRadius.circular(24.0),
                  //                                       ),
                  //                                       child:
                  //                                           Stack(
                  //                                         alignment: AlignmentDirectional(
                  //                                             0.0,
                  //                                             0.0),
                  //                                         children: [
                  //                                           Row(
                  //                                             mainAxisSize: MainAxisSize.max,
                  //                                             mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //                                             children: [
                  //                                               InkWell(
                  //                                                 splashColor: Colors.transparent,
                  //                                                 focusColor: Colors.transparent,
                  //                                                 hoverColor: Colors.transparent,
                  //                                                 highlightColor: Colors.transparent,
                  //                                                 onTap: () async {
                  //                                                   await currentUserReference!.update(createUsersRecordData(
                  //                                                     globalMatching: true,
                  //                                                     localMatching: false,
                  //                                                   ));
                  //                                                   unawaited(
                  //                                                     () async {
                  //                                                       try {
                  //                                                         final result = await FirebaseFunctions.instanceFor(region: 'europe-west2').httpsCallable('changeMatchingStatus').call({});
                  //                                                         _model.changeMatchingStatus = ChangeMatchingStatusCloudFunctionCallResponse(
                  //                                                           succeeded: true,
                  //                                                         );
                  //                                                       } on FirebaseFunctionsException catch (error) {
                  //                                                         _model.changeMatchingStatus = ChangeMatchingStatusCloudFunctionCallResponse(
                  //                                                           errorCode: error.code,
                  //                                                           succeeded: false,
                  //                                                         );
                  //                                                       }
                  //                                                     }(),
                  //                                                   );

                  //                                                   setState(() {});
                  //                                                 },
                  //                                                 child: Container(
                  //                                                   width: 69.0,
                  //                                                   height: 34.0,
                  //                                                   decoration: BoxDecoration(
                  //                                                     borderRadius: BorderRadius.circular(100.0),
                  //                                                   ),
                  //                                                   child: Align(
                  //                                                     alignment: AlignmentDirectional(0.0, 0.0),
                  //                                                     child: Text(
                  //                                                       'GLOBAL',
                  //                                                       textAlign: TextAlign.center,
                  //                                                       style: FlutterFlowTheme.of(context).bodyMedium.override(
                  //                                                             fontFamily: 'BT Beau Sans',
                  //                                                             color: FlutterFlowTheme.of(context).primaryText,
                  //                                                             fontSize: 12.0,
                  //                                                             letterSpacing: 0.0,
                  //                                                             fontWeight: FontWeight.normal,
                  //                                                             useGoogleFonts: false,
                  //                                                           ),
                  //                                                     ),
                  //                                                   ),
                  //                                                 ),
                  //                                               ),
                  //                                               Container(
                  //                                                 width: 84.0,
                  //                                                 height: 34.0,
                  //                                                 decoration: BoxDecoration(
                  //                                                   color: Color(0xFFAE34E8),
                  //                                                   borderRadius: BorderRadius.circular(100.0),
                  //                                                 ),
                  //                                                 child: Align(
                  //                                                   alignment: AlignmentDirectional(0.0, 0.0),
                  //                                                   child: Text(
                  //                                                     'REGIONAL',
                  //                                                     textAlign: TextAlign.center,
                  //                                                     style: FlutterFlowTheme.of(context).bodyMedium.override(
                  //                                                           fontFamily: 'BT Beau Sans',
                  //                                                           color: Colors.white,
                  //                                                           fontSize: 12.0,
                  //                                                           letterSpacing: 0.0,
                  //                                                           fontWeight: FontWeight.normal,
                  //                                                           useGoogleFonts: false,
                  //                                                         ),
                  //                                                   ),
                  //                                                 ),
                  //                                               ),
                  //                                             ],
                  //                                           ),
                  //                                         ],
                  //                                       ),
                  //                                     );
                  //                                   } else if (currentUserDocument?.gender == Gender.Female
                  //                                       ? ((containerOpenRegionsRecord != null) &&
                  //                                           valueOrDefault<bool>(currentUserDocument?.globalMatching, false))
                  //                                       : (valueOrDefault<bool>(currentUserDocument?.localWlAdmitted, false) && valueOrDefault<bool>(currentUserDocument?.globalMatching, false))) {
                  //                                     return Container(
                  //                                       width:
                  //                                           153.0,
                  //                                       height:
                  //                                           30.0,
                  //                                       decoration:
                  //                                           BoxDecoration(
                  //                                         color:
                  //                                             FlutterFlowTheme.of(context).secondaryBackground,
                  //                                         borderRadius:
                  //                                             BorderRadius.circular(24.0),
                  //                                       ),
                  //                                       child:
                  //                                           Stack(
                  //                                         alignment: AlignmentDirectional(
                  //                                             0.0,
                  //                                             0.0),
                  //                                         children: [
                  //                                           Row(
                  //                                             mainAxisSize: MainAxisSize.max,
                  //                                             mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //                                             children: [
                  //                                               Container(
                  //                                                 width: 69.0,
                  //                                                 height: 34.0,
                  //                                                 decoration: BoxDecoration(
                  //                                                   color: FlutterFlowTheme.of(context).primaryText,
                  //                                                   borderRadius: BorderRadius.circular(100.0),
                  //                                                 ),
                  //                                                 child: Align(
                  //                                                   alignment: AlignmentDirectional(0.0, 0.0),
                  //                                                   child: Text(
                  //                                                     'GLOBAL',
                  //                                                     textAlign: TextAlign.center,
                  //                                                     style: FlutterFlowTheme.of(context).bodyMedium.override(
                  //                                                           fontFamily: 'BT Beau Sans',
                  //                                                           color: Colors.white,
                  //                                                           fontSize: 12.0,
                  //                                                           letterSpacing: 0.0,
                  //                                                           fontWeight: FontWeight.normal,
                  //                                                           useGoogleFonts: false,
                  //                                                         ),
                  //                                                   ),
                  //                                                 ),
                  //                                               ),
                  //                                               InkWell(
                  //                                                 splashColor: Colors.transparent,
                  //                                                 focusColor: Colors.transparent,
                  //                                                 hoverColor: Colors.transparent,
                  //                                                 highlightColor: Colors.transparent,
                  //                                                 onTap: () async {
                  //                                                   await currentUserReference!.update(createUsersRecordData(
                  //                                                     globalMatching: false,
                  //                                                     localMatching: true,
                  //                                                   ));
                  //                                                   unawaited(
                  //                                                     () async {
                  //                                                       try {
                  //                                                         final result = await FirebaseFunctions.instanceFor(region: 'europe-west2').httpsCallable('changeMatchingStatus').call({});
                  //                                                         _model.changeMatchingStatus5 = ChangeMatchingStatusCloudFunctionCallResponse(
                  //                                                           succeeded: true,
                  //                                                         );
                  //                                                       } on FirebaseFunctionsException catch (error) {
                  //                                                         _model.changeMatchingStatus5 = ChangeMatchingStatusCloudFunctionCallResponse(
                  //                                                           errorCode: error.code,
                  //                                                           succeeded: false,
                  //                                                         );
                  //                                                       }
                  //                                                     }(),
                  //                                                   );

                  //                                                   setState(() {});
                  //                                                 },
                  //                                                 child: Container(
                  //                                                   width: 84.0,
                  //                                                   height: 34.0,
                  //                                                   decoration: BoxDecoration(
                  //                                                     borderRadius: BorderRadius.circular(100.0),
                  //                                                   ),
                  //                                                   child: Align(
                  //                                                     alignment: AlignmentDirectional(0.0, 0.0),
                  //                                                     child: Text(
                  //                                                       'REGIONAL',
                  //                                                       textAlign: TextAlign.center,
                  //                                                       style: FlutterFlowTheme.of(context).bodyMedium.override(
                  //                                                             fontFamily: 'BT Beau Sans',
                  //                                                             color: FlutterFlowTheme.of(context).primaryText,
                  //                                                             fontSize: 12.0,
                  //                                                             letterSpacing: 0.0,
                  //                                                             fontWeight: FontWeight.normal,
                  //                                                             useGoogleFonts: false,
                  //                                                           ),
                  //                                                     ),
                  //                                                   ),
                  //                                                 ),
                  //                                               ),
                  //                                             ],
                  //                                           ),
                  //                                         ],
                  //                                       ),
                  //                                     );
                  //                                   } else {
                  //                                     return Container(
                  //                                       width:
                  //                                           150.0,
                  //                                       height:
                  //                                           30.0,
                  //                                       decoration:
                  //                                           BoxDecoration(
                  //                                         color:
                  //                                             FlutterFlowTheme.of(context).secondaryBackground,
                  //                                         borderRadius:
                  //                                             BorderRadius.circular(24.0),
                  //                                       ),
                  //                                       child:
                  //                                           Stack(
                  //                                         alignment: AlignmentDirectional(
                  //                                             0.0,
                  //                                             0.0),
                  //                                         children: [
                  //                                           Row(
                  //                                             mainAxisSize: MainAxisSize.max,
                  //                                             mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //                                             children: [
                  //                                               InkWell(
                  //                                                 splashColor: Colors.transparent,
                  //                                                 focusColor: Colors.transparent,
                  //                                                 hoverColor: Colors.transparent,
                  //                                                 highlightColor: Colors.transparent,
                  //                                                 onTap: () async {
                  //                                                   await currentUserReference!.update(createUsersRecordData(
                  //                                                     globalMatching: true,
                  //                                                     localMatching: false,
                  //                                                   ));
                  //                                                   unawaited(
                  //                                                     () async {
                  //                                                       try {
                  //                                                         final result = await FirebaseFunctions.instanceFor(region: 'europe-west2').httpsCallable('changeMatchingStatus').call({});
                  //                                                         _model.changeMatchingStatus3 = ChangeMatchingStatusCloudFunctionCallResponse(
                  //                                                           succeeded: true,
                  //                                                         );
                  //                                                       } on FirebaseFunctionsException catch (error) {
                  //                                                         _model.changeMatchingStatus3 = ChangeMatchingStatusCloudFunctionCallResponse(
                  //                                                           errorCode: error.code,
                  //                                                           succeeded: false,
                  //                                                         );
                  //                                                       }
                  //                                                     }(),
                  //                                                   );

                  //                                                   setState(() {});
                  //                                                 },
                  //                                                 child: Container(
                  //                                                   width: 65.0,
                  //                                                   height: 34.0,
                  //                                                   decoration: BoxDecoration(
                  //                                                     borderRadius: BorderRadius.circular(100.0),
                  //                                                   ),
                  //                                                   child: Align(
                  //                                                     alignment: AlignmentDirectional(0.0, 0.0),
                  //                                                     child: Text(
                  //                                                       'GLOBAL',
                  //                                                       textAlign: TextAlign.center,
                  //                                                       style: FlutterFlowTheme.of(context).bodyMedium.override(
                  //                                                             fontFamily: 'BT Beau Sans',
                  //                                                             color: FlutterFlowTheme.of(context).primaryText,
                  //                                                             fontSize: 12.0,
                  //                                                             letterSpacing: 0.0,
                  //                                                             fontWeight: FontWeight.normal,
                  //                                                             useGoogleFonts: false,
                  //                                                           ),
                  //                                                     ),
                  //                                                   ),
                  //                                                 ),
                  //                                               ),
                  //                                               InkWell(
                  //                                                 splashColor: Colors.transparent,
                  //                                                 focusColor: Colors.transparent,
                  //                                                 hoverColor: Colors.transparent,
                  //                                                 highlightColor: Colors.transparent,
                  //                                                 onTap: () async {
                  //                                                   await currentUserReference!.update(createUsersRecordData(
                  //                                                     globalMatching: false,
                  //                                                     localMatching: true,
                  //                                                   ));
                  //                                                   unawaited(
                  //                                                     () async {
                  //                                                       try {
                  //                                                         final result = await FirebaseFunctions.instanceFor(region: 'europe-west2').httpsCallable('changeMatchingStatus').call({});
                  //                                                         _model.changeMatchingStatus2 = ChangeMatchingStatusCloudFunctionCallResponse(
                  //                                                           succeeded: true,
                  //                                                         );
                  //                                                       } on FirebaseFunctionsException catch (error) {
                  //                                                         _model.changeMatchingStatus2 = ChangeMatchingStatusCloudFunctionCallResponse(
                  //                                                           errorCode: error.code,
                  //                                                           succeeded: false,
                  //                                                         );
                  //                                                       }
                  //                                                     }(),
                  //                                                   );

                  //                                                   setState(() {});
                  //                                                 },
                  //                                                 child: Container(
                  //                                                   width: 84.0,
                  //                                                   height: 34.0,
                  //                                                   decoration: BoxDecoration(
                  //                                                     borderRadius: BorderRadius.circular(100.0),
                  //                                                   ),
                  //                                                   child: Align(
                  //                                                     alignment: AlignmentDirectional(0.0, 0.0),
                  //                                                     child: Text(
                  //                                                       'REGIONAL',
                  //                                                       textAlign: TextAlign.center,
                  //                                                       style: FlutterFlowTheme.of(context).bodyMedium.override(
                  //                                                             fontFamily: 'BT Beau Sans',
                  //                                                             color: FlutterFlowTheme.of(context).primaryText,
                  //                                                             fontSize: 12.0,
                  //                                                             letterSpacing: 0.0,
                  //                                                             fontWeight: FontWeight.normal,
                  //                                                             useGoogleFonts: false,
                  //                                                           ),
                  //                                                     ),
                  //                                                   ),
                  //                                                 ),
                  //                                               ),
                  //                                             ],
                  //                                           ),
                  //                                         ],
                  //                                       ),
                  //                                     );
                  //                                   }
                  //                                 },
                  //                               ),
                  //                             ]);
                  //                       }
                  //                     })
                  //                   ],
                  //                 ),
                  //               ),
                  //               if ((currentUserDocument
                  //                               ?.gender ==
                  //                           Gender.Female
                  //                       ? !(containerOpenRegionsRecord !=
                  //                           null)
                  //                       : (!valueOrDefault<bool>(
                  //                               currentUserDocument
                  //                                   ?.localWlAdmitted,
                  //                               false) &&
                  //                           valueOrDefault<bool>(
                  //                               currentUserDocument
                  //                                   ?.globalMatching,
                  //                               false))) ||
                  //                   (valueOrDefault<
                  //                               bool>(
                  //                           currentUserDocument
                  //                               ?.noGlobalForLocal,
                  //                           false) ||
                  //                       ((currentUserDocument
                  //                                   ?.gender ==
                  //                               Gender.Male) &&
                  //                           getRemoteConfigBool(
                  //                               'noGlobalMatchingForLocalMatchersM') &&
                  //                           valueOrDefault<bool>(
                  //                               currentUserDocument
                  //                                   ?.localWlAdmitted,
                  //                               false)) ||
                  //                       ((currentUserDocument
                  //                                   ?.gender ==
                  //                               Gender
                  //                                   .Female) &&
                  //                           getRemoteConfigBool(
                  //                               'noGlobalMatchingForLocalMatchersW') &&
                  //                           (containerOpenRegionsRecord !=
                  //                               null))) ||
                  //                   !valueOrDefault<bool>(
                  //                       currentUserDocument
                  //                           ?.signUpFinished,
                  //                       false))
                  //                 Builder(
                  //                   builder: (context) =>
                  //                       InkWell(
                  //                     splashColor:
                  //                         Colors.transparent,
                  //                     focusColor:
                  //                         Colors.transparent,
                  //                     hoverColor:
                  //                         Colors.transparent,
                  //                     highlightColor:
                  //                         Colors.transparent,
                  //                     onTap: () async {
                  //                       if (valueOrDefault<
                  //                                   bool>(
                  //                               currentUserDocument
                  //                                   ?.noGlobalForLocal,
                  //                               false) ||
                  //                           ((currentUserDocument
                  //                                       ?.gender ==
                  //                                   Gender
                  //                                       .Male) &&
                  //                               getRemoteConfigBool(
                  //                                   'noGlobalMatchingForLocalMatchersM') &&
                  //                               valueOrDefault<
                  //                                       bool>(
                  //                                   currentUserDocument
                  //                                       ?.localWlAdmitted,
                  //                                   false)) ||
                  //                           ((currentUserDocument
                  //                                       ?.gender ==
                  //                                   Gender
                  //                                       .Female) &&
                  //                               getRemoteConfigBool(
                  //                                   'noGlobalMatchingForLocalMatchersW') &&
                  //                               (containerOpenRegionsRecord !=
                  //                                   null))) {
                  //                         await showDialog(
                  //                           context: context,
                  //                           builder:
                  //                               (dialogContext) {
                  //                             return Dialog(
                  //                               elevation: 0,
                  //                               insetPadding:
                  //                                   EdgeInsets
                  //                                       .zero,
                  //                               backgroundColor:
                  //                                   Colors
                  //                                       .transparent,
                  //                               alignment: AlignmentDirectional(
                  //                                       0.0,
                  //                                       0.0)
                  //                                   .resolve(
                  //                                       Directionality.of(
                  //                                           context)),
                  //                               child:
                  //                                   GestureDetector(
                  //                                 onTap: () => _model
                  //                                         .unfocusNode
                  //                                         .canRequestFocus
                  //                                     ? FocusScope.of(
                  //                                             context)
                  //                                         .requestFocus(_model
                  //                                             .unfocusNode)
                  //                                     : FocusScope.of(
                  //                                             context)
                  //                                         .unfocus(),
                  //                                 child:
                  //                                     GeneralPopupWidget(
                  //                                   alertTitle:
                  //                                       getRemoteConfigString(
                  //                                           'noGlobalForLocalTitle'),
                  //                                   alertText:
                  //                                       getRemoteConfigString(
                  //                                           'noGlobalForLocalBody'),
                  //                                 ),
                  //                               ),
                  //                             );
                  //                           },
                  //                         ).then((value) =>
                  //                             setState(() {}));
                  //                       }
                  //                     },
                  //                     child: Container(
                  //                       width:
                  //                           MediaQuery.sizeOf(
                  //                                       context)
                  //                                   .width *
                  //                               1.0,
                  //                       height: 60.0,
                  //                       decoration:
                  //                           BoxDecoration(
                  //                         color:
                  //                             Color(0x4A929292),
                  //                         borderRadius:
                  //                             BorderRadius
                  //                                 .circular(
                  //                                     10.0),
                  //                       ),
                  //                     ),
                  //                   ),
                  //                 ),
                  //             ],
                  //           ),
                  //         );
                  //       },
                  //     ),
                  //   ),
                  // ),

                  //       if ((currentUserDocument?.gender ==
                  //               Gender.Male) &&
                  //           getRemoteConfigBool(
                  //               'profile_supporter_star_active'))
                  //         AuthUserStreamWidget(
                  //           builder: (context) => ClipRRect(
                  //             borderRadius:
                  //                 BorderRadius.circular(10.0),
                  //             child: Container(
                  //               height: 80.0,
                  //               decoration: BoxDecoration(
                  //                 color: FlutterFlowTheme.of(context)
                  //                     .primaryBackground,
                  //                 borderRadius:
                  //                     BorderRadius.circular(10.0),
                  //               ),
                  //               child: Stack(
                  //                 alignment:
                  //                     AlignmentDirectional(-1.0, 0.0),
                  //                 children: [
                  //                   Padding(
                  //                     padding: EdgeInsetsDirectional
                  //                         .fromSTEB(
                  //                             16.0, 0.0, 16.0, 0.0),
                  //                     child: Row(
                  //                       mainAxisSize:
                  //                           MainAxisSize.max,
                  //                       mainAxisAlignment:
                  //                           MainAxisAlignment
                  //                               .spaceBetween,
                  //                       children: [
                  //                         Flexible(
                  //                           child: Padding(
                  //                             padding:
                  //                                 EdgeInsetsDirectional
                  //                                     .fromSTEB(
                  //                                         0.0,
                  //                                         0.0,
                  //                                         15.0,
                  //                                         0.0),
                  //                             child: Text(
                  //                               getRemoteConfigString(
                  //                                   'settings_standard_support_badge_title'),
                  //                               style: FlutterFlowTheme
                  //                                       .of(context)
                  //                                   .bodyMedium
                  //                                   .override(
                  //                                     fontFamily:
                  //                                         'BT Beau Sans',
                  //                                     fontSize: 16.0,
                  //                                     letterSpacing:
                  //                                         0.0,
                  //                                     useGoogleFonts:
                  //                                         false,
                  //                                   ),
                  //                             ),
                  //                           ),
                  //                         ),
                  //                         Switch.adaptive(
                  //                           value: _model
                  //                               .supporterBadgeSwitchValue!,
                  //                           onChanged:
                  //                               (newValue) async {
                  //                             setState(() => _model
                  //                                     .supporterBadgeSwitchValue =
                  //                                 newValue!);
                  //                             if (newValue!) {
                  //                               if (revenue_cat
                  //                                       .activeEntitlementIds
                  //                                       .contains(
                  //                                           'paid_standard_1w') ||
                  //                                   revenue_cat
                  //                                       .activeEntitlementIds
                  //                                       .contains(
                  //                                           'paid_standard_lifetime')) {
                  //                                 await currentUserReference!
                  //                                     .update(
                  //                                         createUsersRecordData(
                  //                                   supporterBadgeShown:
                  //                                       true,
                  //                                 ));

                  //                                 await currentUserDocument!
                  //                                     .publicProfile!
                  //                                     .update(
                  //                                         createPublicProfileRecordData(
                  //                                   supporterBadgeShown:
                  //                                       true,
                  //                                 ));
                  //                                 return;
                  //                               } else {
                  //                                 if (valueOrDefault<
                  //                                         bool>(
                  //                                     currentUserDocument
                  //                                         ?.purchasesTestingNewDesign1,
                  //                                     false)) {
                  //                                   try {
                  //                                     analytics.logEvent(
                  //                                         'Navigated to Standard from Settings');
                  //                                   } catch (e) {}
                  //                                   ;
                  //                                   context.pushNamed(
                  //                                     'ChyrpeStandardNewNew',
                  //                                     extra: <String,
                  //                                         dynamic>{
                  //                                       kTransitionInfoKey:
                  //                                           TransitionInfo(
                  //                                         hasTransition:
                  //                                             true,
                  //                                         transitionType:
                  //                                             PageTransitionType
                  //                                                 .bottomToTop,
                  //                                       ),
                  //                                     },
                  //                                   );
                  //                                 } else {
                  //                                   try {
                  //                                     analytics.logEvent(
                  //                                         'Navigated to Standard from Settings');
                  //                                   } catch (e) {}
                  //                                   ;
                  //                                   context.pushNamed(
                  //                                     'ChyrpeStandardNew',
                  //                                     extra: <String,
                  //                                         dynamic>{
                  //                                       kTransitionInfoKey:
                  //                                           TransitionInfo(
                  //                                         hasTransition:
                  //                                             true,
                  //                                         transitionType:
                  //                                             PageTransitionType
                  //                                                 .bottomToTop,
                  //                                       ),
                  //                                     },
                  //                                   );
                  //                                 }

                  //                                 return;
                  //                               }
                  //                             } else {
                  //                               if (revenue_cat
                  //                                       .activeEntitlementIds
                  //                                       .contains(
                  //                                           'paid_standard_1w') ||
                  //                                   revenue_cat
                  //                                       .activeEntitlementIds
                  //                                       .contains(
                  //                                           'paid_standard_lifetime')) {
                  //                                 await currentUserReference!
                  //                                     .update(
                  //                                         createUsersRecordData(
                  //                                   supporterBadgeShown:
                  //                                       false,
                  //                                 ));

                  //                                 await currentUserDocument!
                  //                                     .publicProfile!
                  //                                     .update(
                  //                                         createPublicProfileRecordData(
                  //                                   supporterBadgeShown:
                  //                                       false,
                  //                                 ));
                  //                                 return;
                  //                               } else {
                  //                                 if (valueOrDefault<
                  //                                         bool>(
                  //                                     currentUserDocument
                  //                                         ?.purchasesTestingNewDesign1,
                  //                                     false)) {
                  //                                   try {
                  //                                     analytics.logEvent(
                  //                                         'Navigated to Standard from Settings');
                  //                                   } catch (e) {}
                  //                                   ;
                  //                                   context.pushNamed(
                  //                                     'ChyrpeStandardNewNew',
                  //                                     extra: <String,
                  //                                         dynamic>{
                  //                                       kTransitionInfoKey:
                  //                                           TransitionInfo(
                  //                                         hasTransition:
                  //                                             true,
                  //                                         transitionType:
                  //                                             PageTransitionType
                  //                                                 .bottomToTop,
                  //                                       ),
                  //                                     },
                  //                                   );
                  //                                 } else {
                  //                                   try {
                  //                                     analytics.logEvent(
                  //                                         'Navigated to Standard from Settings');
                  //                                   } catch (e) {}
                  //                                   ;
                  //                                   context.pushNamed(
                  //                                     'ChyrpeStandardNew',
                  //                                     extra: <String,
                  //                                         dynamic>{
                  //                                       kTransitionInfoKey:
                  //                                           TransitionInfo(
                  //                                         hasTransition:
                  //                                             true,
                  //                                         transitionType:
                  //                                             PageTransitionType
                  //                                                 .bottomToTop,
                  //                                       ),
                  //                                     },
                  //                                   );
                  //                                 }

                  //                                 return;
                  //                               }
                  //                             }
                  //                           },
                  //                           activeColor:
                  //                               Color(0xFF15ACDC),
                  //                           activeTrackColor:
                  //                               FlutterFlowTheme.of(
                  //                                       context)
                  //                                   .accent1,
                  //                           inactiveTrackColor:
                  //                               FlutterFlowTheme.of(
                  //                                       context)
                  //                                   .alternate,
                  //                           inactiveThumbColor:
                  //                               FlutterFlowTheme.of(
                  //                                       context)
                  //                                   .secondaryText,
                  //                         ),
                  //                       ],
                  //                     ),
                  //                   ),
                  //                   if (!(revenue_cat
                  //                           .activeEntitlementIds
                  //                           .contains(
                  //                               'paid_standard_lifetime') ||
                  //                       revenue_cat
                  //                           .activeEntitlementIds
                  //                           .contains(
                  //                               'paid_standard_1w')))
                  //                     InkWell(
                  //                       splashColor:
                  //                           Colors.transparent,
                  //                       focusColor:
                  //                           Colors.transparent,
                  //                       hoverColor:
                  //                           Colors.transparent,
                  //                       highlightColor:
                  //                           Colors.transparent,
                  //                       onTap: () async {
                  //                         if (valueOrDefault<bool>(
                  //                             currentUserDocument
                  //                                 ?.purchasesTestingNewDesign1,
                  //                             false)) {
                  //                           try {
                  //                             analytics.logEvent(
                  //                                 'Navigated to Standard from Settings');
                  //                           } catch (e) {}
                  //                           ;
                  //                           context.pushNamed(
                  //                             'ChyrpeStandardNewNew',
                  //                             extra: <String,
                  //                                 dynamic>{
                  //                               kTransitionInfoKey:
                  //                                   TransitionInfo(
                  //                                 hasTransition: true,
                  //                                 transitionType:
                  //                                     PageTransitionType
                  //                                         .bottomToTop,
                  //                               ),
                  //                             },
                  //                           );
                  //                         } else {
                  //                           try {
                  //                             analytics.logEvent(
                  //                                 'Navigated to Standard from Settings');
                  //                           } catch (e) {}
                  //                           ;
                  //                           context.pushNamed(
                  //                             'ChyrpeStandardNew',
                  //                             extra: <String,
                  //                                 dynamic>{
                  //                               kTransitionInfoKey:
                  //                                   TransitionInfo(
                  //                                 hasTransition: true,
                  //                                 transitionType:
                  //                                     PageTransitionType
                  //                                         .bottomToTop,
                  //                               ),
                  //                             },
                  //                           );
                  //                         }
                  //                       },
                  //                       child: Container(
                  //                         width: MediaQuery.sizeOf(
                  //                                     context)
                  //                                 .width *
                  //                             1.0,
                  //                         height: 100.0,
                  //                         decoration: BoxDecoration(
                  //                           color: Color(0x4A929292),
                  //                         ),
                  //                       ),
                  //                     ),
                  //                 ],
                  //               ),
                  //             ),
                  //           ),
                  //         ),

                  //     ],
                  //   ),
                  // ),
                  // Column(mainAxisSize: MainAxisSize.max, children: [
                  //   Align(
                  //     alignment: AlignmentDirectional(-1.0, -1.0),
                  //     child: Padding(
                  //       padding: EdgeInsetsDirectional.fromSTEB(
                  //           0.0, 0.0, 0.0, 9.0),
                  //       child: Text(
                  //         'Search Settings',
                  //         style: FlutterFlowTheme.of(context)
                  //             .bodyMedium
                  //             .override(
                  //               fontFamily: 'BT Beau Sans',
                  //               fontSize: 16.0,
                  //               fontWeight: FontWeight.w600,
                  //               useGoogleFonts: false,
                  //             ),
                  //       ),
                  //     ),
                  //   ),
                  //   Column(
                  //     mainAxisSize: MainAxisSize.max,
                  //     children: [
                  //       Padding(
                  //         padding: EdgeInsetsDirectional.fromSTEB(
                  //             0.0, 0.0, 0.0, 5.0),
                  //         child: Container(
                  //           width: double.infinity,
                  //           decoration: BoxDecoration(
                  //             color: FlutterFlowTheme.of(context)
                  //                 .primaryBackground,
                  //             borderRadius:
                  //                 BorderRadius.circular(10.0),
                  //           ),
                  //           child: Column(
                  //             mainAxisSize: MainAxisSize.max,
                  //             children: [
                  //               Padding(
                  //                 padding:
                  //                     EdgeInsetsDirectional.fromSTEB(
                  //                         16.0, 17.0, 16.0, 17.0),
                  //                 child: Column(
                  //                   mainAxisSize: MainAxisSize.max,
                  //                   children: [
                  //                     Column(
                  //                       mainAxisSize:
                  //                           MainAxisSize.max,
                  //                       children: [
                  //                         Row(
                  //                           mainAxisSize:
                  //                               MainAxisSize.max,
                  //                           mainAxisAlignment:
                  //                               MainAxisAlignment
                  //                                   .spaceBetween,
                  //                           children: [
                  //                             Align(
                  //                               alignment:
                  //                                   AlignmentDirectional(
                  //                                       -1.0, 0.0),
                  //                               child: Text(
                  //                                 'Age preference',
                  //                                 style: FlutterFlowTheme
                  //                                         .of(context)
                  //                                     .bodyMedium
                  //                                     .override(
                  //                                       fontFamily:
                  //                                           'BT Beau Sans',
                  //                                       fontSize:
                  //                                           16.0,
                  //                                       useGoogleFonts:
                  //                                           false,
                  //                                     ),
                  //                               ),
                  //                             ),
                  //                             Align(
                  //                               alignment:
                  //                                   AlignmentDirectional(
                  //                                       -1.0, 0.0),
                  //                               child: Text(
                  //                                 '${FFAppState().agePrefLow.toString()}-${FFAppState().agePrefHigh.toString()}',
                  //                                 style: FlutterFlowTheme
                  //                                         .of(context)
                  //                                     .bodyMedium
                  //                                     .override(
                  //                                       fontFamily:
                  //                                           'BT Beau Sans',
                  //                                       color: Color(
                  //                                           0xFF4F5865),
                  //                                       fontSize:
                  //                                           16.0,
                  //                                       useGoogleFonts:
                  //                                           false,
                  //                                     ),
                  //                               ),
                  //                             ),
                  //                           ],
                  //                         ),
                  //                       ],
                  //                     ),
                  //                     Container(
                  //                       width:
                  //                           MediaQuery.sizeOf(context)
                  //                                   .width *
                  //                               0.91,
                  //                       height: 70.0,
                  //                       child: custom_widgets
                  //                           .RangeSliderWidgetAge(
                  //                         width: MediaQuery.sizeOf(
                  //                                     context)
                  //                                 .width *
                  //                             0.91,
                  //                         height: 70.0,
                  //                         refreshPageUI: () async {
                  //                           setState(() {});
                  //                         },
                  //                       ),
                  //                     ),
                  //                   ],
                  //                 ),
                  //               ),
                  //             ],
                  //           ),
                  //         ),
                  //       ),
                  //     ],
                  //   ),
                  //   Column(
                  //     mainAxisSize: MainAxisSize.max,
                  //     children: [
                  //       Padding(
                  //         padding: EdgeInsetsDirectional.fromSTEB(
                  //             0.0, 0.0, 0.0, 5.0),
                  //         child: Container(
                  //           height: 45.0,
                  //           decoration: BoxDecoration(
                  //             color: FlutterFlowTheme.of(context)
                  //                 .primaryBackground,
                  //             borderRadius:
                  //                 BorderRadius.circular(10.0),
                  //           ),
                  //           child: Padding(
                  //             padding: EdgeInsetsDirectional.fromSTEB(
                  //                 16.0, 0.0, 0.0, 0.0),
                  //             child: InkWell(
                  //               splashColor: Colors.transparent,
                  //               focusColor: Colors.transparent,
                  //               hoverColor: Colors.transparent,
                  //               highlightColor: Colors.transparent,
                  //               onTap: () async {
                  //                 await showModalBottomSheet(
                  //                   isScrollControlled: true,
                  //                   backgroundColor:
                  //                       Colors.transparent,
                  //                   enableDrag: false,
                  //                   useSafeArea: true,
                  //                   context: context,
                  //                   builder: (context) {
                  //                     return GestureDetector(
                  //                       onTap: () => _model
                  //                               .unfocusNode
                  //                               .canRequestFocus
                  //                           ? FocusScope.of(context)
                  //                               .requestFocus(_model
                  //                                   .unfocusNode)
                  //                           : FocusScope.of(context)
                  //                               .unfocus(),
                  //                       child: Padding(
                  //                         padding:
                  //                             MediaQuery.viewInsetsOf(
                  //                                 context),
                  //                         child: Container(
                  //                           height: MediaQuery.sizeOf(
                  //                                       context)
                  //                                   .height *
                  //                               0.7,
                  //                           child:
                  //                               GenderSelectorWidget(),
                  //                         ),
                  //                       ),
                  //                     );
                  //                   },
                  //                 ).then(
                  //                     (value) => safeSetState(() {}));
                  //               },
                  //               child: Row(
                  //                 mainAxisSize: MainAxisSize.max,
                  //                 mainAxisAlignment:
                  //                     MainAxisAlignment.spaceBetween,
                  //                 children: [
                  //                   Text(
                  //                     'I want to see',
                  //                     style:
                  //                         FlutterFlowTheme.of(context)
                  //                             .bodyMedium
                  //                             .override(
                  //                               fontFamily:
                  //                                   'BT Beau Sans',
                  //                               fontSize: 16.0,
                  //                               useGoogleFonts: false,
                  //                             ),
                  //                   ),
                  //                   Padding(
                  //                     padding: EdgeInsetsDirectional
                  //                         .fromSTEB(
                  //                             0.0, 0.0, 16.0, 0.0),
                  //                     child: Row(
                  //                       mainAxisSize:
                  //                           MainAxisSize.max,
                  //                       mainAxisAlignment:
                  //                           MainAxisAlignment.end,
                  //                       children: [
                  //                         Row(
                  //                           mainAxisSize:
                  //                               MainAxisSize.max,
                  //                           mainAxisAlignment:
                  //                               MainAxisAlignment.end,
                  //                           children: [
                  //                             Padding(
                  //                               padding:
                  //                                   EdgeInsetsDirectional
                  //                                       .fromSTEB(
                  //                                           0.0,
                  //                                           0.0,
                  //                                           10.0,
                  //                                           0.0),
                  //                               child: Text(
                  //                                 FFAppState()
                  //                                     .genderPref,
                  //                                 style: FlutterFlowTheme
                  //                                         .of(context)
                  //                                     .bodyMedium
                  //                                     .override(
                  //                                       fontFamily:
                  //                                           'BT Beau Sans',
                  //                                       color: Color(
                  //                                           0xFF4F5865),
                  //                                       fontSize:
                  //                                           16.0,
                  //                                       useGoogleFonts:
                  //                                           false,
                  //                                     ),
                  //                               ),
                  //                             ),
                  //                             FaIcon(
                  //                               FontAwesomeIcons
                  //                                   .angleRight,
                  //                               color:
                  //                                   Color(0xFFB9BFC8),
                  //                               size: 18.0,
                  //                             ),
                  //                           ],
                  //                         ),
                  //                       ],
                  //                     ),
                  //                   ),
                  //                 ],
                  //               ),
                  //             ),
                  //           ),
                  //         ),
                  //       ),
                  //     ],
                  //   ),
                  //   Padding(
                  //     padding: EdgeInsetsDirectional.fromSTEB(
                  //         0.0, 0.0, 0.0, 9.0),
                  //     child: Container(
                  //       height: 45.0,
                  //       decoration: BoxDecoration(
                  //         color: FlutterFlowTheme.of(context)
                  //             .primaryBackground,
                  //         borderRadius: BorderRadius.circular(10.0),
                  //       ),
                  //       child: Builder(builder: (context) {
                  //         if ((currentUserDocument?.gender ==
                  //                 Gender.Male) &&
                  //             valueOrDefault<bool>(
                  //                 currentUserDocument?.signUpFinished,
                  //                 false) &&
                  //             !revenue_cat.activeEntitlementIds
                  //                 .contains('paid_standard_1w') &&
                  //             !revenue_cat.activeEntitlementIds
                  //                 .contains(
                  //                     'paid_standard_lifetime') &&
                  //             !revenue_cat.activeEntitlementIds
                  //                 .contains('gold_access') &&
                  //             !revenue_cat.activeEntitlementIds
                  //                 .contains('evolved_access')) {
                  //           return Padding(
                  //             padding: EdgeInsetsDirectional.fromSTEB(
                  //                 16.0, 0.0, 16.0, 0.0),
                  //             child: InkWell(
                  //               splashColor: Colors.transparent,
                  //               focusColor: Colors.transparent,
                  //               hoverColor: Colors.transparent,
                  //               highlightColor: Colors.transparent,
                  //               onTap: () async {
                  //                 analytics.logEvent(
                  //                     'Navigated to Standard from Settings');
                  //                 await action_blocks
                  //                     .goStandard(context);
                  //                 safeSetState(() {});
                  //               },
                  //               child: Row(
                  //                 mainAxisSize: MainAxisSize.max,
                  //                 mainAxisAlignment:
                  //                     MainAxisAlignment.spaceBetween,
                  //                 children: [
                  //                   Row(
                  //                     mainAxisSize: MainAxisSize.max,
                  //                     children: [
                  //                       Text(
                  //                         getRemoteConfigString(
                  //                             'settings_role_matching_title'),
                  //                         style: FlutterFlowTheme.of(
                  //                                 context)
                  //                             .bodyMedium
                  //                             .override(
                  //                               fontFamily:
                  //                                   'BT Beau Sans',
                  //                               color: FlutterFlowTheme
                  //                                       .of(context)
                  //                                   .secondaryText,
                  //                               fontSize: 16.0,
                  //                               letterSpacing: 0.0,
                  //                               useGoogleFonts: false,
                  //                             ),
                  //                       ),
                  //                       Padding(
                  //                         padding:
                  //                             EdgeInsetsDirectional
                  //                                 .fromSTEB(3.0, 0.0,
                  //                                     0.0, 0.0),
                  //                         child: Icon(
                  //                           Icons
                  //                               .lock_outline_rounded,
                  //                           color:
                  //                               FlutterFlowTheme.of(
                  //                                       context)
                  //                                   .primaryText,
                  //                           size: 24.0,
                  //                         ),
                  //                       ),
                  //                     ],
                  //                   ),
                  //                   Switch.adaptive(
                  //                     value: false,
                  //                     onChanged: true
                  //                         ? null
                  //                         : (newValue) async {
                  //                             safeSetState(
                  //                                 () => true);
                  //                           },
                  //                     activeColor: true
                  //                         ? FlutterFlowTheme.of(
                  //                                 context)
                  //                             .alternate
                  //                         : Color(0xFFAE34E8),
                  //                     activeTrackColor: true
                  //                         ? FlutterFlowTheme.of(
                  //                                 context)
                  //                             .alternate
                  //                         : FlutterFlowTheme.of(
                  //                                 context)
                  //                             .accent1,
                  //                     inactiveTrackColor: true
                  //                         ? FlutterFlowTheme.of(
                  //                                 context)
                  //                             .alternate
                  //                         : FlutterFlowTheme.of(
                  //                                 context)
                  //                             .alternate,
                  //                     inactiveThumbColor: true
                  //                         ? FlutterFlowTheme.of(
                  //                                 context)
                  //                             .secondaryText
                  //                         : FlutterFlowTheme.of(
                  //                                 context)
                  //                             .secondaryText,
                  //                   ),
                  //                 ],
                  //               ),
                  //             ),
                  //           );
                  //         } else {
                  //           return Container(
                  //             height: 45.0,
                  //             decoration: BoxDecoration(
                  //               color: FlutterFlowTheme.of(context)
                  //                   .primaryBackground,
                  //               borderRadius:
                  //                   BorderRadius.circular(10.0),
                  //             ),
                  //             child: Padding(
                  //               padding:
                  //                   EdgeInsetsDirectional.fromSTEB(
                  //                       16.0, 0.0, 16.0, 0.0),
                  //               child: Row(
                  //                 mainAxisSize: MainAxisSize.max,
                  //                 mainAxisAlignment:
                  //                     MainAxisAlignment.spaceBetween,
                  //                 children: [
                  //                   Text(
                  //                     getRemoteConfigString(
                  //                         'settings_role_matching_title'),
                  //                     style:
                  //                         FlutterFlowTheme.of(context)
                  //                             .bodyMedium
                  //                             .override(
                  //                               fontFamily:
                  //                                   'BT Beau Sans',
                  //                               fontSize: 16.0,
                  //                               letterSpacing: 0.0,
                  //                               useGoogleFonts: false,
                  //                             ),
                  //                   ),
                  //                   AuthUserStreamWidget(
                  //                     builder: (context) =>
                  //                         Switch.adaptive(
                  //                       value:
                  //                           _model.roleSwitchValue!,
                  //                       onChanged: (newValue) async {
                  //                         safeSetState(() =>
                  //                             _model.roleSwitchValue =
                  //                                 newValue!);
                  //                         if (newValue!) {
                  //                           await currentUserReference!
                  //                               .update(
                  //                                   createUsersRecordData(
                  //                             roleMatching: true,
                  //                           ));
                  //                         } else {
                  //                           await currentUserReference!
                  //                               .update(
                  //                                   createUsersRecordData(
                  //                             roleMatching: false,
                  //                           ));
                  //                         }
                  //                       },
                  //                       activeColor:
                  //                           Color(0xFFAE34E8),
                  //                       activeTrackColor:
                  //                           FlutterFlowTheme.of(
                  //                                   context)
                  //                               .accent1,
                  //                       inactiveTrackColor:
                  //                           FlutterFlowTheme.of(
                  //                                   context)
                  //                               .alternate,
                  //                       inactiveThumbColor:
                  //                           FlutterFlowTheme.of(
                  //                                   context)
                  //                               .secondaryText,
                  //                     ),
                  //                   ),
                  //                 ],
                  //               ),
                  //             ),
                  //           );
                  //         }
                  //       }),
                  //     ),
                  //   )
                  //   ]),
                  // ],
                  // ),
                  //   ),
                  // ],
                  // ),
                  // if (currentUserDocument?.gender == Gender.Male)
                  //   Column(
                  //     mainAxisSize: MainAxisSize.max,
                  //     children: [
                  //       Align(
                  //         alignment: AlignmentDirectional(1.0, 0.0),
                  //         child: AuthUserStreamWidget(
                  //           builder: (context) => FlutterFlowIconButton(
                  //             borderColor: Color(0x004B39EF),
                  //             borderRadius: 20.0,
                  //             borderWidth: 0.0,
                  //             buttonSize: 40.0,
                  //             fillColor: Color(0x004B39EF),
                  //             icon: Icon(
                  //               Icons.info_outline_rounded,
                  //               color: FlutterFlowTheme.of(context).primaryText,
                  //               size: 24.0,
                  //             ),
                  //             onPressed: () async {
                  //               await showModalBottomSheet(
                  //                 isScrollControlled: true,
                  //                 backgroundColor: Colors.transparent,
                  //                 enableDrag: false,
                  //                 context: context,
                  //                 builder: (context) {
                  //                   return GestureDetector(
                  //                     onTap: () {
                  //                       FocusScope.of(context).unfocus();
                  //                       FocusManager.instance.primaryFocus
                  //                           ?.unfocus();
                  //                     },
                  //                     child: Padding(
                  //                       padding:
                  //                           MediaQuery.viewInsetsOf(context),
                  //                       child: InfoSheetScrollableWidget(
                  //                         title: getRemoteConfigString(
                  //                             'settings_professionals_title'),
                  //                         body: getRemoteConfigString(
                  //                             'settings_professionals_info'),
                  //                       ),
                  //                     ),
                  //                   );
                  //                 },
                  //               ).then((value) => safeSetState(() {}));
                  //             },
                  //           ),
                  //         ),
                  //       ),
                  //       if (currentUserDocument?.gender == Gender.Male)
                  //         Padding(
                  //           padding: EdgeInsetsDirectional.fromSTEB(
                  //               0.0, 0.0, 0.0, 6.0),
                  //           child: Container(
                  //             height: 45.0,
                  //             decoration: BoxDecoration(
                  //               color: FlutterFlowTheme.of(context)
                  //                   .primaryBackground,
                  //               borderRadius: BorderRadius.circular(10.0),
                  //             ),
                  //             child: Builder(
                  //               builder: (context) {
                  //                 if (((currentUserDocument?.gender ==
                  //                             Gender.Male) &&
                  //                         valueOrDefault<bool>(
                  //                             currentUserDocument
                  //                                 ?.signUpFinished,
                  //                             false) &&
                  //                         !revenue_cat.activeEntitlementIds
                  //                             .contains('paid_standard_1w') &&
                  //                         !revenue_cat.activeEntitlementIds
                  //                             .contains(
                  //                                 'paid_standard_lifetime') &&
                  //                         !revenue_cat.activeEntitlementIds
                  //                             .contains('gold_access') &&
                  //                         !revenue_cat.activeEntitlementIds
                  //                             .contains('evolved_access')) &&
                  //                     !getRemoteConfigBool(
                  //                         'settings_professionals_forall')) {
                  //                   return Padding(
                  //                     padding: EdgeInsetsDirectional.fromSTEB(
                  //                         16.0, 0.0, 16.0, 0.0),
                  //                     child: InkWell(
                  //                       splashColor: Colors.transparent,
                  //                       focusColor: Colors.transparent,
                  //                       hoverColor: Colors.transparent,
                  //                       highlightColor: Colors.transparent,
                  //                       onTap: () async {
                  //                         analytics.logEvent(
                  //                             'Navigated to Standard from Settings');
                  //                         await action_blocks
                  //                             .goStandard(context);
                  //                         safeSetState(() {});
                  //                       },
                  //                       child: Row(
                  //                         mainAxisSize: MainAxisSize.max,
                  //                         mainAxisAlignment:
                  //                             MainAxisAlignment.spaceBetween,
                  //                         children: [
                  //                           Row(
                  //                             mainAxisSize: MainAxisSize.max,
                  //                             children: [
                  //                               Text(
                  //                                 getRemoteConfigString(
                  //                                     'settings_professionals_title'),
                  //                                 style: FlutterFlowTheme.of(
                  //                                         context)
                  //                                     .bodyMedium
                  //                                     .override(
                  //                                       fontFamily:
                  //                                           'BT Beau Sans',
                  //                                       color:
                  //                                           FlutterFlowTheme.of(
                  //                                                   context)
                  //                                               .secondaryText,
                  //                                       fontSize: 16.0,
                  //                                       letterSpacing: 0.0,
                  //                                       useGoogleFonts: false,
                  //                                     ),
                  //                               ),
                  //                               Padding(
                  //                                 padding: EdgeInsetsDirectional
                  //                                     .fromSTEB(
                  //                                         3.0, 0.0, 0.0, 0.0),
                  //                                 child: Icon(
                  //                                   Icons.lock_outline_rounded,
                  //                                   color: FlutterFlowTheme.of(
                  //                                           context)
                  //                                       .primaryText,
                  //                                   size: 24.0,
                  //                                 ),
                  //                               ),
                  //                             ],
                  //                           ),
                  //                           Switch.adaptive(
                  //                             value: _model
                  //                                 .switchValueProfessionalOut!,
                  //                             onChanged: true
                  //                                 ? null
                  //                                 : (newValue) async {
                  //                                     safeSetState(() =>
                  //                                         _model.switchValue3 =
                  //                                             newValue!);
                  //                                   },
                  //                             activeColor: true
                  //                                 ? FlutterFlowTheme.of(context)
                  //                                     .alternate
                  //                                 : Color(0xFFAE34E8),
                  //                             activeTrackColor: true
                  //                                 ? FlutterFlowTheme.of(context)
                  //                                     .alternate
                  //                                 : FlutterFlowTheme.of(context)
                  //                                     .accent1,
                  //                             inactiveTrackColor: true
                  //                                 ? FlutterFlowTheme.of(context)
                  //                                     .alternate
                  //                                 : FlutterFlowTheme.of(context)
                  //                                     .alternate,
                  //                             inactiveThumbColor: true
                  //                                 ? FlutterFlowTheme.of(context)
                  //                                     .secondaryText
                  //                                 : FlutterFlowTheme.of(context)
                  //                                     .secondaryText,
                  //                           ),
                  //                         ],
                  //                       ),
                  //                     ),
                  //                   );
                  //                 } else {
                  //                   return Padding(
                  //                     padding: EdgeInsetsDirectional.fromSTEB(
                  //                         16.0, 0.0, 16.0, 0.0),
                  //                     child: Row(
                  //                       mainAxisSize: MainAxisSize.max,
                  //                       mainAxisAlignment:
                  //                           MainAxisAlignment.spaceBetween,
                  //                       children: [
                  //                         Text(
                  //                           getRemoteConfigString(
                  //                               'settings_professionals_title'),
                  //                           style: FlutterFlowTheme.of(context)
                  //                               .bodyMedium
                  //                               .override(
                  //                                 fontFamily: 'BT Beau Sans',
                  //                                 fontSize: 16.0,
                  //                                 letterSpacing: 0.0,
                  //                                 useGoogleFonts: false,
                  //                               ),
                  //                         ),
                  //                         AuthUserStreamWidget(
                  //                           builder: (context) =>
                  //                               Switch.adaptive(
                  //                             value: _model
                  //                                 .switchValueProfessional!,
                  //                             onChanged: (newValue) async {
                  //                               safeSetState(() => _model
                  //                                       .switchValueProfessional =
                  //                                   newValue!);
                  //                               if (newValue!) {
                  //                                 await currentUserReference!
                  //                                     .update(
                  //                                         createUsersRecordData(
                  //                                   professionalsReq: true,
                  //                                 ));
                  //                               } else {
                  //                                 await currentUserReference!
                  //                                     .update(
                  //                                         createUsersRecordData(
                  //                                   professionalsReq: false,
                  //                                 ));
                  //                               }
                  //                             },
                  //                             activeColor: Color(0xFFAE34E8),
                  //                             activeTrackColor:
                  //                                 FlutterFlowTheme.of(context)
                  //                                     .accent1,
                  //                             inactiveTrackColor:
                  //                                 FlutterFlowTheme.of(context)
                  //                                     .alternate,
                  //                             inactiveThumbColor:
                  //                                 FlutterFlowTheme.of(context)
                  //                                     .secondaryText,
                  //                           ),
                  //                         ),
                  //                       ],
                  //                     ),
                  //                   );
                  //                 }
                  //               },
                  //             ),
                  //           ),
                  //         ),
                  //     ],
                  //   ),
                  // Padding(
                  //   padding: EdgeInsetsDirectional.fromSTEB(0.0, 5.0, 0.0, 0.0),
                  //   child: Container(
                  //     decoration: BoxDecoration(
                  //       color: FlutterFlowTheme.of(context).primaryBackground,
                  //       borderRadius: BorderRadius.circular(10.0),
                  //     ),
                  //   ),
                  // ),
                  // if (valueOrDefault<bool>(
                  //     currentUserDocument?.localMatching, false))
                  //   Padding(
                  //     padding: const EdgeInsets.fromLTRB(0, 20, 0, 0),
                  //     child: Column(
                  //       mainAxisSize: MainAxisSize.max,
                  //       crossAxisAlignment: CrossAxisAlignment.start,
                  //       children: [
                  //         Row(
                  //           mainAxisSize: MainAxisSize.max,
                  //           mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //           children: [
                  //             Row(
                  //               mainAxisSize: MainAxisSize.max,
                  //               children: [
                  //                 Align(
                  //                   alignment: AlignmentDirectional(-1.0, -1.0),
                  //                   child: Text(
                  //                     'Area',
                  //                     style: FlutterFlowTheme.of(context)
                  //                         .bodyMedium
                  //                         .override(
                  //                           fontFamily: 'BT Beau Sans',
                  //                           fontSize: 16.0,
                  //                           letterSpacing: 0.0,
                  //                           fontWeight: FontWeight.w500,
                  //                           useGoogleFonts: false,
                  //                         ),
                  //                   ),
                  //                 ),
                  //                 if ((currentUserDocument?.gender ==
                  //                         Gender.Male) &&
                  //                     valueOrDefault<bool>(
                  //                         currentUserDocument?.signUpFinished,
                  //                         false) &&
                  //                     !revenue_cat.activeEntitlementIds
                  //                         .contains('paid_standard_1w') &&
                  //                     !revenue_cat.activeEntitlementIds
                  //                         .contains('paid_standard_lifetime') &&
                  //                     !revenue_cat.activeEntitlementIds
                  //                         .contains('gold_access') &&
                  //                     !revenue_cat.activeEntitlementIds
                  //                         .contains('evolved_access'))
                  //                   Padding(
                  //                     padding: EdgeInsetsDirectional.fromSTEB(
                  //                         3.0, 0.0, 0.0, 0.0),
                  //                     child: AuthUserStreamWidget(
                  //                       builder: (context) => Icon(
                  //                         Icons.lock_outline_rounded,
                  //                         color: FlutterFlowTheme.of(context)
                  //                             .primaryText,
                  //                         size: 24.0,
                  //                       ),
                  //                     ),
                  //                   ),
                  //               ],
                  //             ),
                  //             if (valueOrDefault<bool>(
                  //                 currentUserDocument?.localWlAdmitted, false))
                  //               AuthUserStreamWidget(
                  //                 builder: (context) => FlutterFlowIconButton(
                  //                   borderColor: Color(0x004B39EF),
                  //                   borderRadius: 20.0,
                  //                   borderWidth: 0.0,
                  //                   buttonSize: 40.0,
                  //                   fillColor: Color(0x004B39EF),
                  //                   icon: Icon(
                  //                     Icons.info_outline_rounded,
                  //                     color: FlutterFlowTheme.of(context)
                  //                         .primaryText,
                  //                     size: 24.0,
                  //                   ),
                  //                   onPressed: () async {
                  //                     await showModalBottomSheet(
                  //                       isScrollControlled: true,
                  //                       backgroundColor: Colors.transparent,
                  //                       enableDrag: false,
                  //                       context: context,
                  //                       builder: (context) {
                  //                         return GestureDetector(
                  //                           onTap: () {
                  //                             FocusScope.of(context).unfocus();
                  //                             FocusManager.instance.primaryFocus
                  //                                 ?.unfocus();
                  //                           },
                  //                           child: Padding(
                  //                             padding: MediaQuery.viewInsetsOf(
                  //                                 context),
                  //                             child: InfoSheetScrollableWidget(
                  //                               title: 'Area',
                  //                               body: getRemoteConfigString(
                  //                                   'settings_area_info'),
                  //                             ),
                  //                           ),
                  //                         );
                  //                       },
                  //                     ).then((value) => safeSetState(() {}));
                  //                   },
                  //                 ),
                  //               ),
                  //           ],
                  //         ),
                  //         AuthUserStreamWidget(
                  //           builder: (context) => Column(
                  //             mainAxisSize: MainAxisSize.max,
                  //             children: [
                  //               Container(
                  //                 decoration: BoxDecoration(
                  //                   color: FlutterFlowTheme.of(context)
                  //                       .primaryBackground,
                  //                   borderRadius: BorderRadius.circular(10.0),
                  //                 ),
                  //                 child: Stack(
                  //                   children: [
                  //                     Padding(
                  //                       padding: EdgeInsetsDirectional.fromSTEB(
                  //                           17.0, 17.0, 17.0, 17.0),
                  //                       child: Column(
                  //                         mainAxisSize: MainAxisSize.max,
                  //                         children: [
                  //                           Row(
                  //                             mainAxisSize: MainAxisSize.max,
                  //                             mainAxisAlignment:
                  //                                 MainAxisAlignment.end,
                  //                             children: [
                  //                               Align(
                  //                                 alignment:
                  //                                     AlignmentDirectional(
                  //                                         -1.0, 0.0),
                  //                                 child: Text(
                  //                                   FFAppState().locationPref <
                  //                                           getRemoteConfigDouble(
                  //                                               'settings_max_area')
                  //                                       ? '${formatNumber(
                  //                                           FFAppState()
                  //                                               .locationPref,
                  //                                           formatType:
                  //                                               FormatType
                  //                                                   .compact,
                  //                                         )} km'
                  //                                       : '${formatNumber(
                  //                                           getRemoteConfigDouble(
                  //                                               'settings_max_area'),
                  //                                           formatType:
                  //                                               FormatType
                  //                                                   .compact,
                  //                                         )}+ km',
                  //                                   style: FlutterFlowTheme.of(
                  //                                           context)
                  //                                       .bodyMedium
                  //                                       .override(
                  //                                         fontFamily:
                  //                                             'BT Beau Sans',
                  //                                         color:
                  //                                             Color(0xFF4F5865),
                  //                                         fontSize: 16.0,
                  //                                         letterSpacing: 0.0,
                  //                                         useGoogleFonts: false,
                  //                                       ),
                  //                                 ),
                  //                               ),
                  //                             ],
                  //                           ),
                  //                           Container(
                  //                             width: MediaQuery.sizeOf(context)
                  //                                     .width *
                  //                                 1.0,
                  //                             height: 70.0,
                  //                             child: custom_widgets
                  //                                 .SliderWidgetDistance(
                  //                               width:
                  //                                   MediaQuery.sizeOf(context)
                  //                                           .width *
                  //                                       1.0,
                  //                               height: 70.0,
                  //                               refreshPageUI: () async {
                  //                                 safeSetState(() {});
                  //                               },
                  //                             ),
                  //                           ),
                  //                         ],
                  //                       ),
                  //                     ),
                  //                     if ((currentUserDocument?.gender ==
                  //                             Gender.Male) &&
                  //                         valueOrDefault<bool>(
                  //                             currentUserDocument
                  //                                 ?.signUpFinished,
                  //                             false) &&
                  //                         !revenue_cat.activeEntitlementIds
                  //                             .contains('paid_standard_1w') &&
                  //                         !revenue_cat.activeEntitlementIds
                  //                             .contains(
                  //                                 'paid_standard_lifetime') &&
                  //                         !revenue_cat.activeEntitlementIds
                  //                             .contains('gold_access') &&
                  //                         !revenue_cat.activeEntitlementIds
                  //                             .contains('evolved_access'))
                  //                       InkWell(
                  //                         splashColor: Colors.transparent,
                  //                         focusColor: Colors.transparent,
                  //                         hoverColor: Colors.transparent,
                  //                         highlightColor: Colors.transparent,
                  //                         onTap: () async {
                  //                           analytics.logEvent(
                  //                               'Navigated to Standard from Settings');
                  //                           await action_blocks
                  //                               .goStandard(context);
                  //                           safeSetState(() {});
                  //                         },
                  //                         child: Container(
                  //                           width: MediaQuery.sizeOf(context)
                  //                                   .width *
                  //                               1.0,
                  //                           height: 150.0,
                  //                           decoration: BoxDecoration(
                  //                             color: Color(0x4A929292),
                  //                             borderRadius:
                  //                                 BorderRadius.circular(10.0),
                  //                           ),
                  //                         ),
                  //                       ),
                  //                   ],
                  //                 ),
                  //               ),
                  //               Align(
                  //                 alignment: AlignmentDirectional(-1.0, 0.0),
                  //                 child: Padding(
                  //                   padding: EdgeInsetsDirectional.fromSTEB(
                  //                       0.0, 5.0, 25.0, 9.0),
                  //                   child: Text(
                  //                     getRemoteConfigString(
                  //                         'settings_area_info_direct'),
                  //                     style: FlutterFlowTheme.of(context)
                  //                         .bodyMedium
                  //                         .override(
                  //                           fontFamily: 'BT Beau Sans',
                  //                           letterSpacing: 0.0,
                  //                           useGoogleFonts: false,
                  //                         ),
                  //                   ),
                  //                 ),
                  //               ),
                  //             ],
                  //           ),
                  //         ),
                  //       ],
                  //     ),
                  //   ),
                  // if (currentUserDocument?.gender == Gender.Female)
                  //   Align(
                  //     alignment: AlignmentDirectional(-1.0, -1.0),
                  //     child: Padding(
                  //       padding:
                  //           EdgeInsetsDirectional.fromSTEB(3.0, 20.0, 0.0, 0.0),
                  //       child: Column(
                  //         mainAxisSize: MainAxisSize.max,
                  //         children: [
                  //           Row(
                  //             mainAxisSize: MainAxisSize.max,
                  //             mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //             children: [
                  //               Align(
                  //                 alignment: AlignmentDirectional(-1.0, -1.0),
                  //                 child: Text(
                  //                   'Evolved Search',
                  //                   style: FlutterFlowTheme.of(context)
                  //                       .bodyMedium
                  //                       .override(
                  //                         fontFamily: 'BT Beau Sans',
                  //                         fontSize: 16.0,
                  //                         fontWeight: FontWeight.w600,
                  //                         useGoogleFonts: false,
                  //                       ),
                  //                 ),
                  //               ),
                  //               if (currentUserDocument?.gender ==
                  //                   Gender.Female)
                  //                 Padding(
                  //                   padding: EdgeInsetsDirectional.fromSTEB(
                  //                       5.0, 0.0, 0.0, 0.0),
                  //                   child: AuthUserStreamWidget(
                  //                     builder: (context) => Container(
                  //                       height: 19.0,
                  //                       decoration: BoxDecoration(
                  //                         gradient: LinearGradient(
                  //                           colors: [
                  //                             Color(0xFF75D2FB),
                  //                             Color(0xFFFF2191)
                  //                           ],
                  //                           stops: [0.0, 1.0],
                  //                           begin:
                  //                               AlignmentDirectional(1.0, 0.0),
                  //                           end: AlignmentDirectional(-1.0, 0),
                  //                         ),
                  //                         borderRadius:
                  //                             BorderRadius.circular(100.0),
                  //                       ),
                  //                       child: Align(
                  //                         alignment:
                  //                             AlignmentDirectional(0.0, 0.0),
                  //                         child: Padding(
                  //                           padding:
                  //                               EdgeInsetsDirectional.fromSTEB(
                  //                                   7.0, 0.0, 7.0, 0.0),
                  //                           child: Text(
                  //                             'Women exclusive',
                  //                             textAlign: TextAlign.center,
                  //                             style: FlutterFlowTheme.of(
                  //                                     context)
                  //                                 .bodyMedium
                  //                                 .override(
                  //                                   fontFamily: 'BT Beau Sans',
                  //                                   color: FlutterFlowTheme.of(
                  //                                           context)
                  //                                       .info,
                  //                                   fontSize: 12.0,
                  //                                   fontWeight: FontWeight.w500,
                  //                                   useGoogleFonts: false,
                  //                                 ),
                  //                           ),
                  //                         ),
                  //                       ),
                  //                     ),
                  //                   ),
                  //                 ),
                  //               if (currentUserDocument?.gender == Gender.Male)
                  //                 Padding(
                  //                   padding: EdgeInsetsDirectional.fromSTEB(
                  //                       5.0, 0.0, 0.0, 0.0),
                  //                   child: AuthUserStreamWidget(
                  //                     builder: (context) => Container(
                  //                       height: 19.0,
                  //                       decoration: BoxDecoration(
                  //                         gradient: LinearGradient(
                  //                           colors: [
                  //                             Color(0xFFA12CFD),
                  //                             Color(0xFFFF6C3E)
                  //                           ],
                  //                           stops: [0.0, 1.0],
                  //                           begin:
                  //                               AlignmentDirectional(1.0, 0.0),
                  //                           end: AlignmentDirectional(-1.0, 0),
                  //                         ),
                  //                         borderRadius:
                  //                             BorderRadius.circular(100.0),
                  //                       ),
                  //                       child: Align(
                  //                         alignment:
                  //                             AlignmentDirectional(0.0, 0.0),
                  //                         child: Padding(
                  //                           padding:
                  //                               EdgeInsetsDirectional.fromSTEB(
                  //                                   7.0, 0.0, 7.0, 0.0),
                  //                           child: Text(
                  //                             'Evolved',
                  //                             textAlign: TextAlign.center,
                  //                             style: FlutterFlowTheme.of(
                  //                                     context)
                  //                                 .bodyMedium
                  //                                 .override(
                  //                                   fontFamily: 'BT Beau Sans',
                  //                                   color: FlutterFlowTheme.of(
                  //                                           context)
                  //                                       .info,
                  //                                   fontSize: 12.0,
                  //                                   fontWeight: FontWeight.w500,
                  //                                   useGoogleFonts: false,
                  //                                 ),
                  //                           ),
                  //                         ),
                  //                       ),
                  //                     ),
                  //                   ),
                  //                 ),
                  //             ],
                  //           ),
                  //           Padding(
                  //             padding: EdgeInsetsDirectional.fromSTEB(
                  //                 0.0, 6.0, 25.0, 0.0),
                  //             child: Text(
                  //               'Our algorithm will prioritize evolved options, in order to find those who match what you like.',
                  //               style: FlutterFlowTheme.of(context).bodyMedium,
                  //             ),
                  //           ),
                  //           if (getRemoteConfigBool('showKinks'))
                  //             Padding(
                  //               padding: EdgeInsetsDirectional.fromSTEB(
                  //                   0.0, 9.0, 0.0, 0.0),
                  //               child: Container(
                  //                 height: 45.0,
                  //                 decoration: BoxDecoration(
                  //                   color: FlutterFlowTheme.of(context)
                  //                       .primaryBackground,
                  //                   borderRadius: BorderRadius.circular(10.0),
                  //                 ),
                  //                 child: Padding(
                  //                   padding: EdgeInsetsDirectional.fromSTEB(
                  //                       16.0, 0.0, 16.0, 0.0),
                  //                   child: InkWell(
                  //                     splashColor: Colors.transparent,
                  //                     focusColor: Colors.transparent,
                  //                     hoverColor: Colors.transparent,
                  //                     highlightColor: Colors.transparent,
                  //                     onTap: () async {
                  //                       if (revenue_cat.activeEntitlementIds
                  //                               .contains('evolved_access') ||
                  //                           (currentUserDocument?.gender ==
                  //                               Gender.Female)) {
                  //                         _model.kinksAvailable =
                  //                             await queryOptionsForSelectorsRecordOnce(
                  //                           queryBuilder:
                  //                               (optionsForSelectorsRecord) =>
                  //                                   optionsForSelectorsRecord
                  //                                       .where(
                  //                             'name',
                  //                             isEqualTo: 'Kinks',
                  //                           ),
                  //                           singleRecord: true,
                  //                         ).then((s) => s.firstOrNull);
                  //                         await showModalBottomSheet(
                  //                           isScrollControlled: true,
                  //                           backgroundColor: Colors.transparent,
                  //                           enableDrag: false,
                  //                           useSafeArea: true,
                  //                           context: context,
                  //                           builder: (context) {
                  //                             return GestureDetector(
                  //                               onTap: () => _model.unfocusNode
                  //                                       .canRequestFocus
                  //                                   ? FocusScope.of(context)
                  //                                       .requestFocus(
                  //                                           _model.unfocusNode)
                  //                                   : FocusScope.of(context)
                  //                                       .unfocus(),
                  //                               child: Padding(
                  //                                 padding:
                  //                                     MediaQuery.viewInsetsOf(
                  //                                         context),
                  //                                 child: Container(
                  //                                   height: MediaQuery.sizeOf(
                  //                                               context)
                  //                                           .height *
                  //                                       0.9,
                  //                                   child:
                  //                                       KinksReqSelectorSheetWidget(
                  //                                     options: _model
                  //                                         .kinksAvailable!
                  //                                         .options,
                  //                                   ),
                  //                                 ),
                  //                               ),
                  //                             );
                  //                           },
                  //                         ).then(
                  //                             (value) => safeSetState(() {}));
                  //                       } else {
                  //                         if (valueOrDefault<bool>(
                  //                             currentUserDocument
                  //                                 ?.purchasesTestingNewDesign1,
                  //                             false)) {
                  //                           try {
                  //                             analytics.logEvent(
                  //                                 'Navigated to Evolved from Settings');
                  //                           } catch (e) {}
                  //                           ;
                  //                           context.pushNamed(
                  //                             'EvolvedSubscriptionNew',
                  //                             extra: <String, dynamic>{
                  //                               kTransitionInfoKey:
                  //                                   TransitionInfo(
                  //                                 hasTransition: true,
                  //                                 transitionType:
                  //                                     PageTransitionType
                  //                                         .bottomToTop,
                  //                               ),
                  //                             },
                  //                           );
                  //                         } else {
                  //                           try {
                  //                             analytics.logEvent(
                  //                                 'Navigated to Evolved from Settings');
                  //                           } catch (e) {}
                  //                           ;
                  //                           context.pushNamed(
                  //                             'EvolvedSubscription',
                  //                             extra: <String, dynamic>{
                  //                               kTransitionInfoKey:
                  //                                   TransitionInfo(
                  //                                 hasTransition: true,
                  //                                 transitionType:
                  //                                     PageTransitionType
                  //                                         .bottomToTop,
                  //                               ),
                  //                             },
                  //                           );
                  //                         }
                  //                       }

                  //                       setState(() {});
                  //                     },
                  //                     child: Row(
                  //                       mainAxisSize: MainAxisSize.max,
                  //                       mainAxisAlignment:
                  //                           MainAxisAlignment.spaceBetween,
                  //                       children: [
                  //                         Text(
                  //                           'Kinks',
                  //                           style: FlutterFlowTheme.of(context)
                  //                               .bodyMedium
                  //                               .override(
                  //                                 fontFamily: 'BT Beau Sans',
                  //                                 fontSize: 16.0,
                  //                                 letterSpacing: 0.0,
                  //                                 useGoogleFonts: false,
                  //                               ),
                  //                         ),
                  //                         Row(
                  //                           mainAxisSize: MainAxisSize.max,
                  //                           mainAxisAlignment:
                  //                               MainAxisAlignment.end,
                  //                           children: [
                  //                             Padding(
                  //                               padding: EdgeInsetsDirectional
                  //                                   .fromSTEB(
                  //                                       0.0, 0.0, 10.0, 0.0),
                  //                               child: Text(
                  //                                 ' ',
                  //                                 style: FlutterFlowTheme.of(
                  //                                         context)
                  //                                     .bodyMedium
                  //                                     .override(
                  //                                       fontFamily:
                  //                                           'BT Beau Sans',
                  //                                       color:
                  //                                           Color(0xFF4F5865),
                  //                                       fontSize: 16.0,
                  //                                       letterSpacing: 0.0,
                  //                                       useGoogleFonts: false,
                  //                                     ),
                  //                               ),
                  //                             ),
                  //                             FaIcon(
                  //                               FontAwesomeIcons.angleRight,
                  //                               color: Color(0xFFB9BFC8),
                  //                               size: 18.0,
                  //                             ),
                  //                           ],
                  //                         ),
                  //                       ],
                  //                     ),
                  //                   ),
                  //                 ),
                  //               ),
                  //             ),
                  //           Padding(
                  //             padding: EdgeInsetsDirectional.fromSTEB(
                  //                 0.0, 9.0, 0.0, 0.0),
                  //             child: Container(
                  //               height: 45.0,
                  //               decoration: BoxDecoration(
                  //                 color: FlutterFlowTheme.of(context)
                  //                     .primaryBackground,
                  //                 borderRadius: BorderRadius.circular(10.0),
                  //               ),
                  //               child: Padding(
                  //                 padding: EdgeInsetsDirectional.fromSTEB(
                  //                     16.0, 0.0, 16.0, 0.0),
                  //                 child: InkWell(
                  //                   splashColor: Colors.transparent,
                  //                   focusColor: Colors.transparent,
                  //                   hoverColor: Colors.transparent,
                  //                   highlightColor: Colors.transparent,
                  //                   onTap: () async {
                  //                     if (revenue_cat.activeEntitlementIds
                  //                             .contains('evolved_access') ||
                  //                         (currentUserDocument?.gender ==
                  //                             Gender.Female)) {
                  //                       _model.hobbiesAvailable =
                  //                           await queryOptionsForSelectorsRecordOnce(
                  //                         queryBuilder:
                  //                             (optionsForSelectorsRecord) =>
                  //                                 optionsForSelectorsRecord
                  //                                     .where(
                  //                           'name',
                  //                           isEqualTo: 'Hobbies',
                  //                         ),
                  //                         singleRecord: true,
                  //                       ).then((s) => s.firstOrNull);
                  //                       await showModalBottomSheet(
                  //                         isScrollControlled: true,
                  //                         backgroundColor: Colors.transparent,
                  //                         enableDrag: false,
                  //                         useSafeArea: true,
                  //                         context: context,
                  //                         builder: (context) {
                  //                           return GestureDetector(
                  //                             onTap: () => _model.unfocusNode
                  //                                     .canRequestFocus
                  //                                 ? FocusScope.of(context)
                  //                                     .requestFocus(
                  //                                         _model.unfocusNode)
                  //                                 : FocusScope.of(context)
                  //                                     .unfocus(),
                  //                             child: Padding(
                  //                               padding:
                  //                                   MediaQuery.viewInsetsOf(
                  //                                       context),
                  //                               child: Container(
                  //                                 height:
                  //                                     MediaQuery.sizeOf(context)
                  //                                             .height *
                  //                                         0.9,
                  //                                 child:
                  //                                     InterestsReqSelectorSheetWidget(
                  //                                   options: _model
                  //                                       .hobbiesAvailable!
                  //                                       .options,
                  //                                 ),
                  //                               ),
                  //                             ),
                  //                           );
                  //                         },
                  //                       ).then((value) => safeSetState(() {}));
                  //                     } else {
                  //                       if (valueOrDefault<bool>(
                  //                           currentUserDocument
                  //                               ?.purchasesTestingNewDesign1,
                  //                           false)) {
                  //                         try {
                  //                           analytics.logEvent(
                  //                               'Navigated to Evolved from Settings');
                  //                         } catch (e) {}
                  //                         ;
                  //                         context.pushNamed(
                  //                           'EvolvedSubscriptionNew',
                  //                           extra: <String, dynamic>{
                  //                             kTransitionInfoKey:
                  //                                 TransitionInfo(
                  //                               hasTransition: true,
                  //                               transitionType:
                  //                                   PageTransitionType
                  //                                       .bottomToTop,
                  //                             ),
                  //                           },
                  //                         );
                  //                       } else {
                  //                         try {
                  //                           analytics.logEvent(
                  //                               'Navigated to Evolved from Settings');
                  //                         } catch (e) {}
                  //                         ;
                  //                         context.pushNamed(
                  //                           'EvolvedSubscription',
                  //                           extra: <String, dynamic>{
                  //                             kTransitionInfoKey:
                  //                                 TransitionInfo(
                  //                               hasTransition: true,
                  //                               transitionType:
                  //                                   PageTransitionType
                  //                                       .bottomToTop,
                  //                             ),
                  //                           },
                  //                         );
                  //                       }
                  //                     }

                  //                     setState(() {});
                  //                   },
                  //                   child: Row(
                  //                     mainAxisSize: MainAxisSize.max,
                  //                     mainAxisAlignment:
                  //                         MainAxisAlignment.spaceBetween,
                  //                     children: [
                  //                       Text(
                  //                         'Interests',
                  //                         style: FlutterFlowTheme.of(context)
                  //                             .bodyMedium
                  //                             .override(
                  //                               fontFamily: 'BT Beau Sans',
                  //                               fontSize: 16.0,
                  //                               useGoogleFonts: false,
                  //                             ),
                  //                       ),
                  //                       Row(
                  //                         mainAxisSize: MainAxisSize.max,
                  //                         mainAxisAlignment:
                  //                             MainAxisAlignment.end,
                  //                         children: [
                  //                           Padding(
                  //                             padding: EdgeInsetsDirectional
                  //                                 .fromSTEB(
                  //                                     0.0, 0.0, 10.0, 0.0),
                  //                             child: Text(
                  //                               ' ',
                  //                               style:
                  //                                   FlutterFlowTheme.of(context)
                  //                                       .bodyMedium
                  //                                       .override(
                  //                                         fontFamily:
                  //                                             'BT Beau Sans',
                  //                                         color:
                  //                                             Color(0xFF4F5865),
                  //                                         fontSize: 16.0,
                  //                                         useGoogleFonts: false,
                  //                                       ),
                  //                             ),
                  //                           ),
                  //                           FaIcon(
                  //                             FontAwesomeIcons.angleRight,
                  //                             color: Color(0xFFB9BFC8),
                  //                             size: 18.0,
                  //                           ),
                  //                         ],
                  //                       ),
                  //                     ],
                  //                   ),
                  //                 ),
                  //               ),
                  //             ),
                  //           ),
                  //           Padding(
                  //             padding: EdgeInsetsDirectional.fromSTEB(
                  //                 0.0, 9.0, 0.0, 5.0),
                  //             child: Container(
                  //               width: double.infinity,
                  //               height: 120.0,
                  //               decoration: BoxDecoration(
                  //                 color: FlutterFlowTheme.of(context)
                  //                     .primaryBackground,
                  //                 borderRadius: BorderRadius.circular(10.0),
                  //               ),
                  //               child: Stack(
                  //                 children: [
                  //                   Padding(
                  //                     padding: EdgeInsetsDirectional.fromSTEB(
                  //                         16.0, 17.0, 16.0, 17.0),
                  //                     child: Column(
                  //                       mainAxisSize: MainAxisSize.max,
                  //                       children: [
                  //                         Row(
                  //                           mainAxisSize: MainAxisSize.max,
                  //                           mainAxisAlignment:
                  //                               MainAxisAlignment.spaceBetween,
                  //                           children: [
                  //                             Align(
                  //                               alignment: AlignmentDirectional(
                  //                                   -1.0, 0.0),
                  //                               child: Text(
                  //                                 'Height preference',
                  //                                 style: FlutterFlowTheme.of(
                  //                                         context)
                  //                                     .bodyMedium
                  //                                     .override(
                  //                                       fontFamily:
                  //                                           'BT Beau Sans',
                  //                                       fontSize: 16.0,
                  //                                       useGoogleFonts: false,
                  //                                     ),
                  //                               ),
                  //                             ),
                  //                             Align(
                  //                               alignment: AlignmentDirectional(
                  //                                   -1.0, 0.0),
                  //                               child: Text(
                  //                                 '${FFAppState().heightPrefLow <= 80.0 ? '<80cm' : '${formatNumber(
                  //                                     FFAppState()
                  //                                         .heightPrefLow,
                  //                                     formatType:
                  //                                         FormatType.custom,
                  //                                     format: '##',
                  //                                     locale: '',
                  //                                   )}cm'}-${'${valueOrDefault<String>(
                  //                                   formatNumber(
                  //                                     FFAppState()
                  //                                         .heightPrefHigh,
                  //                                     formatType:
                  //                                         FormatType.custom,
                  //                                     format: '##',
                  //                                     locale: '',
                  //                                   ),
                  //                                   '##',
                  //                                 )}cm'}',
                  //                                 style: FlutterFlowTheme.of(
                  //                                         context)
                  //                                     .bodyMedium
                  //                                     .override(
                  //                                       fontFamily:
                  //                                           'BT Beau Sans',
                  //                                       color:
                  //                                           Color(0xFF4F5865),
                  //                                       fontSize: 16.0,
                  //                                       useGoogleFonts: false,
                  //                                     ),
                  //                               ),
                  //                             ),
                  //                           ],
                  //                         ),
                  //                         Column(
                  //                           mainAxisSize: MainAxisSize.max,
                  //                           children: [
                  //                             Container(
                  //                               width:
                  //                                   MediaQuery.sizeOf(context)
                  //                                           .width *
                  //                                       0.9,
                  //                               height: 70.0,
                  //                               child: custom_widgets
                  //                                   .RangeSliderWidgetHeight(
                  //                                 width:
                  //                                     MediaQuery.sizeOf(context)
                  //                                             .width *
                  //                                         0.9,
                  //                                 height: 70.0,
                  //                                 refreshPageUI: () async {
                  //                                   setState(() {});
                  //                                 },
                  //                               ),
                  //                             ),
                  //                           ],
                  //                         ),
                  //                       ],
                  //                     ),
                  //                   ),
                  //                   if (!revenue_cat.activeEntitlementIds
                  //                           .contains('evolved_access') &&
                  //                       (currentUserDocument?.gender !=
                  //                           Gender.Female))
                  //                     AuthUserStreamWidget(
                  //                       builder: (context) => InkWell(
                  //                         splashColor: Colors.transparent,
                  //                         focusColor: Colors.transparent,
                  //                         hoverColor: Colors.transparent,
                  //                         highlightColor: Colors.transparent,
                  //                         onTap: () async {
                  //                           if (currentUserDocument?.gender == Gender.Female &&
                  //                             valueOrDefault(currentUserDocument?.fiveTestGroup, 0) > valueOrDefault(getRemoteConfigInt('divine_legacy_t'), 0)) {
                  //                             analytics.logEvent('Navigated to Divine from Settings');
                  //                             context.pushNamed(
                  //                               'DivineSubscription',
                  //                               extra: <String, dynamic>{
                  //                                 kTransitionInfoKey: TransitionInfo(
                  //                                   hasTransition: true,
                  //                                   transitionType: PageTransitionType.bottomToTop,
                  //                                   duration: Duration(milliseconds: 200),
                  //                                 ),
                  //                               },
                  //                             );
                  //                           } else
                  //                           if (valueOrDefault<bool>(
                  //                               currentUserDocument
                  //                                   ?.purchasesTestingNewDesign1,
                  //                               false)) {
                  //                             try {
                  //                               analytics.logEvent(
                  //                                   'Navigated to Evolved from Settings');
                  //                             } catch (e) {}
                  //                             ;
                  //                             context.pushNamed(
                  //                               'EvolvedSubscriptionNew',
                  //                               extra: <String, dynamic>{
                  //                                 kTransitionInfoKey:
                  //                                     TransitionInfo(
                  //                                   hasTransition: true,
                  //                                   transitionType:
                  //                                       PageTransitionType
                  //                                           .bottomToTop,
                  //                                 ),
                  //                               },
                  //                             );
                  //                           } else {
                  //                             try {
                  //                               analytics.logEvent(
                  //                                   'Navigated to Evolved from Settings');
                  //                             } catch (e) {}
                  //                             ;
                  //                             context.pushNamed(
                  //                               'EvolvedSubscription',
                  //                               extra: <String, dynamic>{
                  //                                 kTransitionInfoKey:
                  //                                     TransitionInfo(
                  //                                   hasTransition: true,
                  //                                   transitionType:
                  //                                       PageTransitionType
                  //                                           .bottomToTop,
                  //                                 ),
                  //                               },
                  //                             );
                  //                           }
                  //                         },
                  //                         child: Container(
                  //                           width: double.infinity,
                  //                           height: 120.0,
                  //                           decoration: BoxDecoration(),
                  //                         ),
                  //                       ),
                  //                     ),
                  //                 ],
                  //         ]
                  //               ),
                  //             ),
                  //   ]
                  //           ),
                  //         ],
                  //       ),
                  //     ),
                  // ),

                  Align(
                    alignment: const AlignmentDirectional(-1.0, -1.0),
                    child: Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(
                          3.0, 20.0, 0.0, 0.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Align(
                                alignment:
                                    const AlignmentDirectional(-1.0, -1.0),
                                child: Text(
                                  'Stay invisible',
                                  style: FlutterFlowTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        fontSize: 16.0,
                                        fontWeight: FontWeight.w600,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    5, 0, 0, 0),
                                child: Container(
                                  height: 19,
                                  decoration: BoxDecoration(
                                    gradient: const LinearGradient(
                                      colors: [
                                        Color(0xFF575757),
                                        Color(0xFF949494)
                                      ],
                                      stops: [0, 1],
                                      begin: AlignmentDirectional(1, 0),
                                      end: AlignmentDirectional(-1, 0),
                                    ),
                                    borderRadius: BorderRadius.circular(100),
                                  ),
                                  child: Align(
                                    alignment: const AlignmentDirectional(0, 0),
                                    child: Padding(
                                      padding:
                                          const EdgeInsetsDirectional.fromSTEB(
                                              7, 0, 7, 0),
                                      child: Text(
                                        'Plus',
                                        textAlign: TextAlign.center,
                                        style: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .override(
                                              fontFamily: 'BT Beau Sans',
                                              color:
                                                  FlutterFlowTheme.of(context)
                                                      .info,
                                              fontSize: 12,
                                              letterSpacing: 0,
                                              fontWeight: FontWeight.w500,
                                              useGoogleFonts: false,
                                            ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          Align(
                            alignment: const AlignmentDirectional(-1.0, 0.0),
                            child: Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 6.0, 25.0, 0.0),
                              child: Text(
                                'See others without being seen.',
                                style: FlutterFlowTheme.of(context).bodyMedium,
                              ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 9.0, 0.0, 0.0),
                            child: Container(
                              height: 45.0,
                              decoration: BoxDecoration(
                                color: FlutterFlowTheme.of(context)
                                    .primaryBackground,
                                borderRadius: BorderRadius.circular(10.0),
                              ),
                              child: Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    16.0, 0.0, 16.0, 0.0),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Stay invisible',
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            fontFamily: 'BT Beau Sans',
                                            fontSize: 16.0,
                                            useGoogleFonts: false,
                                          ),
                                    ),
                                    Align(
                                      alignment:
                                          const AlignmentDirectional(1.0, 0.0),
                                      child: Stack(
                                        alignment: const AlignmentDirectional(
                                            1.0, 0.0),
                                        children: [
                                          Builder(
                                            builder: (context) =>
                                                AuthUserStreamWidget(
                                              builder: (context) =>
                                                  Switch.adaptive(
                                                value: _model
                                                    .incognitoSwitchValue!,
                                                onChanged: (newValue) async {
                                                  setState(() => _model
                                                          .incognitoSwitchValue =
                                                      newValue);
                                                  if (newValue) {
                                                    var shouldSetState = false;
                                                    if (revenue_cat.activeEntitlementIds.contains(
                                                            'plus_access') ||
                                                        revenue_cat
                                                            .activeEntitlementIds
                                                            .contains(
                                                                'evolved_access') ||
                                                        revenue_cat
                                                            .activeEntitlementIds
                                                            .contains(
                                                                'paid_standard_1w') ||
                                                        revenue_cat
                                                            .activeEntitlementIds
                                                            .contains(
                                                                'paid_standard_lifetime') ||
                                                        (revenue_cat
                                                                .activeEntitlementIds
                                                                .contains(
                                                                    'evolved_access') ||
                                                            (currentUserDocument
                                                                        ?.gender ==
                                                                    Gender
                                                                        .Female) &&
                                                                revenue_cat
                                                                    .activeEntitlementIds
                                                                    .contains(
                                                                        'divine_access'))) {
                                                      await currentUserReference!
                                                          .update(
                                                              createUsersRecordData(
                                                        incognito: true,
                                                      ));
                                                      try {
                                                        final result =
                                                            await FirebaseFunctions
                                                                    .instanceFor(
                                                                        region:
                                                                            'europe-west2')
                                                                .httpsCallable(
                                                                    'changeIncognitoMode')
                                                                .call({});
                                                        _model.changeIncognitoCf =
                                                            ChangeIncognitoModeCloudFunctionCallResponse(
                                                          succeeded: true,
                                                        );
                                                      } on FirebaseFunctionsException catch (error) {
                                                        _model.changeIncognitoCf =
                                                            ChangeIncognitoModeCloudFunctionCallResponse(
                                                          errorCode: error.code,
                                                          succeeded: false,
                                                        );
                                                      }

                                                      if (!_model
                                                          .changeIncognitoCf!
                                                          .succeeded!) {
                                                        await showDialog(
                                                          context: context,
                                                          builder:
                                                              (dialogContext) {
                                                            return Dialog(
                                                              elevation: 0,
                                                              insetPadding:
                                                                  EdgeInsets
                                                                      .zero,
                                                              backgroundColor:
                                                                  Colors
                                                                      .transparent,
                                                              alignment: const AlignmentDirectional(
                                                                      0.0, 0.0)
                                                                  .resolve(
                                                                      Directionality.of(
                                                                          context)),
                                                              child:
                                                                  GestureDetector(
                                                                onTap: () => _model
                                                                        .unfocusNode
                                                                        .canRequestFocus
                                                                    ? FocusScope.of(
                                                                            context)
                                                                        .requestFocus(_model
                                                                            .unfocusNode)
                                                                    : FocusScope.of(
                                                                            context)
                                                                        .unfocus(),
                                                                child:
                                                                    const GeneralPopupWidget(
                                                                  alertTitle:
                                                                      'Something went wrong',
                                                                  alertText:
                                                                      'Please try again',
                                                                ),
                                                              ),
                                                            );
                                                          },
                                                        ).then((value) =>
                                                            setState(() {}));
                                                      }
                                                    } else {
                                                      try {
                                                        analytics.logEvent(
                                                            'Navigated to Plus from Settings Incognito');
                                                      } catch (e) {}
                                                      context.pushNamed(
                                                        'PlusSubscription',
                                                        extra: <String,
                                                            dynamic>{
                                                          kTransitionInfoKey:
                                                              const TransitionInfo(
                                                            hasTransition: true,
                                                            transitionType:
                                                                PageTransitionType
                                                                    .bottomToTop,
                                                          ),
                                                        },
                                                      );
                                                    }

                                                    setState(() {});
                                                  } else {
                                                    await currentUserReference!
                                                        .update(
                                                            createUsersRecordData(
                                                      incognito: false,
                                                    ));
                                                    try {
                                                      final result =
                                                          await FirebaseFunctions
                                                                  .instanceFor(
                                                                      region:
                                                                          'europe-west2')
                                                              .httpsCallable(
                                                                  'changeIncognitoMode')
                                                              .call({});
                                                      _model.changeIncognitoOff =
                                                          ChangeIncognitoModeCloudFunctionCallResponse(
                                                        succeeded: true,
                                                      );
                                                    } on FirebaseFunctionsException catch (error) {
                                                      _model.changeIncognitoOff =
                                                          ChangeIncognitoModeCloudFunctionCallResponse(
                                                        errorCode: error.code,
                                                        succeeded: false,
                                                      );
                                                    }

                                                    if (!_model
                                                        .changeIncognitoOff!
                                                        .succeeded!) {
                                                      await showDialog(
                                                        context: context,
                                                        builder:
                                                            (dialogContext) {
                                                          return Dialog(
                                                            elevation: 0,
                                                            insetPadding:
                                                                EdgeInsets.zero,
                                                            backgroundColor:
                                                                Colors
                                                                    .transparent,
                                                            alignment: const AlignmentDirectional(
                                                                    0.0, 0.0)
                                                                .resolve(
                                                                    Directionality.of(
                                                                        context)),
                                                            child:
                                                                GestureDetector(
                                                              onTap: () => _model
                                                                      .unfocusNode
                                                                      .canRequestFocus
                                                                  ? FocusScope.of(
                                                                          context)
                                                                      .requestFocus(
                                                                          _model
                                                                              .unfocusNode)
                                                                  : FocusScope.of(
                                                                          context)
                                                                      .unfocus(),
                                                              child:
                                                                  const GeneralPopupWidget(
                                                                alertTitle:
                                                                    'Something went wrong',
                                                                alertText:
                                                                    'Please try again',
                                                              ),
                                                            ),
                                                          );
                                                        },
                                                      ).then((value) =>
                                                          setState(() {}));
                                                    }

                                                    setState(() {});
                                                  }
                                                },
                                                activeColor:
                                                    const Color(0xFF15ACDC),
                                                activeTrackColor:
                                                    FlutterFlowTheme.of(context)
                                                        .accent1,
                                                inactiveTrackColor:
                                                    FlutterFlowTheme.of(context)
                                                        .alternate,
                                                inactiveThumbColor:
                                                    FlutterFlowTheme.of(context)
                                                        .secondaryText,
                                              ),
                                            ),
                                          ),
                                          if (!(revenue_cat.activeEntitlementIds
                                                  .contains('plus_access') ||
                                              revenue_cat.activeEntitlementIds
                                                  .contains('evolved_access') ||
                                              revenue_cat.activeEntitlementIds
                                                  .contains(
                                                      'paid_standard_1w') ||
                                              revenue_cat.activeEntitlementIds
                                                  .contains(
                                                      'paid_standard_lifetime') ||
                                              (revenue_cat.activeEntitlementIds
                                                      .contains(
                                                          'evolved_access') ||
                                                  (currentUserDocument
                                                              ?.gender ==
                                                          Gender.Female) &&
                                                      revenue_cat
                                                          .activeEntitlementIds
                                                          .contains(
                                                              'divine_access'))))
                                            InkWell(
                                              splashColor: Colors.transparent,
                                              focusColor: Colors.transparent,
                                              hoverColor: Colors.transparent,
                                              highlightColor:
                                                  Colors.transparent,
                                              onTap: () async {
                                                if (currentUserDocument
                                                            ?.gender ==
                                                        Gender.Female &&
                                                    valueOrDefault(
                                                            currentUserDocument
                                                                ?.fiveTestGroup,
                                                            0) >
                                                        valueOrDefault(
                                                            getRemoteConfigInt(
                                                                'divine_legacy_t'),
                                                            0)) {
                                                  analytics.logEvent(
                                                      'Navigated to Divine from Settings');
                                                  context.pushNamed(
                                                    'DivineSubscription',
                                                    extra: <String, dynamic>{
                                                      kTransitionInfoKey:
                                                          const TransitionInfo(
                                                        hasTransition: true,
                                                        transitionType:
                                                            PageTransitionType
                                                                .bottomToTop,
                                                        duration: Duration(
                                                            milliseconds: 200),
                                                      ),
                                                    },
                                                  );
                                                } else if (valueOrDefault<bool>(
                                                    currentUserDocument
                                                        ?.purchasesTestingNewDesign1,
                                                    false)) {
                                                  try {
                                                    analytics.logEvent(
                                                        'Navigated to Plus Evolved from Settings');
                                                  } catch (e) {}
                                                  context.pushNamed(
                                                    'PlusEvolvedSubscriptionNew',
                                                    queryParameters: {
                                                      'initialSubscription':
                                                          serializeParam(
                                                        'Plus',
                                                        ParamType.String,
                                                      ),
                                                    }.withoutNulls,
                                                    extra: <String, dynamic>{
                                                      kTransitionInfoKey:
                                                          const TransitionInfo(
                                                        hasTransition: true,
                                                        transitionType:
                                                            PageTransitionType
                                                                .bottomToTop,
                                                      ),
                                                    },
                                                  );
                                                } else {
                                                  if (valueOrDefault<bool>(
                                                      currentUserDocument
                                                          ?.purchasesTestingNewDesign1,
                                                      false)) {
                                                    try {
                                                      analytics.logEvent(
                                                          'Navigated to Plus Evolved from Settings');
                                                    } catch (e) {}
                                                    context.pushNamed(
                                                      'PlusEvolvedSubscriptionNew',
                                                      queryParameters: {
                                                        'initialSubscription':
                                                            serializeParam(
                                                          'Plus',
                                                          ParamType.String,
                                                        ),
                                                      }.withoutNulls,
                                                      extra: <String, dynamic>{
                                                        kTransitionInfoKey:
                                                            const TransitionInfo(
                                                          hasTransition: true,
                                                          transitionType:
                                                              PageTransitionType
                                                                  .bottomToTop,
                                                        ),
                                                      },
                                                    );
                                                  } else {
                                                    try {
                                                      analytics.logEvent(
                                                          'Navigated to Plus Evolved from Settings');
                                                    } catch (e) {}
                                                    context.pushNamed(
                                                      'PlusEvolvedSubscription',
                                                      queryParameters: {
                                                        'initialSubscription':
                                                            serializeParam(
                                                          'Plus',
                                                          ParamType.String,
                                                        ),
                                                      }.withoutNulls,
                                                      extra: <String, dynamic>{
                                                        kTransitionInfoKey:
                                                            const TransitionInfo(
                                                          hasTransition: true,
                                                          transitionType:
                                                              PageTransitionType
                                                                  .bottomToTop,
                                                        ),
                                                      },
                                                    );
                                                  }
                                                }
                                              },
                                              child: Container(
                                                width: 100.0,
                                                height: 100.0,
                                                decoration:
                                                    const BoxDecoration(),
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Align(
                    alignment: const AlignmentDirectional(-1.0, -1.0),
                    child: Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(
                          3.0, 20.0, 0.0, 0.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Align(
                                alignment:
                                    const AlignmentDirectional(-1.0, -1.0),
                                child: Text(
                                  'Pause your profile',
                                  style: FlutterFlowTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        fontSize: 16.0,
                                        letterSpacing: 0.0,
                                        fontWeight: FontWeight.w600,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                              FlutterFlowIconButton(
                                borderColor: const Color(0x004B39EF),
                                borderRadius: 20.0,
                                borderWidth: 0.0,
                                buttonSize: 40.0,
                                fillColor: const Color(0x004B39EF),
                                icon: Icon(
                                  Icons.info_outline_rounded,
                                  color:
                                      FlutterFlowTheme.of(context).primaryText,
                                  size: 24.0,
                                ),
                                onPressed: () async {
                                  await showModalBottomSheet(
                                    isScrollControlled: true,
                                    backgroundColor: Colors.transparent,
                                    enableDrag: false,
                                    context: context,
                                    builder: (context) {
                                      return GestureDetector(
                                        onTap: () =>
                                            FocusScope.of(context).unfocus(),
                                        child: Padding(
                                          padding:
                                              MediaQuery.viewInsetsOf(context),
                                          child: InfoSheetScrollableWidget(
                                            title: 'Pause your profile',
                                            body: getRemoteConfigString(
                                                'settings_pause_explanation'),
                                          ),
                                        ),
                                      );
                                    },
                                  ).then((value) => safeSetState(() {}));
                                },
                              ),
                            ],
                          ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 9.0, 0.0, 0.0),
                            child: Container(
                              height: 45.0,
                              decoration: BoxDecoration(
                                color: FlutterFlowTheme.of(context)
                                    .primaryBackground,
                                borderRadius: BorderRadius.circular(10.0),
                              ),
                              child: Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    16.0, 0.0, 16.0, 0.0),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Pause profile',
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            fontFamily: 'BT Beau Sans',
                                            fontSize: 16.0,
                                            useGoogleFonts: false,
                                          ),
                                    ),
                                    Builder(
                                      builder: (context) =>
                                          AuthUserStreamWidget(
                                        builder: (context) => Switch.adaptive(
                                          value: _model.switchValue1 ??=
                                              valueOrDefault<bool>(
                                                  currentUserDocument?.paused,
                                                  false),
                                          onChanged: (newValue) async {
                                            setState(() =>
                                                _model.switchValue1 = newValue);
                                            if (newValue) {
                                              await currentUserReference!
                                                  .update({
                                                ...createUsersRecordData(
                                                  paused: true,
                                                ),
                                                ...mapToFirestore(
                                                  {
                                                    'matchingSuggestions':
                                                        FieldValue.delete(),
                                                  },
                                                ),
                                              });
                                              try {
                                                final result =
                                                    await FirebaseFunctions
                                                            .instanceFor(
                                                                region:
                                                                    'europe-west2')
                                                        .httpsCallable(
                                                            'changePausedMode')
                                                        .call({});
                                                _model.changePausedMode1 =
                                                    ChangePausedModeCloudFunctionCallResponse(
                                                  succeeded: true,
                                                );
                                              } on FirebaseFunctionsException catch (error) {
                                                _model.changePausedMode1 =
                                                    ChangePausedModeCloudFunctionCallResponse(
                                                  errorCode: error.code,
                                                  succeeded: false,
                                                );
                                              }

                                              if (!_model.changePausedMode1!
                                                  .succeeded!) {
                                                await showDialog(
                                                  context: context,
                                                  builder: (dialogContext) {
                                                    return Dialog(
                                                      elevation: 0,
                                                      insetPadding:
                                                          EdgeInsets.zero,
                                                      backgroundColor:
                                                          Colors.transparent,
                                                      alignment:
                                                          const AlignmentDirectional(
                                                                  0.0, 0.0)
                                                              .resolve(
                                                                  Directionality.of(
                                                                      context)),
                                                      child: GestureDetector(
                                                        onTap: () => _model
                                                                .unfocusNode
                                                                .canRequestFocus
                                                            ? FocusScope.of(
                                                                    context)
                                                                .requestFocus(_model
                                                                    .unfocusNode)
                                                            : FocusScope.of(
                                                                    context)
                                                                .unfocus(),
                                                        child:
                                                            const GeneralPopupWidget(
                                                          alertTitle:
                                                              'Something went wrong',
                                                          alertText:
                                                              'Please try again',
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                ).then(
                                                    (value) => setState(() {}));
                                              }

                                              setState(() {});
                                            } else {
                                              var shouldSetState = false;

                                              await currentUserReference!
                                                  .update(createUsersRecordData(
                                                paused: false,
                                              ));
                                              try {
                                                final result =
                                                    await FirebaseFunctions
                                                            .instanceFor(
                                                                region:
                                                                    'europe-west2')
                                                        .httpsCallable(
                                                            'changePausedMode')
                                                        .call({});
                                                _model.changePausedOff1 =
                                                    ChangePausedModeCloudFunctionCallResponse(
                                                  succeeded: true,
                                                );
                                              } on FirebaseFunctionsException catch (error) {
                                                _model.changePausedOff1 =
                                                    ChangePausedModeCloudFunctionCallResponse(
                                                  errorCode: error.code,
                                                  succeeded: false,
                                                );
                                              }

                                              shouldSetState = true;
                                              if (!_model.changePausedOff1!
                                                  .succeeded!) {
                                                await showDialog(
                                                  context: context,
                                                  builder: (dialogContext) {
                                                    return Dialog(
                                                      elevation: 0,
                                                      insetPadding:
                                                          EdgeInsets.zero,
                                                      backgroundColor:
                                                          Colors.transparent,
                                                      alignment:
                                                          const AlignmentDirectional(
                                                                  0.0, 0.0)
                                                              .resolve(
                                                                  Directionality.of(
                                                                      context)),
                                                      child: GestureDetector(
                                                        onTap: () => _model
                                                                .unfocusNode
                                                                .canRequestFocus
                                                            ? FocusScope.of(
                                                                    context)
                                                                .requestFocus(_model
                                                                    .unfocusNode)
                                                            : FocusScope.of(
                                                                    context)
                                                                .unfocus(),
                                                        child:
                                                            const GeneralPopupWidget(
                                                          alertTitle:
                                                              'Something went wrong',
                                                          alertText:
                                                              'Please try again',
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                ).then(
                                                    (value) => setState(() {}));

                                                if (shouldSetState) {
                                                  setState(() {});
                                                }
                                                return;
                                              }
                                              try {
                                                final result = await FirebaseFunctions
                                                        .instanceFor(
                                                            region:
                                                                'europe-west1')
                                                    .httpsCallable(
                                                        'generateMatchesAdvanced')
                                                    .call({
                                                  "append": false,
                                                   "smoothSwiping": true,
                                                });
                                                _model.cloudFunctionuws =
                                                    GenerateMatchesCloudFunctionCallResponse(
                                                  succeeded: true,
                                                );
                                              } on FirebaseFunctionsException catch (error) {
                                                _model.cloudFunctionuws =
                                                    GenerateMatchesCloudFunctionCallResponse(
                                                  errorCode: error.code,
                                                  succeeded: false,
                                                );
                                              }

                                              shouldSetState = true;
                                              if (shouldSetState) {
                                                setState(() {});
                                              }
                                            }
                                          },
                                          activeColor: const Color(0xFFAE34E8),
                                          activeTrackColor:
                                              FlutterFlowTheme.of(context)
                                                  .accent1,
                                          inactiveTrackColor:
                                              FlutterFlowTheme.of(context)
                                                  .alternate,
                                          inactiveThumbColor:
                                              FlutterFlowTheme.of(context)
                                                  .secondaryText,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Align(
                    alignment: const AlignmentDirectional(-1.0, -1.0),
                    child: Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(
                          3.0, 20.0, 0.0, 0.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Align(
                                alignment:
                                    const AlignmentDirectional(-1.0, -1.0),
                                child: Text(
                                  'Pause your profile & matches',
                                  style: FlutterFlowTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        fontSize: 16.0,
                                        letterSpacing: 0.0,
                                        fontWeight: FontWeight.w600,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                              FlutterFlowIconButton(
                                borderColor: const Color(0x004B39EF),
                                borderRadius: 20.0,
                                borderWidth: 0.0,
                                buttonSize: 40.0,
                                fillColor: const Color(0x004B39EF),
                                icon: Icon(
                                  Icons.info_outline_rounded,
                                  color:
                                      FlutterFlowTheme.of(context).primaryText,
                                  size: 24.0,
                                ),
                                onPressed: () async {
                                  await showModalBottomSheet(
                                    isScrollControlled: true,
                                    backgroundColor: Colors.transparent,
                                    enableDrag: false,
                                    context: context,
                                    builder: (context) {
                                      return GestureDetector(
                                        onTap: () =>
                                            FocusScope.of(context).unfocus(),
                                        child: Padding(
                                          padding:
                                              MediaQuery.viewInsetsOf(context),
                                          child: InfoSheetScrollableWidget(
                                            title: 'Pause your profile & matches',
                                            body: getRemoteConfigString(
                                                'settings_full_pause_explanation'),
                                          ),
                                        ),
                                      );
                                    },
                                  ).then((value) => safeSetState(() {}));
                                },
                              ),
                            ],
                          ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 9.0, 0.0, 0.0),
                            child: Container(
                              height: 45.0,
                              decoration: BoxDecoration(
                                color: FlutterFlowTheme.of(context)
                                    .primaryBackground,
                                borderRadius: BorderRadius.circular(10.0),
                              ),
                              child: Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    16.0, 0.0, 16.0, 0.0),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Full pause',
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            fontFamily: 'BT Beau Sans',
                                            fontSize: 16.0,
                                            useGoogleFonts: false,
                                          ),
                                    ),
                                    Builder(
                                      builder: (context) =>
                                          AuthUserStreamWidget(
                                        builder: (context) => Switch.adaptive(
                                          value: _model.switchValue5 ??=
                                              valueOrDefault<bool>(
                                                  currentUserDocument?.fullPaused,
                                                  false),
                                          onChanged: (newValue) async {
                                            setState(() =>
                                                _model.switchValue5 = newValue);
                                            if (newValue) {
                                              await currentUserReference!
                                                  .update({
                                                ...createUsersRecordData(
                                                  fullPaused: true,
                                                  paused: true,
                                                ),
                                                ...mapToFirestore(
                                                  {
                                                    'matchingSuggestions':
                                                        FieldValue.delete(),
                                                  },
                                                ),
                                              });
                                              await currentUserDocument?.publicProfile?.update({
                                                'fullPaused': true
                                              });
                                              try {
                                                final result =
                                                    await FirebaseFunctions
                                                            .instanceFor(
                                                                region:
                                                                    'europe-west2')
                                                        .httpsCallable(
                                                            'changePausedMode')
                                                        .call({});
                                                _model.changePausedMode1 =
                                                    ChangePausedModeCloudFunctionCallResponse(
                                                  succeeded: true,
                                                );
                                              } on FirebaseFunctionsException catch (error) {
                                                _model.changePausedMode1 =
                                                    ChangePausedModeCloudFunctionCallResponse(
                                                  errorCode: error.code,
                                                  succeeded: false,
                                                );
                                              }

                                              if (!_model.changePausedMode1!
                                                  .succeeded!) {
                                                await showDialog(
                                                  context: context,
                                                  builder: (dialogContext) {
                                                    return Dialog(
                                                      elevation: 0,
                                                      insetPadding:
                                                          EdgeInsets.zero,
                                                      backgroundColor:
                                                          Colors.transparent,
                                                      alignment:
                                                          const AlignmentDirectional(
                                                                  0.0, 0.0)
                                                              .resolve(
                                                                  Directionality.of(
                                                                      context)),
                                                      child: GestureDetector(
                                                        onTap: () => _model
                                                                .unfocusNode
                                                                .canRequestFocus
                                                            ? FocusScope.of(
                                                                    context)
                                                                .requestFocus(_model
                                                                    .unfocusNode)
                                                            : FocusScope.of(
                                                                    context)
                                                                .unfocus(),
                                                        child:
                                                            const GeneralPopupWidget(
                                                          alertTitle:
                                                              'Something went wrong',
                                                          alertText:
                                                              'Please try again',
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                ).then(
                                                    (value) => setState(() {}));
                                              }

                                              setState(() {});
                                            } else {
                                              var shouldSetState = false;

                                              await currentUserReference!
                                                  .update(createUsersRecordData(
                                                paused: false,
                                                fullPaused: false,
                                              ));
                                              await currentUserDocument?.publicProfile?.update({
                                                'fullPaused': false
                                              });
                                              try {
                                                final result =
                                                    await FirebaseFunctions
                                                            .instanceFor(
                                                                region:
                                                                    'europe-west2')
                                                        .httpsCallable(
                                                            'changePausedMode')
                                                        .call({});
                                                _model.changePausedOff1 =
                                                    ChangePausedModeCloudFunctionCallResponse(
                                                  succeeded: true,
                                                );
                                              } on FirebaseFunctionsException catch (error) {
                                                _model.changePausedOff1 =
                                                    ChangePausedModeCloudFunctionCallResponse(
                                                  errorCode: error.code,
                                                  succeeded: false,
                                                );
                                              }

                                              shouldSetState = true;
                                              if (!_model.changePausedOff1!
                                                  .succeeded!) {
                                                await showDialog(
                                                  context: context,
                                                  builder: (dialogContext) {
                                                    return Dialog(
                                                      elevation: 0,
                                                      insetPadding:
                                                          EdgeInsets.zero,
                                                      backgroundColor:
                                                          Colors.transparent,
                                                      alignment:
                                                          const AlignmentDirectional(
                                                                  0.0, 0.0)
                                                              .resolve(
                                                                  Directionality.of(
                                                                      context)),
                                                      child: GestureDetector(
                                                        onTap: () => _model
                                                                .unfocusNode
                                                                .canRequestFocus
                                                            ? FocusScope.of(
                                                                    context)
                                                                .requestFocus(_model
                                                                    .unfocusNode)
                                                            : FocusScope.of(
                                                                    context)
                                                                .unfocus(),
                                                        child:
                                                            const GeneralPopupWidget(
                                                          alertTitle:
                                                              'Something went wrong',
                                                          alertText:
                                                              'Please try again',
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                ).then(
                                                    (value) => setState(() {}));

                                                if (shouldSetState) {
                                                  setState(() {});
                                                }
                                                return;
                                              }
                                              try {
                                                final result = await FirebaseFunctions
                                                        .instanceFor(
                                                            region:
                                                                'europe-west1')
                                                    .httpsCallable(
                                                        'generateMatchesAdvanced')
                                                    .call({
                                                  "append": false,
                                                });
                                                _model.cloudFunctionuws =
                                                    GenerateMatchesCloudFunctionCallResponse(
                                                  succeeded: true,
                                                );
                                              } on FirebaseFunctionsException catch (error) {
                                                _model.cloudFunctionuws =
                                                    GenerateMatchesCloudFunctionCallResponse(
                                                  errorCode: error.code,
                                                  succeeded: false,
                                                );
                                              }

                                              shouldSetState = true;
                                              if (shouldSetState) {
                                                setState(() {});
                                              }
                                            }
                                          },
                                          activeColor: const Color(0xFFAE34E8),
                                          activeTrackColor:
                                              FlutterFlowTheme.of(context)
                                                  .accent1,
                                          inactiveTrackColor:
                                              FlutterFlowTheme.of(context)
                                                  .alternate,
                                          inactiveThumbColor:
                                              FlutterFlowTheme.of(context)
                                                  .secondaryText,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Visibility(
                    visible: getRemoteConfigBool('kink_shown'),
                    child: Align(
                      alignment: const AlignmentDirectional(-1.0, -1.0),
                      child: Padding(
                        padding: const EdgeInsetsDirectional.fromSTEB(
                            3.0, 20.0, 0.0, 0.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Align(
                                  alignment:
                                      const AlignmentDirectional(-1.0, -1.0),
                                  child: Text(
                                    getRemoteConfigString('settings_kink_title'),
                                    style: FlutterFlowTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'BT Beau Sans',
                                          fontSize: 16.0,
                                          letterSpacing: 0.0,
                                          fontWeight: FontWeight.w600,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ),
                                FlutterFlowIconButton(
                                  borderColor: const Color(0x004B39EF),
                                  borderRadius: 20.0,
                                  borderWidth: 0.0,
                                  buttonSize: 40.0,
                                  fillColor: const Color(0x004B39EF),
                                  icon: Icon(
                                    Icons.info_outline_rounded,
                                    color:
                                        FlutterFlowTheme.of(context).primaryText,
                                    size: 24.0,
                                  ),
                                  onPressed: () async {
                                    await showModalBottomSheet(
                                      isScrollControlled: true,
                                      backgroundColor: Colors.transparent,
                                      enableDrag: false,
                                      context: context,
                                      builder: (context) {
                                        return GestureDetector(
                                          onTap: () =>
                                              FocusScope.of(context).unfocus(),
                                          child: Padding(
                                            padding:
                                                MediaQuery.viewInsetsOf(context),
                                            child: InfoSheetScrollableWidget(
                                              title: getRemoteConfigString(
                                                  'settings_kink_title'),
                                              body: getRemoteConfigString(
                                                  'settings_kink_explanation'),
                                            ),
                                          ),
                                        );
                                      },
                                    ).then((value) => safeSetState(() {}));
                                  },
                                ),
                              ],
                            ),
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 9.0, 0.0, 0.0),
                              child: Container(
                                height: 45.0,
                                decoration: BoxDecoration(
                                  color: FlutterFlowTheme.of(context)
                                      .primaryBackground,
                                  borderRadius: BorderRadius.circular(10.0),
                                ),
                                child: Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      16.0, 0.0, 16.0, 0.0),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        getRemoteConfigString(
                                            'settings_kink_interested_title'),
                                        style: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .override(
                                              fontFamily: 'BT Beau Sans',
                                              fontSize: 16.0,
                                              useGoogleFonts: false,
                                            ),
                                      ),
                                      Builder(
                                        builder: (context) =>
                                            AuthUserStreamWidget(
                                          builder: (context) => Switch.adaptive(
                                            value: _model.kinkInterested ?? false,
                                            onChanged: (newValue) async {
                                              changeKinkInterest(newValue);
                                            },
                                            activeColor: const Color(0xFFAE34E8),
                                            activeTrackColor:
                                                FlutterFlowTheme.of(context)
                                                    .accent1,
                                            inactiveTrackColor:
                                                FlutterFlowTheme.of(context)
                                                    .alternate,
                                            inactiveThumbColor:
                                                FlutterFlowTheme.of(context)
                                                    .secondaryText,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            
                            AnimatedContainer(
                              duration: const Duration(milliseconds: 200),
                              curve: Curves.ease,
                              height: _model.kinkInterested == true ? 108 : 0,
                              child: Column(
                                children: [
                                  Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        0.0, 9.0, 0.0, 0.0),
                                    child: Container(
                                      height: 45.0,
                                      decoration: BoxDecoration(
                                        color: FlutterFlowTheme.of(context)
                                            .primaryBackground,
                                        borderRadius: BorderRadius.circular(10.0),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsetsDirectional.fromSTEB(
                                            16.0, 0.0, 16.0, 0.0),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.max,
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text(
                                              getRemoteConfigString(
                                                  'settings_kinks_others_visible_title'),
                                              style: FlutterFlowTheme.of(context)
                                                  .bodyMedium
                                                  .override(
                                                    fontFamily: 'BT Beau Sans',
                                                    fontSize: 16.0,
                                                    useGoogleFonts: false,
                                                  ),
                                            ),
                                            Builder(
                                              builder: (context) =>
                                                  AuthUserStreamWidget(
                                                builder: (context) => Switch.adaptive(
                                                  value: _model.kinksOthersVisible ??
                                                      false,
                                                  onChanged: (newValue) async {
                                                    changeOthersKinkVisible(newValue);
                                                  },
                                                  activeColor: const Color(0xFFAE34E8),
                                                  activeTrackColor:
                                                      FlutterFlowTheme.of(context)
                                                          .accent1,
                                                  inactiveTrackColor:
                                                      FlutterFlowTheme.of(context)
                                                          .alternate,
                                                  inactiveThumbColor:
                                                      FlutterFlowTheme.of(context)
                                                          .secondaryText,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                  Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 9.0, 0.0, 0.0),
                                child: Container(
                                  height: 45.0,
                                  decoration: BoxDecoration(
                                    color: FlutterFlowTheme.of(context)
                                        .primaryBackground,
                                    borderRadius: BorderRadius.circular(10.0),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        16.0, 0.0, 16.0, 0.0),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          getRemoteConfigString(
                                              'settings_kinks_own_visible_title'),
                                          style: FlutterFlowTheme.of(context)
                                              .bodyMedium
                                              .override(
                                                fontFamily: 'BT Beau Sans',
                                                fontSize: 16.0,
                                                useGoogleFonts: false,
                                              ),
                                        ),
                                        Builder(
                                          builder: (context) =>
                                              AuthUserStreamWidget(
                                            builder: (context) => Switch.adaptive(
                                              value: _model.kinksOwnVisible ?? false,
                                              onChanged: (newValue) async {
                                                changeOwnKinkEnabled(newValue);
                                              },
                                              activeColor: const Color(0xFFAE34E8),
                                              activeTrackColor:
                                                  FlutterFlowTheme.of(context)
                                                      .accent1,
                                              inactiveTrackColor:
                                                  FlutterFlowTheme.of(context)
                                                      .alternate,
                                              inactiveThumbColor:
                                                  FlutterFlowTheme.of(context)
                                                      .secondaryText,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                                ],
                              ),
                            ),
                           
                          ],
                        ),
                      ),
                    ),
                  ),
                  Align(
                    alignment: const AlignmentDirectional(-1.0, -1.0),
                    child: Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(
                          3.0, 20.0, 0.0, 0.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Align(
                                alignment:
                                    const AlignmentDirectional(-1.0, -1.0),
                                child: Text(
                                  'Avoid people you know',
                                  style: FlutterFlowTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        fontSize: 16.0,
                                        letterSpacing: 0.0,
                                        fontWeight: FontWeight.w600,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                              FlutterFlowIconButton(
                                borderColor: const Color(0x004B39EF),
                                borderRadius: 20.0,
                                borderWidth: 0.0,
                                buttonSize: 40.0,
                                fillColor: const Color(0x004B39EF),
                                icon: Icon(
                                  Icons.info_outline_rounded,
                                  color:
                                      FlutterFlowTheme.of(context).primaryText,
                                  size: 24.0,
                                ),
                                onPressed: () async {
                                  await showModalBottomSheet(
                                    isScrollControlled: true,
                                    backgroundColor: Colors.transparent,
                                    enableDrag: false,
                                    context: context,
                                    builder: (context) {
                                      return GestureDetector(
                                        onTap: () =>
                                            FocusScope.of(context).unfocus(),
                                        child: Padding(
                                          padding:
                                              MediaQuery.viewInsetsOf(context),
                                          child: InfoSheetScrollableWidget(
                                            title: 'Avoid people you know',
                                            body: getRemoteConfigString(
                                                'settings_contact_blocking_explanation'),
                                          ),
                                        ),
                                      );
                                    },
                                  ).then((value) => safeSetState(() {}));
                                },
                              ),
                            ],
                          ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 9.0, 0.0, 0.0),
                            child: Container(
                              height: 45.0,
                              decoration: BoxDecoration(
                                color: FlutterFlowTheme.of(context)
                                    .primaryBackground,
                                borderRadius: BorderRadius.circular(10.0),
                              ),
                              child: Builder(
                                builder: (context) => Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      16.0, 0.0, 16.0, 0.0),
                                  child: InkWell(
                                    splashColor: Colors.transparent,
                                    focusColor: Colors.transparent,
                                    hoverColor: Colors.transparent,
                                    highlightColor: Colors.transparent,
                                    onTap: () async {
                                      await requestPermission(
                                          contactsPermission);
                                      if (await getPermissionStatus(
                                          contactsPermission)) {
                                        _model.contacts = await actions
                                            .fetchAndCompareContacts();
                                        await showModalBottomSheet(
                                          isScrollControlled: true,
                                          backgroundColor: Colors.transparent,
                                          enableDrag: false,
                                          useSafeArea: true,
                                          context: context,
                                          builder: (context) {
                                            return GestureDetector(
                                              onTap: () => _model.unfocusNode
                                                      .canRequestFocus
                                                  ? FocusScope.of(context)
                                                      .requestFocus(
                                                          _model.unfocusNode)
                                                  : FocusScope.of(context)
                                                      .unfocus(),
                                              child: Padding(
                                                padding:
                                                    MediaQuery.viewInsetsOf(
                                                        context),
                                                child: SizedBox(
                                                  height:
                                                      MediaQuery.sizeOf(context)
                                                              .height *
                                                          0.9,
                                                  child: ContactSheetWidget(
                                                    contacts: _model.contacts!
                                                        .sortedList(
                                                            (e) => e.name),
                                                    callback: () async {
                                                      await currentUserReference!
                                                          .update({
                                                        ...mapToFirestore(
                                                          {
                                                            'blockedContactNumberOnly':
                                                                FFAppState()
                                                                    .blockedClientNumbersOnly,
                                                            'blockedContacts':
                                                                getContactForBlockingListFirestoreData(
                                                              FFAppState()
                                                                  .blockedContacts,
                                                            ),
                                                            'matchingSuggestions':
                                                                FieldValue
                                                                    .delete(),
                                                          },
                                                        ),
                                                      });
                                                      try {
                                                        final result =
                                                            await FirebaseFunctions
                                                                    .instanceFor(
                                                                        region:
                                                                            'europe-west1')
                                                                .httpsCallable(
                                                                    'generateMatchesAdvanced')
                                                                .call({
                                                          "append": false,
                                                           "smoothSwiping": true,
                                                        });
                                                        _model.matches =
                                                            GenerateMatchesCloudFunctionCallResponse(
                                                          succeeded: true,
                                                        );
                                                      } on FirebaseFunctionsException catch (error) {
                                                        _model.matches =
                                                            GenerateMatchesCloudFunctionCallResponse(
                                                          errorCode: error.code,
                                                          succeeded: false,
                                                        );
                                                      }

                                                      Navigator.pop(context);
                                                    },
                                                  ),
                                                ),
                                              ),
                                            );
                                          },
                                        ).then((value) => safeSetState(() {}));
                                      } else {
                                        await showDialog(
                                          context: context,
                                          builder: (dialogContext) {
                                            return Dialog(
                                              elevation: 0,
                                              insetPadding: EdgeInsets.zero,
                                              backgroundColor:
                                                  Colors.transparent,
                                              alignment:
                                                  const AlignmentDirectional(
                                                          0.0, 0.0)
                                                      .resolve(
                                                          Directionality.of(
                                                              context)),
                                              child: GestureDetector(
                                                onTap: () => _model.unfocusNode
                                                        .canRequestFocus
                                                    ? FocusScope.of(context)
                                                        .requestFocus(
                                                            _model.unfocusNode)
                                                    : FocusScope.of(context)
                                                        .unfocus(),
                                                child: const GeneralPopupWidget(
                                                  alertTitle:
                                                      'Permission needed',
                                                  alertText:
                                                      'To block contacts, you first need to grant Chyrpe permission to access them. Please try it again.',
                                                ),
                                              ),
                                            );
                                          },
                                        ).then((value) => setState(() {}));
                                      }

                                      setState(() {});
                                    },
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          'Block Contacts',
                                          style: FlutterFlowTheme.of(context)
                                              .bodyMedium
                                              .override(
                                                fontFamily: 'BT Beau Sans',
                                                fontSize: 16.0,
                                                letterSpacing: 0.0,
                                                useGoogleFonts: false,
                                              ),
                                        ),
                                        Row(
                                          mainAxisSize: MainAxisSize.max,
                                          mainAxisAlignment:
                                              MainAxisAlignment.end,
                                          children: [
                                            Padding(
                                              padding:
                                                  const EdgeInsetsDirectional
                                                      .fromSTEB(
                                                      0.0, 0.0, 10.0, 0.0),
                                              child: Text(
                                                ' ',
                                                style:
                                                    FlutterFlowTheme.of(context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          color: const Color(
                                                              0xFF4F5865),
                                                          fontSize: 16.0,
                                                          letterSpacing: 0.0,
                                                          useGoogleFonts: false,
                                                        ),
                                              ),
                                            ),
                                            const FaIcon(
                                              FontAwesomeIcons.angleRight,
                                              color: Color(0xFFB9BFC8),
                                              size: 18.0,
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Align(
                    alignment: const AlignmentDirectional(-1.0, -1.0),
                    child: Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(
                          3.0, 20.0, 0.0, 0.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Align(
                                alignment:
                                    const AlignmentDirectional(-1.0, -1.0),
                                child: Text(
                                  'Push Notifications',
                                  style: FlutterFlowTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        fontSize: 16.0,
                                        letterSpacing: 0.0,
                                        fontWeight: FontWeight.w600,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                              FlutterFlowIconButton(
                                borderColor: const Color(0x004B39EF),
                                borderRadius: 20.0,
                                borderWidth: 0.0,
                                buttonSize: 40.0,
                                fillColor: const Color(0x004B39EF),
                                icon: Icon(
                                  Icons.info_outline_rounded,
                                  color:
                                      FlutterFlowTheme.of(context).primaryText,
                                  size: 24.0,
                                ),
                                onPressed: () async {
                                  await showModalBottomSheet(
                                    isScrollControlled: true,
                                    backgroundColor: Colors.transparent,
                                    enableDrag: false,
                                    context: context,
                                    builder: (context) {
                                      return GestureDetector(
                                        onTap: () =>
                                            FocusScope.of(context).unfocus(),
                                        child: Padding(
                                          padding:
                                              MediaQuery.viewInsetsOf(context),
                                          child: InfoSheetScrollableWidget(
                                            title: 'Push Notifications',
                                            body: getRemoteConfigString(
                                                'settings_pushn_explanation'),
                                          ),
                                        ),
                                      );
                                    },
                                  ).then((value) => safeSetState(() {}));
                                },
                              ),
                            ],
                          ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 9.0, 0.0, 0.0),
                            child: Container(
                              height: 45.0,
                              decoration: BoxDecoration(
                                color: FlutterFlowTheme.of(context)
                                    .primaryBackground,
                                borderRadius: BorderRadius.circular(10.0),
                              ),
                              child: Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    16.0, 0.0, 16.0, 0.0),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'New matches',
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            fontFamily: 'BT Beau Sans',
                                            fontSize: 16.0,
                                            useGoogleFonts: false,
                                          ),
                                    ),
                                    AuthUserStreamWidget(
                                      builder: (context) => Switch.adaptive(
                                        value: _model.switchValue2 ??=
                                            valueOrDefault<bool>(
                                                currentUserDocument?.pnMa,
                                                false),
                                        onChanged: (newValue) async {
                                          setState(() =>
                                              _model.switchValue2 = newValue);
                                          if (newValue) {
                                            final firestoreBatch =
                                                FirebaseFirestore.instance
                                                    .batch();
                                            try {
                                              firestoreBatch.update(
                                                  currentUserDocument!
                                                      .publicProfile!,
                                                  createPublicProfileRecordData(
                                                    pnMa: true,
                                                  ));

                                              firestoreBatch.update(
                                                  currentUserReference!,
                                                  createUsersRecordData(
                                                    pnMa: true,
                                                  ));
                                            } finally {
                                              await firestoreBatch.commit();
                                            }
                                          } else {
                                            final firestoreBatch =
                                                FirebaseFirestore.instance
                                                    .batch();
                                            try {
                                              firestoreBatch.update(
                                                  currentUserDocument!
                                                      .publicProfile!,
                                                  createPublicProfileRecordData(
                                                    pnMa: false,
                                                  ));

                                              firestoreBatch.update(
                                                  currentUserReference!,
                                                  createUsersRecordData(
                                                    pnMa: false,
                                                  ));
                                            } finally {
                                              await firestoreBatch.commit();
                                            }
                                          }
                                        },
                                        activeColor: const Color(0xFFAE34E8),
                                        activeTrackColor:
                                            FlutterFlowTheme.of(context)
                                                .accent1,
                                        inactiveTrackColor:
                                            FlutterFlowTheme.of(context)
                                                .alternate,
                                        inactiveThumbColor:
                                            FlutterFlowTheme.of(context)
                                                .secondaryText,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 9.0, 0.0, 0.0),
                            child: Container(
                              height: 45.0,
                              decoration: BoxDecoration(
                                color: FlutterFlowTheme.of(context)
                                    .primaryBackground,
                                borderRadius: BorderRadius.circular(10.0),
                              ),
                              child: Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    16.0, 0.0, 16.0, 0.0),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'New messages',
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            fontFamily: 'BT Beau Sans',
                                            fontSize: 16.0,
                                            useGoogleFonts: false,
                                          ),
                                    ),
                                    AuthUserStreamWidget(
                                      builder: (context) => Switch.adaptive(
                                        value: _model.switchValue3 ??=
                                            valueOrDefault<bool>(
                                                currentUserDocument?.pnMe,
                                                false),
                                        onChanged: (newValue) async {
                                          setState(() =>
                                              _model.switchValue3 = newValue);
                                          if (newValue) {
                                            final firestoreBatch =
                                                FirebaseFirestore.instance
                                                    .batch();
                                            try {
                                              firestoreBatch.update(
                                                  currentUserDocument!
                                                      .publicProfile!,
                                                  createPublicProfileRecordData(
                                                    pnMe: true,
                                                  ));

                                              firestoreBatch.update(
                                                  currentUserReference!,
                                                  createUsersRecordData(
                                                    pnMe: true,
                                                  ));
                                            } finally {
                                              await firestoreBatch.commit();
                                            }
                                          } else {
                                            final firestoreBatch =
                                                FirebaseFirestore.instance
                                                    .batch();
                                            try {
                                              firestoreBatch.update(
                                                  currentUserDocument!
                                                      .publicProfile!,
                                                  createPublicProfileRecordData(
                                                    pnMe: false,
                                                  ));

                                              firestoreBatch.update(
                                                  currentUserReference!,
                                                  createUsersRecordData(
                                                    pnMe: false,
                                                  ));
                                            } finally {
                                              await firestoreBatch.commit();
                                            }
                                          }
                                        },
                                        activeColor: const Color(0xFFAE34E8),
                                        activeTrackColor:
                                            FlutterFlowTheme.of(context)
                                                .accent1,
                                        inactiveTrackColor:
                                            FlutterFlowTheme.of(context)
                                                .alternate,
                                        inactiveThumbColor:
                                            FlutterFlowTheme.of(context)
                                                .secondaryText,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Align(
                    alignment: const AlignmentDirectional(-1.0, -1.0),
                    child: Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(
                          3.0, 20.0, 0.0, 0.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Align(
                            alignment: const AlignmentDirectional(-1.0, -1.0),
                            child: Text(
                              'Get help',
                              style: FlutterFlowTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'BT Beau Sans',
                                    fontSize: 16.0,
                                    fontWeight: FontWeight.w600,
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 9.0, 0.0, 0.0),
                            child: InkWell(
                              splashColor: Colors.transparent,
                              focusColor: Colors.transparent,
                              hoverColor: Colors.transparent,
                              highlightColor: Colors.transparent,
                              onTap: () async {
                                await launchURL(
                                    'https://www.chyrpe.com/contact-us');
                              },
                              child: Container(
                                height: 45.0,
                                decoration: BoxDecoration(
                                  color: FlutterFlowTheme.of(context)
                                      .primaryBackground,
                                  borderRadius: BorderRadius.circular(10.0),
                                ),
                                child: Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      16.0, 0.0, 16.0, 0.0),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Contact us',
                                        style: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .override(
                                              fontFamily: 'BT Beau Sans',
                                              fontSize: 16.0,
                                              useGoogleFonts: false,
                                            ),
                                      ),
                                      Row(
                                        mainAxisSize: MainAxisSize.max,
                                        mainAxisAlignment:
                                            MainAxisAlignment.end,
                                        children: [
                                          Padding(
                                            padding: const EdgeInsetsDirectional
                                                .fromSTEB(0.0, 0.0, 10.0, 0.0),
                                            child: Text(
                                              ' ',
                                              style: FlutterFlowTheme.of(
                                                      context)
                                                  .bodyMedium
                                                  .override(
                                                    fontFamily: 'BT Beau Sans',
                                                    color:
                                                        const Color(0xFF4F5865),
                                                    fontSize: 16.0,
                                                    useGoogleFonts: false,
                                                  ),
                                            ),
                                          ),
                                          const FaIcon(
                                            FontAwesomeIcons.angleRight,
                                            color: Color(0xFFB9BFC8),
                                            size: 18.0,
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 9.0, 0.0, 0.0),
                            child: InkWell(
                              splashColor: Colors.transparent,
                              focusColor: Colors.transparent,
                              hoverColor: Colors.transparent,
                              highlightColor: Colors.transparent,
                              onTap: () async {
                                await launchURL(
                                    'https://www.chyrpe.com/safety');
                              },
                              child: Container(
                                height: 45.0,
                                decoration: BoxDecoration(
                                  color: FlutterFlowTheme.of(context)
                                      .primaryBackground,
                                  borderRadius: BorderRadius.circular(10.0),
                                ),
                                child: Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      16.0, 0.0, 16.0, 0.0),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Safety on Chyrpe',
                                        style: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .override(
                                              fontFamily: 'BT Beau Sans',
                                              fontSize: 16.0,
                                              useGoogleFonts: false,
                                            ),
                                      ),
                                      Row(
                                        mainAxisSize: MainAxisSize.max,
                                        mainAxisAlignment:
                                            MainAxisAlignment.end,
                                        children: [
                                          Padding(
                                            padding: const EdgeInsetsDirectional
                                                .fromSTEB(0.0, 0.0, 10.0, 0.0),
                                            child: Text(
                                              ' ',
                                              style: FlutterFlowTheme.of(
                                                      context)
                                                  .bodyMedium
                                                  .override(
                                                    fontFamily: 'BT Beau Sans',
                                                    color:
                                                        const Color(0xFF4F5865),
                                                    fontSize: 16.0,
                                                    useGoogleFonts: false,
                                                  ),
                                            ),
                                          ),
                                          const FaIcon(
                                            FontAwesomeIcons.angleRight,
                                            color: Color(0xFFB9BFC8),
                                            size: 18.0,
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                          Builder(
                            builder: (context) => Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 9.0, 0.0, 0.0),
                              child: InkWell(
                                splashColor: Colors.transparent,
                                focusColor: Colors.transparent,
                                hoverColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                onTap: () async {
                                  // syncing both standard and advanced from Purchases
                                  await revenue_cat.restorePurchases();
                                  await Purchases.syncPurchases();
                                  try {
                                    final result =
                                        await FirebaseFunctions.instanceFor(
                                                region: 'europe-west2')
                                            .httpsCallable(
                                                'syncAfterOneTimePurchase')
                                            .call({});
                                    _model.syncAfter1TPcf =
                                        SyncAfterOneTimePurchaseCloudFunctionCallResponse(
                                      succeeded: true,
                                    );
                                  } on FirebaseFunctionsException catch (error) {
                                    _model.syncAfter1TPcf =
                                        SyncAfterOneTimePurchaseCloudFunctionCallResponse(
                                      errorCode: error.code,
                                      succeeded: false,
                                    );
                                  }
                                  if (_model.syncAfter1TPcf!.succeeded!) {
                                    await showDialog(
                                      context: context,
                                      builder: (dialogContext) {
                                        return Dialog(
                                          elevation: 0,
                                          insetPadding: EdgeInsets.zero,
                                          backgroundColor: Colors.transparent,
                                          alignment: const AlignmentDirectional(
                                                  0.0, 0.0)
                                              .resolve(
                                                  Directionality.of(context)),
                                          child: GestureDetector(
                                            onTap: () =>
                                                FocusScope.of(dialogContext)
                                                    .unfocus(),
                                            child: const GeneralPopupWidget(
                                              alertTitle: 'Purchases Restored',
                                              alertText:
                                                  'Your purchases have been restored. If something is wrong, <NAME_EMAIL>',
                                            ),
                                          ),
                                        );
                                      },
                                    );
                                  } else {
                                    await showDialog(
                                      context: context,
                                      builder: (dialogContext) {
                                        return Dialog(
                                          elevation: 0,
                                          insetPadding: EdgeInsets.zero,
                                          backgroundColor: Colors.transparent,
                                          alignment: const AlignmentDirectional(
                                                  0.0, 0.0)
                                              .resolve(
                                                  Directionality.of(context)),
                                          child: GestureDetector(
                                            onTap: () =>
                                                FocusScope.of(dialogContext)
                                                    .unfocus(),
                                            child: const GeneralPopupWidget(
                                              alertTitle:
                                                  'Something went wrong',
                                              alertText:
                                                  'Please try again <NAME_EMAIL>',
                                            ),
                                          ),
                                        );
                                      },
                                    );
                                  }
                                  safeSetState(() {});
                                },
                                child: Container(
                                  height: 45.0,
                                  decoration: BoxDecoration(
                                    color: FlutterFlowTheme.of(context)
                                        .primaryBackground,
                                    borderRadius: BorderRadius.circular(10.0),
                                  ),
                                  child: Padding(
                                    padding:
                                        const EdgeInsetsDirectional.fromSTEB(
                                            16.0, 0.0, 16.0, 0.0),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          'Restore Purchases',
                                          style: FlutterFlowTheme.of(context)
                                              .bodyMedium
                                              .override(
                                                fontFamily: 'BT Beau Sans',
                                                fontSize: 16.0,
                                                letterSpacing: 0.0,
                                                useGoogleFonts: false,
                                              ),
                                        ),
                                        Row(
                                          mainAxisSize: MainAxisSize.max,
                                          mainAxisAlignment:
                                              MainAxisAlignment.end,
                                          children: [
                                            Padding(
                                              padding:
                                                  const EdgeInsetsDirectional
                                                      .fromSTEB(
                                                      0.0, 0.0, 10.0, 0.0),
                                              child: Text(
                                                ' ',
                                                style:
                                                    FlutterFlowTheme.of(context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          color: const Color(
                                                              0xFF4F5865),
                                                          fontSize: 16.0,
                                                          letterSpacing: 0.0,
                                                          useGoogleFonts: false,
                                                        ),
                                              ),
                                            ),
                                            const FaIcon(
                                              FontAwesomeIcons.angleRight,
                                              color: Color(0xFFB9BFC8),
                                              size: 18.0,
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          if (isAndroid)
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 9.0, 0.0, 0.0),
                              child: InkWell(
                                splashColor: Colors.transparent,
                                focusColor: Colors.transparent,
                                hoverColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                onTap: () async {
                                  await launchURL(
                                      'https://play.google.com/store/account/subscriptions');
                                },
                                child: Container(
                                  height: 45.0,
                                  decoration: BoxDecoration(
                                    color: FlutterFlowTheme.of(context)
                                        .primaryBackground,
                                    borderRadius: BorderRadius.circular(10.0),
                                  ),
                                  child: Padding(
                                    padding:
                                        const EdgeInsetsDirectional.fromSTEB(
                                            16.0, 0.0, 16.0, 0.0),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          'Manage Subscriptions',
                                          style: FlutterFlowTheme.of(context)
                                              .bodyMedium
                                              .override(
                                                fontFamily: 'BT Beau Sans',
                                                fontSize: 16.0,
                                                letterSpacing: 0.0,
                                                useGoogleFonts: false,
                                              ),
                                        ),
                                        Row(
                                          mainAxisSize: MainAxisSize.max,
                                          mainAxisAlignment:
                                              MainAxisAlignment.end,
                                          children: [
                                            Padding(
                                              padding:
                                                  const EdgeInsetsDirectional
                                                      .fromSTEB(
                                                      0.0, 0.0, 10.0, 0.0),
                                              child: Text(
                                                ' ',
                                                style:
                                                    FlutterFlowTheme.of(context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          color: const Color(
                                                              0xFF4F5865),
                                                          fontSize: 16.0,
                                                          letterSpacing: 0.0,
                                                          useGoogleFonts: false,
                                                        ),
                                              ),
                                            ),
                                            const FaIcon(
                                              FontAwesomeIcons.angleRight,
                                              color: Color(0xFFB9BFC8),
                                              size: 18.0,
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                  Align(
                    alignment: const AlignmentDirectional(-1.0, -1.0),
                    child: Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(
                          3.0, 20.0, 0.0, 0.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Align(
                            alignment: const AlignmentDirectional(-1.0, -1.0),
                            child: Text(
                              'Legal',
                              style: FlutterFlowTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'BT Beau Sans',
                                    fontSize: 16.0,
                                    fontWeight: FontWeight.w600,
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 9.0, 0.0, 0.0),
                            child: InkWell(
                              splashColor: Colors.transparent,
                              focusColor: Colors.transparent,
                              hoverColor: Colors.transparent,
                              highlightColor: Colors.transparent,
                              onTap: () async {
                                await launchURL(
                                    'https://www.chyrpe.com/legal/overview');
                              },
                              child: Container(
                                height: 45.0,
                                decoration: BoxDecoration(
                                  color: FlutterFlowTheme.of(context)
                                      .primaryBackground,
                                  borderRadius: BorderRadius.circular(10.0),
                                ),
                                child: Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      16.0, 0.0, 16.0, 0.0),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Our guidelines & policies',
                                        style: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .override(
                                              fontFamily: 'BT Beau Sans',
                                              fontSize: 16.0,
                                              useGoogleFonts: false,
                                            ),
                                      ),
                                      Row(
                                        mainAxisSize: MainAxisSize.max,
                                        mainAxisAlignment:
                                            MainAxisAlignment.end,
                                        children: [
                                          Padding(
                                            padding: const EdgeInsetsDirectional
                                                .fromSTEB(0.0, 0.0, 10.0, 0.0),
                                            child: Text(
                                              ' ',
                                              style: FlutterFlowTheme.of(
                                                      context)
                                                  .bodyMedium
                                                  .override(
                                                    fontFamily: 'BT Beau Sans',
                                                    color:
                                                        const Color(0xFF4F5865),
                                                    fontSize: 16.0,
                                                    useGoogleFonts: false,
                                                  ),
                                            ),
                                          ),
                                          const FaIcon(
                                            FontAwesomeIcons.angleRight,
                                            color: Color(0xFFB9BFC8),
                                            size: 18.0,
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 9.0, 0.0, 0.0),
                            child: InkWell(
                              splashColor: Colors.transparent,
                              focusColor: Colors.transparent,
                              hoverColor: Colors.transparent,
                              highlightColor: Colors.transparent,
                              onTap: () async {
                                context.pushNamed('Licences');
                              },
                              child: Container(
                                height: 45.0,
                                decoration: BoxDecoration(
                                  color: FlutterFlowTheme.of(context)
                                      .primaryBackground,
                                  borderRadius: BorderRadius.circular(10.0),
                                ),
                                child: Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      16.0, 0.0, 16.0, 0.0),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Licences',
                                        style: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .override(
                                              fontFamily: 'BT Beau Sans',
                                              fontSize: 16.0,
                                              useGoogleFonts: false,
                                            ),
                                      ),
                                      Row(
                                        mainAxisSize: MainAxisSize.max,
                                        mainAxisAlignment:
                                            MainAxisAlignment.end,
                                        children: [
                                          Padding(
                                            padding: const EdgeInsetsDirectional
                                                .fromSTEB(0.0, 0.0, 10.0, 0.0),
                                            child: Text(
                                              ' ',
                                              style: FlutterFlowTheme.of(
                                                      context)
                                                  .bodyMedium
                                                  .override(
                                                    fontFamily: 'BT Beau Sans',
                                                    color:
                                                        const Color(0xFF4F5865),
                                                    fontSize: 16.0,
                                                    useGoogleFonts: false,
                                                  ),
                                            ),
                                          ),
                                          const FaIcon(
                                            FontAwesomeIcons.angleRight,
                                            color: Color(0xFFB9BFC8),
                                            size: 18.0,
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Align(
                    alignment: const AlignmentDirectional(-1.0, -1.0),
                    child: Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(
                          3.0, 20.0, 0.0, 0.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Align(
                            alignment: const AlignmentDirectional(-1.0, -1.0),
                            child: Text(
                              'The last bit',
                              style: FlutterFlowTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'BT Beau Sans',
                                    fontSize: 16.0,
                                    fontWeight: FontWeight.w600,
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 9.0, 0.0, 0.0),
                            child: Container(
                              height: 45.0,
                              decoration: BoxDecoration(
                                color: FlutterFlowTheme.of(context)
                                    .primaryBackground,
                                borderRadius: BorderRadius.circular(10.0),
                              ),
                              child: Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    16.0, 0.0, 16.0, 0.0),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Take a break',
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            fontFamily: 'BT Beau Sans',
                                            fontSize: 16.0,
                                            useGoogleFonts: false,
                                          ),
                                    ),
                                    Builder(
                                      builder: (context) =>
                                          AuthUserStreamWidget(
                                        builder: (context) => Switch.adaptive(
                                          value: _model.switchValue1 ??=
                                              valueOrDefault<bool>(
                                                  currentUserDocument?.paused,
                                                  false),
                                          onChanged: (newValue) async {
                                            setState(() =>
                                                _model.switchValue1 = newValue);
                                            if (newValue) {
                                              await currentUserReference!
                                                  .update({
                                                ...createUsersRecordData(
                                                  paused: true,
                                                ),
                                                ...mapToFirestore(
                                                  {
                                                    'matchingSuggestions':
                                                        FieldValue.delete(),
                                                  },
                                                ),
                                              });
                                              try {
                                                final result =
                                                    await FirebaseFunctions
                                                            .instanceFor(
                                                                region:
                                                                    'europe-west2')
                                                        .httpsCallable(
                                                            'changePausedMode')
                                                        .call({});
                                                _model.changePausedMode1 =
                                                    ChangePausedModeCloudFunctionCallResponse(
                                                  succeeded: true,
                                                );
                                              } on FirebaseFunctionsException catch (error) {
                                                _model.changePausedMode1 =
                                                    ChangePausedModeCloudFunctionCallResponse(
                                                  errorCode: error.code,
                                                  succeeded: false,
                                                );
                                              }

                                              if (!_model.changePausedMode1!
                                                  .succeeded!) {
                                                await showDialog(
                                                  context: context,
                                                  builder: (dialogContext) {
                                                    return Dialog(
                                                      elevation: 0,
                                                      insetPadding:
                                                          EdgeInsets.zero,
                                                      backgroundColor:
                                                          Colors.transparent,
                                                      alignment:
                                                          const AlignmentDirectional(
                                                                  0.0, 0.0)
                                                              .resolve(
                                                                  Directionality.of(
                                                                      context)),
                                                      child: GestureDetector(
                                                        onTap: () => _model
                                                                .unfocusNode
                                                                .canRequestFocus
                                                            ? FocusScope.of(
                                                                    context)
                                                                .requestFocus(_model
                                                                    .unfocusNode)
                                                            : FocusScope.of(
                                                                    context)
                                                                .unfocus(),
                                                        child:
                                                            const GeneralPopupWidget(
                                                          alertTitle:
                                                              'Something went wrong',
                                                          alertText:
                                                              'Please try again',
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                ).then(
                                                    (value) => setState(() {}));
                                              }

                                              setState(() {});
                                            } else {
                                              var shouldSetState = false;

                                              await currentUserReference!
                                                  .update(createUsersRecordData(
                                                paused: false,
                                              ));
                                              try {
                                                final result =
                                                    await FirebaseFunctions
                                                            .instanceFor(
                                                                region:
                                                                    'europe-west2')
                                                        .httpsCallable(
                                                            'changePausedMode')
                                                        .call({});
                                                _model.changePausedOff1 =
                                                    ChangePausedModeCloudFunctionCallResponse(
                                                  succeeded: true,
                                                );
                                              } on FirebaseFunctionsException catch (error) {
                                                _model.changePausedOff1 =
                                                    ChangePausedModeCloudFunctionCallResponse(
                                                  errorCode: error.code,
                                                  succeeded: false,
                                                );
                                              }

                                              shouldSetState = true;
                                              if (!_model.changePausedOff1!
                                                  .succeeded!) {
                                                await showDialog(
                                                  context: context,
                                                  builder: (dialogContext) {
                                                    return Dialog(
                                                      elevation: 0,
                                                      insetPadding:
                                                          EdgeInsets.zero,
                                                      backgroundColor:
                                                          Colors.transparent,
                                                      alignment:
                                                          const AlignmentDirectional(
                                                                  0.0, 0.0)
                                                              .resolve(
                                                                  Directionality.of(
                                                                      context)),
                                                      child: GestureDetector(
                                                        onTap: () => _model
                                                                .unfocusNode
                                                                .canRequestFocus
                                                            ? FocusScope.of(
                                                                    context)
                                                                .requestFocus(_model
                                                                    .unfocusNode)
                                                            : FocusScope.of(
                                                                    context)
                                                                .unfocus(),
                                                        child:
                                                            const GeneralPopupWidget(
                                                          alertTitle:
                                                              'Something went wrong',
                                                          alertText:
                                                              'Please try again',
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                ).then(
                                                    (value) => setState(() {}));

                                                if (shouldSetState) {
                                                  setState(() {});
                                                }
                                                return;
                                              }
                                              try {
                                                final result = await FirebaseFunctions
                                                        .instanceFor(
                                                            region:
                                                                'europe-west1')
                                                    .httpsCallable(
                                                        'generateMatchesAdvanced')
                                                    .call({
                                                  "append": false,
                                                   "smoothSwiping": true,
                                                });
                                                _model.cloudFunctionuws =
                                                    GenerateMatchesCloudFunctionCallResponse(
                                                  succeeded: true,
                                                );
                                              } on FirebaseFunctionsException catch (error) {
                                                _model.cloudFunctionuws =
                                                    GenerateMatchesCloudFunctionCallResponse(
                                                  errorCode: error.code,
                                                  succeeded: false,
                                                );
                                              }

                                              shouldSetState = true;
                                              if (shouldSetState) {
                                                setState(() {});
                                              }
                                            }
                                          },
                                          activeColor: const Color(0xFFAE34E8),
                                          activeTrackColor:
                                              FlutterFlowTheme.of(context)
                                                  .accent1,
                                          inactiveTrackColor:
                                              FlutterFlowTheme.of(context)
                                                  .alternate,
                                          inactiveThumbColor:
                                              FlutterFlowTheme.of(context)
                                                  .secondaryText,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 9.0, 0.0, 0.0),
                            child: InkWell(
                              splashColor: Colors.transparent,
                              focusColor: Colors.transparent,
                              hoverColor: Colors.transparent,
                              highlightColor: Colors.transparent,
                              onTap: () async {
                                GoRouter.of(context).prepareAuthEvent();
                                await authManager.signOut();
                                GoRouter.of(context).clearRedirectLocation();

                                context.goNamedAuth(
                                    'WelcomeScreen', context.mounted);
                              },
                              child: Container(
                                height: 45.0,
                                decoration: BoxDecoration(
                                  color: FlutterFlowTheme.of(context)
                                      .primaryBackground,
                                  borderRadius: BorderRadius.circular(10.0),
                                ),
                                child: Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      16.0, 0.0, 16.0, 0.0),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Log out',
                                        style: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .override(
                                              fontFamily: 'BT Beau Sans',
                                              fontSize: 16.0,
                                              useGoogleFonts: false,
                                            ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                          if (((currentUserDocument?.gender == Gender.Male) &&
                                  revenue_cat.activeEntitlementIds
                                      .contains('paid_standard_1w') &&
                                  getRemoteConfigBool(
                                      'settings_freeswitch_visible') &&
                                  !revenue_cat.activeEntitlementIds
                                      .contains('paid_standard_lifetime') &&
                                  (valueOrDefault<bool>(currentUserDocument?.showFreeMode, false) ||
                                      (valueOrDefault<bool>(currentUserDocument?.bGroup, false)
                                          ? functions
                                              .getStringListFromJson(
                                                  getRemoteConfigString(
                                                      'freestandard_availability_group_b'))
                                              .contains(valueOrDefault(
                                                  currentUserDocument?.wlRegion,
                                                  ''))
                                          : functions
                                              .getStringListFromJson(
                                                  getRemoteConfigString(
                                                      'freestandard_availability_group_a'))
                                              .contains(valueOrDefault(
                                                  currentUserDocument?.wlRegion,
                                                  ''))))) ||
                              valueOrDefault<bool>(
                                  currentUserDocument?.showFreeModeSettings, false))
                            Builder(
                              builder: (context) => Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 9.0, 0.0, 0.0),
                                child: AuthUserStreamWidget(
                                  builder: (context) => InkWell(
                                    splashColor: Colors.transparent,
                                    focusColor: Colors.transparent,
                                    hoverColor: Colors.transparent,
                                    highlightColor: Colors.transparent,
                                    onTap: () async {
                                      await showDialog(
                                        context: context,
                                        builder: (dialogContext) {
                                          return Dialog(
                                            elevation: 0,
                                            insetPadding: EdgeInsets.zero,
                                            backgroundColor: Colors.transparent,
                                            alignment:
                                                const AlignmentDirectional(
                                                        0.0, 0.0)
                                                    .resolve(Directionality.of(
                                                        context)),
                                            child: GestureDetector(
                                              onTap: () =>
                                                  FocusScope.of(dialogContext)
                                                      .unfocus(),
                                              child: SwitchFreeInfoPopupWidget(
                                                alertTitle: getRemoteConfigString(
                                                    'settings_freeswitch_title'),
                                                alertText: getRemoteConfigString(
                                                    'settings_freeswitch_body'),
                                                buttonText: getRemoteConfigString(
                                                    'settings_freeswitch_buttontitle'),
                                              ),
                                            ),
                                          );
                                        },
                                      );
                                    },
                                    child: Container(
                                      height: 45.0,
                                      decoration: BoxDecoration(
                                        color: FlutterFlowTheme.of(context)
                                            .primaryBackground,
                                        borderRadius:
                                            BorderRadius.circular(10.0),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsetsDirectional
                                            .fromSTEB(16.0, 0.0, 16.0, 0.0),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.max,
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text(
                                              getRemoteConfigString(
                                                  'settings_freeswitch_entrybuttontitle'),
                                              style:
                                                  FlutterFlowTheme.of(context)
                                                      .bodyMedium
                                                      .override(
                                                        fontFamily:
                                                            'BT Beau Sans',
                                                        fontSize: 16.0,
                                                        letterSpacing: 0.0,
                                                        useGoogleFonts: false,
                                                      ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 20.0, 0.0, 0.0),
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                Align(
                                  alignment:
                                      const AlignmentDirectional(-1.0, -1.0),
                                  child: Text(
                                    'Delete your account',
                                    style: FlutterFlowTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'BT Beau Sans',
                                          fontSize: 16.0,
                                          letterSpacing: 0.0,
                                          fontWeight: FontWeight.w600,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ),
                                Align(
                                  alignment:
                                      const AlignmentDirectional(-1.0, 0.0),
                                  child: Padding(
                                    padding:
                                        const EdgeInsetsDirectional.fromSTEB(
                                            0.0, 6.0, 25.0, 0.0),
                                    child: Text(
                                      'When you delete your account, we will remove all information we have on you. You are always free to rejoin chyrpe in the future, so there is no disadvantage.\nIf something bothered you about chyrpe, contact us directly above and we will do our best to adapt.',
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            fontFamily: 'BT Beau Sans',
                                            letterSpacing: 0.0,
                                            useGoogleFonts: false,
                                          ),
                                    ),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      0.0, 9.0, 0.0, 0.0),
                                  child: Container(
                                    height: 45.0,
                                    decoration: BoxDecoration(
                                      color: FlutterFlowTheme.of(context)
                                          .primaryBackground,
                                      borderRadius: BorderRadius.circular(10.0),
                                    ),
                                    child: Builder(
                                      builder: (context) => Padding(
                                        padding: const EdgeInsetsDirectional
                                            .fromSTEB(16.0, 0.0, 16.0, 0.0),
                                        child: InkWell(
                                          splashColor: Colors.transparent,
                                          focusColor: Colors.transparent,
                                          hoverColor: Colors.transparent,
                                          highlightColor: Colors.transparent,
                                          onTap: () async {
                                            analytics.logEvent(
                                                'Navigated to Delete Pause Popup Widget from Settings');
                                            await showDialog(
                                              barrierColor:
                                                  const Color.fromARGB(
                                                      229, 217, 217, 217),
                                              barrierDismissible: false,
                                              context: context,
                                              builder: (dialogContext) {
                                                return Dialog(
                                                  elevation: 0,
                                                  insetPadding: EdgeInsets.zero,
                                                  backgroundColor:
                                                      Colors.transparent,
                                                  alignment:
                                                      const AlignmentDirectional(
                                                              0.0, 0.0)
                                                          .resolve(
                                                              Directionality.of(
                                                                  context)),
                                                  child: GestureDetector(
                                                    onTap: () => FocusScope.of(
                                                            dialogContext)
                                                        .unfocus(),
                                                    child:
                                                        const DeletePausePopupWidget(),
                                                  ),
                                                );
                                              },
                                            );
                                          },
                                          child: Row(
                                            mainAxisSize: MainAxisSize.max,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                'Delete account',
                                                style:
                                                    FlutterFlowTheme.of(context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          fontSize: 16.0,
                                                          letterSpacing: 0.0,
                                                          useGoogleFonts: false,
                                                        ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ]
                    .addToStart(const SizedBox(height: 18.0))
                    .addToEnd(const SizedBox(height: 200.0)),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
