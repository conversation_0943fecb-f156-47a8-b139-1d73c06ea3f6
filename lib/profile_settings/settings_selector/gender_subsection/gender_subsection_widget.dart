import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:provider/provider.dart';
import 'gender_subsection_model.dart';
export 'gender_subsection_model.dart';

class GenderSubsectionWidget extends StatefulWidget {
  const GenderSubsectionWidget({
    super.key,
    required this.initChoice,
  });

  final String? initChoice;

  @override
  State<GenderSubsectionWidget> createState() => _GenderSubsectionWidgetState();
}

class _GenderSubsectionWidgetState extends State<GenderSubsectionWidget> {
  late GenderSubsectionModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => GenderSubsectionModel());

    // On component load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      setState(() {
        _model.choiceSet = widget.initChoice!;
      });
    });
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(0.0, 15.0, 0.0, 0.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Align(
            alignment: const AlignmentDirectional(-1.0, -1.0),
            child: Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(0.0, 18.0, 0.0, 18.0),
              child: AuthUserStreamWidget(
                builder: (context) =>
                    StreamBuilder<List<OptionsForSelectorsRecord>>(
                  stream: queryOptionsForSelectorsRecord(
                    queryBuilder: (optionsForSelectorsRecord) =>
                        optionsForSelectorsRecord.where(
                      'name',
                      isEqualTo: () {
                        if (valueOrDefault<bool>(
                            currentUserDocument?.alternativeG, false)) {
                          return 'Alternative Gender Reqs';
                        } else if (currentUserDocument?.gender == Gender.Male) {
                          return 'Male Gender Reqs';
                        } else {
                          return 'Female Gender Reqs';
                        }
                      }(),
                    ),
                    singleRecord: true,
                  ),
                  builder: (context, snapshot) {
                    // Customize what your widget looks like when it's loading.
                    if (!snapshot.hasData) {
                      return Center(
                        child: SizedBox(
                          width: 50.0,
                          height: 50.0,
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                              FlutterFlowTheme.of(context).accent2,
                            ),
                          ),
                        ),
                      );
                    }
                    List<OptionsForSelectorsRecord>
                        wrapOptionsForSelectorsRecordList = snapshot.data!;
                    final wrapOptionsForSelectorsRecord =
                        wrapOptionsForSelectorsRecordList.isNotEmpty
                            ? wrapOptionsForSelectorsRecordList.first
                            : null;
                    return Builder(
                      builder: (context) {
                        final choices =
                            wrapOptionsForSelectorsRecord?.options.toList() ??
                                [];
                        return Wrap(
                          spacing: 0.0,
                          runSpacing: 0.0,
                          alignment: WrapAlignment.start,
                          crossAxisAlignment: WrapCrossAlignment.start,
                          direction: Axis.horizontal,
                          runAlignment: WrapAlignment.start,
                          verticalDirection: VerticalDirection.down,
                          clipBehavior: Clip.none,
                          children:
                              List.generate(choices.length, (choicesIndex) {
                            final choicesItem = choices[choicesIndex];
                            return Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 0.0, 15.0, 14.0),
                              child: InkWell(
                                splashColor: Colors.transparent,
                                focusColor: Colors.transparent,
                                hoverColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                onTap: () async {
                                  setState(() {
                                    _model.choiceSet = choicesItem;
                                  });
                                },
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: const Color(0x00FFFFFF),
                                    borderRadius: BorderRadius.circular(24.0),
                                    border: Border.all(
                                      color: choicesItem == _model.choiceSet
                                          ? const Color(0xFFAE34E8)
                                          : const Color(0xFFB9BFC8),
                                    ),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        10.0, 6.0, 10.0, 6.0),
                                    child: Text(
                                      choicesItem,
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            fontFamily: 'BT Beau Sans',
                                            color:
                                                choicesItem == _model.choiceSet
                                                    ? const Color(0xFF21252E)
                                                    : const Color(0xFF21252E),
                                            fontWeight: FontWeight.w600,
                                            useGoogleFonts: false,
                                          ),
                                    ),
                                  ),
                                ),
                              ),
                            );
                          }),
                        );
                      },
                    );
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
