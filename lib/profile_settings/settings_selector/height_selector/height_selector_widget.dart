import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:provider/provider.dart';
import 'height_selector_model.dart';
export 'height_selector_model.dart';

class HeightSelectorWidget extends StatefulWidget {
  const HeightSelectorWidget({
    super.key,
    required this.height,
  });

  final int? height;

  @override
  State<HeightSelectorWidget> createState() => _HeightSelectorWidgetState();
}

class _HeightSelectorWidgetState extends State<HeightSelectorWidget> {
  late HeightSelectorModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => HeightSelectorModel());

    // On component load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      setState(() {
        _model.height = widget.height!.toDouble();
      });
    });

    WidgetsBinding.instance.addPostFrameCallback((_) => setState(() {}));
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return Material(
      color: Colors.transparent,
      elevation: 5.0,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(0.0),
          bottomRight: Radius.circular(0.0),
          topLeft: Radius.circular(16.0),
          topRight: Radius.circular(16.0),
        ),
      ),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: FlutterFlowTheme.of(context).primaryBackground,
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(0.0),
            bottomRight: Radius.circular(0.0),
            topLeft: Radius.circular(16.0),
            topRight: Radius.circular(16.0),
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(0.0, 12.0, 0.0, 0.0),
                  child: Container(
                    width: 50.0,
                    height: 4.0,
                    decoration: BoxDecoration(
                      color: FlutterFlowTheme.of(context).primaryBackground,
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(16.0, 16.0, 16.0, 0.0),
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'What\'s your height?',
                        style:
                            FlutterFlowTheme.of(context).headlineSmall.override(
                                  fontFamily: 'BT Beau Sans',
                                  letterSpacing: 0.0,
                                  useGoogleFonts: false,
                                ),
                      ),
                      InkWell(
                        splashColor: Colors.transparent,
                        focusColor: Colors.transparent,
                        hoverColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        onTap: () async {
                          Navigator.pop(context);
                        },
                        child: Icon(
                          Icons.close_rounded,
                          color: FlutterFlowTheme.of(context).primaryText,
                          size: 30.0,
                        ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(0.0, 70.0, 0.0, 0.0),
                  child: Text(
                    '${formatNumber(
                      _model.height,
                      formatType: FormatType.custom,
                      format: '###',
                      locale: '',
                    )}cm',
                    style: FlutterFlowTheme.of(context).titleLarge.override(
                          fontFamily: 'BT Beau Sans',
                          letterSpacing: 0.0,
                          useGoogleFonts: false,
                        ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(0.0, 7.0, 0.0, 0.0),
                  child: Text(
                    '${((_model.height / 30.48).floor()).toString()}\'${(((_model.height / 2.54).floor() - (((_model.height / 30.48).floor()) * 12)).floor()).toString()}"',
                    style: FlutterFlowTheme.of(context).labelLarge.override(
                          fontFamily: 'BT Beau Sans',
                          letterSpacing: 0.0,
                          useGoogleFonts: false,
                        ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(0.0, 50.0, 0.0, 0.0),
                  child: Slider(
                    activeColor: FlutterFlowTheme.of(context).accent2,
                    inactiveColor: FlutterFlowTheme.of(context).alternate,
                    min: 0.0,
                    max: 250.0,
                    value: _model.sliderValue ??= valueOrDefault<double>(
                      widget.height?.toDouble(),
                      160.0,
                    ),
                    divisions: 250,
                    onChanged: (newValue) async {
                      newValue = double.parse(newValue.toStringAsFixed(2));
                      setState(() => _model.sliderValue = newValue);
                      setState(() {
                        _model.height = _model.sliderValue!;
                      });
                    },
                  ),
                ),
                Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(80.0, 90.0, 80.0, 0.0),
                  child: wrapWithModel(
                    model: _model.gradientButtonModel,
                    updateCallback: () => setState(() {}),
                    child: GradientButtonWidget(
                      title: 'Update',
                      action: () async {
                        await currentUserDocument!.publicProfile!
                            .update(createPublicProfileRecordData(
                          height: _model.height.toInt(),
                        ));
          
                        await currentUserReference!.update(createUsersRecordData(
                          height: _model.height.toInt(),
                        ));
                        Navigator.pop(context);
                      },
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(0.0, 30.0, 0.0, 40.0),
                  child: InkWell(
                    splashColor: Colors.transparent,
                    focusColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    onTap: () async {
                      await currentUserDocument!.publicProfile!.update({
                        ...mapToFirestore(
                          {
                            'height': FieldValue.delete(),
                          },
                        ),
                      });
                      Navigator.pop(context);
                    },
                    child: Text(
                      'Remove from profile',
                      style: FlutterFlowTheme.of(context).bodyMedium.override(
                            fontFamily: 'BT Beau Sans',
                            fontSize: 16.0,
                            letterSpacing: 0.0,
                            fontWeight: FontWeight.w500,
                            useGoogleFonts: false,
                          ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
