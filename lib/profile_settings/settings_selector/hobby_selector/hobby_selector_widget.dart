import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/done_button_grey/done_button_grey_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:provider/provider.dart';
import 'hobby_selector_model.dart';
export 'hobby_selector_model.dart';

class HobbySelectorWidget extends StatefulWidget {
  const HobbySelectorWidget({super.key});

  @override
  State<HobbySelectorWidget> createState() => _HobbySelectorWidgetState();
}

class _HobbySelectorWidgetState extends State<HobbySelectorWidget> {
  late HobbySelectorModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => HobbySelectorModel());

    // On component load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      _model.publicProfile = await PublicProfileRecord.getDocumentOnce(
          currentUserDocument!.publicProfile!);
      setState(() {
        _model.selected = _model.publicProfile!.hobbies.toList().cast<String>();
      });
    });
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return Material(
      color: Colors.transparent,
      elevation: 5.0,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(0.0),
          bottomRight: Radius.circular(0.0),
          topLeft: Radius.circular(16.0),
          topRight: Radius.circular(16.0),
        ),
      ),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: FlutterFlowTheme.of(context).primaryBackground,
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(0.0),
            bottomRight: Radius.circular(0.0),
            topLeft: Radius.circular(16.0),
            topRight: Radius.circular(16.0),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(0.0, 12.0, 0.0, 0.0),
              child: Container(
                width: 50.0,
                height: 4.0,
                decoration: BoxDecoration(
                  color: FlutterFlowTheme.of(context).alternate,
                  borderRadius: BorderRadius.circular(8.0),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(17.0, 0.0, 17.0, 0.0),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    splashColor: Colors.transparent,
                    focusColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    onTap: () async {
                      Navigator.pop(context);
                    },
                    child: Icon(
                      Icons.close_sharp,
                      color: FlutterFlowTheme.of(context).secondaryText,
                      size: 30.0,
                    ),
                  ),
                  wrapWithModel(
                    model: _model.doneButtonGreyModel,
                    updateCallback: () => setState(() {}),
                    updateOnChange: true,
                    child: DoneButtonGreyWidget(
                      action: () async {
                        await currentUserDocument!.publicProfile!.update({
                          ...mapToFirestore(
                            {
                              'hobbies': _model.selected,
                            },
                          ),
                        });

                        await currentUserReference!.update({
                          ...mapToFirestore(
                            {
                              'hobbies': _model.selected,
                            },
                          ),
                        });
                        Navigator.pop(context);
                      },
                    ),
                  ),
                ],
              ),
            ),
            Align(
              alignment: const AlignmentDirectional(-1.0, -1.0),
              child: Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(17.0, 17.0, 17.0, 0.0),
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Text(
                      'What are your hobbies?',
                      style:
                          FlutterFlowTheme.of(context).headlineSmall.override(
                                fontFamily: 'BT Beau Sans',
                                fontSize: 28.0,
                                fontWeight: FontWeight.bold,
                                useGoogleFonts: false,
                              ),
                    ),
                  ],
                ),
              ),
            ),
            Flexible(
              child: Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
                child: Container(
                  decoration: const BoxDecoration(),
                  child: Padding(
                    padding:
                        const EdgeInsetsDirectional.fromSTEB(17.0, 0.0, 17.0, 0.0),
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Align(
                            alignment: const AlignmentDirectional(-1.0, 0.0),
                            child: Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 0.0, 60.0, 0.0),
                              child: Text(
                                'Let others know more about yourself. They are here for you.',
                                style: FlutterFlowTheme.of(context).labelMedium,
                              ),
                            ),
                          ),
                          Align(
                            alignment: const AlignmentDirectional(-1.0, -1.0),
                            child: Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 18.0, 17.0, 18.0),
                              child: StreamBuilder<
                                  List<OptionsForSelectorsRecord>>(
                                stream: queryOptionsForSelectorsRecord(
                                  queryBuilder: (optionsForSelectorsRecord) =>
                                      optionsForSelectorsRecord.where(
                                    'name',
                                    isEqualTo: 'Hobbies',
                                  ),
                                  singleRecord: true,
                                ),
                                builder: (context, snapshot) {
                                  // Customize what your widget looks like when it's loading.
                                  if (!snapshot.hasData) {
                                    return Center(
                                      child: SizedBox(
                                        width: 50.0,
                                        height: 50.0,
                                        child: CircularProgressIndicator(
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                            FlutterFlowTheme.of(context)
                                                .accent2,
                                          ),
                                        ),
                                      ),
                                    );
                                  }
                                  List<OptionsForSelectorsRecord>
                                      wrapOptionsForSelectorsRecordList =
                                      snapshot.data!;
                                  // Return an empty Container when the item does not exist.
                                  if (snapshot.data!.isEmpty) {
                                    return Container();
                                  }
                                  final wrapOptionsForSelectorsRecord =
                                      wrapOptionsForSelectorsRecordList
                                              .isNotEmpty
                                          ? wrapOptionsForSelectorsRecordList
                                              .first
                                          : null;
                                  return Builder(
                                    builder: (context) {
                                      final options =
                                          wrapOptionsForSelectorsRecord?.options
                                                  .toList() ??
                                              [];
                                      return Wrap(
                                        spacing: 0.0,
                                        runSpacing: 0.0,
                                        alignment: WrapAlignment.start,
                                        crossAxisAlignment:
                                            WrapCrossAlignment.start,
                                        direction: Axis.horizontal,
                                        runAlignment: WrapAlignment.start,
                                        verticalDirection:
                                            VerticalDirection.down,
                                        clipBehavior: Clip.none,
                                        children: List.generate(options.length,
                                            (optionsIndex) {
                                          final optionsItem =
                                              options[optionsIndex];
                                          return Padding(
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(
                                                    0.0, 0.0, 15.0, 14.0),
                                            child: InkWell(
                                              splashColor: Colors.transparent,
                                              focusColor: Colors.transparent,
                                              hoverColor: Colors.transparent,
                                              highlightColor:
                                                  Colors.transparent,
                                              onTap: () async {
                                                if (_model.selected
                                                    .contains(optionsItem)) {
                                                  setState(() {
                                                    _model.removeFromSelected(
                                                        optionsItem);
                                                  });
                                                } else {
                                                  if ((List<String> choiceSet) {
                                                    return choiceSet.length >=
                                                        5;
                                                  }(_model.selected.toList())) {
                                                    ScaffoldMessenger.of(
                                                            context)
                                                        .showSnackBar(
                                                      SnackBar(
                                                        content: Text(
                                                          'Please add at most 5 options',
                                                          style: TextStyle(
                                                            color: FlutterFlowTheme
                                                                    .of(context)
                                                                .primaryText,
                                                          ),
                                                        ),
                                                        duration: const Duration(
                                                            milliseconds: 4000),
                                                        backgroundColor:
                                                            FlutterFlowTheme.of(
                                                                    context)
                                                                .secondary,
                                                      ),
                                                    );
                                                  } else {
                                                    setState(() {
                                                      _model.addToSelected(
                                                          optionsItem);
                                                    });
                                                  }
                                                }
                                              },
                                              child: Container(
                                                decoration: BoxDecoration(
                                                  color: const Color(0x00FFFFFF),
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          24.0),
                                                  border: Border.all(
                                                    color: _model.selected
                                                            .contains(
                                                                optionsItem)
                                                        ? const Color(0xFFAE34E8)
                                                        : const Color(0xFFB9BFC8),
                                                  ),
                                                ),
                                                child: Padding(
                                                  padding: const EdgeInsetsDirectional
                                                      .fromSTEB(
                                                          10.0, 6.0, 10.0, 6.0),
                                                  child: Text(
                                                    optionsItem,
                                                    style:
                                                        FlutterFlowTheme.of(
                                                                context)
                                                            .bodyMedium
                                                            .override(
                                                              fontFamily:
                                                                  'BT Beau Sans',
                                                              color: _model.selected
                                                                      .contains(
                                                                          optionsItem)
                                                                  ? FlutterFlowTheme.of(
                                                                          context)
                                                                      .primaryText
                                                                  : FlutterFlowTheme.of(
                                                                          context)
                                                                      .secondaryText,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w600,
                                                              useGoogleFonts:
                                                                  false,
                                                            ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          );
                                        }).addToEnd(Container(height: 50)),
                                      );
                                    },
                                  );
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
