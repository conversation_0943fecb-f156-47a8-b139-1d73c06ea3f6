import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:provider/provider.dart';
import 'kinks_subsection_model.dart';
export 'kinks_subsection_model.dart';

class KinksSubsectionWidget extends StatefulWidget {
  const KinksSubsectionWidget({
    super.key,
    required this.choices,
    required this.initChoices,
  });

  final List<String>? choices;
  final List<String>? initChoices;

  @override
  State<KinksSubsectionWidget> createState() => _KinksSubsectionWidgetState();
}

class _KinksSubsectionWidgetState extends State<KinksSubsectionWidget> {
  late KinksSubsectionModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => KinksSubsectionModel());

    // On component load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      setState(() {
        _model.choiceSet = widget.initChoices!.toList().cast<String>();
      });
    });
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(0.0, 15.0, 0.0, 0.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Align(
            alignment: const AlignmentDirectional(-1.0, -1.0),
            child: Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(0.0, 18.0, 0.0, 18.0),
              child: Builder(
                builder: (context) {
                  final choices = widget.choices!.toList();
                  return Wrap(
                    spacing: 0.0,
                    runSpacing: 0.0,
                    alignment: WrapAlignment.start,
                    crossAxisAlignment: WrapCrossAlignment.start,
                    direction: Axis.horizontal,
                    runAlignment: WrapAlignment.start,
                    verticalDirection: VerticalDirection.down,
                    clipBehavior: Clip.none,
                    children: List.generate(choices.length, (choicesIndex) {
                      final choicesItem = choices[choicesIndex];
                      return Padding(
                        padding: const EdgeInsetsDirectional.fromSTEB(
                            0.0, 0.0, 15.0, 14.0),
                        child: InkWell(
                          splashColor: Colors.transparent,
                          focusColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          onTap: () async {
                            if (_model.choiceSet.contains(choicesItem)) {
                              if (!((List<String> choiceSet) {
                                return choiceSet.length <= 1;
                              }(_model.choiceSet.toList()))) {
                                setState(() {
                                  _model.removeFromChoiceSet(choicesItem);
                                });
                              }
                            } else {
                              if (!((List<String> choiceSet) {
                                return choiceSet.length >= 6;
                              }(_model.choiceSet.toList()))) {
                                setState(() {
                                  _model.addToChoiceSet(choicesItem);
                                });
                              }
                            }
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              color: const Color(0x00FFFFFF),
                              borderRadius: BorderRadius.circular(24.0),
                              border: Border.all(
                                color: _model.choiceSet.contains(choicesItem)
                                    ? const Color(0xFFAE34E8)
                                    : const Color(0xFFB9BFC8),
                              ),
                            ),
                            child: Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  10.0, 6.0, 10.0, 6.0),
                              child: Text(
                                choicesItem,
                                style: FlutterFlowTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'BT Beau Sans',
                                      color:
                                          _model.choiceSet.contains(choicesItem)
                                              ? const Color(0xFF21252E)
                                              : const Color(0xFF21252E),
                                      fontWeight: FontWeight.w600,
                                      useGoogleFonts: false,
                                    ),
                              ),
                            ),
                          ),
                        ),
                      );
                    }),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
