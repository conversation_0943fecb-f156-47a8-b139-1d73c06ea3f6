import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:provider/provider.dart';
import 'more_about_you_subsection_model.dart';
export 'more_about_you_subsection_model.dart';

class MoreAboutYouSubsectionWidget extends StatefulWidget {
  const MoreAboutYouSubsectionWidget({
    super.key,
    required this.title,
    required this.question,
    required this.choices,
    required this.initChoices,
  });

  final String? title;
  final String? question;
  final List<String>? choices;
  final String? initChoices;

  @override
  State<MoreAboutYouSubsectionWidget> createState() =>
      _MoreAboutYouSubsectionWidgetState();
}

class _MoreAboutYouSubsectionWidgetState
    extends State<MoreAboutYouSubsectionWidget> {
  late MoreAboutYouSubsectionModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => MoreAboutYouSubsectionModel());

    // On component load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      setState(() {
        _model.choiceSet = widget.initChoices!;
      });
    });
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(0.0, 15.0, 0.0, 0.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Align(
            alignment: const AlignmentDirectional(-1.0, 0.0),
            child: Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(0.0, 19.0, 0.0, 0.0),
              child: Text(
                valueOrDefault<String>(
                  widget.question,
                  'What do you think about...?',
                ),
                style: FlutterFlowTheme.of(context).bodyLarge.override(
                      fontFamily: 'BT Beau Sans',
                      fontWeight: FontWeight.w600,
                      useGoogleFonts: false,
                    ),
              ),
            ),
          ),
          Align(
            alignment: const AlignmentDirectional(-1.0, -1.0),
            child: Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(0.0, 18.0, 0.0, 18.0),
              child: Builder(
                builder: (context) {
                  final choices = widget.choices!.toList();
                  return Wrap(
                    spacing: 0.0,
                    runSpacing: 0.0,
                    alignment: WrapAlignment.start,
                    crossAxisAlignment: WrapCrossAlignment.start,
                    direction: Axis.horizontal,
                    runAlignment: WrapAlignment.start,
                    verticalDirection: VerticalDirection.down,
                    clipBehavior: Clip.none,
                    children: List.generate(choices.length, (choicesIndex) {
                      final choicesItem = choices[choicesIndex];
                      return Padding(
                        padding: const EdgeInsetsDirectional.fromSTEB(
                            0.0, 0.0, 15.0, 14.0),
                        child: InkWell(
                          splashColor: Colors.transparent,
                          focusColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          onTap: () async {
                            if (_model.choiceSet == choicesItem) {
                              _model.updatePage(() {
                                _model.choiceSet = '';
                              });
                            } else {
                              setState(() {
                                _model.choiceSet = choicesItem;
                              });
                            }
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              color: const Color(0x00FFFFFF),
                              borderRadius: BorderRadius.circular(24.0),
                              border: Border.all(
                                color: const Color(0xFFB9BFC8),
                              ),
                            ),
                            child: Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  10.0, 6.0, 10.0, 6.0),
                              child: Text(
                                choicesItem,
                                style: FlutterFlowTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'BT Beau Sans',
                                      color: const Color(0xFF646D7B),
                                      fontWeight: FontWeight.w600,
                                      useGoogleFonts: false,
                                    ),
                              ),
                            ),
                          ),
                        ),
                      );
                    }),
                  );
                },
              ),
            ),
          ),
          Divider(
            thickness: 1.0,
            color: FlutterFlowTheme.of(context).secondaryText,
          ),
        ],
      ),
    );
  }
}
