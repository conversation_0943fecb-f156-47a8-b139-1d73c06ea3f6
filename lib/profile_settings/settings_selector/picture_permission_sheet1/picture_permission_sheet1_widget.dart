import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/profile_settings/settings_selector/picture_permission_sheet2/picture_permission_sheet2_widget.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'picture_permission_sheet1_model.dart';
export 'picture_permission_sheet1_model.dart';

class PicturePermissionSheet1Widget extends StatefulWidget {
  const PicturePermissionSheet1Widget({
    super.key,
    required this.pictureDoc,
  });

  final ImagesRecord? pictureDoc;

  @override
  State<PicturePermissionSheet1Widget> createState() =>
      _PicturePermissionSheet1WidgetState();
}

class _PicturePermissionSheet1WidgetState
    extends State<PicturePermissionSheet1Widget> {
  late PicturePermissionSheet1Model _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => PicturePermissionSheet1Model());

    // On component load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      if (widget.pictureDoc!.availableToAll) {
        setState(() {
          _model.choiceCurrently = 'Everyone';
        });
      } else {
        if (widget.pictureDoc!.availableToMatches) {
          setState(() {
            _model.choiceCurrently = 'All my matches';
          });
        } else {
          setState(() {
            _model.choiceCurrently = 'Certain matches only';
          });
        }
      }
    });
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return Container(
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).primaryBackground,
        boxShadow: const [
          BoxShadow(
            blurRadius: 5.0,
            color: Color(0x3B1D2429),
            offset: Offset(0.0, -3.0),
          )
        ],
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(0.0),
          bottomRight: Radius.circular(0.0),
          topLeft: Radius.circular(0.0),
          topRight: Radius.circular(0.0),
        ),
      ),
      child: Align(
        alignment: const AlignmentDirectional(0.0, 1.0),
        child: Stack(
          alignment: const AlignmentDirectional(0.0, 1.0),
          children: [
            Align(
              alignment: const AlignmentDirectional(0.0, 0.0),
              child: Stack(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              0.0, 5.0, 0.0, 0.0),
                          child: Stack(
                            children: [
                              Align(
                                alignment: const AlignmentDirectional(0.0, 0.0),
                                child: Text(
                                  'Change visibility',
                                  style: FlutterFlowTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        fontSize: 16.0,
                                        fontWeight: FontWeight.bold,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                              Align(
                                alignment: const AlignmentDirectional(-1.0, 0.0),
                                child: InkWell(
                                  splashColor: Colors.transparent,
                                  focusColor: Colors.transparent,
                                  hoverColor: Colors.transparent,
                                  highlightColor: Colors.transparent,
                                  onTap: () async {
                                    Navigator.pop(context);
                                  },
                                  child: Icon(
                                    Icons.close,
                                    color: FlutterFlowTheme.of(context)
                                        .secondaryText,
                                    size: 24.0,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              0.0, 26.0, 0.0, 0.0),
                          child: Text(
                            'Choose who should be able to see this image.',
                            textAlign: TextAlign.center,
                            style: FlutterFlowTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'BT Beau Sans',
                                  fontWeight: FontWeight.normal,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              0.0, 42.0, 0.0, 0.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 0.0, 0.0, 21.0),
                                child: InkWell(
                                  splashColor: Colors.transparent,
                                  focusColor: Colors.transparent,
                                  hoverColor: Colors.transparent,
                                  highlightColor: Colors.transparent,
                                  onTap: () async {
                                    await widget.pictureDoc!.reference.update({
                                      ...createImagesRecordData(
                                        availableToAll: true,
                                        availableToNone: false,
                                        availableToMatches: false,
                                      ),
                                      ...mapToFirestore(
                                        {
                                          'availableToSpecified':
                                              FieldValue.delete(),
                                        },
                                      ),
                                    });
                                    Navigator.pop(context);
                                  },
                                  child: Material(
                                    color: Colors.transparent,
                                    elevation: 0.0,
                                    shape: RoundedRectangleBorder(
                                      borderRadius:
                                          BorderRadius.circular(100.0),
                                    ),
                                    child: Container(
                                      height: 52.0,
                                      constraints: const BoxConstraints(
                                        maxWidth: 400.0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: FlutterFlowTheme.of(context)
                                            .primaryBackground,
                                        borderRadius:
                                            BorderRadius.circular(100.0),
                                        border: Border.all(
                                          color: _model.choiceCurrently ==
                                                  'Everyone'
                                              ? const Color(0xFFAE34E8)
                                              : const Color(0xFFB9BFC8),
                                          width: 2.0,
                                        ),
                                      ),
                                      child: Align(
                                        alignment:
                                            const AlignmentDirectional(0.0, 0.0),
                                        child: Text(
                                          'Everyone',
                                          style: FlutterFlowTheme.of(context)
                                              .bodyMedium
                                              .override(
                                                fontFamily: 'BT Beau Sans',
                                                color:
                                                    FlutterFlowTheme.of(context)
                                                        .secondaryText,
                                                fontSize: 19.0,
                                                fontWeight: FontWeight.bold,
                                                useGoogleFonts: false,
                                              ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 0.0, 0.0, 21.0),
                                child: InkWell(
                                  splashColor: Colors.transparent,
                                  focusColor: Colors.transparent,
                                  hoverColor: Colors.transparent,
                                  highlightColor: Colors.transparent,
                                  onTap: () async {
                                    await widget.pictureDoc!.reference.update({
                                      ...createImagesRecordData(
                                        availableToAll: false,
                                        availableToNone: false,
                                        availableToMatches: true,
                                      ),
                                      ...mapToFirestore(
                                        {
                                          'availableToSpecified':
                                              FieldValue.delete(),
                                        },
                                      ),
                                    });
                                    Navigator.pop(context);
                                  },
                                  child: Material(
                                    color: Colors.transparent,
                                    elevation: 0.0,
                                    shape: RoundedRectangleBorder(
                                      borderRadius:
                                          BorderRadius.circular(100.0),
                                    ),
                                    child: Container(
                                      height: 52.0,
                                      constraints: const BoxConstraints(
                                        maxWidth: 400.0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: FlutterFlowTheme.of(context)
                                            .primaryBackground,
                                        borderRadius:
                                            BorderRadius.circular(100.0),
                                        border: Border.all(
                                          color: _model.choiceCurrently ==
                                                  'All my matches'
                                              ? const Color(0xFFAE34E8)
                                              : const Color(0xFFB9BFC8),
                                          width: 2.0,
                                        ),
                                      ),
                                      child: Align(
                                        alignment:
                                            const AlignmentDirectional(0.0, 0.0),
                                        child: Text(
                                          'All my matches',
                                          style: FlutterFlowTheme.of(context)
                                              .bodyMedium
                                              .override(
                                                fontFamily: 'BT Beau Sans',
                                                color:
                                                    FlutterFlowTheme.of(context)
                                                        .secondaryText,
                                                fontSize: 19.0,
                                                fontWeight: FontWeight.bold,
                                                useGoogleFonts: false,
                                              ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 0.0, 0.0, 21.0),
                                child: Material(
                                  color: Colors.transparent,
                                  elevation: 0.0,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(100.0),
                                  ),
                                  child: Container(
                                    height: 52.0,
                                    constraints: const BoxConstraints(
                                      maxWidth: 400.0,
                                    ),
                                    decoration: BoxDecoration(
                                      color: FlutterFlowTheme.of(context)
                                          .primaryBackground,
                                      borderRadius:
                                          BorderRadius.circular(100.0),
                                      border: Border.all(
                                        color: _model.choiceCurrently ==
                                                'Certain matches only'
                                            ? const Color(0xFFAE34E8)
                                            : const Color(0xFFB9BFC8),
                                        width: 2.0,
                                      ),
                                    ),
                                    child: Builder(
                                      builder: (context) => InkWell(
                                        splashColor: Colors.transparent,
                                        focusColor: Colors.transparent,
                                        hoverColor: Colors.transparent,
                                        highlightColor: Colors.transparent,
                                        onTap: () async {
                                          _model.likes =
                                              await queryLikesRecordCount(
                                            queryBuilder: (likesRecord) =>
                                                likesRecord
                                                    .where(
                                                      'involvedUsersProfileRefOnly',
                                                      arrayContains:
                                                          currentUserDocument
                                                              ?.publicProfile,
                                                    )
                                                    .where(
                                                      'mutual',
                                                      isEqualTo: true,
                                                    )
                                                    .where(
                                                      'unmatched',
                                                      isEqualTo: false,
                                                    ),
                                          );
                                          _model.feedbackAgentLike =
                                              await queryLikesRecordOnce(
                                            queryBuilder: (likesRecord) =>
                                                likesRecord
                                                    .where(
                                                      'involedUIDs',
                                                      arrayContains:
                                                          'feedbackAgent',
                                                    )
                                                    .where(
                                                      'mutual',
                                                      isEqualTo: true,
                                                    )
                                                    .where(
                                                      'likedUser',
                                                      isEqualTo:
                                                          currentUserReference,
                                                    ),
                                            singleRecord: true,
                                          ).then((s) => s.firstOrNull);
                                          if (_model.feedbackAgentLike != null
                                              ? (_model.likes! > 2)
                                              : (_model.likes! > 1)) {
                                            await showModalBottomSheet(
                                              isScrollControlled: true,
                                              backgroundColor:
                                                  Colors.transparent,
                                              enableDrag: false,
                                              useSafeArea: true,
                                              context: context,
                                              builder: (context) {
                                                return Padding(
                                                  padding:
                                                      MediaQuery.viewInsetsOf(
                                                          context),
                                                  child:
                                                      PicturePermissionSheet2Widget(
                                                    pictureDoc:
                                                        widget.pictureDoc!,
                                                  ),
                                                );
                                              },
                                            ).then(
                                                (value) => safeSetState(() {}));
                                          } else {
                                            await showDialog(
                                              context: context,
                                              builder: (dialogContext) {
                                                return Dialog(
                                                  elevation: 0,
                                                  insetPadding: EdgeInsets.zero,
                                                  backgroundColor:
                                                      Colors.transparent,
                                                  alignment:
                                                      const AlignmentDirectional(
                                                              0.0, 0.0)
                                                          .resolve(
                                                              Directionality.of(
                                                                  context)),
                                                  child: const GeneralPopupWidget(
                                                    alertTitle:
                                                        'You don\'t have any matches',
                                                    alertText:
                                                        'You need to have at least one match before you can use this option',
                                                  ),
                                                );
                                              },
                                            ).then((value) => setState(() {}));
                                          }

                                          setState(() {});
                                        },
                                        child: Stack(
                                          children: [
                                            Align(
                                              alignment: const AlignmentDirectional(
                                                  0.0, 0.0),
                                              child: Text(
                                                'Certain matches only',
                                                style:
                                                    FlutterFlowTheme.of(context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          color: FlutterFlowTheme
                                                                  .of(context)
                                                              .secondaryText,
                                                          fontSize: 19.0,
                                                          letterSpacing: 0.0,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                          useGoogleFonts: false,
                                                        ),
                                              ),
                                            ),
                                            Align(
                                              alignment: const AlignmentDirectional(
                                                  1.0, 0.0),
                                              child: Padding(
                                                padding: const EdgeInsetsDirectional
                                                    .fromSTEB(
                                                        0.0, 0.0, 13.0, 0.0),
                                                child: FaIcon(
                                                  FontAwesomeIcons.chevronRight,
                                                  color: FlutterFlowTheme.of(
                                                          context)
                                                      .secondaryText,
                                                  size: 24.0,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
