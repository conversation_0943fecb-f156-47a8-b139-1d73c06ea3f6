import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:provider/provider.dart';
import 'picture_permission_sheet2_model.dart';
export 'picture_permission_sheet2_model.dart';

class PicturePermissionSheet2Widget extends StatefulWidget {
  const PicturePermissionSheet2Widget({
    super.key,
    required this.pictureDoc,
  });

  final ImagesRecord? pictureDoc;

  @override
  State<PicturePermissionSheet2Widget> createState() =>
      _PicturePermissionSheet2WidgetState();
}

class _PicturePermissionSheet2WidgetState
    extends State<PicturePermissionSheet2Widget> {
  late PicturePermissionSheet2Model _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => PicturePermissionSheet2Model());

    // On component load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      setState(() {
        _model.allowedUsers = widget.pictureDoc!.availableToSpecified
            .toList()
            .cast<DocumentReference>();
      });
    });
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return Container(
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).primaryBackground,
        boxShadow: const [
          BoxShadow(
            blurRadius: 5.0,
            color: Color(0x3B1D2429),
            offset: Offset(0.0, -3.0),
          )
        ],
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(0.0),
          bottomRight: Radius.circular(0.0),
          topLeft: Radius.circular(0.0),
          topRight: Radius.circular(0.0),
        ),
      ),
      child: Align(
        alignment: const AlignmentDirectional(0.0, 1.0),
        child: SafeArea(
          child: Stack(
            alignment: const AlignmentDirectional(0.0, 1.0),
            children: [
              Align(
                alignment: const AlignmentDirectional(0.0, 0.0),
                child: Stack(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 5.0, 0.0, 0.0),
                            child: Stack(
                              children: [
                                Align(
                                  alignment: const AlignmentDirectional(0.0, 0.0),
                                  child: Text(
                                    'Change visibility',
                                    style: FlutterFlowTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'BT Beau Sans',
                                          fontSize: 16.0,
                                          fontWeight: FontWeight.bold,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ),
                                Align(
                                  alignment: const AlignmentDirectional(-1.0, 0.0),
                                  child: InkWell(
                                    splashColor: Colors.transparent,
                                    focusColor: Colors.transparent,
                                    hoverColor: Colors.transparent,
                                    highlightColor: Colors.transparent,
                                    onTap: () async {
                                      Navigator.pop(context);
                                    },
                                    child: Icon(
                                      Icons.close,
                                      color: FlutterFlowTheme.of(context)
                                          .secondaryText,
                                      size: 24.0,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                20.0, 26.0, 20.0, 0.0),
                            child: Text(
                              'Choose the matches who should be able to see this image.',
                              textAlign: TextAlign.center,
                              style: FlutterFlowTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'BT Beau Sans',
                                    fontWeight: FontWeight.normal,
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 47.0, 0.0, 12.0),
                            child: Text(
                              'Matches',
                              textAlign: TextAlign.center,
                              style: FlutterFlowTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'BT Beau Sans',
                                    fontWeight: FontWeight.bold,
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ),
                          Divider(
                            thickness: 1.0,
                            color: FlutterFlowTheme.of(context).secondaryText,
                          ),
                          Column(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              SingleChildScrollView(
                                child: Column(
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    Container(
                                      constraints: BoxConstraints(
                                        maxHeight:
                                            MediaQuery.sizeOf(context).height *
                                                0.7,
                                      ),
                                      decoration: const BoxDecoration(),
                                      child: AuthUserStreamWidget(
                                        builder: (context) =>
                                            StreamBuilder<List<LikesRecord>>(
                                          stream: queryLikesRecord(
                                            queryBuilder: (likesRecord) =>
                                                likesRecord
                                                    .where(
                                                      'involvedUsersProfileRefOnly',
                                                      arrayContains:
                                                          currentUserDocument
                                                              ?.publicProfile,
                                                    )
                                                    .where(
                                                      'mutual',
                                                      isEqualTo: true,
                                                    ),
                                          ),
                                          builder: (context, snapshot) {
                                            // Customize what your widget looks like when it's loading.
                                            if (!snapshot.hasData) {
                                              return Center(
                                                child: SizedBox(
                                                  width: 50.0,
                                                  height: 50.0,
                                                  child:
                                                      CircularProgressIndicator(
                                                    valueColor:
                                                        AlwaysStoppedAnimation<
                                                            Color>(
                                                      FlutterFlowTheme.of(context)
                                                          .accent2,
                                                    ),
                                                  ),
                                                ),
                                              );
                                            }
                                            List<LikesRecord>
                                                listViewLikesRecordList =
                                                snapshot.data!;
                                            return ListView.builder(
                                              padding: EdgeInsets.zero,
                                              primary: false,
                                              scrollDirection: Axis.vertical,
                                              itemCount:
                                                  listViewLikesRecordList.length,
                                              itemBuilder:
                                                  (context, listViewIndex) {
                                                final listViewLikesRecord =
                                                    listViewLikesRecordList[
                                                        listViewIndex];
                                                return StreamBuilder<
                                                    PublicProfileRecord>(
                                                  stream: PublicProfileRecord
                                                      .getDocument(listViewLikesRecord
                                                          .involvedUsersProfileRefOnly
                                                          .where((e) =>
                                                              e.id !=
                                                              currentUserDocument
                                                                  ?.publicProfile
                                                                  ?.id)
                                                          .toList()
                                                          .first),
                                                  builder: (context, snapshot) {
                                                    // Customize what your widget looks like when it's loading.
                                                    if (!snapshot.hasData) {
                                                      return Center(
                                                        child: SizedBox(
                                                          width: 50.0,
                                                          height: 50.0,
                                                          child:
                                                              CircularProgressIndicator(
                                                            valueColor:
                                                                AlwaysStoppedAnimation<
                                                                    Color>(
                                                              FlutterFlowTheme.of(
                                                                      context)
                                                                  .accent2,
                                                            ),
                                                          ),
                                                        ),
                                                      );
                                                    }
                                                    final cardPublicProfileRecord =
                                                        snapshot.data!;
                                                    return Card(
                                                      clipBehavior: Clip
                                                          .antiAliasWithSaveLayer,
                                                      color: FlutterFlowTheme.of(
                                                              context)
                                                          .primaryBackground,
                                                      elevation: 0.0,
                                                      shape:
                                                          RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius.circular(
                                                                0.0),
                                                      ),
                                                      child: Visibility(
                                                        visible: (cardPublicProfileRecord
                                                                    .uid !=
                                                                'feedbackAgent') &&
                                                            (cardPublicProfileRecord
                                                                    .uid !=
                                                                'fehBchlcouUcVwb66DJL03ZQJPH3'),
                                                        child: Column(
                                                          mainAxisSize:
                                                              MainAxisSize.max,
                                                          children: [
                                                            InkWell(
                                                              splashColor: Colors
                                                                  .transparent,
                                                              focusColor: Colors
                                                                  .transparent,
                                                              hoverColor: Colors
                                                                  .transparent,
                                                              highlightColor:
                                                                  Colors
                                                                      .transparent,
                                                              onTap: () async {
                                                                if (_model
                                                                    .allowedUsers
                                                                    .contains(
                                                                        cardPublicProfileRecord
                                                                            .parentReference)) {
                                                                  setState(() {
                                                                    _model.removeFromAllowedUsers(
                                                                        cardPublicProfileRecord
                                                                            .parentReference);
                                                                  });
                                                                } else {
                                                                  setState(() {
                                                                    _model.addToAllowedUsers(
                                                                        cardPublicProfileRecord
                                                                            .parentReference);
                                                                  });
                                                                }
                                                              },
                                                              child: Row(
                                                                mainAxisSize:
                                                                    MainAxisSize
                                                                        .max,
                                                                mainAxisAlignment:
                                                                    MainAxisAlignment
                                                                        .spaceBetween,
                                                                children: [
                                                                  Padding(
                                                                    padding: const EdgeInsetsDirectional
                                                                        .fromSTEB(
                                                                            0.0,
                                                                            0.0,
                                                                            0.0,
                                                                            10.0),
                                                                    child: Column(
                                                                      mainAxisSize:
                                                                          MainAxisSize
                                                                              .max,
                                                                      crossAxisAlignment:
                                                                          CrossAxisAlignment
                                                                              .start,
                                                                      children: [
                                                                        if (_model
                                                                            .allowedUsers
                                                                            .contains(
                                                                                cardPublicProfileRecord.parentReference))
                                                                          Padding(
                                                                            padding: const EdgeInsetsDirectional.fromSTEB(
                                                                                0.0,
                                                                                21.0,
                                                                                0.0,
                                                                                0.0),
                                                                            child:
                                                                                Text(
                                                                              cardPublicProfileRecord.publicName,
                                                                              style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                    fontFamily: 'BT Beau Sans',
                                                                                    fontSize: 15.0,
                                                                                    letterSpacing: 0.0,
                                                                                    fontWeight: FontWeight.bold,
                                                                                    useGoogleFonts: false,
                                                                                  ),
                                                                            ),
                                                                          ),
                                                                        if (!_model
                                                                            .allowedUsers
                                                                            .contains(
                                                                                cardPublicProfileRecord.parentReference))
                                                                          Padding(
                                                                            padding: const EdgeInsetsDirectional.fromSTEB(
                                                                                0.0,
                                                                                21.0,
                                                                                0.0,
                                                                                0.0),
                                                                            child:
                                                                                Text(
                                                                              cardPublicProfileRecord.publicName,
                                                                              style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                    fontFamily: 'BT Beau Sans',
                                                                                    fontSize: 15.0,
                                                                                    letterSpacing: 0.0,
                                                                                    fontWeight: FontWeight.normal,
                                                                                    useGoogleFonts: false,
                                                                                  ),
                                                                            ),
                                                                          ),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                  Align(
                                                                    alignment:
                                                                        const AlignmentDirectional(
                                                                            1.0,
                                                                            0.0),
                                                                    child: Column(
                                                                      mainAxisSize:
                                                                          MainAxisSize
                                                                              .max,
                                                                      children: [
                                                                        if (_model
                                                                            .allowedUsers
                                                                            .contains(
                                                                                cardPublicProfileRecord.parentReference))
                                                                          const Icon(
                                                                            Icons
                                                                                .check_rounded,
                                                                            color:
                                                                                Color(0xFF9200D6),
                                                                            size:
                                                                                24.0,
                                                                          ),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                            Divider(
                                                              thickness: 1.0,
                                                              color: FlutterFlowTheme
                                                                      .of(context)
                                                                  .secondaryText,
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                );
                                              },
                                            );
                                          },
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(17.0, 0.0, 17.0, 27.0),
                child: wrapWithModel(
                  model: _model.gradientButtonModel,
                  updateCallback: () => setState(() {}),
                  child: GradientButtonWidget(
                    title:
                        'Make visible to ${_model.allowedUsers.length.toString()} ${_model.allowedUsers.length == 1 ? 'match' : 'matches'}',
                    action: () async {
                      await widget.pictureDoc!.reference.update({
                        ...createImagesRecordData(
                          availableToAll: false,
                          availableToNone: false,
                          availableToMatches: false,
                        ),
                        ...mapToFirestore(
                          {
                            'availableToSpecified': _model.allowedUsers,
                          },
                        ),
                      });
                      Navigator.pop(context);
                      Navigator.pop(context);
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
