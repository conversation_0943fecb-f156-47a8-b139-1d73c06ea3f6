import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/gradient_button_disabled/gradient_button_disabled_widget.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'report1_model.dart';
export 'report1_model.dart';

class Report1Widget extends StatefulWidget {
  const Report1Widget({
    super.key,
    this.reportedUser,
    this.reportedMatch,
  });

  final PublicProfileRecord? reportedUser;
  final DocumentReference? reportedMatch;

  @override
  State<Report1Widget> createState() => _Report1WidgetState();
}

class _Report1WidgetState extends State<Report1Widget> {
  late Report1Model _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => Report1Model());
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return GestureDetector(
      onTap: () => _model.unfocusNode.canRequestFocus
          ? FocusScope.of(context).requestFocus(_model.unfocusNode)
          : FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        appBar: AppBar(
          backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
          automaticallyImplyLeading: false,
          title: Stack(
            children: [
              Align(
                alignment: const AlignmentDirectional(0.0, 0.0),
                child: Stack(
                  alignment: const AlignmentDirectional(0.0, 0.0),
                  children: [
                    Text(
                      'Report',
                      style:
                          FlutterFlowTheme.of(context).headlineMedium.override(
                                fontFamily: 'BT Beau Sans',
                                color: const Color(0xFF262A36),
                                fontSize: 20.0,
                                fontWeight: FontWeight.bold,
                                useGoogleFonts: false,
                              ),
                    ),
                    Align(
                      alignment: const AlignmentDirectional(1.0, 0.0),
                      child: InkWell(
                        splashColor: Colors.transparent,
                        focusColor: Colors.transparent,
                        hoverColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        onTap: () async {
                          context.safePop();
                        },
                        child: Text(
                          'Cancel',
                          style:
                              FlutterFlowTheme.of(context).bodyMedium.override(
                                    fontFamily: 'BT Beau Sans',
                                    color: const Color(0xFF4F5765),
                                    fontSize: 16.0,
                                    fontWeight: FontWeight.bold,
                                    useGoogleFonts: false,
                                  ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: const [],
          centerTitle: false,
          elevation: 1.0,
        ),
        body: SafeArea(
          top: true,
          child: Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(16.0, 52.0, 16.0, 30.0),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Flexible(
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Text(
                          'Reason',
                          textAlign: TextAlign.center,
                          style:
                              FlutterFlowTheme.of(context).bodyMedium.override(
                                    fontFamily: 'BT Beau Sans',
                                    fontSize: 20.0,
                                    fontWeight: FontWeight.w500,
                                    useGoogleFonts: false,
                                  ),
                        ),
                        Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              10.0, 45.0, 10.0, 0.0),
                          child: Text(
                            'We want you and this platform to be safe. Please let us know if anything is wrong. \nIn case of imminent emergencies, please call your local authorities.',
                            textAlign: TextAlign.center,
                            style: FlutterFlowTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'BT Beau Sans',
                                  fontSize: 16.0,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              0.0, 26.0, 0.0, 0.0),
                          child: StreamBuilder<List<OptionsForSelectorsRecord>>(
                            stream: queryOptionsForSelectorsRecord(
                              queryBuilder: (optionsForSelectorsRecord) =>
                                  optionsForSelectorsRecord.where(
                                'name',
                                isEqualTo: 'Report Reasons',
                              ),
                              singleRecord: true,
                            ),
                            builder: (context, snapshot) {
                              // Customize what your widget looks like when it's loading.
                              if (!snapshot.hasData) {
                                return Center(
                                  child: SizedBox(
                                    width: 50.0,
                                    height: 50.0,
                                    child: CircularProgressIndicator(
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        FlutterFlowTheme.of(context).accent2,
                                      ),
                                    ),
                                  ),
                                );
                              }
                              List<OptionsForSelectorsRecord>
                                  columnOptionsForSelectorsRecordList =
                                  snapshot.data!;
                              // Return an empty Container when the item does not exist.
                              if (snapshot.data!.isEmpty) {
                                return Container();
                              }
                              final columnOptionsForSelectorsRecord =
                                  columnOptionsForSelectorsRecordList.isNotEmpty
                                      ? columnOptionsForSelectorsRecordList
                                          .first
                                      : null;
                              return Builder(
                                builder: (context) {
                                  final reportReasons =
                                      columnOptionsForSelectorsRecord?.options
                                              .toList() ??
                                          [];
                                  return Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children:
                                        List.generate(reportReasons.length,
                                            (reportReasonsIndex) {
                                      final reportReasonsItem =
                                          reportReasons[reportReasonsIndex];
                                      return Row(
                                        mainAxisSize: MainAxisSize.max,
                                        children: [
                                          Flexible(
                                            child: Padding(
                                              padding: const EdgeInsetsDirectional
                                                  .fromSTEB(0.0, 9.0, 0.0, 0.0),
                                              child: InkWell(
                                                splashColor: Colors.transparent,
                                                focusColor: Colors.transparent,
                                                hoverColor: Colors.transparent,
                                                highlightColor:
                                                    Colors.transparent,
                                                onTap: () async {
                                                  if (_model.selectedReason ==
                                                      reportReasonsItem) {
                                                    setState(() {
                                                      _model.selectedReason =
                                                          '';
                                                    });
                                                  } else {
                                                    setState(() {
                                                      _model.selectedReason =
                                                          reportReasonsItem;
                                                    });
                                                  }
                                                },
                                                child: Container(
                                                  width: double.infinity,
                                                  height: 40.0,
                                                  decoration: BoxDecoration(
                                                    color: FlutterFlowTheme.of(
                                                            context)
                                                        .primaryBackground,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8.0),
                                                    border: Border.all(
                                                      color: _model
                                                                  .selectedReason ==
                                                              reportReasonsItem
                                                          ? FlutterFlowTheme.of(
                                                                  context)
                                                              .accent2
                                                          : FlutterFlowTheme.of(
                                                                  context)
                                                              .secondaryText,
                                                      width: 2.0,
                                                    ),
                                                  ),
                                                  child: Padding(
                                                    padding:
                                                        const EdgeInsetsDirectional
                                                            .fromSTEB(15.0, 0.0,
                                                                15.0, 0.0),
                                                    child: Row(
                                                      mainAxisSize:
                                                          MainAxisSize.max,
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceBetween,
                                                      children: [
                                                        if (_model
                                                                .selectedReason !=
                                                            reportReasonsItem)
                                                          Text(
                                                            reportReasonsItem,
                                                            style: FlutterFlowTheme
                                                                    .of(context)
                                                                .bodyMedium
                                                                .override(
                                                                  fontFamily:
                                                                      'BT Beau Sans',
                                                                  fontSize:
                                                                      15.0,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w500,
                                                                  useGoogleFonts:
                                                                      false,
                                                                ),
                                                          ),
                                                        if (_model
                                                                .selectedReason ==
                                                            reportReasonsItem)
                                                          Text(
                                                            reportReasonsItem,
                                                            style: FlutterFlowTheme
                                                                    .of(context)
                                                                .bodyMedium
                                                                .override(
                                                                  fontFamily:
                                                                      'BT Beau Sans',
                                                                  fontSize:
                                                                      15.0,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w600,
                                                                  useGoogleFonts:
                                                                      false,
                                                                ),
                                                          ),
                                                        if (_model
                                                                .selectedReason ==
                                                            reportReasonsItem)
                                                          FaIcon(
                                                            FontAwesomeIcons
                                                                .check,
                                                            color: FlutterFlowTheme
                                                                    .of(context)
                                                                .accent2,
                                                            size: 18.0,
                                                          ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      );
                                    }).addToEnd(const SizedBox(height: 30.0)),
                                  );
                                },
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Text(
                      'Your report will be treated with discretion.',
                      style: FlutterFlowTheme.of(context).bodyMedium.override(
                            fontFamily: 'BT Beau Sans',
                            fontSize: 15.0,
                            useGoogleFonts: false,
                          ),
                    ),
                    if (_model.selectedReason != '')
                      Align(
                        alignment: const AlignmentDirectional(0.0, 1.0),
                        child: Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              0.0, 10.0, 0.0, 0.0),
                          child: wrapWithModel(
                            model: _model.gradientButtonModel,
                            updateCallback: () => setState(() {}),
                            child: GradientButtonWidget(
                              title: 'Next',
                              action: () async {
                                if (Navigator.of(context).canPop()) {
                                  context.pop();
                                }
                                context.pushNamed(
                                  'Report2',
                                  queryParameters: {
                                    'userToReport': serializeParam(
                                      widget.reportedUser,
                                      ParamType.Document,
                                    ),
                                    'matchToReport': serializeParam(
                                      widget.reportedMatch,
                                      ParamType.DocumentReference,
                                    ),
                                    'reason': serializeParam(
                                      _model.selectedReason,
                                      ParamType.String,
                                    ),
                                  }.withoutNulls,
                                  extra: <String, dynamic>{
                                    'userToReport': widget.reportedUser,
                                  },
                                );
                              },
                            ),
                          ),
                        ),
                      ),
                    if (_model.selectedReason == '')
                      wrapWithModel(
                        model: _model.gradientButtonDisabledModel,
                        updateCallback: () => setState(() {}),
                        child: GradientButtonDisabledWidget(
                          title: 'Next',
                          action: () async {},
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
