import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import 'dart:async';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'report2_model.dart';
export 'report2_model.dart';

class Report2Widget extends StatefulWidget {
  const Report2Widget({
    super.key,
    this.userToReport,
    this.matchToReport,
    required this.reason,
  });

  final PublicProfileRecord? userToReport;
  final DocumentReference? matchToReport;
  final String? reason;

  @override
  State<Report2Widget> createState() => _Report2WidgetState();
}

class _Report2WidgetState extends State<Report2Widget> {
  late Report2Model _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => Report2Model());

    _model.textController ??= TextEditingController();
    _model.textFieldFocusNode ??= FocusNode();
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return GestureDetector(
      onTap: () => _model.unfocusNode.canRequestFocus
          ? FocusScope.of(context).requestFocus(_model.unfocusNode)
          : FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        appBar: AppBar(
          backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
          automaticallyImplyLeading: false,
          title: Stack(
            children: [
              Align(
                alignment: const AlignmentDirectional(0.0, 0.0),
                child: Stack(
                  alignment: const AlignmentDirectional(0.0, 0.0),
                  children: [
                    Text(
                      'Report',
                      style:
                          FlutterFlowTheme.of(context).headlineMedium.override(
                                fontFamily: 'BT Beau Sans',
                                color: const Color(0xFF262A36),
                                fontSize: 20.0,
                                fontWeight: FontWeight.bold,
                                useGoogleFonts: false,
                              ),
                    ),
                    Align(
                      alignment: const AlignmentDirectional(1.0, 0.0),
                      child: InkWell(
                        splashColor: Colors.transparent,
                        focusColor: Colors.transparent,
                        hoverColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        onTap: () async {
                          context.safePop();
                        },
                        child: Text(
                          'Cancel',
                          style:
                              FlutterFlowTheme.of(context).bodyMedium.override(
                                    fontFamily: 'BT Beau Sans',
                                    color: const Color(0xFF4F5765),
                                    fontSize: 16.0,
                                    fontWeight: FontWeight.bold,
                                    useGoogleFonts: false,
                                  ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: const [],
          centerTitle: false,
          elevation: 1.0,
        ),
        body: SafeArea(
          top: true,
          child: Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(16.0, 52.0, 16.0, 0.0),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Flexible(
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Text(
                          'Tell us what went wrong',
                          textAlign: TextAlign.center,
                          style:
                              FlutterFlowTheme.of(context).bodyMedium.override(
                                    fontFamily: 'BT Beau Sans',
                                    fontSize: 20.0,
                                    fontWeight: FontWeight.w500,
                                    useGoogleFonts: false,
                                  ),
                        ),
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 40.0, 0.0, 0.0),
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(5.0),
                                  border: Border.all(
                                    color: const Color(0xFF747E90),
                                    width: 1.0,
                                  ),
                                ),
                                child: Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      8.0, 0.0, 8.0, 0.0),
                                  child: TextFormField(
                                    controller: _model.textController,
                                    focusNode: _model.textFieldFocusNode,
                                    autofocus: true,
                                    obscureText: false,
                                    decoration: InputDecoration(
                                      labelStyle: FlutterFlowTheme.of(context)
                                          .labelMedium,
                                      hintText:
                                          'Provide any details that may be important to you or help us in processing your report.',
                                      hintStyle: FlutterFlowTheme.of(context)
                                          .labelMedium
                                          .override(
                                            fontFamily: 'BT Beau Sans',
                                            fontSize: 14.0,
                                            fontWeight: FontWeight.w500,
                                            useGoogleFonts: false,
                                          ),
                                      enabledBorder: InputBorder.none,
                                      focusedBorder: InputBorder.none,
                                      errorBorder: InputBorder.none,
                                      focusedErrorBorder: InputBorder.none,
                                    ),
                                    style: FlutterFlowTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'BT Beau Sans',
                                          fontSize: 14.0,
                                          fontWeight: FontWeight.w500,
                                          useGoogleFonts: false,
                                        ),
                                    textAlign: TextAlign.start,
                                    maxLines: 9,
                                    minLines: 9,
                                    maxLength: 2000,
                                    maxLengthEnforcement:
                                        MaxLengthEnforcement.enforced,
                                    keyboardType: TextInputType.multiline,
                                    validator: _model.textControllerValidator
                                        .asValidator(context),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Text(
                      'Your report will be treated with discretion.',
                      style: FlutterFlowTheme.of(context).bodyMedium.override(
                            fontFamily: 'BT Beau Sans',
                            fontSize: 15.0,
                            useGoogleFonts: false,
                          ),
                    ),
                    Builder(
                      builder: (context) => Padding(
                        padding: const EdgeInsetsDirectional.fromSTEB(
                            0.0, 10.0, 0.0, 10.0),
                        child: wrapWithModel(
                          model: _model.gradientButtonModel,
                          updateCallback: () => setState(() {}),
                          child: GradientButtonWidget(
                            title:
                                'Report & Block ${widget.userToReport?.publicName}',
                            action: () async {
                              var shouldSetState = false;
                              if (widget.matchToReport != null) {
                                var reportsRecordReference1 =
                                    ReportsRecord.collection.doc();
                                await reportsRecordReference1
                                    .set(createReportsRecordData(
                                  reportedUser:
                                      widget.userToReport?.parentReference,
                                  reportedMatch: widget.matchToReport,
                                  reason: widget.reason,
                                  comment: _model.textController.text,
                                  reviewed: false,
                                  userTakingAction: currentUserReference,
                                  reportedName: widget.userToReport?.publicName,
                                  reportedUID: widget.userToReport?.uid,
                                  reportingUID: currentUserUid,
                                  time: getCurrentTimestamp,
                                ));
                                _model.report =
                                    ReportsRecord.getDocumentFromData(
                                        createReportsRecordData(
                                          reportedUser: widget
                                              .userToReport?.parentReference,
                                          reportedMatch: widget.matchToReport,
                                          reason: widget.reason,
                                          comment: _model.textController.text,
                                          reviewed: false,
                                          userTakingAction:
                                              currentUserReference,
                                          reportedName:
                                              widget.userToReport?.publicName,
                                          reportedUID: widget.userToReport?.uid,
                                          reportingUID: currentUserUid,
                                          time: getCurrentTimestamp,
                                        ),
                                        reportsRecordReference1);
                                shouldSetState = true;
                                try {
                                  final result =
                                      await FirebaseFunctions.instanceFor(
                                              region: 'europe-west2')
                                          .httpsCallable('unmatch')
                                          .call({
                                    "otherUserUID": widget.userToReport!.uid,
                                  });
                                  _model.unmatchcf =
                                      UnmatchCloudFunctionCallResponse(
                                    succeeded: true,
                                  );
                                } on FirebaseFunctionsException catch (error) {
                                  _model.unmatchcf =
                                      UnmatchCloudFunctionCallResponse(
                                    errorCode: error.code,
                                    succeeded: false,
                                  );
                                }

                                shouldSetState = true;
                                if (!_model.unmatchcf!.succeeded!) {
                                  await showDialog(
                                    context: context,
                                    builder: (dialogContext) {
                                      return Dialog(
                                        elevation: 0,
                                        insetPadding: EdgeInsets.zero,
                                        backgroundColor: Colors.transparent,
                                        alignment:
                                            const AlignmentDirectional(0.0, 0.0)
                                                .resolve(
                                                    Directionality.of(context)),
                                        child: GestureDetector(
                                          onTap: () => _model
                                                  .unfocusNode.canRequestFocus
                                              ? FocusScope.of(context)
                                                  .requestFocus(
                                                      _model.unfocusNode)
                                              : FocusScope.of(context)
                                                  .unfocus(),
                                          child: const GeneralPopupWidget(
                                            alertTitle: 'Error',
                                            alertText:
                                                'Reporting could not be completed, please try again.',
                                          ),
                                        ),
                                      );
                                    },
                                  ).then((value) => setState(() {}));

                                  if (shouldSetState) setState(() {});
                                  return;
                                }

                                await currentUserReference!.update({
                                  ...mapToFirestore(
                                    {
                                      'blockedProfiles': FieldValue.arrayUnion([
                                        getPublicProfileUIDFirestoreData(
                                          updatePublicProfileUIDStruct(
                                            PublicProfileUIDStruct(
                                              publicProfile: widget
                                                  .userToReport?.reference,
                                              uid: widget.userToReport?.uid,
                                            ),
                                            clearUnsetFields: false,
                                          ),
                                          true,
                                        )
                                      ]),
                                    },
                                  ),
                                });
                              } else {
                                var reportsRecordReference2 =
                                    ReportsRecord.collection.doc();
                                await reportsRecordReference2
                                    .set(createReportsRecordData(
                                  reportedUser:
                                      widget.userToReport?.parentReference,
                                  reason: widget.reason,
                                  comment: _model.textController.text,
                                  reviewed: false,
                                  userTakingAction: currentUserReference,
                                  reportedName: widget.userToReport?.publicName,
                                  reportedUID: widget.userToReport?.uid,
                                  reportingUID: currentUserUid,
                                  time: getCurrentTimestamp,
                                ));
                                _model.reportSuggestion =
                                    ReportsRecord.getDocumentFromData(
                                        createReportsRecordData(
                                          reportedUser: widget
                                              .userToReport?.parentReference,
                                          reason: widget.reason,
                                          comment: _model.textController.text,
                                          reviewed: false,
                                          userTakingAction:
                                              currentUserReference,
                                          reportedName:
                                              widget.userToReport?.publicName,
                                          reportedUID: widget.userToReport?.uid,
                                          reportingUID: currentUserUid,
                                          time: getCurrentTimestamp,
                                        ),
                                        reportsRecordReference2);
                                shouldSetState = true;

                                await currentUserReference!.update({
                                  ...mapToFirestore(
                                    {
                                      'blockedProfiles': FieldValue.arrayUnion([
                                        getPublicProfileUIDFirestoreData(
                                          updatePublicProfileUIDStruct(
                                            PublicProfileUIDStruct(
                                              publicProfile: widget
                                                  .userToReport?.reference,
                                              uid: widget.userToReport?.uid,
                                            ),
                                            clearUnsetFields: false,
                                          ),
                                          true,
                                        )
                                      ]),
                                      'matchingSuggestions':
                                          FieldValue.arrayRemove([
                                        getPublicProfileUIDFirestoreData(
                                          updatePublicProfileUIDStruct(
                                            PublicProfileUIDStruct(
                                              publicProfile: widget
                                                  .userToReport?.reference,
                                              uid: widget.userToReport?.uid,
                                            ),
                                            clearUnsetFields: false,
                                          ),
                                          true,
                                        )
                                      ]),
                                      'savedSuggestions':
                                          FieldValue.arrayRemove([
                                        getPublicProfileUIDFirestoreData(
                                          updatePublicProfileUIDStruct(
                                            PublicProfileUIDStruct(
                                              publicProfile: widget
                                                  .userToReport?.reference,
                                              uid: widget.userToReport?.uid,
                                            ),
                                            clearUnsetFields: false,
                                          ),
                                          true,
                                        )
                                      ]),
                                    },
                                  ),
                                });
                              }

                              unawaited(
                                () async {
                                  try {
                                    final result =
                                        await FirebaseFunctions.instanceFor(
                                                region: 'europe-west2')
                                            .httpsCallable(
                                                'createReportNotification')
                                            .call({
                                      "name": valueOrDefault(
                                          currentUserDocument?.name, ''),
                                      "gender":
                                          currentUserDocument!.gender!.name,
                                      "time": dateTimeFormat(
                                        'd/M h:mm a',
                                        getCurrentTimestamp,
                                        locale: FFLocalizations.of(context)
                                            .languageCode,
                                      ),
                                      "userUID": currentUserUid,
                                      "reportedReason": widget.reason!,
                                      "reportedUserName":
                                          widget.userToReport!.publicName,
                                      "reportedUserUID":
                                          widget.userToReport!.uid,
                                      "reportedUserGender":
                                          widget.userToReport!.gender!.name,
                                    });
                                    _model.createReportNotificationCF =
                                        CreateReportNotificationCloudFunctionCallResponse(
                                      succeeded: true,
                                    );
                                  } on FirebaseFunctionsException catch (error) {
                                    _model.createReportNotificationCF =
                                        CreateReportNotificationCloudFunctionCallResponse(
                                      errorCode: error.code,
                                      succeeded: false,
                                    );
                                  }
                                }(),
                              );
                              shouldSetState = true;
                              setState(() {
                                FFAppState().matchingSuggestions =
                                    (currentUserDocument?.matchingSuggestions
                                                .toList() ??
                                            [])
                                        .toList()
                                        .cast<PublicProfileUIDStruct>();
                              });

                             context.goNamed(
                                                        'TempLoaderScreen',
                                                        extra: <String,
                                                            dynamic>{
                                                          kTransitionInfoKey:
                                                              const TransitionInfo(
                                                            hasTransition: true,
                                                            transitionType:
                                                                PageTransitionType.fade,
                                              duration: Duration(milliseconds: 0),
                                                          ),
                                                        },
                                                      );

                              if (shouldSetState) setState(() {});
                            },
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
