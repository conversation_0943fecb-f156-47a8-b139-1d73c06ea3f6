import '/auth/firebase_auth/auth_util.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import '/backend/backend.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/backend/schema/enums/enums.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/gradient_button_disabled/gradient_button_disabled_widget.dart';
import '/general/info_sheet_scrollable/info_sheet_scrollable_widget.dart';
import '/general/lightbulb_tip/lightbulb_tip_widget.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import 'package:cloud_functions/cloud_functions.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'bio_bio_component_model.dart';
export 'bio_bio_component_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';

class BioBioComponentWidget extends StatefulWidget {
  const BioBioComponentWidget({
    super.key,
    required this.nextCallback,
    required this.doLaterCallback,
    required this.switchBioCallback,
    required this.backCallback,
  });

  final Future Function()? nextCallback;
  final Future Function()? doLaterCallback;
  final Future Function()? switchBioCallback;
  final Future Function()? backCallback;

  @override
  State<BioBioComponentWidget> createState() => _BioBioComponentWidgetState();
}

class _BioBioComponentWidgetState extends State<BioBioComponentWidget> {
  late BioBioComponentModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => BioBioComponentModel());

    _model.textFieldFocusNode ??= FocusNode();
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<PublicProfileRecord>>(
      stream: queryPublicProfileRecord(
        parent: currentUserReference,
        singleRecord: true,
      ),
      builder: (context, snapshot) {
        // Customize what your widget looks like when it's loading.
        if (!snapshot.hasData) {
          return Center(
            child: SizedBox(
              width: 50.0,
              height: 50.0,
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  FlutterFlowTheme.of(context).accent2,
                ),
              ),
            ),
          );
        }
        List<PublicProfileRecord> columnPublicProfileRecordList =
            snapshot.data!;
        // Return an empty Container when the item does not exist.
        if (snapshot.data!.isEmpty) {
          return Container();
        }
        final columnPublicProfileRecord =
            columnPublicProfileRecordList.isNotEmpty
                ? columnPublicProfileRecordList.first
                : null;

        return Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
              child: Container(
                decoration: const BoxDecoration(),
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Padding(
                        padding: const EdgeInsetsDirectional.fromSTEB(
                            10.0, 10.0, 0.0, 0.0),
                        child: Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Align(
                              alignment: const AlignmentDirectional(-1.0, 0.0),
                              child: FlutterFlowIconButton(
                                borderColor: const Color(0x00FFFFFF),
                                borderRadius: 20.0,
                                borderWidth: 1.0,
                                buttonSize: 40.0,
                                fillColor: const Color(0x00FFFFFF),
                                icon: FaIcon(
                                  FontAwesomeIcons.angleLeft,
                                  color:
                                      FlutterFlowTheme.of(context).primaryText,
                                  size: 24.0,
                                ),
                                onPressed: () async {
                                  await widget.backCallback?.call();
                                },
                              ),
                            ),
                            Align(
                              alignment: const AlignmentDirectional(1.0, 0.0),
                              child: FFButtonWidget(
                                onPressed: () async {
                                  try {
                                  analytics.logEvent('Sign Up: Went To Bio Prompts from Bio Text');
                                  } catch(e) {}
                                  await widget.switchBioCallback?.call();
                                },
                                text: getRemoteConfigString(
                                    'bio_bio_prompts_instead'),
                                options: FFButtonOptions(
                                  height: 40.0,
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      24.0, 0.0, 24.0, 0.0),
                                  iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                      0.0, 0.0, 0.0, 0.0),
                                  color: Colors.white,
                                  textStyle: FlutterFlowTheme.of(context)
                                      .titleSmall
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        color: FlutterFlowTheme.of(context)
                                            .secondaryText,
                                        fontSize: 14.0,
                                        letterSpacing: 0.0,
                                        useGoogleFonts: false,
                                      ),
                                  elevation: 0.0,
                                  borderSide: const BorderSide(
                                    color: Colors.transparent,
                                    width: 1.0,
                                  ),
                                  borderRadius: BorderRadius.circular(8.0),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Align(
                        alignment: const AlignmentDirectional(-1.0, -1.0),
                        child: Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              20.0, 20.0, 20.0, 16.0),
                          child: Text(
                            getRemoteConfigString('bio_prompts_headline'),
                            style: FlutterFlowTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'BT Beau Sans',
                                  fontSize: 32.0,
                                  letterSpacing: 0.0,
                                  fontWeight: FontWeight.bold,
                                  useGoogleFonts: false,
                                  lineHeight: 1.29,
                                ),
                          ),
                        ),
                      ),
                      Align(
                        alignment: const AlignmentDirectional(-1.0, -1.0),
                        child: Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              20.0, 0.0, 0.0, 12.0),
                          child: Text(
                            getRemoteConfigString('bio_bio_description'),
                            style: FlutterFlowTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'BT Beau Sans',
                                  fontSize: 16.0,
                                  letterSpacing: 0.0,
                                  fontWeight: FontWeight.w500,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                      ),
                      Form(
                        key: _model.formKey,
                        autovalidateMode: AutovalidateMode.disabled,
                        child: Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              6.0, 0.0, 6.0, 5.0),
                          child: Container(
                            constraints: const BoxConstraints(
                              minHeight: 150.0,
                              maxHeight: 500.0,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(0xFFF0F2F4),
                              borderRadius: BorderRadius.circular(10.0),
                            ),
                            child: Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  16.0, 10.0, 16.0, 10.0),
                              child: TextFormField(
                                controller: _model.textController ??=
                                    TextEditingController(
                                  text: columnPublicProfileRecord?.bio,
                                ),
                                focusNode: _model.textFieldFocusNode,
                                onChanged: (_) => EasyDebounce.debounce(
                                  '_model.textController',
                                  const Duration(milliseconds: 1),
                                  () => safeSetState(() {}),
                                ),
                                autofocus: false,
                                textCapitalization:
                                    TextCapitalization.sentences,
                                obscureText: false,
                                decoration: InputDecoration(
                                  isDense: false,
                                  hintText:
                                      getRemoteConfigString('signup_bio_bio_textfield_descriptior'),
                                  hintStyle: FlutterFlowTheme.of(context)
                                      .labelMedium
                                      .override(
                                        fontSize: 13,
                                        fontFamily: 'BT Beau Sans',
                                        letterSpacing: 0.0,
                                        useGoogleFonts: false,
                                      ),
                                  enabledBorder: InputBorder.none,
                                  focusedBorder: InputBorder.none,
                                  errorBorder: InputBorder.none,
                                  focusedErrorBorder: InputBorder.none,
                                ),
                                style: FlutterFlowTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'BT Beau Sans',
                                      fontSize: 16.0,
                                      letterSpacing: 0.0,
                                      useGoogleFonts: false,
                                    ),
                                maxLines: null,
                                minLines: 4,
                                maxLength: functions
                    .getSignupConfigFromRemoteConfigString("bio").maxCharsTextfield,
                                maxLengthEnforcement:
                                    MaxLengthEnforcement.enforced,
                                keyboardType: TextInputType.multiline,
                                validator: _model.textControllerValidator
                                    .asValidator(context),
                              ),
                            ),
                          ),
                        ),
                      ),
                      FFButtonWidget(
                        onPressed: () async {
                          await showModalBottomSheet(
                            isScrollControlled: true,
                            backgroundColor: Colors.transparent,
                            enableDrag: false,
                            context: context,
                            builder: (context) {
                              return Padding(
                                padding: MediaQuery.viewInsetsOf(context),
                                child: InfoSheetScrollableWidget(
                                  title: getRemoteConfigString(
                                      'signup_biobio_ibutton_title'),
                                  body: getRemoteConfigString(
                                      'signup_biobio_info'),
                                ),
                              );
                            },
                          ).then((value) => safeSetState(() {}));
                        },
                        text: getRemoteConfigString(
                            'signup_biobio_ibutton_title'),
                        icon: Icon(
                          Icons.info_outlined,
                          color: FlutterFlowTheme.of(context).secondaryText,
                          size: 18.0,
                        ),
                        options: FFButtonOptions(
                          width: MediaQuery.sizeOf(context).width * 1.0,
                          height: 40.0,
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              15.0, 0.0, 15.0, 0.0),
                          iconPadding: const EdgeInsetsDirectional.fromSTEB(
                              0.0, 0.0, 0.0, 0.0),
                          color: Colors.white,
                          textStyle: FlutterFlowTheme.of(context)
                              .titleSmall
                              .override(
                                fontFamily: 'BT Beau Sans',
                                color:
                                    FlutterFlowTheme.of(context).secondaryText,
                                fontSize: 4.0,
                                letterSpacing: 0.0,
                                fontWeight: FontWeight.w500,
                                useGoogleFonts: false,
                              ),
                          elevation: 0.0,
                          borderSide: const BorderSide(
                            color: Color(0x0057636C),
                            width: 0.0,
                          ),
                          borderRadius: BorderRadius.circular(100.0),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                if (!(_model.textFieldFocusNode?.hasFocus ?? false))
                  Padding(
                    padding:
                        const EdgeInsetsDirectional.fromSTEB(16.0, 25.0, 16.0, 23.0),
                    child: wrapWithModel(
                      model: _model.lightbulbTipModel,
                      updateCallback: () => safeSetState(() {}),
                      child: LightbulbTipWidget(
                        tip: getRemoteConfigString('bio_prompts_tip'),
                      ),
                    ),
                  ),
                Padding(
                  padding:
                      const EdgeInsetsDirectional.fromSTEB(20.0, 0.0, 20.0, 20.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      if (!((currentUserDocument?.gender == Gender.Male) &&
                          (_model.textController.text.length <
                              getRemoteConfigInt('bio_bio_min_chars_m'))))
                        Builder(
                          builder: (context) => Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 10.0, 0.0, 0.0),
                            child: AuthUserStreamWidget(
                              builder: (context) => InkWell(
                                splashColor: Colors.transparent,
                                focusColor: Colors.transparent,
                                hoverColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                onTap: () async {
                                  // Set bio to public profile


                                },
                                child: wrapWithModel(
                                  model: _model.gradientButtonModel,
                                  updateCallback: () => () {},
                                  child: GradientButtonWidget(
                                    title: 'Next',
                                    action: () async {
                                      try {
                                        if (mounted) {
                                            _model.textFieldFocusNode?.unfocus();
                                            await Future.delayed(const Duration(milliseconds: 50));
                                            }
                                      } catch(e) {}
                                      var shouldSetState = false;
                                      try {
                                        final result =
                                            await FirebaseFunctions.instanceFor(
                                                    region: 'europe-west1')
                                                .httpsCallable('saveSignUpData')
                                                .call({
                                          "screenName": 'bio',
                                          "multiSelections": ([]).toList(),
                                          "singleSelection":
                                              _model.textController.text,
                                          "hidden": false,
                                          "signUpFinished":
                                              valueOrDefault<bool>(
                                                  currentUserDocument
                                                      ?.signUpFinished,
                                                  false),
                                        });
                                        _model.saveSignUpDataCf =
                                            SaveSignUpDataCloudFunctionCallResponse(
                                          data: result.data,
                                          succeeded: true,
                                          resultAsString:
                                              result.data.toString(),
                                          jsonBody: result.data,
                                        );
                                      } on FirebaseFunctionsException catch (error) {
                                        _model.saveSignUpDataCf =
                                            SaveSignUpDataCloudFunctionCallResponse(
                                          errorCode: error.code,
                                          succeeded: false,
                                        );
                                      }

                                      shouldSetState = true;
                                      if (_model.saveSignUpDataCf!.succeeded!) {
                                        if ((String response) {
                                          return response.contains("rejected");
                                        }(_model.saveSignUpDataCf!.data!)) {
                                          await showDialog(
                                            context: context,
                                            builder: (dialogContext) {
                                              return Dialog(
                                                elevation: 0,
                                                insetPadding: EdgeInsets.zero,
                                                backgroundColor:
                                                    Colors.transparent,
                                                alignment: const AlignmentDirectional(
                                                        0.0, 0.0)
                                                    .resolve(Directionality.of(
                                                        context)),
                                                child: GeneralPopupWidget(
                                                  alertText: functions
                                                      .getFlexiPopupFromJson(
                                                          _model
                                                              .saveSignUpDataCf!
                                                              .data!)
                                                      .content,
                                                  alertTitle: functions
                                                      .getFlexiPopupFromJson(
                                                          _model
                                                              .saveSignUpDataCf!
                                                              .data!)
                                                      .title,
                                                ),
                                              );
                                            },
                                          );
                                        } else {
                                          await widget.nextCallback?.call();
                                        }
                                      } else {
                                        await showDialog(
                                          context: context,
                                          builder: (dialogContext) {
                                            return Dialog(
                                              elevation: 0,
                                              insetPadding: EdgeInsets.zero,
                                              backgroundColor:
                                                  Colors.transparent,
                                              alignment:
                                                  const AlignmentDirectional(0.0, 0.0)
                                                      .resolve(
                                                          Directionality.of(
                                                              context)),
                                              child: const GeneralPopupWidget(
                                                alertText:
                                                    'Something went wrong',
                                                alertTitle:
                                                    'Please try again <NAME_EMAIL>',
                                              ),
                                            );
                                          },
                                        );

                                        if (shouldSetState) {
                                          safeSetState(() {});
                                        }
                                        return;
                                      }

                                      if (shouldSetState) safeSetState(() {});
                                    },
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      if ((currentUserDocument?.gender == Gender.Male) &&
                          (_model.textController.text.length <
                              getRemoteConfigInt('bio_bio_min_chars_m')))
                        Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              0.0, 10.0, 0.0, 0.0),
                          child: AuthUserStreamWidget(
                            builder: (context) => wrapWithModel(
                              model: _model.gradientButtonDisabledModel,
                              updateCallback: () => safeSetState(() {}),
                              child: GradientButtonDisabledWidget(
                                title: 'Next',
                                action: () async {},
                              ),
                            ),
                          ),
                        ),
                      if (!(_model.textFieldFocusNode?.hasFocus ?? false) &&
                          (currentUserDocument?.gender == Gender.Female))
                        Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              0.0, 10.0, 0.0, 0.0),
                          child: AuthUserStreamWidget(
                            builder: (context) => FFButtonWidget(
                              onPressed: () async {
                                try {
                                analytics.logEvent('Sign Up: Entered Bio Text Do Later', eventProperties: {'Length in Chars': _model.textController.text.length});
                                } catch(e) {}
                                await widget.doLaterCallback?.call();
                              },
                              text: getRemoteConfigString(
                                  'bio_prompts_do_later_button'),
                              options: FFButtonOptions(
                                width: MediaQuery.sizeOf(context).width * 1.0,
                                height: 50.0,
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    24.0, 0.0, 24.0, 0.0),
                                iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 0.0, 0.0, 0.0),
                                color: Colors.white,
                                textStyle: FlutterFlowTheme.of(context)
                                    .titleSmall
                                    .override(
                                      fontFamily: 'BT Beau Sans',
                                      color: FlutterFlowTheme.of(context)
                                          .secondaryText,
                                      letterSpacing: 0.0,
                                      useGoogleFonts: false,
                                    ),
                                elevation: 0.0,
                                borderSide: const BorderSide(
                                  color: Colors.transparent,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(100.0),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }
}
