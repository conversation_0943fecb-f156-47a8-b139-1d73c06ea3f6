import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/gradient_button_disabled/gradient_button_disabled_widget.dart';
import '/general/lightbulb_tip/lightbulb_tip_widget.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'bio_prompts_component_model.dart';
export 'bio_prompts_component_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';
import '/advanced_filters_editors/prompts/prompts_tile_new/prompts_tile_new_widget.dart';

class BioPromptsComponentWidget extends StatefulWidget {
  const BioPromptsComponentWidget({
    super.key,
    required this.nextCallback,
    required this.backCallback,
    required this.bioSwitchCallback,
    required this.doLaterCallback,
  });

  final Future Function()? nextCallback;
  final Future Function()? backCallback;
  final Future Function()? bioSwitchCallback;
  final Future Function()? doLaterCallback;

  @override
  State<BioPromptsComponentWidget> createState() =>
      _BioPromptsComponentWidgetState();
}

class _BioPromptsComponentWidgetState extends State<BioPromptsComponentWidget> {
  late BioPromptsComponentModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => BioPromptsComponentModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<PublicProfileRecord>>(
      stream: queryPublicProfileRecord(
        parent: currentUserReference,
        singleRecord: true,
      ),
      builder: (context, snapshot) {
        // Customize what your widget looks like when it's loading.
        if (!snapshot.hasData) {
          return Center(
            child: SizedBox(
              width: 50.0,
              height: 50.0,
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  FlutterFlowTheme.of(context).accent2,
                ),
              ),
            ),
          );
        }
        List<PublicProfileRecord> columnPublicProfileRecordList =
            snapshot.data!;
        // Return an empty Container when the item does not exist.
        if (snapshot.data!.isEmpty) {
          return Container();
        }
        final columnPublicProfileRecord =
            columnPublicProfileRecordList.isNotEmpty
                ? columnPublicProfileRecordList.first
                : null;

        return Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
              child: Container(
                decoration: const BoxDecoration(),
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Padding(
                        padding: const EdgeInsetsDirectional.fromSTEB(
                            10.0, 10.0, 0.0, 0.0),
                        child: Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Align(
                              alignment: const AlignmentDirectional(-1.0, 0.0),
                              child: FlutterFlowIconButton(
                                borderColor: const Color(0x00FFFFFF),
                                borderRadius: 20.0,
                                borderWidth: 1.0,
                                buttonSize: 40.0,
                                fillColor: const Color(0x00FFFFFF),
                                icon: FaIcon(
                                  FontAwesomeIcons.angleLeft,
                                  color:
                                      FlutterFlowTheme.of(context).primaryText,
                                  size: 24.0,
                                ),
                                onPressed: () async {
                                  await widget.backCallback?.call();
                                },
                              ),
                            ),
                            Align(
                              alignment: const AlignmentDirectional(1.0, 0.0),
                              child: FFButtonWidget(
                                onPressed: () async {
                                  try {
                                  analytics.logEvent('Sign Up: Went To Bio Text from Bio Prompts');
                                  } catch(e) {}
                                  await widget.bioSwitchCallback?.call();
                                },
                                text: getRemoteConfigString(
                                    'bio_prompts_bio_instead'),
                                options: FFButtonOptions(
                                  height: 40.0,
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      24.0, 0.0, 24.0, 0.0),
                                  iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                      0.0, 0.0, 0.0, 0.0),
                                  color: Colors.white,
                                  textStyle: FlutterFlowTheme.of(context)
                                      .titleSmall
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        color: FlutterFlowTheme.of(context)
                                            .secondaryText,
                                        fontSize: 14.0,
                                        letterSpacing: 0.0,
                                        useGoogleFonts: false,
                                      ),
                                  elevation: 0.0,
                                  borderSide: const BorderSide(
                                    color: Colors.transparent,
                                    width: 1.0,
                                  ),
                                  borderRadius: BorderRadius.circular(8.0),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Align(
                        alignment: const AlignmentDirectional(-1.0, -1.0),
                        child: Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              20.0, 20.0, 20.0, 16.0),
                          child: Text(
                            getRemoteConfigString('bio_prompts_headline'),
                            style: FlutterFlowTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'BT Beau Sans',
                                  fontSize: 32.0,
                                  letterSpacing: 0.0,
                                  fontWeight: FontWeight.bold,
                                  useGoogleFonts: false,
                                  lineHeight: 1.29,
                                ),
                          ),
                        ),
                      ),
                      Form(
                        key: _model.formKey,
                        autovalidateMode: AutovalidateMode.disabled,
                        child: Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              6.0, 11.0, 6.0, 0.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              Align(
                                alignment: const AlignmentDirectional(-1.0, -1.0),
                                child: Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      14.0, 0.0, 0.0, 12.0),
                                  child: Text(
                                    getRemoteConfigString(
                                        'bio_prompts_description'),
                                    style: FlutterFlowTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'BT Beau Sans',
                                          fontSize: 16.0,
                                          letterSpacing: 0.0,
                                          fontWeight: FontWeight.w500,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    6.0, 20.0, 6.0, 0.0),
                                child: Column(
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    
                                    Builder(
                                      builder: (context) {
                                        final promptsList = [0, 1, 2];
                                        return Padding(
                                                              padding: const EdgeInsets.fromLTRB(0, 0, 0, 20),
                                                              child: ListView.separated(
                                                                padding: EdgeInsets.zero,
                                                                primary: false,
                                                                shrinkWrap: true,
                                                                scrollDirection: Axis.vertical,
                                                                itemCount: promptsList.length,
                                                                separatorBuilder: (_, __) => const SizedBox(height: 20.0),
                                                                itemBuilder: (context, promptsListIndex) {
                                                                  final promptsListItem = promptsList[promptsListIndex];
                                                                  return PromptsTileNewWidget(
                                                                    key: Key(
                                                                        'Keytdm_${promptsListIndex}_of_${promptsList.length}'),
                                                                    exists: valueOrDefault(columnPublicProfileRecord?.prompts.length, 0) >
                                                                        promptsListIndex ?? false,
                                                                    title: getRemoteConfigString(
                                                                          'prompt_editor_title'),
                                                                    name: 'prompts$promptsListIndex',
                                                                    promptObject: columnPublicProfileRecord?.prompts
                                                                        .elementAtOrNull(promptsListIndex) ?? PromptAndAnswerStruct(),
                                                                    config: functions
                                                                          .getSignupConfigFromRemoteConfigString(
                                                                              'prompts'),
                                                                    realIndex: (((columnPublicProfileRecord?.prompts?.length ?? 0) >= promptsListIndex + 1) ? promptsListIndex : columnPublicProfileRecord?.prompts.length ?? 2),
                                                                  );
                                                                },
                                                              ),
                                                            );
                                      }
                                    )
                                    // Column(
                                    //   mainAxisSize: MainAxisSize.max,
                                    //   children: [
                                    //     Padding(
                                    //       padding:
                                    //           EdgeInsetsDirectional.fromSTEB(
                                    //               0.0, 0.0, 0.0, 19.0),
                                    //       child: Column(
                                    //         mainAxisSize: MainAxisSize.max,
                                    //         children: [
                                    //           if ((columnPublicProfileRecord
                                    //                       ?.hasPrompts() ==
                                    //                   false) ||
                                    //               (columnPublicProfileRecord!
                                    //                       .prompts.length <=
                                    //                   0))
                                    //             wrapWithModel(
                                    //               model: _model.promptNEModel1,
                                    //               updateCallback: () =>
                                    //                   safeSetState(() {}),
                                    //               child: PromptNEWidget(),
                                    //             ),
                                    //           if ((columnPublicProfileRecord
                                    //                       ?.hasPrompts() ==
                                    //                   true) &&
                                    //               (columnPublicProfileRecord!
                                    //                       .prompts.length >=
                                    //                   1))
                                    //             wrapWithModel(
                                    //               model: _model
                                    //                   .promptFilledInModel1,
                                    //               updateCallback: () =>
                                    //                   safeSetState(() {}),
                                    //               updateOnChange: true,
                                    //               child: PromptFilledInWidget(
                                    //                 promptNo: 1,
                                    //                 publicProfile:
                                    //                     columnPublicProfileRecord!,
                                    //               ),
                                    //             ),
                                    //         ],
                                    //       ),
                                    //     ),
                                    //     Padding(
                                    //       padding:
                                    //           EdgeInsetsDirectional.fromSTEB(
                                    //               0.0, 0.0, 0.0, 19.0),
                                    //       child: Column(
                                    //         mainAxisSize: MainAxisSize.max,
                                    //         children: [
                                    //           if ((columnPublicProfileRecord
                                    //                       ?.hasPrompts() ==
                                    //                   false) ||
                                    //               (columnPublicProfileRecord!
                                    //                       .prompts.length <=
                                    //                   1))
                                    //             wrapWithModel(
                                    //               model: _model.promptNEModel2,
                                    //               updateCallback: () =>
                                    //                   safeSetState(() {}),
                                    //               child: PromptNEWidget(),
                                    //             ),
                                    //           if ((columnPublicProfileRecord
                                    //                       ?.hasPrompts() ==
                                    //                   true) &&
                                    //               (columnPublicProfileRecord!
                                    //                       .prompts.length >=
                                    //                   2))
                                    //             wrapWithModel(
                                    //               model: _model
                                    //                   .promptFilledInModel2,
                                    //               updateCallback: () =>
                                    //                   safeSetState(() {}),
                                    //               updateOnChange: true,
                                    //               child: PromptFilledInWidget(
                                    //                 promptNo: 2,
                                    //                 publicProfile:
                                    //                     columnPublicProfileRecord!,
                                    //               ),
                                    //             ),
                                    //         ],
                                    //       ),
                                    //     ),
                                    //     Padding(
                                    //       padding:
                                    //           EdgeInsetsDirectional.fromSTEB(
                                    //               0.0, 0.0, 0.0, 19.0),
                                    //       child: Column(
                                    //         mainAxisSize: MainAxisSize.max,
                                    //         children: [
                                    //           if ((columnPublicProfileRecord
                                    //                       ?.hasPrompts() ==
                                    //                   false) ||
                                    //               (columnPublicProfileRecord!
                                    //                       .prompts.length <=
                                    //                   2))
                                    //             wrapWithModel(
                                    //               model: _model.promptNEModel3,
                                    //               updateCallback: () =>
                                    //                   safeSetState(() {}),
                                    //               child: PromptNEWidget(),
                                    //             ),
                                    //           if ((columnPublicProfileRecord
                                    //                       ?.hasPrompts() ==
                                    //                   true) &&
                                    //               (columnPublicProfileRecord!
                                    //                       .prompts.length >=
                                    //                   3))
                                    //             wrapWithModel(
                                    //               model: _model
                                    //                   .promptFilledInModel3,
                                    //               updateCallback: () =>
                                    //                   safeSetState(() {}),
                                    //               updateOnChange: true,
                                    //               child: PromptFilledInWidget(
                                    //                 promptNo: 3,
                                    //                 publicProfile:
                                    //                     columnPublicProfileRecord!,
                                    //               ),
                                    //             ),
                                    //         ],
                                    //       ),
                                    //     ),
                                    //   ],
                                    // ),
                                  
                                  ],
                                ),
                              ),
                              if (currentUserDocument?.gender == Gender.Male)
                                Align(
                                  alignment: const AlignmentDirectional(-1.0, -1.0),
                                  child: Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        14.0, 0.0, 0.0, 12.0),
                                    child: AuthUserStreamWidget(
                                      builder: (context) => Text(
                                        getRemoteConfigString(
                                            'bio_prompts_min_prompts_info'),
                                        style: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .override(
                                              fontFamily: 'BT Beau Sans',
                                              color:
                                                  FlutterFlowTheme.of(context)
                                                      .secondaryText,
                                              fontSize: 13.0,
                                              letterSpacing: 0.0,
                                              fontWeight: FontWeight.w500,
                                              useGoogleFonts: false,
                                            ),
                                      ),
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(20.0, 0.0, 20.0, 20.0),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  Align(
                    alignment: const AlignmentDirectional(0.0, 0.0),
                    child: Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(0.0, 20.0, 0.0, 23.0),
                      child: wrapWithModel(
                        model: _model.lightbulbTipModel,
                        updateCallback: () => safeSetState(() {}),
                        child: LightbulbTipWidget(
                          tip: getRemoteConfigString('bio_prompts_tip'),
                        ),
                      ),
                    ),
                  ),
                  if (!((currentUserDocument?.gender == Gender.Male) &&
                      (columnPublicProfileRecord!.prompts.length <
                          getRemoteConfigInt('bio_prompts_min_prompts_m'))))
                    Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
                      child: AuthUserStreamWidget(
                        builder: (context) => wrapWithModel(
                          model: _model.gradientButtonModel,
                          updateCallback: () => safeSetState(() {}),
                          child: GradientButtonWidget(
                            title: 'Next',
                            action: () async {
                              try {
                              analytics.setUserProperties({"Prompts": columnPublicProfileRecord?.prompts.length ?? 0});
                                  analytics.logEvent('Sign Up: Entered Bio', eventProperties: {'Prompts Added Count': columnPublicProfileRecord?.prompts.length ?? 0});
                                  analytics.logEvent('Sign Up: Entered Bio Prompts', eventProperties: {'Prompts Added Count': columnPublicProfileRecord?.prompts.length ?? 0});
                              } catch(e) {}
                              await widget.nextCallback?.call();
                            },
                          ),
                        ),
                      ),
                    ),
                  if ((currentUserDocument?.gender == Gender.Male) &&
                      (columnPublicProfileRecord!.prompts.length <
                          getRemoteConfigInt('bio_prompts_min_prompts_m')))
                    Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
                      child: AuthUserStreamWidget(
                        builder: (context) => wrapWithModel(
                          model: _model.gradientButtonDisabledModel,
                          updateCallback: () => safeSetState(() {}),
                          child: GradientButtonDisabledWidget(
                            title: 'Next',
                            action: () async {},
                          ),
                        ),
                      ),
                    ),
                  if (currentUserDocument?.gender == Gender.Female)
                    Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
                      child: AuthUserStreamWidget(
                        builder: (context) => FFButtonWidget(
                          onPressed: () async {
                            try {
                            analytics.logEvent('Sign Up: Entered Bio Prompts Do Later', eventProperties: {'Prompts Added Count': columnPublicProfileRecord?.prompts.length ?? 0});
                            } catch(e) {}
                            await widget.doLaterCallback?.call();
                          },
                          text: getRemoteConfigString(
                              'bio_prompts_do_later_button'),
                          options: FFButtonOptions(
                            width: MediaQuery.sizeOf(context).width * 1.0,
                            height: 50.0,
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                24.0, 0.0, 24.0, 0.0),
                            iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 0.0, 0.0, 0.0),
                            color: Colors.white,
                            textStyle: FlutterFlowTheme.of(context)
                                .titleSmall
                                .override(
                                  fontFamily: 'BT Beau Sans',
                                  color: FlutterFlowTheme.of(context)
                                      .secondaryText,
                                  letterSpacing: 0.0,
                                  useGoogleFonts: false,
                                ),
                            elevation: 0.0,
                            borderSide: const BorderSide(
                              color: Colors.transparent,
                              width: 1.0,
                            ),
                            borderRadius: BorderRadius.circular(100.0),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
