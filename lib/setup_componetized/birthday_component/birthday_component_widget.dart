import 'package:chyrpe/components/birthday_form_field_widget.dart';

import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/backend/schema/enums/enums.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/gradient_button_disabled/gradient_button_disabled_widget.dart';
import '/general/info_sheet_scrollable/info_sheet_scrollable_widget.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'birthday_component_model.dart';
export 'birthday_component_model.dart';

class BirthdayComponentWidget extends StatefulWidget {
  const BirthdayComponentWidget({
    super.key,
    required this.nextCallback,
    required this.backCallback,
  });

  final Future Function()? nextCallback;
  final Future Function()? backCallback;

  @override
  State<BirthdayComponentWidget> createState() =>
      _BirthdayComponentWidgetState();
}

class _BirthdayComponentWidgetState extends State<BirthdayComponentWidget> {
  late BirthdayComponentModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => BirthdayComponentModel());

    _model.textController1 ??= TextEditingController();
    _model.textFieldFocusNode1 ??= FocusNode();

    _model.textController2 ??= TextEditingController();
    _model.textFieldFocusNode2 ??= FocusNode();

    _model.textController3 ??= TextEditingController();
    _model.textFieldFocusNode3 ??= FocusNode();
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    String locale = ui.PlatformDispatcher.instance.locale.toString();
    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(20.0, 0.0, 20.0, 0.0),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(0.0, 20.0, 0.0, 0.0),
            child: FlutterFlowIconButton(
              borderColor: Colors.transparent,
              borderRadius: 20.0,
              borderWidth: 1.0,
              buttonSize: 40.0,
              icon: FaIcon(
                FontAwesomeIcons.angleLeft,
                color: FlutterFlowTheme.of(context).primaryText,
                size: 24.0,
              ),
              onPressed: () async {
                await widget.backCallback?.call();
              },
            ),
          ),
          Flexible(
            child: Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(20.0, 0.0, 20.0, 0.0),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding:
                        const EdgeInsetsDirectional.fromSTEB(0.0, 15.0, 0.0, 0.0),
                    child: AuthUserStreamWidget(
                      builder: (context) => Text(
                        '${valueOrDefault(currentUserDocument?.name, '')}, what\'s your birthday?',
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'BT Beau Sans',
                              fontSize: 32.0,
                              letterSpacing: 0.0,
                              fontWeight: FontWeight.bold,
                              useGoogleFonts: false,
                              lineHeight: 1.29,
                            ),
                      ),
                    ),
                  ),
                  SizedBox(
                    width: MediaQuery.sizeOf(context).width * 1.0,
                    child: Form(
                      key: _model.formKey,
                      autovalidateMode: AutovalidateMode.disabled,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 78.0, 0.0, 0.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                if (locale == 'en_US')
                                  BirthdayFormFieldWidget(
                                    controller: _model.textController2,
                                    focusNode: _model.textFieldFocusNode2,
                                    debounceTag: '_model.textController2',
                                    inputFormatters: [],
                                    label: 'MONTH',
                                    hintText: 'MM',
                                    validator: _model.textController2Validator
                                        .asValidator(context),
                                  )
                                else
                                  BirthdayFormFieldWidget(
                                    controller: _model.textController1,
                                    focusNode: _model.textFieldFocusNode1,
                                    debounceTag: '_model.textController1',
                                    validator: _model.textController1Validator
                                        .asValidator(context),
                                    inputFormatters: [_model.textFieldMask1],
                                    label: 'DAY',
                                    hintText: 'DD',
                                  ),
                                Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      7.0, 0.0, 7.0, 15.0),
                                  child: Text(
                                    '/',
                                    style: FlutterFlowTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'BT Beau Sans',
                                          color: const Color(0xFF747E90),
                                          fontSize: 20.0,
                                          letterSpacing: 0.0,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ),
                                if(locale == 'en_US')
                                  BirthdayFormFieldWidget(
                                    controller: _model.textController1,
                                    focusNode: _model.textFieldFocusNode1,
                                    debounceTag: '_model.textController1',
                                    validator: _model.textController1Validator
                                        .asValidator(context),
                                    inputFormatters: [_model.textFieldMask1],
                                    label: 'DAY',
                                    hintText: 'DD',
                                  )
                                else
                                  BirthdayFormFieldWidget(
                                  controller: _model.textController2,
                                  focusNode: _model.textFieldFocusNode2,
                                  debounceTag: '_model.textController2',
                                  inputFormatters: [],
                                  label: 'MONTH',
                                  hintText: 'MM',
                                  validator: _model.textController2Validator
                                      .asValidator(context),
                                ),
                                Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      7.0, 0.0, 7.0, 15.0),
                                  child: Text(
                                    '/',
                                    style: FlutterFlowTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'BT Beau Sans',
                                          color: const Color(0xFF747E90),
                                          fontSize: 20.0,
                                          letterSpacing: 0.0,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ),
                                BirthdayFormFieldWidget(
                                  controller: _model.textController3,
                                  focusNode: _model.textFieldFocusNode3,
                                  debounceTag: '_model.textController3',
                                  inputFormatters: [
                                    _model.textFieldMask3
                                  ],
                                  label: 'YEAR',
                                  hintText: 'YYYY',
                                  validator: _model.textController3Validator
                                      .asValidator(context),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Padding(
                    padding:
                        const EdgeInsetsDirectional.fromSTEB(0.0, 15.0, 0.0, 0.0),
                    child: FFButtonWidget(
                      onPressed: () async {
                        await showModalBottomSheet(
                          isScrollControlled: true,
                          backgroundColor: Colors.transparent,
                          enableDrag: false,
                          context: context,
                          builder: (context) {
                            return Padding(
                              padding: MediaQuery.viewInsetsOf(context),
                              child: InfoSheetScrollableWidget(
                                title: getRemoteConfigString(
                                    'signup_birthday_sheet_title'),
                                body: getRemoteConfigString(
                                    'signup_birthday_info'),
                              ),
                            );
                          },
                        ).then((value) => safeSetState(() {}));
                      },
                      text: getRemoteConfigString(
                          'signup_birthday_ibutton_title'),
                      icon: Icon(
                        Icons.info_outlined,
                        color: FlutterFlowTheme.of(context).secondaryText,
                        size: 18.0,
                      ),
                      options: FFButtonOptions(
                        width: MediaQuery.sizeOf(context).width * 1.0,
                        height: 40.0,
                        padding: const EdgeInsetsDirectional.fromSTEB(
                            15.0, 0.0, 15.0, 0.0),
                        iconPadding:
                            const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                        color: Colors.white,
                        textStyle: FlutterFlowTheme.of(context)
                            .titleSmall
                            .override(
                              fontFamily: 'BT Beau Sans',
                              color: FlutterFlowTheme.of(context).secondaryText,
                              fontSize: 4.0,
                              letterSpacing: 0.0,
                              fontWeight: FontWeight.w500,
                              useGoogleFonts: false,
                            ),
                        elevation: 0.0,
                        borderSide: const BorderSide(
                          color: Color(0x0057636C),
                          width: 0.0,
                        ),
                        borderRadius: BorderRadius.circular(100.0),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(20.0, 0.0, 20.0, 25.0),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                if ((String dd, String mm, String yyyy) {
                  return ((int.tryParse(dd) != null &&
                          int.parse(dd) >= 1 &&
                          int.parse(dd) <= 31) &&
                      (int.tryParse(mm) != null &&
                          int.parse(mm) >= 1 &&
                          int.parse(mm) <= 12) &&
                      (int.tryParse(yyyy) != null &&
                          int.parse(yyyy) >= 1900 &&
                          int.parse(yyyy) <= 2007));
                }(_model.textController1.text, _model.textController2.text,
                    _model.textController3.text))
                  Builder(
                    builder: (context) => wrapWithModel(
                      model: _model.gradientButtonModel,
                      updateCallback: () => safeSetState(() {}),
                      child: GradientButtonWidget(
                        title: 'Next',
                        action: () async {
                          var shouldSetState = false;
                          // Validate inputs
                          if (_model.formKey.currentState == null ||
                              !_model.formKey.currentState!.validate()) {
                            return;
                          }
                          try {
                            final result = await FirebaseFunctions.instanceFor(
                                    region: 'europe-west2')
                                .httpsCallable('saveAge')
                                .call({
                              "dd": _model.textController1.text,
                              "mm": _model.textController2.text,
                              "yyyy": _model.textController3.text,
                              "newStyleVerification": true,
                            });
                            _model.birthdayDoc =
                                SaveAgeCloudFunctionCallResponse(
                              data: BirthdayStruct.fromMap(result.data),
                              succeeded: true,
                              resultAsString: result.data.toString(),
                              jsonBody: result.data,
                            );
                          } on FirebaseFunctionsException catch (error) {
                            _model.birthdayDoc =
                                SaveAgeCloudFunctionCallResponse(
                              errorCode: error.code,
                              succeeded: false,
                            );
                          }

                          shouldSetState = true;
                          if (_model.birthdayDoc!.data!.valid) {
                            if (_model.birthdayDoc!.data!.underAge) {
                              await showDialog(
                                context: context,
                                builder: (dialogContext) {
                                  return Dialog(
                                    elevation: 0,
                                    insetPadding: EdgeInsets.zero,
                                    backgroundColor: Colors.transparent,
                                    alignment: const AlignmentDirectional(0.0, 0.0)
                                        .resolve(Directionality.of(context)),
                                    child: const GeneralPopupWidget(
                                      alertTitle: 'Not for minors',
                                      alertText:
                                          'To use Chyrpe, you have to be at least 18 years old.',
                                    ),
                                  );
                                },
                              );

                              if (shouldSetState) safeSetState(() {});
                              return;
                            } else {
                              await currentUserReference!
                                  .update(createUsersRecordData(
                                nextSignUpStage: SetUpStage.genderPrefs,
                              ));
                              _model.textFieldFocusNode1?.unfocus();
                              _model.textFieldFocusNode2?.unfocus();
                              _model.textFieldFocusNode3?.unfocus();
                              await widget.nextCallback?.call();
                            }

                            if (shouldSetState) safeSetState(() {});
                            return;
                          } else {
                            await showDialog(
                              context: context,
                              builder: (dialogContext) {
                                return Dialog(
                                  elevation: 0,
                                  insetPadding: EdgeInsets.zero,
                                  backgroundColor: Colors.transparent,
                                  alignment: const AlignmentDirectional(0.0, 0.0) 
                                      .resolve(Directionality.of(context)),
                                  child: const GeneralPopupWidget(
                                    alertTitle: 'Invalid date',
                                    alertText:
                                        'Please enter a valid birthdate.',
                                  ),
                                );
                              },
                            );

                            if (shouldSetState) safeSetState(() {});
                            return;
                          }

                          if (shouldSetState) safeSetState(() {});
                        },
                      ),
                    ),
                  ),
                if ((String dd, String mm, String yyyy) {
                  return !((int.tryParse(dd) != null &&
                          int.parse(dd) >= 1 &&
                          int.parse(dd) <= 31) &&
                      (int.tryParse(mm) != null &&
                          int.parse(mm) >= 1 &&
                          int.parse(mm) <= 12) &&
                      (int.tryParse(yyyy) != null &&
                          int.parse(yyyy) >= 1900 &&
                          int.parse(yyyy) <= 2007));
                }(_model.textController1.text, _model.textController2.text,
                    _model.textController3.text))
                  wrapWithModel(
                    model: _model.gradientButtonDisabledModel,
                    updateCallback: () => safeSetState(() {}),
                    child: GradientButtonDisabledWidget(
                      title: 'Next',
                      action: () async {},
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
