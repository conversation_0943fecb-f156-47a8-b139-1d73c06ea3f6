import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/gradient_button_disabled/gradient_button_disabled_widget.dart';
import '/general/info_sheet_scrollable/info_sheet_scrollable_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'experience_level_component_model.dart';
export 'experience_level_component_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';

class ExperienceLevelComponentWidget extends StatefulWidget {
  const ExperienceLevelComponentWidget({
    super.key,
    required this.nextCallback,
    required this.backCallback,
  });

  final Future Function()? nextCallback;
  final Future Function()? backCallback;

  @override
  State<ExperienceLevelComponentWidget> createState() =>
      _ExperienceLevelComponentWidgetState();
}

class _ExperienceLevelComponentWidgetState
    extends State<ExperienceLevelComponentWidget> {
  late ExperienceLevelComponentModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => ExperienceLevelComponentModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(15.0, 0.0, 15.0, 0.0),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(0.0, 20.0, 0.0, 0.0),
            child: FlutterFlowIconButton(
              borderColor: Colors.transparent,
              borderRadius: 20.0,
              borderWidth: 1.0,
              buttonSize: 40.0,
              icon: FaIcon(
                FontAwesomeIcons.angleLeft,
                color: FlutterFlowTheme.of(context).primaryText,
                size: 24.0,
              ),
              onPressed: () async {
                await widget.backCallback?.call();
              },
            ),
          ),
          Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(8.0, 0.0, 8.0, 32.0),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding:
                        const EdgeInsetsDirectional.fromSTEB(0.0, 20.0, 0.0, 25.0),
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'What is your level\nof experience?',
                          style:
                              FlutterFlowTheme.of(context).bodyMedium.override(
                                    fontFamily: 'BT Beau Sans',
                                    fontSize: 32.0,
                                    letterSpacing: 0.0,
                                    fontWeight: FontWeight.bold,
                                    useGoogleFonts: false,
                                    lineHeight: 1.29,
                                  ),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding:
                        const EdgeInsetsDirectional.fromSTEB(0.0, 25.0, 0.0, 0.0),
                    child: FutureBuilder<List<OptionsForSelectorsRecord>>(
                      future: queryOptionsForSelectorsRecordOnce(
                        queryBuilder: (optionsForSelectorsRecord) =>
                            optionsForSelectorsRecord.where(
                          'name',
                          isEqualTo: 'Experience Levels',
                        ),
                        singleRecord: true,
                      ),
                      builder: (context, snapshot) {
                        // Customize what your widget looks like when it's loading.
                        if (!snapshot.hasData) {
                          return Center(
                            child: SizedBox(
                              width: 40.0,
                              height: 40.0,
                              child: SpinKitCircle(
                                color:
                                    FlutterFlowTheme.of(context).secondaryText,
                                size: 40.0,
                              ),
                            ),
                          );
                        }
                        List<OptionsForSelectorsRecord>
                            columnOptionsForSelectorsRecordList =
                            snapshot.data!;
                        // Return an empty Container when the item does not exist.
                        if (snapshot.data!.isEmpty) {
                          return Container();
                        }
                        final columnOptionsForSelectorsRecord =
                            columnOptionsForSelectorsRecordList.isNotEmpty
                                ? columnOptionsForSelectorsRecordList.first
                                : null;

                        return Builder(
                          builder: (context) {
                            final options = columnOptionsForSelectorsRecord
                                    ?.options
                                    .toList() ??
                                [];

                            return SingleChildScrollView(
                              child: Column(
                                mainAxisSize: MainAxisSize.max,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: List.generate(options.length,
                                    (optionsIndex) {
                                  final optionsItem = options[optionsIndex];
                                  return Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        0.0, 0.0, 0.0, 21.0),
                                    child: InkWell(
                                      splashColor: Colors.transparent,
                                      focusColor: Colors.transparent,
                                      hoverColor: Colors.transparent,
                                      highlightColor: Colors.transparent,
                                      onTap: () async {
                                        _model.selectedExperienceLevel =
                                            optionsItem;
                                        safeSetState(() {});
                                      },
                                      child: Material(
                                        color: Colors.transparent,
                                        elevation: 0.0,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(100.0),
                                        ),
                                        child: Container(
                                          height: 52.0,
                                          constraints: const BoxConstraints(
                                            maxWidth: 400.0,
                                          ),
                                          decoration: BoxDecoration(
                                            color: FlutterFlowTheme.of(context)
                                                .primaryBackground,
                                            borderRadius:
                                                BorderRadius.circular(100.0),
                                            border: Border.all(
                                              color:
                                                  _model.selectedExperienceLevel ==
                                                          optionsItem
                                                      ? FlutterFlowTheme.of(
                                                              context)
                                                          .accent2
                                                      : FlutterFlowTheme.of(
                                                              context)
                                                          .secondaryText,
                                              width: 2.0,
                                            ),
                                          ),
                                          child: Align(
                                            alignment:
                                                const AlignmentDirectional(0.0, 0.0),
                                            child: Text(
                                              optionsItem,
                                              style: FlutterFlowTheme.of(
                                                      context)
                                                  .bodyMedium
                                                  .override(
                                                    fontFamily: 'BT Beau Sans',
                                                    color: FlutterFlowTheme.of(
                                                            context)
                                                        .secondaryText,
                                                    fontSize: 19.0,
                                                    letterSpacing: 0.0,
                                                    fontWeight: FontWeight.bold,
                                                    useGoogleFonts: false,
                                                  ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                }),
                              ),
                            );
                          },
                        );
                      },
                    ),
                  ),
                  Align(
                    alignment: const AlignmentDirectional(0.0, 0.0),
                    child: FFButtonWidget(
                      onPressed: () async {
                        await showModalBottomSheet(
                          isScrollControlled: true,
                          backgroundColor: Colors.transparent,
                          enableDrag: false,
                          context: context,
                          builder: (context) {
                            return Padding(
                              padding: MediaQuery.viewInsetsOf(context),
                              child: InfoSheetScrollableWidget(
                                title: getRemoteConfigString(
                                    'signup_experienceLevel_info_button_title'),
                                body: getRemoteConfigString(
                                    'signup_experienceLevel_info'),
                              ),
                            );
                          },
                        ).then((value) => safeSetState(() {}));
                      },
                      text: getRemoteConfigString(
                          'signup_experienceLevel_info_button_title'),
                      icon: Icon(
                        Icons.info_outlined,
                        color: FlutterFlowTheme.of(context).secondaryText,
                        size: 18.0,
                      ),
                      options: FFButtonOptions(
                        height: 40.0,
                        padding: const EdgeInsetsDirectional.fromSTEB(
                            15.0, 0.0, 15.0, 0.0),
                        iconPadding:
                            const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                        color: Colors.white,
                        textStyle: FlutterFlowTheme.of(context)
                            .titleSmall
                            .override(
                              fontFamily: 'BT Beau Sans',
                              color: FlutterFlowTheme.of(context).secondaryText,
                              fontSize: 4.0,
                              letterSpacing: 0.0,
                              fontWeight: FontWeight.w500,
                              useGoogleFonts: false,
                            ),
                        elevation: 0.0,
                        borderSide: const BorderSide(
                          color: Color(0x0057636C),
                          width: 0.0,
                        ),
                        borderRadius: BorderRadius.circular(100.0),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Flexible(
            child: Align(
              alignment: const AlignmentDirectional(0.0, 1.0),
              child: Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(8.0, 0.0, 8.0, 40.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    if (_model.selectedExperienceLevel != null &&
                        _model.selectedExperienceLevel != '')
                      Builder(
                        builder: (context) => wrapWithModel(
                          model: _model.gradientButtonModel,
                          updateCallback: () => safeSetState(() {}),
                          child: GradientButtonWidget(
                            title: 'Next',
                            action: () async {
                              if (_model.selectedExperienceLevel != null &&
                                  _model.selectedExperienceLevel != '') {
                                // Set role in user
                                analytics.logEvent('Sign Up: Entered Experience Level', eventProperties: {'Experience Level': _model.selectedExperienceLevel});
                                              analytics.setUserProperties({'Experience Level': _model.selectedExperienceLevel});

                                await currentUserReference!
                                    .update(createUsersRecordData(
                                  experienceLevel:
                                      _model.selectedExperienceLevel,
                                ));
                                // Depending on choice, set public role

                                await currentUserDocument!.publicProfile!
                                    .update(createPublicProfileRecordData(
                                  experienceLevel:
                                      _model.selectedExperienceLevel,
                                ));
                                await widget.nextCallback?.call();
                              } else {
                                await showDialog(
                                  context: context,
                                  builder: (dialogContext) {
                                    return Dialog(
                                      elevation: 0,
                                      insetPadding: EdgeInsets.zero,
                                      backgroundColor: Colors.transparent,
                                      alignment: const AlignmentDirectional(0.0, 0.0)
                                          .resolve(Directionality.of(context)),
                                      child: const GeneralPopupWidget(
                                        alertTitle: 'No option selected',
                                        alertText:
                                            'Please select an option to continue',
                                      ),
                                    );
                                  },
                                );
                              }
                            },
                          ),
                        ),
                      ),
                    if (_model.selectedExperienceLevel == null ||
                        _model.selectedExperienceLevel == '')
                      wrapWithModel(
                        model: _model.gradientButtonDisabledModel,
                        updateCallback: () => safeSetState(() {}),
                        child: GradientButtonDisabledWidget(
                          title: 'Next',
                          action: () async {},
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
