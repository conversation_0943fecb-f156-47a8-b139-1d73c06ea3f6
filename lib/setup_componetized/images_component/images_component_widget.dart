import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/gradient_button_disabled/gradient_button_disabled_widget.dart';
import '/general/info_sheet_scrollable/info_sheet_scrollable_widget.dart';
import '/general/lightbulb_tip/lightbulb_tip_widget.dart';
import '/general/picture_upload/picture_upload_widget.dart';
import '/general/picture_upload_n_e/picture_upload_n_e_widget.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'images_component_model.dart';
export 'images_component_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';
import 'package:reorderable_grid/reorderable_grid.dart';
import 'package:flutter/src/gestures/recognizer.dart';

class ImagesComponentWidget extends StatefulWidget {
  const ImagesComponentWidget({
    super.key,
    required this.backCallback,
    required this.nextCallback,
  });

  final Future Function()? backCallback;
  final Future Function()? nextCallback;

  @override
  State<ImagesComponentWidget> createState() => _ImagesComponentWidgetState();
}

class _ImagesComponentWidgetState extends State<ImagesComponentWidget> {
  late ImagesComponentModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => ImagesComponentModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  bool Function(int) _dragEnable(record) {
  return (int index) {
    return record.nPictures.length - 1 >= index;
  };
}

  Future<void> moveElementInArray({
  required PublicProfileRecord ppRecord,
  required int oldIndex,
  required int newIndex,
}) async {

  // Step 2: Get the array from the document
  final List<dynamic> array = ppRecord.nPictures;

  if (oldIndex < 0 || oldIndex >= array.length || newIndex < 0 || newIndex >= array.length) {
    throw Exception("Invalid indices for the array");
  }

  // Step 3: Move the element in memory
  final element = array.removeAt(oldIndex);
  array.insert(newIndex, element);

  // Step 4: Update the document with the modified array
  updateNPicturesArray(array) async {
    await currentUserDocument?.publicProfile?.update({
                                  ...mapToFirestore(
                                    {
                                      'nPictures': array
                                    },
                                  ),
                                });
  }

  updatePictureAvailability(index) async{
     FirebaseFirestore.instance.runTransaction((transaction) async {
                          // Fetch the public profile document
                          var queryPuPro = await transaction.get(currentUserDocument!.publicProfile!); 

                          List<dynamic>? npictures;
                          
                          // Safely attempt to retrieve nPictures field
                          try {
                            npictures = List.from(queryPuPro['nPictures'] ?? []); // If null, default to an empty list
                          } catch (e) {
                            npictures = []; // If any error occurs, initialize as an empty list
                          }
                          

                          var firstPic = await ImagesRecord.getDocumentOnce(
                                npictures[0]);

                          var currPic = await ImagesRecord.getDocumentOnce(
                                npictures[index]);

                          // Update the document with the new list
                          transaction.update(currentUserDocument!.publicProfile!, {"profilePhoto": firstPic.url});
                          transaction.update(firstPic.reference, {"availableToAll": true});
                          transaction.update(currPic.reference, {"availableToAll": true});
                        });
  }

  updateNPicturesArray(array);

  if (newIndex <= 2) {
    updatePictureAvailability(newIndex);
  }
}


  @override
  Widget build(BuildContext context) {
    return AuthUserStreamWidget(
      builder: (context) => StreamBuilder<PublicProfileRecord>(
        stream: PublicProfileRecord.getDocument(
            currentUserDocument!.publicProfile!),
        builder: (context, snapshot) {
          // Customize what your widget looks like when it's loading.
          if (!snapshot.hasData) {
            return Center(
              child: SizedBox(
                width: 50.0,
                height: 50.0,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    FlutterFlowTheme.of(context).accent2,
                  ),
                ),
              ),
            );
          }

          final columnPublicProfileRecord = snapshot.data!;

          return Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Stack(
                            children: [
                              Container(
                                width: MediaQuery.sizeOf(context).width * 1.0,
                                height:
                                    MediaQuery.sizeOf(context).height * 0.173,
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      FlutterFlowTheme.of(context)
                                          .primaryBackground,
                                      const Color(0x00FFFFFF)
                                    ],
                                    stops: const [0.9, 1.0],
                                    begin: const AlignmentDirectional(0.0, -1.0),
                                    end: const AlignmentDirectional(0, 1.0),
                                  ),
                                ),
                              ),
                              Column(
                                mainAxisSize: MainAxisSize.max,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        13.0, 70.0, 0.0, 0.0),
                                    child: Text(
                                      getRemoteConfigString('images_headline'),
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            fontFamily: 'BT Beau Sans',
                                            fontSize: 32.0,
                                            letterSpacing: 0.0,
                                            fontWeight: FontWeight.bold,
                                            useGoogleFonts: false,
                                            lineHeight: 1.29,
                                          ),
                                    ),
                                  ),
                                  Align(
                                    alignment: const AlignmentDirectional(-1.0, -1.0),
                                    child: Padding(
                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                          14.0, 14.0, 0.0, 11.0),
                                      child: Text(
                                        getRemoteConfigString(
                                            'images_description'),
                                        style: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .override(
                                              fontFamily: 'BT Beau Sans',
                                              fontSize: 16.0,
                                              letterSpacing: 0.0,
                                              fontWeight: FontWeight.w500,
                                              useGoogleFonts: false,
                                            ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 15.0, 0.0, 0.0),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    FlutterFlowIconButton(
                                      borderColor: const Color(0x004B39EF),
                                      borderRadius: 20.0,
                                      borderWidth: 1.0,
                                      buttonSize: 40.0,
                                      fillColor: const Color(0x004B39EF),
                                      icon: FaIcon(
                                        FontAwesomeIcons.angleLeft,
                                        color: FlutterFlowTheme.of(context)
                                            .primaryText,
                                        size: 24.0,
                                      ),
                                      onPressed: () async {
                                        await widget.backCallback?.call();
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      Padding(
                        padding: const EdgeInsetsDirectional.fromSTEB(
                            10.0, 36.0, 10.0, 0.0),
                        child: SingleChildScrollView(
                          primary: false,
                          child: Column(
                            mainAxisSize: MainAxisSize.max,
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                        SizedBox(
                              height: 260,
                              width: MediaQuery.of(context).size.width,
                              child: ReorderableGridView.builder(
                                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                                    crossAxisCount: 3,
                                    crossAxisSpacing: 10.0,
                                    mainAxisSpacing: 10.0,
                                    childAspectRatio: MediaQuery.of(context).size.width /
                                    (MediaQuery.of(context).size.height * 0.5),
                                  ), 
                                  onReorder:(oldIndex, newIndex) {
                                    print(oldIndex);
                                    print(newIndex);
                                    moveElementInArray(ppRecord: columnPublicProfileRecord, 
                                    oldIndex: oldIndex, newIndex: newIndex);
                                  },
                                  onReorderStart: (index) => print(index),
                                  primary: false,
                                  dragStartBehavior: DragStartBehavior.start,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemDragEnable: _dragEnable(columnPublicProfileRecord),
                                  proxyDecorator: (widget, index, animation) {
                                    return Material(
                             color: Colors.transparent,
                             child: widget
                             );
                                  },
                                  itemCount: 6,
                                  itemBuilder: (context, index) {
                                    if (columnPublicProfileRecord
                                                .nPictures.length - 1 >=
                                            index) {
                                          return Container(
                                            key: ValueKey(columnPublicProfileRecord
                                                .nPictures[index].id),
                                            child: wrapWithModel(
                                              model: PictureUploadModel(),
                                              updateCallback: () {
                                                safeSetState(() {});
                                              },
                                              child: Center(
                                                child: PictureUploadWidget(
                                                    signUp: true,
                                                    picturePosition: index + 1,
                                                    publicProfile:
                                                        columnPublicProfileRecord,
                                                ),
                                              ),
                                            ),
                                          );
                                            }
                                          return Container(
                                            key: ValueKey('fdadshj_$index'),
                                            child: wrapWithModel(
                                              model: PictureUploadNEModel(),
                                              updateCallback: () {
                                                  safeSetState(() {});
                                                },
                                              child: Container(
                                                child: Center(
                                                  child: PictureUploadNEWidget(
                                                      signUp: true,
                                                      picturePosition: index + 1,
                                                      publicProfile:
                                                          columnPublicProfileRecord,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          );
                                  },),
                            ),
                              // GridView(
                              //   padding: EdgeInsets.fromLTRB(
                              //     0,
                              //     0,
                              //     0,
                              //     35.0,
                              //   ),
                              //   gridDelegate:
                              //       SliverGridDelegateWithFixedCrossAxisCount(
                              //     crossAxisCount: 3,
                              //     crossAxisSpacing: 10.0,
                              //     mainAxisSpacing: 10.0,
                              //     childAspectRatio: 0.9,
                              //   ),
                              //   primary: false,
                              //   shrinkWrap: true,
                              //   scrollDirection: Axis.vertical,
                              //   children: [
                              //     Row(
                              //       mainAxisSize: MainAxisSize.max,
                              //       children: [
                              //         if (columnPublicProfileRecord
                              //                 .nPictures.length >=
                              //             1)
                              //           wrapWithModel(
                              //             model: _model.pictureUploadModel1,
                              //             updateCallback: () =>
                              //                 safeSetState(() {}),
                              //             updateOnChange: true,
                              //             child: PictureUploadWidget(
                              //               signUp: true,
                              //               picturePosition: 1,
                              //               publicProfile:
                              //                   columnPublicProfileRecord,
                              //             ),
                              //           ),
                              //         if (columnPublicProfileRecord
                              //                 .nPictures.length <
                              //             1)
                              //           wrapWithModel(
                              //             model: _model.pictureUploadNEModel1,
                              //             updateCallback: () =>
                              //                 safeSetState(() {}),
                              //             updateOnChange: true,
                              //             child: PictureUploadNEWidget(
                              //               signUp: true,
                              //               picturePosition: 1,
                              //               publicProfile:
                              //                   columnPublicProfileRecord,
                              //             ),
                              //           ),
                              //       ],
                              //     ),
                              //     Row(
                              //       mainAxisSize: MainAxisSize.max,
                              //       children: [
                              //         if (columnPublicProfileRecord
                              //                 .nPictures.length >=
                              //             2)
                              //           wrapWithModel(
                              //             model: _model.pictureUploadModel2,
                              //             updateCallback: () =>
                              //                 safeSetState(() {}),
                              //             updateOnChange: true,
                              //             child: PictureUploadWidget(
                              //               signUp: true,
                              //               picturePosition: 2,
                              //               publicProfile:
                              //                   columnPublicProfileRecord,
                              //             ),
                              //           ),
                              //         if (columnPublicProfileRecord
                              //                 .nPictures.length <
                              //             2)
                              //           wrapWithModel(
                              //             model: _model.pictureUploadNEModel2,
                              //             updateCallback: () =>
                              //                 safeSetState(() {}),
                              //             updateOnChange: true,
                              //             child: PictureUploadNEWidget(
                              //               signUp: true,
                              //               picturePosition: 2,
                              //               publicProfile:
                              //                   columnPublicProfileRecord,
                              //             ),
                              //           ),
                              //       ],
                              //     ),
                              //     Row(
                              //       mainAxisSize: MainAxisSize.max,
                              //       children: [
                              //         if (columnPublicProfileRecord
                              //                 .nPictures.length >=
                              //             3)
                              //           wrapWithModel(
                              //             model: _model.pictureUploadModel3,
                              //             updateCallback: () =>
                              //                 safeSetState(() {}),
                              //             updateOnChange: true,
                              //             child: PictureUploadWidget(
                              //               signUp: true,
                              //               picturePosition: 3,
                              //               publicProfile:
                              //                   columnPublicProfileRecord,
                              //             ),
                              //           ),
                              //         if (columnPublicProfileRecord
                              //                 .nPictures.length <
                              //             3)
                              //           wrapWithModel(
                              //             model: _model.pictureUploadNEModel3,
                              //             updateCallback: () =>
                              //                 safeSetState(() {}),
                              //             updateOnChange: true,
                              //             child: PictureUploadNEWidget(
                              //               signUp: true,
                              //               picturePosition: 3,
                              //               publicProfile:
                              //                   columnPublicProfileRecord,
                              //             ),
                              //           ),
                              //       ],
                              //     ),
                              //     Row(
                              //       mainAxisSize: MainAxisSize.max,
                              //       children: [
                              //         if (columnPublicProfileRecord
                              //                 .nPictures.length >=
                              //             4)
                              //           wrapWithModel(
                              //             model: _model.pictureUploadModel4,
                              //             updateCallback: () =>
                              //                 safeSetState(() {}),
                              //             updateOnChange: true,
                              //             child: PictureUploadWidget(
                              //               signUp: true,
                              //               picturePosition: 4,
                              //               publicProfile:
                              //                   columnPublicProfileRecord,
                              //             ),
                              //           ),
                              //         if (columnPublicProfileRecord
                              //                 .nPictures.length <
                              //             4)
                              //           wrapWithModel(
                              //             model: _model.pictureUploadNEModel4,
                              //             updateCallback: () =>
                              //                 safeSetState(() {}),
                              //             updateOnChange: true,
                              //             child: PictureUploadNEWidget(
                              //               signUp: true,
                              //               picturePosition: 4,
                              //               publicProfile:
                              //                   columnPublicProfileRecord,
                              //             ),
                              //           ),
                              //       ],
                              //     ),
                              //     Row(
                              //       mainAxisSize: MainAxisSize.max,
                              //       children: [
                              //         if (columnPublicProfileRecord
                              //                 .nPictures.length >=
                              //             5)
                              //           wrapWithModel(
                              //             model: _model.pictureUploadModel5,
                              //             updateCallback: () =>
                              //                 safeSetState(() {}),
                              //             updateOnChange: true,
                              //             child: PictureUploadWidget(
                              //               signUp: true,
                              //               picturePosition: 5,
                              //               publicProfile:
                              //                   columnPublicProfileRecord,
                              //             ),
                              //           ),
                              //         if (columnPublicProfileRecord
                              //                 .nPictures.length <
                              //             5)
                              //           wrapWithModel(
                              //             model: _model.pictureUploadNEModel5,
                              //             updateCallback: () =>
                              //                 safeSetState(() {}),
                              //             updateOnChange: true,
                              //             child: PictureUploadNEWidget(
                              //               signUp: true,
                              //               picturePosition: 5,
                              //               publicProfile:
                              //                   columnPublicProfileRecord,
                              //             ),
                              //           ),
                              //       ],
                              //     ),
                              //     Row(
                              //       mainAxisSize: MainAxisSize.max,
                              //       children: [
                              //         if (columnPublicProfileRecord
                              //                 .nPictures.length >=
                              //             6)
                              //           wrapWithModel(
                              //             model: _model.pictureUploadModel6,
                              //             updateCallback: () =>
                              //                 safeSetState(() {}),
                              //             updateOnChange: true,
                              //             child: PictureUploadWidget(
                              //               signUp: true,
                              //               picturePosition: 6,
                              //               publicProfile:
                              //                   columnPublicProfileRecord,
                              //             ),
                              //           ),
                              //         if (columnPublicProfileRecord
                              //                 .nPictures.length <
                              //             6)
                              //           wrapWithModel(
                              //             model: _model.pictureUploadNEModel6,
                              //             updateCallback: () =>
                              //                 safeSetState(() {}),
                              //             updateOnChange: true,
                              //             child: PictureUploadNEWidget(
                              //               signUp: true,
                              //               picturePosition: 6,
                              //               publicProfile:
                              //                   columnPublicProfileRecord,
                              //             ),
                              //           ),
                              //       ],
                              //     ),
                              //   ],
                              // ),
                              
                              FFButtonWidget(
                                onPressed: () async {
                                  await showModalBottomSheet(
                                    isScrollControlled: true,
                                    backgroundColor: Colors.transparent,
                                    enableDrag: false,
                                    context: context,
                                    builder: (context) {
                                      return Padding(
                                        padding:
                                            MediaQuery.viewInsetsOf(context),
                                        child: InfoSheetScrollableWidget(
                                          title: getRemoteConfigString(
                                              'signup_images_ibutton_title'),
                                          body: getRemoteConfigString(
                                              'signup_images_info'),
                                        ),
                                      );
                                    },
                                  ).then((value) => safeSetState(() {}));
                                },
                                text: getRemoteConfigString(
                                    'signup_images_ibutton_title'),
                                icon: Icon(
                                  Icons.info_outlined,
                                  color: FlutterFlowTheme.of(context)
                                      .secondaryText,
                                  size: 18.0,
                                ),
                                options: FFButtonOptions(
                                  width: MediaQuery.sizeOf(context).width * 1.0,
                                  height: 40.0,
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      15.0, 0.0, 15.0, 0.0),
                                  iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                      0.0, 0.0, 0.0, 0.0),
                                  color: Colors.white,
                                  textStyle: FlutterFlowTheme.of(context)
                                      .titleSmall
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        color: FlutterFlowTheme.of(context)
                                            .secondaryText,
                                        fontSize: 4.0,
                                        letterSpacing: 0.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                  elevation: 0.0,
                                  borderSide: const BorderSide(
                                    color: Color(0x0057636C),
                                    width: 0.0,
                                  ),
                                  borderRadius: BorderRadius.circular(100.0),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Column(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Padding(
                        padding: const EdgeInsetsDirectional.fromSTEB(
                            27.0, 0.0, 27.0, 33.0),
                        child: wrapWithModel(
                          model: _model.lightbulbTipModel,
                          updateCallback: () => safeSetState(() {}),
                          child: LightbulbTipWidget(
                            tip: getRemoteConfigString('images_tips'),
                          ),
                        ),
                      ),
                    ],
                  ),
                  Align(
                    alignment: const AlignmentDirectional(0.0, 1.0),
                    child: Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(27.0, 0.0, 27.0, 15.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          if (columnPublicProfileRecord.nPictures.length >= 3)
                            Align(
                              alignment: const AlignmentDirectional(0.0, 1.0),
                              child: Builder(
                                builder: (context) => wrapWithModel(
                                  model: _model.gradientButtonModel,
                                  updateCallback: () => safeSetState(() {}),
                                  child: GradientButtonWidget(
                                    title: 'Next',
                                    action: () async {
                                      if (columnPublicProfileRecord
                                              .nPictures.length >=
                                          3) {
                                        await currentUserReference!
                                            .update(createUsersRecordData(
                                          incognito: false,
                                          paused: false,
                                          lowerAgeReq: 18,
                                          upperAgeReq: 99,
                                          lowerHeightReq: 75,
                                          upperHeightReq: 250,
                                          distanceReq: 50.1,
                                        ));
                                        analytics.setUserProperties({'Pictures': columnPublicProfileRecord
                                                        .nPictures.length});
                                        analytics.logEvent('Sign Up: Uploaded Pictures', eventProperties: {'Pictures Added Count': columnPublicProfileRecord
                                                        .nPictures.length});
                                        await widget.nextCallback?.call();
                                      } else {
                                        await showDialog(
                                          context: context,
                                          builder: (dialogContext) {
                                            return Dialog(
                                              elevation: 0,
                                              insetPadding: EdgeInsets.zero,
                                              backgroundColor:
                                                  Colors.transparent,
                                              alignment:
                                                  const AlignmentDirectional(0.0, 0.0)
                                                      .resolve(
                                                          Directionality.of(
                                                              context)),
                                              child: const GeneralPopupWidget(
                                                alertTitle:
                                                    '3 images are required',
                                                alertText:
                                                    'Please upload at least 3 images before you continue',
                                              ),
                                            );
                                          },
                                        );

                                        return;
                                      }
                                    },
                                  ),
                                ),
                              ),
                            ),
                          if (columnPublicProfileRecord.nPictures.length < 3)
                            wrapWithModel(
                              model: _model.gradientButtonDisabledModel,
                              updateCallback: () => safeSetState(() {}),
                              child: GradientButtonDisabledWidget(
                                title: 'Next',
                                action: () async {},
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ],
          );
        },
      ),
    );
  }
}
