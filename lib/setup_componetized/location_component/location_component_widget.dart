import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/info_sheet_scrollable/info_sheet_scrollable_widget.dart';
import '/custom_code/actions/index.dart' as actions;
import '/flutter_flow/permissions_util.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'location_component_model.dart';
export 'location_component_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';

class LocationComponentWidget extends StatefulWidget {
  const LocationComponentWidget({
    super.key,
    required this.nextCallback,
    required this.backCallback,
  });

  final Future Function()? nextCallback;
  final Future Function()? backCallback;

  @override
  State<LocationComponentWidget> createState() =>
      _LocationComponentWidgetState();
}

class _LocationComponentWidgetState extends State<LocationComponentWidget> {
  late LocationComponentModel _model;

  LatLng? currentUserLocationValue;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => LocationComponentModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(25.0, 0.0, 25.0, 40.0),
      child: FutureBuilder<List<AKeyRecord>>(
        future: queryAKeyRecordOnce(
          queryBuilder: (aKeyRecord) => aKeyRecord.where(
            'name',
            isEqualTo: 'Here',
          ),
          singleRecord: true,
        ),
        builder: (context, snapshot) {
          // Customize what your widget looks like when it's loading.
          if (!snapshot.hasData) {
            return Center(
              child: SizedBox(
                width: 50.0,
                height: 50.0,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    FlutterFlowTheme.of(context).accent2,
                  ),
                ),
              ),
            );
          }
          List<AKeyRecord> columnAKeyRecordList = snapshot.data!;
          final columnAKeyRecord = columnAKeyRecordList.isNotEmpty
              ? columnAKeyRecordList.first
              : null;

          return Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Align(
                alignment: const AlignmentDirectional(-1.0, -1.0),
                child: Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(0.0, 20.0, 0.0, 0.0),
                  child: FlutterFlowIconButton(
                    borderColor: Colors.transparent,
                    borderRadius: 20.0,
                    borderWidth: 1.0,
                    buttonSize: 40.0,
                    icon: FaIcon(
                      FontAwesomeIcons.angleLeft,
                      color: FlutterFlowTheme.of(context).primaryText,
                      size: 24.0,
                    ),
                    onPressed: () async {
                      await widget.backCallback?.call();
                    },
                  ),
                ),
              ),
              Flexible(
                child: Align(
                  alignment: const AlignmentDirectional(0.0, 0.0),
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              0.0, 0.0, 10.0, 20.0),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8.0),
                            child: Image.asset(
                              'assets/images/location.webp',
                              width: 300.0,
                              height: 200.0,
                              fit: BoxFit.contain,
                            ),
                          ),
                        ),
                        Text(
                          getRemoteConfigString('location_allowing_headline'),
                          textAlign: TextAlign.center,
                          style:
                              FlutterFlowTheme.of(context).bodyMedium.override(
                                    fontFamily: 'BT Beau Sans',
                                    fontSize: 32.0,
                                    letterSpacing: 0.0,
                                    fontWeight: FontWeight.bold,
                                    useGoogleFonts: false,
                                  ),
                        ),
                        Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              0.0, 13.0, 0.0, 0.0),
                          child: Text(
                            getRemoteConfigString(
                                'location_allowing_explanation'),
                            textAlign: TextAlign.center,
                            style: FlutterFlowTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'BT Beau Sans',
                                  fontSize: 14.0,
                                  letterSpacing: 0.0,
                                  fontWeight: FontWeight.normal,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 24.0),
                child: FFButtonWidget(
                  onPressed: () async {
                    await showModalBottomSheet(
                      isScrollControlled: true,
                      backgroundColor: Colors.transparent,
                      enableDrag: false,
                      context: context,
                      builder: (context) {
                        return Padding(
                          padding: MediaQuery.viewInsetsOf(context),
                          child: InfoSheetScrollableWidget(
                            title: getRemoteConfigString("location_explainer"),
                            body:
                                getRemoteConfigString("location_explainer_detail"),
                          ),
                        );
                      },
                    ).then((value) => safeSetState(() {}));
                  },
                  text: getRemoteConfigString("location_explainer"),
                  icon: Icon(
                    Icons.info_outlined,
                    color: FlutterFlowTheme.of(context).secondaryText,
                    size: 18.0,
                  ),
                  options: FFButtonOptions(
                    width: MediaQuery.sizeOf(context).width * 1.0,
                    height: 40.0,
                    padding:
                        const EdgeInsetsDirectional.fromSTEB(15.0, 0.0, 15.0, 0.0),
                    iconPadding:
                        const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                    color: Colors.white,
                    textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                          fontFamily: 'BT Beau Sans',
                          color: FlutterFlowTheme.of(context).secondaryText,
                          fontSize: 4.0,
                          letterSpacing: 0.0,
                          fontWeight: FontWeight.w500,
                          useGoogleFonts: false,
                        ),
                    elevation: 0.0,
                    borderSide: const BorderSide(
                      color: Color(0x0057636C),
                      width: 0.0,
                    ),
                    borderRadius: BorderRadius.circular(100.0),
                  ),
                ),
              ),
              Align(
                alignment: const AlignmentDirectional(0.0, 1.0),
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Builder(
                        builder: (context) => wrapWithModel(
                          model: _model.gradientButtonModel,
                          updateCallback: () => safeSetState(() {}),
                          child: GradientButtonWidget(
                            title: getRemoteConfigString(
                                'location_allowing_button_title'),
                            action: () async {
                              currentUserLocationValue =
                                  await getCurrentUserLocation(
                                      defaultLocation: const LatLng(0.0, 0.0));
                              await requestPermission(locationPermission);
                              if (await getPermissionStatus(
                                  locationPermission)) {
                                await actions.getLocationData(
                                  currentUserLocationValue!,
                                  columnAKeyRecord!.key,
                                );
                                // Save coordinates to user document
                                try {

                                analytics.logEvent('Sign Up: Gave Location Permission');
                                      analytics.setUserProperties({'City(Here)': FFAppState().hereLocation, "Country (Here)": valueOrDefault<String>(
                                          FFAppState().regionName,
                                          'Other',
                                        )});
                                } catch(e) {}

                                await currentUserReference!
                                    .update(createUsersRecordData(
                                  location: FFAppState().currentLocation,
                                  city: FFAppState().hereLocation,
                                  wlRegion: valueOrDefault<String>(
                                    FFAppState().regionName,
                                    'Other',
                                  ),
                                  nextSignUpStage: SetUpStage.verification,
                                ));
                                // Save city to public profile

                                await currentUserDocument!.publicProfile!
                                    .update(createPublicProfileRecordData(
                                  city: FFAppState().hereLocation,
                                ));
                                await widget.nextCallback?.call();
                              } else {
                                await showDialog(
                                  barrierDismissible: false,
                                  context: context,
                                  builder: (dialogContext) {
                                    return Dialog(
                                      elevation: 0,
                                      insetPadding: EdgeInsets.zero,
                                      backgroundColor: Colors.transparent,
                                      alignment: const AlignmentDirectional(0.0, 0.0)
                                          .resolve(Directionality.of(context)),
                                      child: const GeneralPopupWidget(
                                        alertTitle: 'Location access needed',
                                        alertText:
                                            'In order to use chyrpe, you need to grant us access to your location. You can do so in your settings app.',
                                      ),
                                    );
                                  },
                                );
                              }
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
