import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/gradient_button_disabled/gradient_button_disabled_widget.dart';
import 'name_component_widget.dart' show NameComponentWidget;
import 'package:flutter/material.dart';

class NameComponentModel extends FlutterFlowModel<NameComponentWidget> {
  ///  State fields for stateful widgets in this component.
  /// 
  
  PublicProfileRecord? publicDocument;

  final formKey = GlobalKey<FormState>();
  // State field(s) for TextField widget.
  FocusNode? textFieldFocusNode;
  TextEditingController? textController;
  String? Function(BuildContext, String?)? textControllerValidator;
  String? _textControllerValidator(BuildContext context, String? val) {
    if (val == null || val.isEmpty) {
      return 'Field is required';
    }

    if (val.isEmpty) {
      return 'Minimum: 1 character';
    }
    if (val.length > 40) {
      return 'Maximum: 40 characters';
    }

    return null;
  }

  // Model for GradientButton component.
  late GradientButtonModel gradientButtonModel;
  // Model for GradientButtonDisabled component.
  late GradientButtonDisabledModel gradientButtonDisabledModel;

  @override
  void initState(BuildContext context) {
    textControllerValidator = _textControllerValidator;
    gradientButtonModel = createModel(context, () => GradientButtonModel());
    gradientButtonDisabledModel =
        createModel(context, () => GradientButtonDisabledModel());
  }

  @override
  void dispose() {
    textFieldFocusNode?.dispose();
    textController?.dispose();

    gradientButtonModel.dispose();
    gradientButtonDisabledModel.dispose();
  }
}
