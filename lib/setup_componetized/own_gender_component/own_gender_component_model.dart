import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/gradient_button_disabled/gradient_button_disabled_widget.dart';
import '/advanced_signup_profiles_filtering/radio_button_list/radio_button_list_widget.dart';
import 'own_gender_component_widget.dart' show OwnGenderComponentWidget;
import 'package:flutter/material.dart';

class OwnGenderComponentModel
    extends FlutterFlowModel<OwnGenderComponentWidget> {
  ///  Local state fields for this component.

  GenderToShow? selectedGender;

  ///  State fields for stateful widgets in this component.

  // Model for GradientButton component.
  late GradientButtonModel gradientButtonModel;
  // Stores action output result for [Backend Call - Create Document] action in GradientButton widget.
  PublicProfileRecord? publicDocument;
  // Model for GradientButtonDisabled component.
  late GradientButtonDisabledModel gradientButtonDisabledModel;


  late RadioButtonListModel radioButtonListModel;

  @override
  void initState(BuildContext context) {
    gradientButtonModel = createModel(context, () => GradientButtonModel());
    radioButtonListModel = createModel(context, () => RadioButtonListModel());
    gradientButtonDisabledModel =
        createModel(context, () => GradientButtonDisabledModel());
  }

  @override
  void dispose() {
    gradientButtonModel.dispose();
    radioButtonListModel.dispose();
    gradientButtonDisabledModel.dispose();
  }
}
