import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/gradient_button_disabled/gradient_button_disabled_widget.dart';
import '/general/info_sheet_scrollable/info_sheet_scrollable_widget.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import 'package:flutter/material.dart';
import 'own_gender_component_model.dart';
export 'own_gender_component_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';
import '/advanced_signup_profiles_filtering/radio_button_list/radio_button_list_widget.dart';

class OwnGenderComponentWidget extends StatefulWidget {
  const OwnGenderComponentWidget({
    super.key,
    required this.nextCallback,
  });

  final Future Function()? nextCallback;

  @override
  State<OwnGenderComponentWidget> createState() =>
      _OwnGenderComponentWidgetState();
}

class _OwnGenderComponentWidgetState extends State<OwnGenderComponentWidget> {
  late OwnGenderComponentModel _model;

  Map<GenderToShow, String> genderMap = {
  GenderToShow.Male: "Man",
 GenderToShow.Female: "Woman",
  GenderToShow.Alternative: "Non-binary"
};

Map<String, GenderToShow> reverseGenderMap = {
  "Man": GenderToShow.Male,
 "Woman": GenderToShow.Female,
  "Non-binary": GenderToShow.Alternative
};

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => OwnGenderComponentModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(23.0, 0.0, 23.0, 0.0),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(0.0, 70.0, 0.0, 25.0),
            child: Text(
              'What describes you best?',
              style: FlutterFlowTheme.of(context).bodyMedium.override(
                    fontFamily: 'BT Beau Sans',
                    fontSize: 32.0,
                    letterSpacing: 0.0,
                    fontWeight: FontWeight.bold,
                    useGoogleFonts: false,
                    lineHeight: 1.29,
                  ),
            ),
          ),
          Builder(
            builder: (context) {
              final options = GenderToShow.values.toList();

              return wrapWithModel(
                        model: _model.radioButtonListModel,
                        updateCallback: () => safeSetState(() {}),
                        updateOnChange: true,
                        child: RadioButtonListWidget(
                          mandatory: false,
                          options: GenderToShow.values.map((gender) => genderMap[gender] ?? gender.name).toList(),
                        )
              );
                          },
          ),
          
          const Spacer(),
          Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 47.0),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 10.0),
                  child: FFButtonWidget(
                              onPressed: () async {
                                await showModalBottomSheet(
                  isScrollControlled: true,
                  backgroundColor: Colors.transparent,
                  enableDrag: false,
                  context: context,
                  builder: (context) {
                    return Padding(
                      padding: MediaQuery.viewInsetsOf(context),
                      child: InfoSheetScrollableWidget(
                        title:
                            getRemoteConfigString('signup_ownGender_sheet_title'),
                        body: getRemoteConfigString('signup_ownGender_info'),
                      ),
                    );
                  },
                                ).then((value) => safeSetState(() {}));
                              },
                              text: getRemoteConfigString('signup_ownGender_info_button_title'),
                              icon: Icon(
                                Icons.info_outlined,
                                color: FlutterFlowTheme.of(context).secondaryText,
                                size: 18.0,
                              ),
                              options: FFButtonOptions(
                                height: 40.0,
                                padding: const EdgeInsetsDirectional.fromSTEB(15.0, 0.0, 15.0, 0.0),
                                iconPadding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                                color: Colors.white,
                                textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                      fontFamily: 'BT Beau Sans',
                      color: FlutterFlowTheme.of(context).secondaryText,
                      fontSize: 4.0,
                      letterSpacing: 0.0,
                      fontWeight: FontWeight.w500,
                      useGoogleFonts: false,
                    ),
                                elevation: 0.0,
                                borderSide: const BorderSide(
                  color: Color(0x0057636C),
                  width: 0.0,
                                ),
                                borderRadius: BorderRadius.circular(100.0),
                              ),
                            ),
                ),
                if (_model.radioButtonListModel.selectedOption != null)
                  Builder(
                    builder: (context) => wrapWithModel(
                      model: _model.gradientButtonModel,
                      updateCallback: () => safeSetState(() {}),
                      child: GradientButtonWidget(
                        title: 'Next',
                        action: () async {
                          _model.selectedGender = reverseGenderMap[_model.radioButtonListModel.selectedOption];

                          if (_model.selectedGender != null) {
                            // Set gender to user document

                            try {
                                      analytics.setUserProperties({'Gender': _model.selectedGender?.name ?? ''});
                                      analytics.logEvent('Sign Up: Selected Gender', eventProperties: {"Gender Selected": _model.selectedGender?.name ?? ''});
                                      } catch (error) {}

                            await currentUserReference!
                                .update(createUsersRecordData(
                              gender:
                                  _model.selectedGender == GenderToShow.Female
                                      ? Gender.Female
                                      : Gender.Male,
                              nextSignUpStage: SetUpStage.name,
                              alternativeG: (_model.selectedGender ==
                                          GenderToShow.Male) ||
                                      (_model.selectedGender ==
                                          GenderToShow.Female)
                                  ? false
                                  : true,
                              signUpTestingCohort: functions
                                  .getRandomSignUpTestingCohort(functions
                                      .getNumberArrayFromJsonString(
                                          getRemoteConfigString(
                                              'signup_testing_cohorts_chances'))
                                      .toList()),
                              savedSignUpStageNew: "gender"
                            ));
                            if (currentUserDocument?.publicProfile != null) {
                              // Set gender to public profile

                              await currentUserDocument!.publicProfile!
                                  .update(createPublicProfileRecordData(
                                gender:
                                    _model.selectedGender == GenderToShow.Female
                                        ? Gender.Female
                                        : Gender.Male,
                                alternativeG: (_model.selectedGender ==
                                            GenderToShow.Male) ||
                                        (_model.selectedGender ==
                                            GenderToShow.Female)
                                    ? false
                                    : true,
                                displayG: _model.selectedGender?.name,
                              ));
                            } else {
                              // Set public name in public profile

                              var publicProfileRecordReference =
                                  PublicProfileRecord.createDoc(
                                      currentUserReference!);
                              await publicProfileRecordReference
                                  .set(createPublicProfileRecordData(
                                uid: currentUserUid,
                                gender:
                                    _model.selectedGender == GenderToShow.Female
                                        ? Gender.Female
                                        : Gender.Male,
                                alternativeG: (_model.selectedGender ==
                                            GenderToShow.Male) ||
                                        (_model.selectedGender ==
                                            GenderToShow.Female)
                                    ? false
                                    : true,
                                displayG: _model.selectedGender?.name,
                              ));
                              _model.publicDocument =
                                  PublicProfileRecord.getDocumentFromData(
                                      createPublicProfileRecordData(
                                        uid: currentUserUid,
                                        gender: _model.selectedGender ==
                                                GenderToShow.Female
                                            ? Gender.Female
                                            : Gender.Male,
                                        alternativeG: (_model.selectedGender ==
                                                    GenderToShow.Male) ||
                                                (_model.selectedGender ==
                                                    GenderToShow.Female)
                                            ? false
                                            : true,
                                        displayG: _model.selectedGender?.name,
                                      ),
                                      publicProfileRecordReference);
                              // Save public profile document for user

                              await currentUserReference!
                                  .update(createUsersRecordData(
                                publicProfile: _model.publicDocument?.reference,
                              ));
                            }

                            await widget.nextCallback?.call();
                          } else {
                            await showDialog(
                              context: context,
                              builder: (dialogContext) {
                                return Dialog(
                                  elevation: 0,
                                  insetPadding: EdgeInsets.zero,
                                  backgroundColor: Colors.transparent,
                                  alignment: const AlignmentDirectional(0.0, 0.0)
                                      .resolve(Directionality.of(context)),
                                  child: const GeneralPopupWidget(
                                    alertTitle: 'No option selected',
                                    alertText:
                                        'Please select an option to continue',
                                  ),
                                );
                              },
                            );
                          }

                          safeSetState(() {});
                        },
                      ),
                    ),
                  ),
                if (_model.radioButtonListModel.selectedOption == null)
                  wrapWithModel(
                    model: _model.gradientButtonDisabledModel,
                    updateCallback: () => safeSetState(() {}),
                    child: GradientButtonDisabledWidget(
                      title: 'Next',
                      action: () async {},
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
