import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/gradient_button_disabled/gradient_button_disabled_widget.dart';
import '/advanced_signup_profiles_filtering/radio_button_list/radio_button_list_widget.dart';
import 'package:flutter/material.dart';
import 'preferred_gender_component_model.dart';
export 'preferred_gender_component_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';

class PreferredGenderComponentWidget extends StatefulWidget {
  const PreferredGenderComponentWidget({
    super.key,
    required this.nextCallback,
  });

  final Future Function()? nextCallback;

  @override
  State<PreferredGenderComponentWidget> createState() =>
      _PreferredGenderComponentWidgetState();
}

class _PreferredGenderComponentWidgetState
    extends State<PreferredGenderComponentWidget> {
  late PreferredGenderComponentModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => PreferredGenderComponentModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(30.0, 0.0, 30.0, 0.0),
      child: AuthUserStreamWidget(
        builder: (context) => StreamBuilder<List<OptionsForSelectorsRecord>>(
          stream: queryOptionsForSelectorsRecord(
            queryBuilder: (optionsForSelectorsRecord) =>
                optionsForSelectorsRecord.where(
              'name',
              isEqualTo: () {
                if (valueOrDefault<bool>(
                    currentUserDocument?.alternativeG, false)) {
                  return 'Alternative Gender Reqs';
                } else if (currentUserDocument?.gender == Gender.Male) {
                  return 'Male Gender Reqs';
                } else {
                  return 'Female Gender Reqs';
                }
              }(),
            ),
            singleRecord: true,
          ),
          builder: (context, snapshot) {
            // Customize what your widget looks like when it's loading.
            if (!snapshot.hasData) {
              return Center(
                child: SizedBox(
                  width: 50.0,
                  height: 50.0,
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      FlutterFlowTheme.of(context).accent2,
                    ),
                  ),
                ),
              );
            }
            List<OptionsForSelectorsRecord>
                columnOptionsForSelectorsRecordList = snapshot.data!;
            final columnOptionsForSelectorsRecord =
                columnOptionsForSelectorsRecordList.isNotEmpty
                    ? columnOptionsForSelectorsRecordList.first
                    : null;

            return Column(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Padding(
                  padding:
                      const EdgeInsetsDirectional.fromSTEB(0.0, 70.0, 0.0, 25.0),
                  child: Text(
                    'Who would you like to see?',
                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                          fontFamily: 'BT Beau Sans',
                          fontSize: 32.0,
                          letterSpacing: 0.0,
                          fontWeight: FontWeight.bold,
                          useGoogleFonts: false,
                          lineHeight: 1.29,
                        ),
                  ),
                ),
                Builder(
                  builder: (context) {
                    final preferredGenderVar =
                        columnOptionsForSelectorsRecord?.options.toList() ??
                            [];
            
                    return wrapWithModel(
                      model: _model.radioButtonListModel,
                      updateCallback: () => safeSetState(() {
                        _model.selectedGender = _model.radioButtonListModel.selectedOption;
                      }),
                      updateOnChange: true,
                      child: RadioButtonListWidget(
                        mandatory: false,
                        options: preferredGenderVar,
                      )
            );
                  },
                ),
                if ((getRemoteConfigString('signup_genderPref_info') !=
                            '') &&
                    (getRemoteConfigString('signup_genderPref_info') != ' '))
                  Padding(
                    padding:
                        const EdgeInsetsDirectional.fromSTEB(0.0, 12.0, 0.0, 25.0),
                    child: Text(
                      getRemoteConfigString('signup_genderPref_info'),
                      style: FlutterFlowTheme.of(context).bodyMedium.override(
                            fontFamily: 'BT Beau Sans',
                            fontSize: 15.0,
                            letterSpacing: 0.0,
                            useGoogleFonts: false,
                            lineHeight: 1.21,
                          ),
                    ),
                  ),
                
                const Spacer(),
                Padding(
                  padding:
                      const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 40.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      if (_model.selectedGender != null &&
                          _model.selectedGender != '')
                        Builder(
                          builder: (context) => wrapWithModel(
                            model: _model.gradientButtonModel,
                            updateCallback: () => safeSetState(() {}),
                            child: GradientButtonWidget(
                              title: 'Next',
                              action: () async {
                                if (_model.selectedGender != null &&
                                    _model.selectedGender != '') {
                                  analytics.logEvent('Sign Up: Entered Orientation', eventProperties: {'Orientation Selected': _model.selectedGender});
                                  analytics.setUserProperties({'Ortientation': _model.selectedGender});
                                  await currentUserReference!
                                      .update(createUsersRecordData(
                                    genderReq: _model.selectedGender,
                                    savedSignUpStageNew: "gender_preferences"
                                  ));
                                  await widget.nextCallback?.call();
                                } else {
                                  await showDialog(
                                    context: context,
                                    builder: (dialogContext) {
                                      return Dialog(
                                        elevation: 0,
                                        insetPadding: EdgeInsets.zero,
                                        backgroundColor: Colors.transparent,
                                        alignment: const AlignmentDirectional(
                                                0.0, 0.0)
                                            .resolve(
                                                Directionality.of(context)),
                                        child: const GeneralPopupWidget(
                                          alertTitle: 'No option selected',
                                          alertText:
                                              'Please select an option to continue',
                                        ),
                                      );
                                    },
                                  );
                                  safeSetState(() {});
                                }
                              },
                            ),
                          ),
                        ),
                      if (_model.selectedGender == null ||
                          _model.selectedGender == '')
                        wrapWithModel(
                          model: _model.gradientButtonDisabledModel,
                          updateCallback: () => safeSetState(() {}),
                          child: GradientButtonDisabledWidget(
                            title: 'Next',
                            action: () async {},
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
