import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/gradient_button_disabled/gradient_button_disabled_widget.dart';
import '/general/list_multiselect/list_multiselect_widget.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'relationship_types_component_model.dart';
export 'relationship_types_component_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';

class RelationshipTypesComponentWidget extends StatefulWidget {
  const RelationshipTypesComponentWidget({
    super.key,
    required this.nextCallback,
    required this.backCallback,
  });

  final Future Function()? nextCallback;
  final Future Function()? backCallback;

  @override
  State<RelationshipTypesComponentWidget> createState() =>
      _RelationshipTypesComponentWidgetState();
}

class _RelationshipTypesComponentWidgetState
    extends State<RelationshipTypesComponentWidget> {
  late RelationshipTypesComponentModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => RelationshipTypesComponentModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AuthUserStreamWidget(
      builder: (context) => StreamBuilder<PublicProfileRecord>(
        stream: PublicProfileRecord.getDocument(
            currentUserDocument!.publicProfile!),
        builder: (context, snapshot) {
          // Customize what your widget looks like when it's loading.
          if (!snapshot.hasData) {
            return Center(
              child: SizedBox(
                width: 50.0,
                height: 50.0,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    FlutterFlowTheme.of(context).accent2,
                  ),
                ),
              ),
            );
          }

          final stackPublicProfileRecord = snapshot.data!;

          return Stack(
            children: [
              StreamBuilder<List<OptionsForSelectorsRecord>>(
                stream: queryOptionsForSelectorsRecord(
                  queryBuilder: (optionsForSelectorsRecord) =>
                      optionsForSelectorsRecord.where(
                    'name',
                    isEqualTo: 'Relationship Aims',
                  ),
                  singleRecord: true,
                ),
                builder: (context, snapshot) {
                  // Customize what your widget looks like when it's loading.
                  if (!snapshot.hasData) {
                    return Center(
                      child: SizedBox(
                        width: 50.0,
                        height: 50.0,
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                            FlutterFlowTheme.of(context).accent2,
                          ),
                        ),
                      ),
                    );
                  }
                  List<OptionsForSelectorsRecord>
                      columnOptionsForSelectorsRecordList = snapshot.data!;
                  final columnOptionsForSelectorsRecord =
                      columnOptionsForSelectorsRecordList.isNotEmpty
                          ? columnOptionsForSelectorsRecordList.first
                          : null;

                  return Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Padding(
                        padding: const EdgeInsetsDirectional.fromSTEB(
                            23.0, 0.0, 23.0, 0.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 70.0, 0.0, 30.0),
                              child: Text(
                                'I\'m looking for...',
                                style: FlutterFlowTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'BT Beau Sans',
                                      fontSize: 32.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.bold,
                                      useGoogleFonts: false,
                                      lineHeight: 1.29,
                                    ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 0.0, 0.0, 30.0),
                              child: wrapWithModel(
                                model: _model.listMultiselectModel,
                                updateCallback: () => safeSetState(() {}),
                                updateOnChange: true,
                                child: ListMultiselectWidget(
                                  choices:
                                      columnOptionsForSelectorsRecord!.options,
                                  previouslyMadeChoices:
                                      stackPublicProfileRecord
                                          .relationPreferences,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Spacer(),
                      Align(
                        alignment: const AlignmentDirectional(0.0, 1.0),
                        child: Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              27.0, 0.0, 27.0, 47.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              if (_model
                                      .listMultiselectModel.choicesMade.isNotEmpty)
                                wrapWithModel(
                                  model: _model.gradientButtonModel,
                                  updateCallback: () => safeSetState(() {}),
                                  child: GradientButtonWidget(
                                    title: 'Next',
                                    action: () async {
                                      if (_model.listMultiselectModel
                                          .choicesMade.isNotEmpty) {
                                            
                                            analytics.logEvent('Sign Up: Entered Relationship Preferences');
                                            analytics.setUserProperties({'Relationship Preferences': _model.listMultiselectModel.choicesMade});
                                        // Set relationship preferences to public profile

                                        await currentUserDocument!
                                            .publicProfile!
                                            .update({
                                          ...mapToFirestore(
                                            {
                                              'relationPreferences': _model
                                                  .listMultiselectModel
                                                  .choicesMade,
                                            },
                                          ),
                                        });
                                        await widget.nextCallback?.call();
                                      } else {
                                        await showDialog(
                                          context: context,
                                          builder: (alertDialogContext) {
                                            return AlertDialog(
                                              title: const Text('No option selected'),
                                              content: const Text(
                                                  'Please select at least one option to continue'),
                                              actions: [
                                                TextButton(
                                                  onPressed: () =>
                                                      Navigator.pop(
                                                          alertDialogContext),
                                                  child: const Text('Ok'),
                                                ),
                                              ],
                                            );
                                          },
                                        );
                                      }
                                    },
                                  ),
                                ),
                              if (_model.listMultiselectModel.choicesMade.isEmpty)
                                wrapWithModel(
                                  model: _model.gradientButtonDisabledModel,
                                  updateCallback: () => safeSetState(() {}),
                                  child: GradientButtonDisabledWidget(
                                    title: 'Next',
                                    action: () async {},
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(10.0, 20.0, 0.0, 0.0),
                child: FlutterFlowIconButton(
                  borderColor: Colors.transparent,
                  borderRadius: 20.0,
                  borderWidth: 1.0,
                  buttonSize: 40.0,
                  icon: FaIcon(
                    FontAwesomeIcons.angleLeft,
                    color: FlutterFlowTheme.of(context).primaryText,
                    size: 24.0,
                  ),
                  onPressed: () async {
                    await widget.backCallback?.call();
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
