import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/gradient_button_disabled/gradient_button_disabled_widget.dart';
import '/general/info_sheet_scrollable_multi/info_sheet_scrollable_multi_widget.dart';
import '/advanced_signup_profiles_filtering/radio_button_list/radio_button_list_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'role_component_model.dart';
export 'role_component_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';

class RoleComponentWidget extends StatefulWidget {
  const RoleComponentWidget({
    super.key,
    required this.nextCallback,
    required this.backCallback,
  });

  final Future Function()? nextCallback;
  final Future Function()? backCallback;

  @override
  State<RoleComponentWidget> createState() => _RoleComponentWidgetState();
}

class _RoleComponentWidgetState extends State<RoleComponentWidget> {
  late RoleComponentModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => RoleComponentModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Padding(
          padding: const EdgeInsetsDirectional.fromSTEB(30.0, 0.0, 30.0, 0.0),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Flexible(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(
                          0.0, 70.0, 0.0, 25.0),
                      child: Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'What are you?',
                            style: FlutterFlowTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'BT Beau Sans',
                                  fontSize: 32.0,
                                  letterSpacing: 0.0,
                                  fontWeight: FontWeight.bold,
                                  useGoogleFonts: false,
                                  lineHeight: 1.29,
                                ),
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(
                          0.0, 12.0, 0.0, 30.0),
                      child: Text(
                        'Chyrpe will help you find the relationship dynamic that suits you best. ',
                        style:
                            FlutterFlowTheme.of(context).bodyMedium.override(
                                  fontFamily: 'BT Beau Sans',
                                  fontSize: 12.0,
                                  letterSpacing: 0.0,
                                  useGoogleFonts: false,
                                  lineHeight: 1.21,
                                ),
                      ),
                    ),
                    Flexible(
                      child: AuthUserStreamWidget(
                        builder: (context) =>
                            StreamBuilder<List<OptionsForSelectorsRecord>>(
                          stream: queryOptionsForSelectorsRecord(
                            queryBuilder: (optionsForSelectorsRecord) =>
                                optionsForSelectorsRecord.where(
                              'name',
                              isEqualTo: valueOrDefault<bool>(
                                      currentUserDocument?.alternativeG,
                                      false)
                                  ? 'Alternative Roles'
                                  : '${currentUserDocument?.gender?.name}-${currentUserDocument?.genderReq} Roles',
                            ),
                            singleRecord: true,
                          ),
                          builder: (context, snapshot) {
                            // Customize what your widget looks like when it's loading.
                            if (!snapshot.hasData) {
                              return Center(
                                child: SizedBox(
                                  width: 40.0,
                                  height: 40.0,
                                  child: SpinKitCircle(
                                    color: FlutterFlowTheme.of(context)
                                        .secondaryText,
                                    size: 40.0,
                                  ),
                                ),
                              );
                            }
                            List<OptionsForSelectorsRecord>
                                columnOptionsForSelectorsRecordList =
                                snapshot.data!;
                            // Return an empty Container when the item does not exist.
                            if (snapshot.data!.isEmpty) {
                              return Container();
                            }
                            final columnOptionsForSelectorsRecord =
                                columnOptionsForSelectorsRecordList.isNotEmpty
                                    ? columnOptionsForSelectorsRecordList
                                        .first
                                    : null;
                
                            return Builder(
                              builder: (context) {
                                final options =
                                    columnOptionsForSelectorsRecord?.options
                                            .toList() ??
                                        [];
                
                                return wrapWithModel(
                      model: _model.radioButtonListModel,
                      updateCallback: () => safeSetState(() {
                        _model.selectedRole = _model.radioButtonListModel.selectedOption;
                      }),
                      updateOnChange: true,
                      child: RadioButtonListWidget(
                        mandatory: false,
                        options: options,
                      )
                              );
                              },
                            );
                          },
                        ),
                      ),
                    ),
                    
                    
                  ],
                ),
              ),
              Align(
                alignment: const AlignmentDirectional(0.0, 1.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(0.0, 24.0, 0.0, 40.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 10.0),
                      child: FFButtonWidget(
                        onPressed: () async {
                          await showModalBottomSheet(
                            isScrollControlled: true,
                            backgroundColor: Colors.transparent,
                            enableDrag: false,
                            context: context,
                            builder: (context) {
                              return Padding(
                                padding: MediaQuery.viewInsetsOf(context),
                                child: InfoSheetScrollableMultiWidget(
                                  title: 'Quick explanations',
                                  subtitle1:
                                      getRemoteConfigString('dominant_term'),
                                  subbody1: getRemoteConfigString(
                                      'dominant_term_info_body'),
                                  subtitle2: getRemoteConfigString(
                                      'submissive_term'),
                                  subbody2: getRemoteConfigString(
                                      'submissive_term_info_body'),
                                  subtitle3:
                                      getRemoteConfigString('switch_term'),
                                  subbody3: getRemoteConfigString(
                                      'switch_term_info_body'),
                                ),
                              );
                            },
                          ).then((value) => safeSetState(() {}));
                        },
                        text: getRemoteConfigString(
                            'signup_kink_ibutton_title'),
                        icon: Icon(
                          Icons.info_outlined,
                          color: FlutterFlowTheme.of(context).secondaryText,
                          size: 18.0,
                        ),
                        options: FFButtonOptions(
                          width: MediaQuery.sizeOf(context).width * 1.0,
                          height: 40.0,
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              15.0, 0.0, 15.0, 0.0),
                          iconPadding: const EdgeInsetsDirectional.fromSTEB(
                              0.0, 0.0, 0.0, 0.0),
                          color: Colors.white,
                          textStyle: FlutterFlowTheme.of(context)
                              .titleSmall
                              .override(
                                fontFamily: 'BT Beau Sans',
                                color: FlutterFlowTheme.of(context)
                                    .secondaryText,
                                fontSize: 4.0,
                                letterSpacing: 0.0,
                                fontWeight: FontWeight.w500,
                                useGoogleFonts: false,
                              ),
                          elevation: 0.0,
                          borderSide: const BorderSide(
                            color: Color(0x0057636C),
                            width: 0.0,
                          ),
                          borderRadius: BorderRadius.circular(100.0),
                        ),
                      ),
                    ),
                    const Divider(
                      thickness: 1.0,
                      color: Color(0xFFD4D8DE),
                    ),
                    Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(
                          27.0, 15.0, 27.0, 25.0),
                      child: InkWell(
                      splashColor: Colors.transparent,
                      focusColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      onTap: () async {
                        _model.checkboxValue = !(_model.checkboxValue ?? false);
                        safeSetState(() {});
                      },
                        child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        getRemoteConfigString(
                                            'generic_signup_hide_string'),
                                        style: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .override(
                                              fontFamily: 'BT Beau Sans',
                                              letterSpacing: 0.0,
                                              useGoogleFonts: false,
                                            ),
                                      ),
                                      Stack(
                        alignment: Alignment.center,
                        children: [
                          Container(
                            width: 18.0,
                            height: 18.0,
                            decoration: BoxDecoration(
                              color: _model.checkboxValue
                                  ? const Color(0xFFBAABF1)
                                  : const Color(0xFFF1F0F0),
                              shape: BoxShape.circle,
                            ),
                          ),
                          if (_model.checkboxValue)
                          Align(
                            alignment: Alignment.center,
                            child: Container(
                              width: 5.5,
                              height: 5.5,
                              decoration: const BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                              ),
                            ),
                          ),
                        ],
                      ),
                                    ].divide(const SizedBox(width: 8.0)),
                                  ),
                      ),
                    ),
                          if (_model.selectedRole != null &&
                              _model.selectedRole != '')
                            Builder(
                              builder: (context) => wrapWithModel(
                                model: _model.gradientButtonModel,
                                updateCallback: () => safeSetState(() {}),
                                child: GradientButtonWidget(
                                  title: 'Next',
                                  action: () async {
                                    if (_model.selectedRole != null &&
                                        _model.selectedRole != '') {

                                           analytics.logEvent('Sign Up: Entered Role', eventProperties: {'Role Selected': _model.selectedRole, 'Role Hidden': _model.checkboxValue});
                                            analytics.setUserProperties({'Role': _model.selectedRole});
                                      // Set role in user

                                      await currentUserReference!
                                          .update(createUsersRecordData(
                                        position: _model.selectedRole,
                                        savedSignUpStageNew: "femdom_role"
                                      ));
                                      // Depending on choice, set public role

                                      await currentUserDocument!.publicProfile!
                                          .update(createPublicProfileRecordData(
                                        publicRole: _model.checkboxValue
                                            ? ''
                                            : _model.selectedRole,
                                        publicRoleShown: !_model.checkboxValue,
                                      ));
                                      await widget.nextCallback?.call();
                                    } else {
                                      await showDialog(
                                        context: context,
                                        builder: (dialogContext) {
                                          return Dialog(
                                            elevation: 0,
                                            insetPadding: EdgeInsets.zero,
                                            backgroundColor: Colors.transparent,
                                            alignment: const AlignmentDirectional(
                                                    0.0, 0.0)
                                                .resolve(
                                                    Directionality.of(context)),
                                            child: const GeneralPopupWidget(
                                              alertTitle: 'No option selected',
                                              alertText:
                                                  'Please select an option to continue',
                                            ),
                                          );
                                        },
                                      );
                                    }
                                  },
                                ),
                              ),
                            ),
                          if (_model.selectedRole == null ||
                              _model.selectedRole == '')
                            wrapWithModel(
                              model: _model.gradientButtonDisabledModel,
                              updateCallback: () => safeSetState(() {}),
                              child: GradientButtonDisabledWidget(
                                title: 'Next',
                                action: () async {},
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsetsDirectional.fromSTEB(15.0, 20.0, 0.0, 0.0),
          child: FlutterFlowIconButton(
            borderColor: Colors.transparent,
            borderRadius: 20.0,
            borderWidth: 1.0,
            buttonSize: 40.0,
            icon: FaIcon(
              FontAwesomeIcons.angleLeft,
              color: FlutterFlowTheme.of(context).primaryText,
              size: 24.0,
            ),
            onPressed: () async {
              await widget.backCallback?.call();
            },
          ),
        ),
      ],
    );
  }
}
