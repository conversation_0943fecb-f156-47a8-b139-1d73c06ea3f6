import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/flutter_flow/flutter_flow_rive_controller.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/setup_componetized/bio_bio_component/bio_bio_component_widget.dart';
import '/setup_componetized/bio_prompts_component/bio_prompts_component_widget.dart';
import '/setup_componetized/birthday_component/birthday_component_widget.dart';
import '/setup_componetized/experience_level_component/experience_level_component_widget.dart';
import '/setup_componetized/images_component/images_component_widget.dart';
import '/setup_componetized/location_component/location_component_widget.dart';
import '/setup_componetized/name_component/name_component_widget.dart';
import '/setup_componetized/own_gender_component/own_gender_component_widget.dart';
import '/setup_componetized/preferred_gender_component/preferred_gender_component_widget.dart';
import '/setup_componetized/relationship_types_component/relationship_types_component_widget.dart';
import '/setup_componetized/role_component/role_component_widget.dart';
import 'sign_up_l_a_widget.dart' show SignUpLAWidget;
import 'package:flutter/material.dart';

class SignUpLAModel extends FlutterFlowModel<SignUpLAWidget> {
  ///  Local state fields for this page.

  bool bioBioShown = false;

  ///  State fields for stateful widgets in this page.

  // State field(s) for RiveAnimation widget.
  final riveAnimationAnimationsList = [
    '',
  ];
  List<FlutterFlowRiveController> riveAnimationControllers = [];
  // State field(s) for PageView widget.
  PageController? pageViewController;

  int get pageViewCurrentIndex => pageViewController != null &&
          pageViewController!.hasClients &&
          pageViewController!.page != null
      ? pageViewController!.page!.round()
      : 0;
  // Model for OwnGenderComponent component.
  late OwnGenderComponentModel ownGenderComponentModel;
  // Model for NameComponent component.
  late NameComponentModel nameComponentModel;
  // Model for BirthdayComponent component.
  late BirthdayComponentModel birthdayComponentModel;
  // Model for PreferredGenderComponent component.
  late PreferredGenderComponentModel preferredGenderComponentModel;
  // Model for RoleComponent component.
  late RoleComponentModel roleComponentModel;
  // Model for ExperienceLevelComponent component.
  late ExperienceLevelComponentModel experienceLevelComponentModel;
  // Model for RelationshipTypesComponent component.
  late RelationshipTypesComponentModel relationshipTypesComponentModel;
  // Model for ImagesComponent component.
  late ImagesComponentModel imagesComponentModel;
  // Model for BioBioComponent component.
  late BioBioComponentModel bioBioComponentModel;
  // Model for BioPromptsComponent component.
  late BioPromptsComponentModel bioPromptsComponentModel;
  // Model for LocationComponent component.
  late LocationComponentModel locationComponentModel;
   // Stores action output result for [Cloud Function - addNewUserWaitingList] action in LocationComponent widget.
  AddNewUserWaitingListCloudFunctionCallResponse? addWaitingListCf;

  @override
  void initState(BuildContext context) {
    for (var name in riveAnimationAnimationsList) {
      riveAnimationControllers.add(FlutterFlowRiveController(
        name,
      ));
    }

    ownGenderComponentModel =
        createModel(context, () => OwnGenderComponentModel());
    nameComponentModel = createModel(context, () => NameComponentModel());
    birthdayComponentModel =
        createModel(context, () => BirthdayComponentModel());
    preferredGenderComponentModel =
        createModel(context, () => PreferredGenderComponentModel());
    roleComponentModel = createModel(context, () => RoleComponentModel());
    experienceLevelComponentModel =
        createModel(context, () => ExperienceLevelComponentModel());
    relationshipTypesComponentModel =
        createModel(context, () => RelationshipTypesComponentModel());
    imagesComponentModel = createModel(context, () => ImagesComponentModel());
    bioBioComponentModel = createModel(context, () => BioBioComponentModel());
    bioPromptsComponentModel =
        createModel(context, () => BioPromptsComponentModel());
    locationComponentModel =
        createModel(context, () => LocationComponentModel());
  }

  @override
  void dispose() {
    ownGenderComponentModel.dispose();
    nameComponentModel.dispose();
    birthdayComponentModel.dispose();
    preferredGenderComponentModel.dispose();
    roleComponentModel.dispose();
    experienceLevelComponentModel.dispose();
    relationshipTypesComponentModel.dispose();
    imagesComponentModel.dispose();
    bioBioComponentModel.dispose();
    bioPromptsComponentModel.dispose();
    locationComponentModel.dispose();
  }
}
