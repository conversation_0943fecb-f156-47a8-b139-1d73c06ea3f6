import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/backend/schema/enums/enums.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/setup_componetized/bio_bio_component/bio_bio_component_widget.dart';
import '/setup_componetized/bio_prompts_component/bio_prompts_component_widget.dart';
import '/setup_componetized/birthday_component/birthday_component_widget.dart';
import '/setup_componetized/experience_level_component/experience_level_component_widget.dart';
import '/setup_componetized/images_component/images_component_widget.dart';
import '/setup_componetized/location_component/location_component_widget.dart';
import '/setup_componetized/men_extra_rules_callback/men_extra_rules_callback_widget.dart';
import '/setup_componetized/men_extra_rules_doubt_callback/men_extra_rules_doubt_callback_widget.dart';
import '/setup_componetized/name_component/name_component_widget.dart';
import '/setup_componetized/name_confirmation_alert_callback/name_confirmation_alert_callback_widget.dart';
import '/setup_componetized/own_gender_component/own_gender_component_widget.dart';
import '/setup_componetized/preferred_gender_component/preferred_gender_component_widget.dart';
import '/setup_componetized/relationship_types_component/relationship_types_component_widget.dart';
import '/setup_componetized/role_component/role_component_widget.dart';
import '/setup_componetized/women_extra_rules_doubt_callback/women_extra_rules_doubt_callback_widget.dart';
import '/setup_componetized/women_extra_rules_new_callback/women_extra_rules_new_callback_widget.dart';
import 'package:rive/rive.dart' hide LinearGradient;
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'sign_up_l_a_model.dart';
export 'sign_up_l_a_model.dart';
import 'package:flutter_branch_sdk/flutter_branch_sdk.dart';
import 'package:cloud_functions/cloud_functions.dart';

class SignUpLAWidget extends StatefulWidget {
  const SignUpLAWidget({super.key});

  @override
  State<SignUpLAWidget> createState() => _SignUpLAWidgetState();
}

class _SignUpLAWidgetState extends State<SignUpLAWidget> {
  late SignUpLAModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => SignUpLAModel());
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      if (currentUserDocument?.nextSignUpStage == SetUpStage.name) {
        await _model.pageViewController?.animateToPage(
          1,
          duration: const Duration(milliseconds: 1),
          curve: Curves.linear,
        );
      } else {
        if (currentUserDocument?.nextSignUpStage == SetUpStage.birthday) {
          await _model.pageViewController?.animateToPage(
            2,
            duration: const Duration(milliseconds: 1),
            curve: Curves.linear,
          );
        } else {
          if (currentUserDocument?.nextSignUpStage == SetUpStage.genderPrefs) {
            await _model.pageViewController?.animateToPage(
              3,
              duration: const Duration(milliseconds: 1),
              curve: Curves.linear,
            );
          }
        }
      }
    });
    
  }

  late StateMachineController _controller;
  SMITrigger? _start1;
  SMITrigger? _start2;
  SMITrigger? _start3;
  SMITrigger? _start4;
  SMITrigger? _next1;
  SMITrigger? _next2;
  SMITrigger? _next3;
  SMITrigger? _next4;
  SMITrigger? _next5;
  SMITrigger? _next6;
  SMITrigger? _next7;
  SMITrigger? _next8;
  SMITrigger? _next9;
  SMITrigger? _back10;
  SMITrigger? _back2;
  SMITrigger? _back3;
  SMITrigger? _back4;
  SMITrigger? _back5;
  SMITrigger? _back6;
  SMITrigger? _back7;
  SMITrigger? _back8;
  SMITrigger? _back9;

  void _onInit(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) {
    art.addController(ctrl);
    _controller = ctrl;
    _start1 = ctrl.getTriggerInput('start_1');
    _start2 = ctrl.getTriggerInput('start_2');
    _start3 = ctrl.getTriggerInput('start_3');
    _start4 = ctrl.getTriggerInput('start_4');
    _back10 = ctrl.getTriggerInput('back_10');
    _back2 = ctrl.getTriggerInput('back_2');
    _back3 = ctrl.getTriggerInput('back_3');
    _back4 = ctrl.getTriggerInput('back_4');
    _back5 = ctrl.getTriggerInput('back_5');
    _back6 = ctrl.getTriggerInput('back_6');
    _back7 = ctrl.getTriggerInput('back_7');
    _back8 = ctrl.getTriggerInput('back_8');
    _back9 = ctrl.getTriggerInput('back_9');
    _next1 = ctrl.getTriggerInput('next_1');
    _next2 = ctrl.getTriggerInput('next_2');
    _next3 = ctrl.getTriggerInput('next_3');
    _next4 = ctrl.getTriggerInput('next_4');
    _next5 = ctrl.getTriggerInput('next_5');
    _next6 = ctrl.getTriggerInput('next_6');
    _next7 = ctrl.getTriggerInput('next_7');
    _next8 = ctrl.getTriggerInput('next_8');
    _next9 = ctrl.getTriggerInput('next_9');
    _controller.isActive = true;
    if (currentUserDocument?.nextSignUpStage == SetUpStage.name) {
        _start2?.fire();
      } else {
        if (currentUserDocument?.nextSignUpStage == SetUpStage.birthday) {
          _start3?.fire();
        } else {
          if (currentUserDocument?.nextSignUpStage == SetUpStage.genderPrefs) {
           _start4?.fire();
          } else {
            _start1?.fire();
          }
        }
      }
     // Keep it inactive initially
  }

  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: WillPopScope(
        onWillPop: () async => false,
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          key: scaffoldKey,
          backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
          body: Stack(
            children: [
              Align(
                alignment: const AlignmentDirectional(0.0, 0.0),
                child: Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 75),
                  child: Container(
                    width: MediaQuery.sizeOf(context).width,
                    decoration: const BoxDecoration(),
                    child: Container(
                      child: RiveAnimation.asset(
                        'assets/rive_animations/lineart_signup_simple.riv',
                        artboard: 'lineart_signup_simple',
                        fit: BoxFit.fitWidth,
                        alignment: Alignment.bottomCenter,
                        onInit: _onInit,
                      ),
                    ),
                  ),
                ),
              ),
              Container(
                  width: MediaQuery.sizeOf(context).width * 1.0,
                  height: MediaQuery.sizeOf(context).height * 0.66,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.white, Color(0x00FFFFFF)],
                      stops: [0.0, 1.0],
                      begin: AlignmentDirectional(0.0, -1.0),
                      end: AlignmentDirectional(0, 1.0),
                    ),
                  ),
                ),
              Scaffold(
                resizeToAvoidBottomInset: true,
                backgroundColor: Colors.transparent,
                body: SafeArea(
                  child: SizedBox(
                    width: MediaQuery.sizeOf(context).width * 1.0,
                    height: MediaQuery.sizeOf(context).height * 1.0,
                    child: PageView(
                      physics: const NeverScrollableScrollPhysics(),
                      controller: _model.pageViewController ??=
                          PageController(initialPage: 0),
                      onPageChanged: (_) => safeSetState(() {}),
                      scrollDirection: Axis.horizontal,
                      children: [
                        wrapWithModel(
                          model: _model.ownGenderComponentModel,
                          updateCallback: () => safeSetState(() {}),
                          updateOnChange: true,
                          child: OwnGenderComponentWidget(
                            nextCallback: () async {
                              if (currentUserDocument?.gender == Gender.Female) {
                                await showModalBottomSheet(
                                  isScrollControlled: true,
                                  backgroundColor: Colors.transparent,
                                  enableDrag: false,
                                  context: context,
                                  builder: (context) {
                                    return GestureDetector(
                                      onTap: () =>
                                          FocusScope.of(context).unfocus(),
                                      child: Padding(
                                        padding: MediaQuery.viewInsetsOf(context),
                                        child: WomenExtraRulesNewCallbackWidget(
                                          nextCallback: () async {
                                            Navigator.pop(context);
                                            _model.pageViewController?.nextPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.linear,
                              );
                              _next1?.fire();
                                          },
                                          doubtCallback: () async {
                                            Navigator.pop(context);
                                            await showModalBottomSheet(
                                              isScrollControlled: true,
                                              backgroundColor: Colors.transparent,
                                              enableDrag: false,
                                              context: context,
                                              builder: (context) {
                                                return GestureDetector(
                                                  onTap: () =>
                                                      FocusScope.of(context)
                                                          .unfocus(),
                                                  child: Padding(
                                                    padding:
                                                        MediaQuery.viewInsetsOf(
                                                            context),
                                                    child:
                                                        WomenExtraRulesDoubtCallbackWidget(
                                                      nextCallback: () async {
                                                        Navigator.pop(context);
                                                        _model.pageViewController?.nextPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.linear,
                              );
                              _next1?.fire();
                                                      },
                                                    ),
                                                  ),
                                                );
                                              },
                                            ).then(
                                                (value) => safeSetState(() {}));
                                          },
                                        ),
                                      ),
                                    );
                                  },
                                ).then((value) => safeSetState(() {}));
                              } else {
                                await showModalBottomSheet(
                                  isScrollControlled: true,
                                  backgroundColor: Colors.transparent,
                                  enableDrag: false,
                                  context: context,
                                  builder: (context) {
                                    return GestureDetector(
                                      onTap: () =>
                                          FocusScope.of(context).unfocus(),
                                      child: Padding(
                                        padding: MediaQuery.viewInsetsOf(context),
                                        child: MenExtraRulesCallbackWidget(
                                          nextCallback: () async {
                                            Navigator.pop(context);
                                            _model.pageViewController?.nextPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.linear,
                              );
                              _next1?.fire();
                                          },
                                          doubtCallback: () async {
                                            Navigator.pop(context);
                                            await showModalBottomSheet(
                                              isScrollControlled: true,
                                              backgroundColor: Colors.transparent,
                                              enableDrag: false,
                                              context: context,
                                              builder: (context) {
                                                return GestureDetector(
                                                  onTap: () =>
                                                      FocusScope.of(context)
                                                          .unfocus(),
                                                  child: Padding(
                                                    padding:
                                                        MediaQuery.viewInsetsOf(
                                                            context),
                                                    child:
                                                        MenExtraRulesDoubtCallbackWidget(
                                                      nextCallback: () async {
                                                        Navigator.pop(context);
                                                       _model.pageViewController?.nextPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.linear,
                              );
                              _next1?.fire();
                                                      },
                                                    ),
                                                  ),
                                                );
                                              },
                                            ).then(
                                                (value) => safeSetState(() {}));
                                          },
                                        ),
                                      ),
                                    );
                                  },
                                ).then((value) => safeSetState(() {}));
                              }
                              },
                          ),
                        ),
                        Builder(
                          builder: (context) => wrapWithModel(
                            model: _model.nameComponentModel,
                            updateCallback: () => safeSetState(() {}),
                            updateOnChange: true,
                            child: NameComponentWidget(
                              nextCallback: () async {
                                await showDialog(
                                  context: context,
                                  builder: (dialogContext) {
                                    return Dialog(
                                      elevation: 0,
                                      insetPadding: EdgeInsets.zero,
                                      backgroundColor: Colors.transparent,
                                      alignment: const AlignmentDirectional(0.0, 0.0)
                                          .resolve(Directionality.of(context)),
                                      child: GestureDetector(
                                        onTap: () => FocusScope.of(dialogContext)
                                            .unfocus(),
                                        child:
                                            NameConfirmationAlertCallbackWidget(
                                          name: _model.nameComponentModel
                                              .textController.text,
                                          nextCallback: () async {
                                            Navigator.pop(context);
                                            _model.pageViewController?.nextPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.linear,
                              );
                                            _next2?.fire();
                                            
                                          },
                                          editNameCallback: () async {
                                            Navigator.pop(context);
                                          },
                                        ),
                                      ),
                                    );
                                  },
                                );
                              },
                            ),
                          ),
                        ),
                        wrapWithModel(
                          model: _model.birthdayComponentModel,
                          updateCallback: () => safeSetState(() {}),
                          updateOnChange: true,
                          child: BirthdayComponentWidget(
                            nextCallback: () async {
                              _model.pageViewController?.nextPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.linear,
                              );
                               _next3?.fire();
                            },
                            backCallback: () async {
                              _back3?.fire();
                              await Future.delayed(const Duration(milliseconds: 916));
                               _model.pageViewController?.previousPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.linear,
                              );
                            },
                          ),
                        ),
                        wrapWithModel(
                          model: _model.preferredGenderComponentModel,
                          updateCallback: () => safeSetState(() {}),
                          updateOnChange: true,
                          child: PreferredGenderComponentWidget(
                            nextCallback: () async {
                              _model.pageViewController?.nextPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.linear,
                              );
                               _next4?.fire();
                            },
                          ),
                        ),
                        wrapWithModel(
                          model: _model.roleComponentModel,
                          updateCallback: () => safeSetState(() {}),
                          updateOnChange: true,
                          child: RoleComponentWidget(
                            nextCallback: () async {
                              _model.pageViewController?.nextPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.linear,
                              );
                               _next5?.fire();
                            },
                            backCallback: () async {
                              
                              _back5?.fire();
                              await Future.delayed(const Duration(milliseconds: 916));
                              _model.pageViewController?.previousPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.linear,
                              );
                              
                               
                            },
                          ),
                        ),
                        wrapWithModel(
                          model: _model.experienceLevelComponentModel,
                          updateCallback: () => safeSetState(() {}),
                          updateOnChange: true,
                          child: ExperienceLevelComponentWidget(
                            nextCallback: () async {
                              _model.pageViewController?.nextPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.linear,
                              );
                               _next6?.fire();
                            },
                            backCallback: () async {
                              
                               _back6?.fire();
                               await Future.delayed(const Duration(milliseconds: 916));
                              _model.pageViewController?.previousPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.linear,
                              );
                            },
                          ),
                        ),
                        wrapWithModel(
                          model: _model.relationshipTypesComponentModel,
                          updateCallback: () => safeSetState(() {}),
                          updateOnChange: true,
                          child: RelationshipTypesComponentWidget(
                            nextCallback: () async {
                              _model.pageViewController?.nextPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.linear,
                              );
                               _next7?.fire();
                            },
                            backCallback: () async {
                              
                               _back7?.fire();
                               await Future.delayed(const Duration(milliseconds: 916));
                              _model.pageViewController?.previousPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.linear,
                              );
                            },
                          ),
                        ),
                        wrapWithModel(
                          model: _model.imagesComponentModel,
                          updateCallback: () => safeSetState(() {}),
                          updateOnChange: true,
                          child: ImagesComponentWidget(
                            backCallback: () async {
                              
                               
                               _back8?.fire();
                               await Future.delayed(const Duration(milliseconds: 916));
                              _model.pageViewController?.previousPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.linear,
                              );
                            },
                            nextCallback: () async {
                
                               _model.pageViewController?.nextPage(
                                      duration: const Duration(milliseconds: 300),
                                      curve: Curves.linear,
                                    );
                               
                               _next8?.fire();
                               
                            },
                          ),
                        ),
                        Builder(
                          builder: (context) {
                            if (_model.bioBioShown) {
                              return wrapWithModel(
                                model: _model.bioBioComponentModel,
                                updateCallback: () => safeSetState(() {}),
                                updateOnChange: true,
                                child: BioBioComponentWidget(
                                  nextCallback: () async {
                                    _model.pageViewController?.nextPage(
                                      duration: const Duration(milliseconds: 300),
                                      curve: Curves.linear,
                                    );
                                     _next9?.fire();
                                  },
                                  doLaterCallback: () async {
                                    _model.pageViewController?.nextPage(
                                      duration: const Duration(milliseconds: 300),
                                      curve: Curves.linear,
                                    );
                                     _next9?.fire();
                                  },
                                  switchBioCallback: () async {
                                    _model.bioBioShown = false;
                                    safeSetState(() {});
                                  },
                                  backCallback: () async {
                                    
                                    _back9?.fire();
                                    await Future.delayed(const Duration(milliseconds: 916));
                              _model.pageViewController?.previousPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.linear,
                              );
                
                                  },
                                ),
                              );
                            } else {
                              return wrapWithModel(
                                model: _model.bioPromptsComponentModel,
                                updateCallback: () => safeSetState(() {}),
                                updateOnChange: true,
                                child: BioPromptsComponentWidget(
                                  nextCallback: () async {
                                    _model.pageViewController?.nextPage(
                                      duration: const Duration(milliseconds: 300),
                                      curve: Curves.linear,
                                    );
                                     _next9?.fire();
                                  },
                                  backCallback: () async {
                                     
                                    _back9?.fire();
                                    await Future.delayed(const Duration(milliseconds: 916));
                              _model.pageViewController?.previousPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.linear,
                              );
                                  },
                                  bioSwitchCallback: () async {
                                    _model.bioBioShown = true;
                                    safeSetState(() {});
                                  },
                                  doLaterCallback: () async {
                                     _model.pageViewController?.nextPage(
                                      duration: const Duration(milliseconds: 300),
                                      curve: Curves.linear,
                                    );
                                    _next9?.fire();
                                  },
                                ),
                              );
                            }
                          },
                        ),
                         Builder(
                        builder: (context) => wrapWithModel(
                          model: _model.locationComponentModel,
                          updateCallback: () => safeSetState(() {}),
                          updateOnChange: true,
                          child: LocationComponentWidget(
                            nextCallback: () async {
                              if (currentUserDocument?.gender == Gender.Male) {
                                try {
                                  final result = await FirebaseFunctions
                                          .instanceFor(region: 'europe-west2')
                                      .httpsCallable('addNewUserWaitingList')
                                      .call({});
                                  _model.addWaitingListCf =
                                      AddNewUserWaitingListCloudFunctionCallResponse(
                                    succeeded: true,
                                  );
                                } on FirebaseFunctionsException catch (error) {
                                  _model.addWaitingListCf =
                                      AddNewUserWaitingListCloudFunctionCallResponse(
                                    errorCode: error.code,
                                    succeeded: false,
                                  );
                                }
                                if (_model.addWaitingListCf!.succeeded!) {
                                  try {
                      BranchEvent eventLoca0 = BranchEvent.customEvent('put_on_waitlist'); 
                      FlutterBranchSdk.trackContentWithoutBuo(branchEvent: eventLoca0);

                      BranchEvent eventLoca1 = BranchEvent.customEvent('navigated_location_to_discovery'); 
                      FlutterBranchSdk.trackContentWithoutBuo(branchEvent: eventLoca1);

                          } catch(e) {}
                                  context.goNamed('Discovery');
                                } else {
                                  await showDialog(
                                    context: context,
                                    builder: (dialogContext) {
                                      return Dialog(
                                        elevation: 0,
                                        insetPadding: EdgeInsets.zero,
                                        backgroundColor: Colors.transparent,
                                        alignment:
                                            const AlignmentDirectional(0.0, 0.0)
                                                .resolve(
                                                    Directionality.of(context)),
                                        child: GestureDetector(
                                          onTap: () =>
                                              FocusScope.of(dialogContext)
                                                  .unfocus(),
                                          child: const GeneralPopupWidget(
                                            alertTitle: 'Something went wrong',
                                            alertText:
                                                'Please try again or contact <NAME_EMAIL>',
                                          ),
                                        ),
                                      );
                                    },
                                  );
                                }
                              } else {
                                try {
                      BranchEvent eventLoca1 = BranchEvent.customEvent('navigated_location_to_discovery'); 
                      FlutterBranchSdk.trackContentWithoutBuo(branchEvent: eventLoca1);

                          } catch(e) {}
                                await currentUserReference!
                                    .update(createUsersRecordData(
                                  verificationPendingSignUp: true,
                                ));
                                context.goNamed('Discovery');
                              }
                              safeSetState(() {});
                            },
                            backCallback: () async {
                              await _model.pageViewController?.previousPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.linear,
                              );
                            },
                          ),
                        ),
                         ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
