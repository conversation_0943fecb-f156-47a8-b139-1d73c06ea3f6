import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'big_button_selector_gender_model.dart';
export 'big_button_selector_gender_model.dart';

class BigButtonSelectorGenderWidget extends StatefulWidget {
  const BigButtonSelectorGenderWidget({
    super.key,
    required this.options,
  });

  final List<String>? options;

  @override
  State<BigButtonSelectorGenderWidget> createState() =>
      _BigButtonSelectorGenderWidgetState();
}

class _BigButtonSelectorGenderWidgetState
    extends State<BigButtonSelectorGenderWidget> {
  late BigButtonSelectorGenderModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => BigButtonSelectorGenderModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return Builder(
      builder: (context) {
        final options = widget.options!.toList();
        return Column(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: List.generate(options.length, (optionsIndex) {
            final optionsItem = options[optionsIndex];
            return Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 21.0),
              child: InkWell(
                splashColor: Colors.transparent,
                focusColor: Colors.transparent,
                hoverColor: Colors.transparent,
                highlightColor: Colors.transparent,
                onTap: () async {
                  setState(() {});
                },
                child: Material(
                  color: Colors.transparent,
                  elevation: 0.0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(100.0),
                  ),
                  child: Container(
                    height: 52.0,
                    constraints: const BoxConstraints(
                      maxWidth: 400.0,
                    ),
                    decoration: BoxDecoration(
                      color: FlutterFlowTheme.of(context).primaryBackground,
                      borderRadius: BorderRadius.circular(100.0),
                      border: Border.all(
                        width: 2.0,
                      ),
                    ),
                    child: Align(
                      alignment: const AlignmentDirectional(0.0, 0.0),
                      child: Text(
                        optionsItem,
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'BT Beau Sans',
                              color: FlutterFlowTheme.of(context).secondaryText,
                              fontSize: 19.0,
                              fontWeight: FontWeight.bold,
                              useGoogleFonts: false,
                            ),
                      ),
                    ),
                  ),
                ),
              ),
            );
          }),
        );
      },
    );
  }
}
