import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
import '/components/prompt_filled_in_widget.dart';
import '/components/prompt_n_e_widget.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/link/link_widget.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'bio_men_model.dart';
export 'bio_men_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';

class BioMenWidget extends StatefulWidget {
  const BioMenWidget({super.key});

  @override
  State<BioMenWidget> createState() => _BioMenWidgetState();
}

class _BioMenWidgetState extends State<BioMenWidget> {
  late BioMenModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => BioMenModel());
    logFirebaseEvent('screen_view', parameters: {'screen_name': 'BioMen'});

    // On page load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      if (currentUserDocument?.gender == Gender.Female) {
        context.pushNamed('BioWomen');
      } else {
        _model.ownPublicProfileMen = await PublicProfileRecord.getDocumentOnce(
            currentUserDocument!.publicProfile!);
        if (_model.ownPublicProfileMen?.bio != null &&
            _model.ownPublicProfileMen?.bio != '') {
          setState(() {
            _model.preBio = _model.ownPublicProfileMen!.bio;
          });
        } else {
          setState(() {
            _model.preBio = '';
          });
        }
      }
    });

    _model.textController ??= TextEditingController(text: _model.preBio);
    _model.textFieldFocusNode ??= FocusNode();
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return StreamBuilder<List<PublicProfileRecord>>(
      stream: queryPublicProfileRecord(
        parent: currentUserReference,
        singleRecord: true,
      ),
      builder: (context, snapshot) {
        // Customize what your widget looks like when it's loading.
        if (!snapshot.hasData) {
          return Scaffold(
            backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
            body: Center(
              child: SizedBox(
                width: 50.0,
                height: 50.0,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    FlutterFlowTheme.of(context).accent2,
                  ),
                ),
              ),
            ),
          );
        }
        List<PublicProfileRecord> bioMenPublicProfileRecordList =
            snapshot.data!;
        // Return an empty Container when the item does not exist.
        if (snapshot.data!.isEmpty) {
          return Container();
        }
        final bioMenPublicProfileRecord =
            bioMenPublicProfileRecordList.isNotEmpty
                ? bioMenPublicProfileRecordList.first
                : null;
        return GestureDetector(
          onTap: () => _model.unfocusNode.canRequestFocus
              ? FocusScope.of(context).requestFocus(_model.unfocusNode)
              : FocusScope.of(context).unfocus(),
          child: Scaffold(
            key: scaffoldKey,
            backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
            body: SafeArea(
              top: true,
              child: Stack(
                children: [
                  Align(
                    alignment: const AlignmentDirectional(0.0, -1.0),
                    child: Stack(
                      alignment: const AlignmentDirectional(1.0, -1.0),
                      children: [
                        Container(
                          width: MediaQuery.sizeOf(context).width * 1.0,
                          height: 5.0,
                          decoration: const BoxDecoration(
                            gradient: LinearGradient(
                              colors: [Color(0xFF67B0E5), Color(0xFFF49BD1)],
                              stops: [0.0, 1.0],
                              begin: AlignmentDirectional(1.0, 0.0),
                              end: AlignmentDirectional(-1.0, 0),
                            ),
                          ),
                        ),
                        Container(
                          width: MediaQuery.sizeOf(context).width * 0.09,
                          height: 5.0,
                          decoration: const BoxDecoration(
                            color: Color(0xD4FFFFFF),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Flexible(
                        child: Container(
                          decoration: const BoxDecoration(),
                          child: SingleChildScrollView(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Align(
                                  alignment: const AlignmentDirectional(-1.0, -1.0),
                                  child: Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        20.0, 25.0, 0.0, 0.0),
                                    child: InkWell(
                                      splashColor: Colors.transparent,
                                      focusColor: Colors.transparent,
                                      hoverColor: Colors.transparent,
                                      highlightColor: Colors.transparent,
                                      onTap: () async {
                                        if (getRemoteConfigBool('showEdu')) {
                                          context.goNamed(
                                            'Education',
                                            extra: <String, dynamic>{
                                              kTransitionInfoKey:
                                                  const TransitionInfo(
                                                hasTransition: true,
                                                transitionType:
                                                    PageTransitionType
                                                        .leftToRight,
                                              ),
                                            },
                                          );
                                        } else {
                                          if (getRemoteConfigBool(
                                              'showHobbies')) {
                                            context.goNamed(
                                              'Hobbies',
                                              extra: <String, dynamic>{
                                                kTransitionInfoKey:
                                                    const TransitionInfo(
                                                  hasTransition: true,
                                                  transitionType:
                                                      PageTransitionType
                                                          .leftToRight,
                                                ),
                                              },
                                            );
                                          } else {
                                            context.goNamed(
                                              'RelationshipAims',
                                              extra: <String, dynamic>{
                                                kTransitionInfoKey:
                                                    const TransitionInfo(
                                                  hasTransition: true,
                                                  transitionType:
                                                      PageTransitionType
                                                          .leftToRight,
                                                ),
                                              },
                                            );
                                          }
                                        }
                                      },
                                      child: FaIcon(
                                        FontAwesomeIcons.angleLeft,
                                        color: FlutterFlowTheme.of(context)
                                            .secondaryText,
                                        size: 30.0,
                                      ),
                                    ),
                                  ),
                                ),
                                Align(
                                  alignment: const AlignmentDirectional(-1.0, -1.0),
                                  child: Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        23.0, 20.0, 0.0, 16.0),
                                    child: Text(
                                      'Let\'s write your bio',
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            fontFamily: 'BT Beau Sans',
                                            fontSize: 32.0,
                                            fontWeight: FontWeight.bold,
                                            useGoogleFonts: false,
                                            lineHeight: 1.29,
                                          ),
                                    ),
                                  ),
                                ),
                                Form(
                                  key: _model.formKey,
                                  autovalidateMode: AutovalidateMode.disabled,
                                  child: Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        6.0, 11.0, 6.0, 0.0),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.stretch,
                                      children: [
                                        Align(
                                          alignment:
                                              const AlignmentDirectional(-1.0, -1.0),
                                          child: Padding(
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(
                                                    6.0, 0.0, 0.0, 12.0),
                                            child: Text(
                                              'Part 1: Write a brief paragraph about yourself (optional)',
                                              style: FlutterFlowTheme.of(
                                                      context)
                                                  .bodyMedium
                                                  .override(
                                                    fontFamily: 'BT Beau Sans',
                                                    fontSize: 16.0,
                                                    fontWeight: FontWeight.w600,
                                                    useGoogleFonts: false,
                                                  ),
                                            ),
                                          ),
                                        ),
                                        Padding(
                                          padding:
                                              const EdgeInsetsDirectional.fromSTEB(
                                                  6.0, 0.0, 6.0, 5.0),
                                          child: Container(
                                            constraints: const BoxConstraints(
                                              maxHeight: 156.0,
                                            ),
                                            decoration: BoxDecoration(
                                              color: const Color(0xFFF0F2F4),
                                              borderRadius:
                                                  BorderRadius.circular(10.0),
                                            ),
                                            child: Padding(
                                              padding: const EdgeInsetsDirectional
                                                  .fromSTEB(
                                                      16.0, 0.0, 16.0, 10.0),
                                              child: TextFormField(
                                                controller:
                                                    _model.textController,
                                                focusNode:
                                                    _model.textFieldFocusNode,
                                                onChanged: (_) =>
                                                    EasyDebounce.debounce(
                                                  '_model.textController',
                                                  const Duration(milliseconds: 1),
                                                  () => setState(() {}),
                                                ),
                                                textCapitalization:
                                                    TextCapitalization
                                                        .sentences,
                                                obscureText: false,
                                                decoration: InputDecoration(
                                                  hintText:
                                                      'Tell others something about you...',
                                                  hintStyle:
                                                      FlutterFlowTheme.of(
                                                              context)
                                                          .labelMedium,
                                                  enabledBorder:
                                                      InputBorder.none,
                                                  focusedBorder:
                                                      InputBorder.none,
                                                  errorBorder: InputBorder.none,
                                                  focusedErrorBorder:
                                                      InputBorder.none,
                                                ),
                                                style:
                                                    FlutterFlowTheme.of(context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          fontSize: 16.0,
                                                          useGoogleFonts: false,
                                                        ),
                                                maxLines: null,
                                                maxLength: 500,
                                                maxLengthEnforcement:
                                                    MaxLengthEnforcement
                                                        .enforced,
                                                keyboardType:
                                                    TextInputType.multiline,
                                                validator: _model
                                                    .textControllerValidator
                                                    .asValidator(context),
                                              ),
                                            ),
                                          ),
                                        ),
                                        Padding(
                                          padding:
                                              const EdgeInsetsDirectional.fromSTEB(
                                                  6.0, 11.0, 0.0, 0.0),
                                          child: wrapWithModel(
                                            model: _model.linkModel,
                                            updateCallback: () =>
                                                setState(() {}),
                                            child: LinkWidget(
                                              url:
                                                  'https://www.chyrpe.com/how-to-write-a-great-bio',
                                              title: getRemoteConfigString(
                                                  'bio_tips_info_title'),
                                            ),
                                          ),
                                        ),
                                        Align(
                                          alignment:
                                              const AlignmentDirectional(-1.0, -1.0),
                                          child: Padding(
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(
                                                    6.0, 32.0, 0.0, 12.0),
                                            child: Text(
                                              'Part 2: Select and answer at least one prompt (optional)',
                                              style: FlutterFlowTheme.of(
                                                      context)
                                                  .bodyMedium
                                                  .override(
                                                    fontFamily: 'BT Beau Sans',
                                                    fontSize: 16.0,
                                                    fontWeight: FontWeight.w600,
                                                    useGoogleFonts: false,
                                                  ),
                                            ),
                                          ),
                                        ),
                                        Padding(
                                          padding:
                                              const EdgeInsetsDirectional.fromSTEB(
                                                  6.0, 20.0, 6.0, 0.0),
                                          child: Column(
                                            mainAxisSize: MainAxisSize.max,
                                            children: [
                                              Column(
                                                mainAxisSize: MainAxisSize.max,
                                                children: [
                                                  Padding(
                                                    padding:
                                                        const EdgeInsetsDirectional
                                                            .fromSTEB(0.0, 0.0,
                                                                0.0, 19.0),
                                                    child: Column(
                                                      mainAxisSize:
                                                          MainAxisSize.max,
                                                      children: [
                                                        if ((bioMenPublicProfileRecord
                                                                    ?.hasPrompts() ==
                                                                false) ||
                                                            (bioMenPublicProfileRecord!
                                                                    .prompts.isEmpty))
                                                          wrapWithModel(
                                                            model: _model
                                                                .promptNEModel1,
                                                            updateCallback:
                                                                () => setState(
                                                                    () {}),
                                                            child:
                                                                const PromptNEWidget(),
                                                          ),
                                                        if ((bioMenPublicProfileRecord
                                                                    ?.hasPrompts() ==
                                                                true) &&
                                                            (bioMenPublicProfileRecord!
                                                                    .prompts.isNotEmpty))
                                                          wrapWithModel(
                                                            model: _model
                                                                .promptFilledInModel1,
                                                            updateCallback:
                                                                () => setState(
                                                                    () {}),
                                                            updateOnChange:
                                                                true,
                                                            child:
                                                                PromptFilledInWidget(
                                                              promptNo: 1,
                                                              publicProfile:
                                                                  bioMenPublicProfileRecord,
                                                            ),
                                                          ),
                                                      ],
                                                    ),
                                                  ),
                                                  Padding(
                                                    padding:
                                                        const EdgeInsetsDirectional
                                                            .fromSTEB(0.0, 0.0,
                                                                0.0, 19.0),
                                                    child: Column(
                                                      mainAxisSize:
                                                          MainAxisSize.max,
                                                      children: [
                                                        if ((bioMenPublicProfileRecord
                                                                    ?.hasPrompts() ==
                                                                false) ||
                                                            (bioMenPublicProfileRecord!
                                                                    .prompts
                                                                    .length <=
                                                                1))
                                                          wrapWithModel(
                                                            model: _model
                                                                .promptNEModel2,
                                                            updateCallback:
                                                                () => setState(
                                                                    () {}),
                                                            child:
                                                                const PromptNEWidget(),
                                                          ),
                                                        if ((bioMenPublicProfileRecord
                                                                    ?.hasPrompts() ==
                                                                true) &&
                                                            (bioMenPublicProfileRecord!
                                                                    .prompts
                                                                    .length >=
                                                                2))
                                                          wrapWithModel(
                                                            model: _model
                                                                .promptFilledInModel2,
                                                            updateCallback:
                                                                () => setState(
                                                                    () {}),
                                                            updateOnChange:
                                                                true,
                                                            child:
                                                                PromptFilledInWidget(
                                                              promptNo: 2,
                                                              publicProfile:
                                                                  bioMenPublicProfileRecord,
                                                            ),
                                                          ),
                                                      ],
                                                    ),
                                                  ),
                                                  Padding(
                                                    padding:
                                                        const EdgeInsetsDirectional
                                                            .fromSTEB(0.0, 0.0,
                                                                0.0, 19.0),
                                                    child: Column(
                                                      mainAxisSize:
                                                          MainAxisSize.max,
                                                      children: [
                                                        if ((bioMenPublicProfileRecord
                                                                    ?.hasPrompts() ==
                                                                false) ||
                                                            (bioMenPublicProfileRecord!
                                                                    .prompts
                                                                    .length <=
                                                                2))
                                                          wrapWithModel(
                                                            model: _model
                                                                .promptNEModel3,
                                                            updateCallback:
                                                                () => setState(
                                                                    () {}),
                                                            child:
                                                                const PromptNEWidget(),
                                                          ),
                                                        if ((bioMenPublicProfileRecord
                                                                    ?.hasPrompts() ==
                                                                true) &&
                                                            (bioMenPublicProfileRecord!
                                                                    .prompts
                                                                    .length >=
                                                                3))
                                                          wrapWithModel(
                                                            model: _model
                                                                .promptFilledInModel3,
                                                            updateCallback:
                                                                () => setState(
                                                                    () {}),
                                                            updateOnChange:
                                                                true,
                                                            child:
                                                                PromptFilledInWidget(
                                                              promptNo: 3,
                                                              publicProfile:
                                                                  bioMenPublicProfileRecord,
                                                            ),
                                                          ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsetsDirectional.fromSTEB(
                            27.0, 0.0, 27.0, 20.0),
                        child: wrapWithModel(
                          model: _model.gradientButtonModel,
                          updateCallback: () => setState(() {}),
                          child: GradientButtonWidget(
                            title: 'Next',
                            action: () async {
                              // Set bio to public profile

                              analytics.setUserProperties({'Bio Length': _model.textController.text.length, "Prompts": bioMenPublicProfileRecord?.prompts.length ?? 0});
                              analytics.logEvent('Sign Up: Entered Bio', eventProperties: {'Length in Chars': _model.textController.text.length, 'Prompts Added Count': bioMenPublicProfileRecord?.prompts.length ?? 0});

                              await currentUserDocument!.publicProfile!
                                  .update(createPublicProfileRecordData(
                                bio: _model.textController.text,
                              ));

                              context.pushNamed('Images');
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
