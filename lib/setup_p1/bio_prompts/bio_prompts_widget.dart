import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
import '/components/prompt_filled_in_widget.dart';
import '/components/prompt_n_e_widget.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/gradient_button_disabled/gradient_button_disabled_widget.dart';
import '/general/lightbulb_tip/lightbulb_tip_widget.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'bio_prompts_model.dart';
export 'bio_prompts_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';

class BioPromptsWidget extends StatefulWidget {
  const BioPromptsWidget({super.key});

  @override
  State<BioPromptsWidget> createState() => _BioPromptsWidgetState();
}

class _BioPromptsWidgetState extends State<BioPromptsWidget> {
  late BioPromptsModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => BioPromptsModel());
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<PublicProfileRecord>>(
      stream: queryPublicProfileRecord(
        parent: currentUserReference,
        singleRecord: true,
      ),
      builder: (context, snapshot) {
        // Customize what your widget looks like when it's loading.
        if (!snapshot.hasData) {
          return Scaffold(
            backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
            body: Center(
              child: SizedBox(
                width: 50.0,
                height: 50.0,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    FlutterFlowTheme.of(context).accent2,
                  ),
                ),
              ),
            ),
          );
        }
        List<PublicProfileRecord> bioPromptsPublicProfileRecordList =
            snapshot.data!;
        // Return an empty Container when the item does not exist.
        if (snapshot.data!.isEmpty) {
          return Container();
        }
        final bioPromptsPublicProfileRecord =
            bioPromptsPublicProfileRecordList.isNotEmpty
                ? bioPromptsPublicProfileRecordList.first
                : null;

        return GestureDetector(
          onTap: () => FocusScope.of(context).unfocus(),
          child: Scaffold(
            key: scaffoldKey,
            backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
            body: SafeArea(
              top: true,
              child: Stack(
                children: [
                  Align(
                    alignment: const AlignmentDirectional(0.0, -1.0),
                    child: Stack(
                      alignment: const AlignmentDirectional(1.0, -1.0),
                      children: [
                        Container(
                          width: MediaQuery.sizeOf(context).width * 1.0,
                          height: 5.0,
                          decoration: const BoxDecoration(
                            gradient: LinearGradient(
                              colors: [Color(0xFF67B0E5), Color(0xFFF49BD1)],
                              stops: [0.0, 1.0],
                              begin: AlignmentDirectional(1.0, 0.0),
                              end: AlignmentDirectional(-1.0, 0),
                            ),
                          ),
                        ),
                        Container(
                          width: MediaQuery.sizeOf(context).width * 0.03,
                          height: 5.0,
                          decoration: const BoxDecoration(
                            color: Color(0xD4FFFFFF),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Flexible(
                        child: Container(
                          decoration: const BoxDecoration(),
                          child: SingleChildScrollView(
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      10.0, 10.0, 0.0, 0.0),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Align(
                                        alignment:
                                            const AlignmentDirectional(-1.0, 0.0),
                                        child: FlutterFlowIconButton(
                                          borderColor: const Color(0x00FFFFFF),
                                          borderRadius: 20.0,
                                          borderWidth: 1.0,
                                          buttonSize: 40.0,
                                          fillColor: const Color(0x00FFFFFF),
                                          icon: FaIcon(
                                            FontAwesomeIcons.angleLeft,
                                            color: FlutterFlowTheme.of(context)
                                                .primaryText,
                                            size: 24.0,
                                          ),
                                          onPressed: () async {
                                            context.goNamed(
                                              'Images',
                                              extra: <String, dynamic>{
                                                kTransitionInfoKey:
                                                    const TransitionInfo(
                                                  hasTransition: true,
                                                  transitionType:
                                                      PageTransitionType
                                                          .leftToRight,
                                                ),
                                              },
                                            );
                                          },
                                        ),
                                      ),
                                      Align(
                                        alignment:
                                            const AlignmentDirectional(1.0, 0.0),
                                        child: FFButtonWidget(
                                          onPressed: () async {
                                            analytics.logEvent('Sign Up: Went To Bio Text from Bio Prompts');
                                            context.goNamed(
                                              'BioBio',
                                              extra: <String, dynamic>{
                                                kTransitionInfoKey:
                                                    const TransitionInfo(
                                                  hasTransition: true,
                                                  transitionType:
                                                      PageTransitionType
                                                          .bottomToTop,
                                                ),
                                              },
                                            );
                                          },
                                          text: getRemoteConfigString(
                                              'bio_prompts_bio_instead'),
                                          options: FFButtonOptions(
                                            height: 40.0,
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(
                                                    24.0, 0.0, 24.0, 0.0),
                                            iconPadding:
                                                const EdgeInsetsDirectional.fromSTEB(
                                                    0.0, 0.0, 0.0, 0.0),
                                            color: Colors.white,
                                            textStyle: FlutterFlowTheme.of(
                                                    context)
                                                .titleSmall
                                                .override(
                                                  fontFamily: 'BT Beau Sans',
                                                  color: FlutterFlowTheme.of(
                                                          context)
                                                      .secondaryText,
                                                  fontSize: 14.0,
                                                  letterSpacing: 0.0,
                                                  useGoogleFonts: false,
                                                ),
                                            elevation: 0.0,
                                            borderSide: const BorderSide(
                                              color: Colors.transparent,
                                              width: 1.0,
                                            ),
                                            borderRadius:
                                                BorderRadius.circular(8.0),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Align(
                                  alignment: const AlignmentDirectional(-1.0, -1.0),
                                  child: Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        20.0, 20.0, 20.0, 16.0),
                                    child: Text(
                                      getRemoteConfigString(
                                          'bio_prompts_headline'),
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            fontFamily: 'BT Beau Sans',
                                            fontSize: 32.0,
                                            letterSpacing: 0.0,
                                            fontWeight: FontWeight.bold,
                                            useGoogleFonts: false,
                                            lineHeight: 1.29,
                                          ),
                                    ),
                                  ),
                                ),
                                Form(
                                  key: _model.formKey,
                                  autovalidateMode: AutovalidateMode.disabled,
                                  child: Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        6.0, 11.0, 6.0, 0.0),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.stretch,
                                      children: [
                                        Align(
                                          alignment:
                                              const AlignmentDirectional(-1.0, -1.0),
                                          child: Padding(
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(
                                                    14.0, 0.0, 0.0, 12.0),
                                            child: Text(
                                              getRemoteConfigString(
                                                  'bio_prompts_description'),
                                              style: FlutterFlowTheme.of(
                                                      context)
                                                  .bodyMedium
                                                  .override(
                                                    fontFamily: 'BT Beau Sans',
                                                    fontSize: 16.0,
                                                    letterSpacing: 0.0,
                                                    fontWeight: FontWeight.w500,
                                                    useGoogleFonts: false,
                                                  ),
                                            ),
                                          ),
                                        ),
                                        Padding(
                                          padding:
                                              const EdgeInsetsDirectional.fromSTEB(
                                                  6.0, 20.0, 6.0, 0.0),
                                          child: Column(
                                            mainAxisSize: MainAxisSize.max,
                                            children: [
                                              Column(
                                                mainAxisSize: MainAxisSize.max,
                                                children: [
                                                  Padding(
                                                    padding:
                                                        const EdgeInsetsDirectional
                                                            .fromSTEB(0.0, 0.0,
                                                                0.0, 19.0),
                                                    child: Column(
                                                      mainAxisSize:
                                                          MainAxisSize.max,
                                                      children: [
                                                        if ((bioPromptsPublicProfileRecord
                                                                    ?.hasPrompts() ==
                                                                false) ||
                                                            (bioPromptsPublicProfileRecord!
                                                                    .prompts.isEmpty))
                                                          wrapWithModel(
                                                            model: _model
                                                                .promptNEModel1,
                                                            updateCallback:
                                                                () => setState(
                                                                    () {}),
                                                            child:
                                                                const PromptNEWidget(),
                                                          ),
                                                        if ((bioPromptsPublicProfileRecord
                                                                    ?.hasPrompts() ==
                                                                true) &&
                                                            (bioPromptsPublicProfileRecord!
                                                                    .prompts.isNotEmpty))
                                                          wrapWithModel(
                                                            model: _model
                                                                .promptFilledInModel1,
                                                            updateCallback:
                                                                () => setState(
                                                                    () {}),
                                                            updateOnChange:
                                                                true,
                                                            child:
                                                                PromptFilledInWidget(
                                                              promptNo: 1,
                                                              publicProfile:
                                                                  bioPromptsPublicProfileRecord,
                                                            ),
                                                          ),
                                                      ],
                                                    ),
                                                  ),
                                                  Padding(
                                                    padding:
                                                        const EdgeInsetsDirectional
                                                            .fromSTEB(0.0, 0.0,
                                                                0.0, 19.0),
                                                    child: Column(
                                                      mainAxisSize:
                                                          MainAxisSize.max,
                                                      children: [
                                                        if ((bioPromptsPublicProfileRecord
                                                                    ?.hasPrompts() ==
                                                                false) ||
                                                            (bioPromptsPublicProfileRecord!
                                                                    .prompts
                                                                    .length <=
                                                                1))
                                                          wrapWithModel(
                                                            model: _model
                                                                .promptNEModel2,
                                                            updateCallback:
                                                                () => setState(
                                                                    () {}),
                                                            child:
                                                                const PromptNEWidget(),
                                                          ),
                                                        if ((bioPromptsPublicProfileRecord
                                                                    ?.hasPrompts() ==
                                                                true) &&
                                                            (bioPromptsPublicProfileRecord!
                                                                    .prompts
                                                                    .length >=
                                                                2))
                                                          wrapWithModel(
                                                            model: _model
                                                                .promptFilledInModel2,
                                                            updateCallback:
                                                                () => setState(
                                                                    () {}),
                                                            updateOnChange:
                                                                true,
                                                            child:
                                                                PromptFilledInWidget(
                                                              promptNo: 2,
                                                              publicProfile:
                                                                  bioPromptsPublicProfileRecord,
                                                            ),
                                                          ),
                                                      ],
                                                    ),
                                                  ),
                                                  Padding(
                                                    padding:
                                                        const EdgeInsetsDirectional
                                                            .fromSTEB(0.0, 0.0,
                                                                0.0, 19.0),
                                                    child: Column(
                                                      mainAxisSize:
                                                          MainAxisSize.max,
                                                      children: [
                                                        if ((bioPromptsPublicProfileRecord
                                                                    ?.hasPrompts() ==
                                                                false) ||
                                                            (bioPromptsPublicProfileRecord!
                                                                    .prompts
                                                                    .length <=
                                                                2))
                                                          wrapWithModel(
                                                            model: _model
                                                                .promptNEModel3,
                                                            updateCallback:
                                                                () => setState(
                                                                    () {}),
                                                            child:
                                                                const PromptNEWidget(),
                                                          ),
                                                        if ((bioPromptsPublicProfileRecord
                                                                    ?.hasPrompts() ==
                                                                true) &&
                                                            (bioPromptsPublicProfileRecord!
                                                                    .prompts
                                                                    .length >=
                                                                3))
                                                          wrapWithModel(
                                                            model: _model
                                                                .promptFilledInModel3,
                                                            updateCallback:
                                                                () => setState(
                                                                    () {}),
                                                            updateOnChange:
                                                                true,
                                                            child:
                                                                PromptFilledInWidget(
                                                              promptNo: 3,
                                                              publicProfile:
                                                                  bioPromptsPublicProfileRecord,
                                                            ),
                                                          ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                        if (bioPromptsPublicProfileRecord
                                                ?.gender ==
                                            Gender.Male)
                                          Align(
                                            alignment: const AlignmentDirectional(
                                                -1.0, -1.0),
                                            child: Padding(
                                              padding: const EdgeInsetsDirectional
                                                  .fromSTEB(
                                                      14.0, 0.0, 0.0, 12.0),
                                              child: Text(
                                                getRemoteConfigString(
                                                    'bio_prompts_min_prompts_info'),
                                                style:
                                                    FlutterFlowTheme.of(context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          color: FlutterFlowTheme
                                                                  .of(context)
                                                              .secondaryText,
                                                          fontSize: 13.0,
                                                          letterSpacing: 0.0,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                          useGoogleFonts: false,
                                                        ),
                                              ),
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsetsDirectional.fromSTEB(
                            20.0, 0.0, 20.0, 20.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Align(
                              alignment: const AlignmentDirectional(0.0, 0.0),
                              child: Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 20.0, 0.0, 23.0),
                                child: wrapWithModel(
                                  model: _model.lightbulbTipModel,
                                  updateCallback: () => safeSetState(() {}),
                                  child: LightbulbTipWidget(
                                    tip: getRemoteConfigString(
                                        'bio_prompts_tip'),
                                  ),
                                ),
                              ),
                            ),
                            if (!((currentUserDocument?.gender ==
                                    Gender.Male) &&
                                (bioPromptsPublicProfileRecord!.prompts.length <
                                    getRemoteConfigInt(
                                        'bio_prompts_min_prompts_m'))))
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 10.0, 0.0, 0.0),
                                child: AuthUserStreamWidget(
                                  builder: (context) => wrapWithModel(
                                    model: _model.gradientButtonModel,
                                    updateCallback: () => safeSetState(() {}),
                                    child: GradientButtonWidget(
                                      title: 'Next',
                                      action: () async {
                                        analytics.setUserProperties({"Prompts": bioPromptsPublicProfileRecord?.prompts.length ?? 0});
                                  analytics.logEvent('Sign Up: Entered Bio', eventProperties: {'Prompts Added Count': bioPromptsPublicProfileRecord?.prompts.length ?? 0});
                                  analytics.logEvent('Sign Up: Entered Bio Prompts', eventProperties: {'Prompts Added Count': bioPromptsPublicProfileRecord?.prompts.length ?? 0});
                                  
                                        if (currentUserDocument?.location !=
                                            null) {
                                          context
                                              .pushNamed('VerificationPrompt');
                                        } else {
                                          if (currentUserDocument
                                                  ?.signUpTestingCohort ==
                                              SignUpTestingCohort
                                                  .Confirmation) {
                                            context.pushNamed(
                                                'BioConfirmationPage');
                                          } else {
                                            context.pushNamed(
                                                'LocationAllowingEntry');
                                          }
                                        }
                                      },
                                    ),
                                  ),
                                ),
                              ),
                            if ((currentUserDocument?.gender == Gender.Male) &&
                                (bioPromptsPublicProfileRecord!.prompts.length <
                                    getRemoteConfigInt(
                                        'bio_prompts_min_prompts_m')))
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 10.0, 0.0, 0.0),
                                child: AuthUserStreamWidget(
                                  builder: (context) => wrapWithModel(
                                    model: _model.gradientButtonDisabledModel,
                                    updateCallback: () => safeSetState(() {}),
                                    child: GradientButtonDisabledWidget(
                                      title: 'Next',
                                      action: () async {},
                                    ),
                                  ),
                                ),
                              ),
                            if (currentUserDocument?.gender == Gender.Female)
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 10.0, 0.0, 0.0),
                                child: AuthUserStreamWidget(
                                  builder: (context) => FFButtonWidget(
                                    onPressed: () async {
                                     analytics.logEvent('Sign Up: Entered Bio Prompts Do Later', eventProperties: {'Prompts Added Count': bioPromptsPublicProfileRecord?.prompts.length ?? 0});

                                      if (currentUserDocument?.location !=
                                          null) {
                                        context.pushNamed('VerificationPrompt');
                                      } else {
                                        if (currentUserDocument
                                                ?.signUpTestingCohort ==
                                            SignUpTestingCohort.Confirmation) {
                                          context
                                              .pushNamed('BioConfirmationPage');
                                        } else {
                                          context.pushNamed(
                                              'LocationAllowingEntry');
                                        }
                                      }
                                    },
                                    text: getRemoteConfigString(
                                        'bio_prompts_do_later_button'),
                                    options: FFButtonOptions(
                                      width: MediaQuery.sizeOf(context).width *
                                          1.0,
                                      height: 50.0,
                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                          24.0, 0.0, 24.0, 0.0),
                                      iconPadding:
                                          const EdgeInsetsDirectional.fromSTEB(
                                              0.0, 0.0, 0.0, 0.0),
                                      color: Colors.white,
                                      textStyle: FlutterFlowTheme.of(context)
                                          .titleSmall
                                          .override(
                                            fontFamily: 'BT Beau Sans',
                                            color: FlutterFlowTheme.of(context)
                                                .secondaryText,
                                            letterSpacing: 0.0,
                                            useGoogleFonts: false,
                                          ),
                                      elevation: 0.0,
                                      borderSide: const BorderSide(
                                        color: Colors.transparent,
                                        width: 1.0,
                                      ),
                                      borderRadius:
                                          BorderRadius.circular(100.0),
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
