import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_radio_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/form_field_controller.dart';
import '/general/gradient_button_small/gradient_button_small_widget.dart';
import 'package:flutter/material.dart';
import 'findom_question_model.dart';
export 'findom_question_model.dart';

class FindomQuestionWidget extends StatefulWidget {
  const FindomQuestionWidget({super.key});

  @override
  State<FindomQuestionWidget> createState() => _FindomQuestionWidgetState();
}

class _FindomQuestionWidgetState extends State<FindomQuestionWidget> {
  late FindomQuestionModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => FindomQuestionModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: const AlignmentDirectional(0.0, 0.0),
      child: Padding(
        padding: const EdgeInsetsDirectional.fromSTEB(20.0, 0.0, 20.0, 0.0),
        child: Container(
          width: MediaQuery.sizeOf(context).width * 1.0,
          height: 420.0,
          constraints: const BoxConstraints(
            maxWidth: 400.0,
          ),
          decoration: BoxDecoration(
            color: FlutterFlowTheme.of(context).secondaryBackground,
            borderRadius: BorderRadius.circular(12.0),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(0.0, 40.0, 0.0, 0.0),
                child: Text(
                  'Are you a Findomme?',
                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                        fontFamily: 'BT Beau Sans',
                        fontSize: 20.0,
                        letterSpacing: 0.0,
                        fontWeight: FontWeight.bold,
                        useGoogleFonts: false,
                      ),
                ),
              ),
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(33.0, 11.0, 33.0, 0.0),
                child: Text(
                  'We allow Findommes and other professionals on the app, but you have to declare it if you are one. No declaration can lead to a ban for life.',
                  textAlign: TextAlign.center,
                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                        fontFamily: 'BT Beau Sans',
                        fontSize: 16.0,
                        letterSpacing: 0.0,
                        useGoogleFonts: false,
                        lineHeight: 1.5,
                      ),
                ),
              ),
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(0.0, 45.0, 0.0, 0.0),
                child: FlutterFlowRadioButton(
                  options: ['Yes', 'No'].toList(),
                  onChanged: (val) => setState(() {}),
                  controller: _model.radioButtonValueController ??=
                      FormFieldController<String>('No'),
                  optionHeight: 32.0,
                  textStyle: FlutterFlowTheme.of(context).labelMedium.override(
                        fontFamily: 'BT Beau Sans',
                        letterSpacing: 0.0,
                        useGoogleFonts: false,
                      ),
                  textPadding:
                      const EdgeInsetsDirectional.fromSTEB(15.0, 0.0, 0.0, 0.0),
                  buttonPosition: RadioButtonPosition.left,
                  direction: Axis.vertical,
                  radioButtonColor: FlutterFlowTheme.of(context).accent2,
                  inactiveRadioButtonColor:
                      FlutterFlowTheme.of(context).secondaryText,
                  toggleable: false,
                  horizontalAlignment: WrapAlignment.start,
                  verticalAlignment: WrapCrossAlignment.start,
                ),
              ),
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(27.0, 45.0, 27.0, 0.0),
                child: InkWell(
                  splashColor: Colors.transparent,
                  focusColor: Colors.transparent,
                  hoverColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  onTap: () async {
                    await currentUserReference!.update(createUsersRecordData(
                      findom: _model.radioButtonValue == 'Yes' ? true : false,
                    ));

                    await currentUserDocument!.publicProfile!
                        .update(createPublicProfileRecordData(
                      findom: _model.radioButtonValue == 'Yes' ? true : false,
                    ));

                    context.pushNamed('RelationshipAims');
                  },
                  child: wrapWithModel(
                    model: _model.gradientButtonSmallModel,
                    updateCallback: () => setState(() {}),
                    child: GradientButtonSmallWidget(
                      title: 'Next',
                      action: () async {
                        await currentUserDocument!.publicProfile!
                            .update(createPublicProfileRecordData(
                          findom:
                              _model.radioButtonValue == 'Yes' ? true : false,
                        ));

                        await currentUserReference!
                            .update(createUsersRecordData(
                          findom:
                              _model.radioButtonValue == 'Yes' ? true : false,
                        ));

                        context.pushNamed('RelationshipAims');
                      },
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
