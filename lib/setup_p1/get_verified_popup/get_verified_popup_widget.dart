import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/backend/firebase_storage/storage.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/upload_data.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import 'dart:async';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'get_verified_popup_model.dart';
export 'get_verified_popup_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';

class GetVerifiedPopupWidget extends StatefulWidget {
  const GetVerifiedPopupWidget({super.key});

  @override
  State<GetVerifiedPopupWidget> createState() => _GetVerifiedPopupWidgetState();
}

class _GetVerifiedPopupWidgetState extends State<GetVerifiedPopupWidget> {
  late GetVerifiedPopupModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => GetVerifiedPopupModel());
    logFirebaseEvent('screen_view', parameters: {'screen_name': 'GetVerifiedPopup'});
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return StreamBuilder<List<OptionsForSelectorsRecord>>(
      stream: queryOptionsForSelectorsRecord(
        queryBuilder: (optionsForSelectorsRecord) =>
            optionsForSelectorsRecord.where(
          'name',
          isEqualTo: 'Gestures for Verifications',
        ),
        singleRecord: true,
      ),
      builder: (context, snapshot) {
        // Customize what your widget looks like when it's loading.
        if (!snapshot.hasData) {
          return Center(
            child: SizedBox(
              width: 50.0,
              height: 50.0,
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  FlutterFlowTheme.of(context).accent2,
                ),
              ),
            ),
          );
        }
        List<OptionsForSelectorsRecord> containerOptionsForSelectorsRecordList =
            snapshot.data!;
        final containerOptionsForSelectorsRecord =
            containerOptionsForSelectorsRecordList.isNotEmpty
                ? containerOptionsForSelectorsRecordList.first
                : null;
        return Container(
          width: MediaQuery.sizeOf(context).width * 1.0,
          height: 550.0,
          decoration: BoxDecoration(
            color: FlutterFlowTheme.of(context).primaryBackground,
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(0.0),
              bottomRight: Radius.circular(0.0),
              topLeft: Radius.circular(25.0),
              topRight: Radius.circular(25.0),
            ),
          ),
          child: Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(7.0, 15.0, 7.0, 0.0),
            child: SafeArea(
              child: Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Align(
                    alignment: const AlignmentDirectional(0.0, 0.0),
                    child: Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(0.0, 1.0, 0.0, 0.0),
                      child: Stack(
                        alignment: const AlignmentDirectional(0.0, 0.0),
                        children: [
                          Align(
                            alignment: const AlignmentDirectional(0.0, 0.0),
                            child: Text(
                              'Get verified',
                              style: FlutterFlowTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'BT Beau Sans',
                                    fontSize: 20.0,
                                    letterSpacing: 0.0,
                                    fontWeight: FontWeight.bold,
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ),
                          Align(
                            alignment: const AlignmentDirectional(-1.0, 0.0),
                            child: Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  10.0, 0.0, 0.0, 0.0),
                              child: FlutterFlowIconButton(
                                borderColor: const Color(0x004B39EF),
                                borderRadius: 20.0,
                                borderWidth: 1.0,
                                buttonSize: 40.0,
                                fillColor: const Color(0x004B39EF),
                                icon: Icon(
                                  Icons.close_rounded,
                                  color: FlutterFlowTheme.of(context).primaryText,
                                  size: 24.0,
                                ),
                                onPressed: () async {
                                  Navigator.pop(context);
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Padding(
                    padding:
                        const EdgeInsetsDirectional.fromSTEB(50.0, 16.0, 50.0, 0.0),
                    child: Text(
                      'Please take a clear selfie in daylight, with your face visible, on which you perform the following gesture:',
                      textAlign: TextAlign.center,
                      style: FlutterFlowTheme.of(context).bodyMedium.override(
                            fontFamily: 'BT Beau Sans',
                            fontSize: 16.0,
                            useGoogleFonts: false,
                            lineHeight: 1.29,
                          ),
                    ),
                  ),
                  Flexible(
                    child: Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(0.0, 15.0, 0.0, 0.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: 300.0,
                            height: 170.0,
                            decoration: BoxDecoration(
                              color: FlutterFlowTheme.of(context)
                                  .secondaryBackground,
                              image: DecorationImage(
                                fit: BoxFit.contain,
                                image: Image.network(
                                  containerOptionsForSelectorsRecord!
                                      .gestureOptions.first.pictureURL,
                                ).image,
                              ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                50.0, 16.0, 50.0, 0.0),
                            child: Text(
                              valueOrDefault<String>(
                                containerOptionsForSelectorsRecord
                                    .gestureOptions.first.description,
                                'A hand gesture',
                              ),
                              textAlign: TextAlign.center,
                              style: FlutterFlowTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'BT Beau Sans',
                                    fontSize: 16.0,
                                    useGoogleFonts: false,
                                    lineHeight: 1.29,
                                  ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Padding(
                    padding:
                        const EdgeInsetsDirectional.fromSTEB(20.0, 25.0, 20.0, 30.0),
                    child: Container(
                      decoration: const BoxDecoration(),
                      child: wrapWithModel(
                        model: _model.gradientButtonModel,
                        updateCallback: () => setState(() {}),
                        updateOnChange: true,
                        child: GradientButtonWidget(
                          title: 'Upload selfie and verify',
                          action: () async {
                            var shouldSetState = false;
                            final selectedMedia = await selectMedia(
                              maxWidth: 800.00,
                              imageQuality: 70,
                              multiImage: false,
                            );
                            if (selectedMedia != null &&
                                selectedMedia.every((m) =>
                                    validateFileFormat(m.storagePath, context))) {
                              setState(() => _model.isDataUploading = true);
                              var selectedUploadedFiles = <FFUploadedFile>[];
              
                              var downloadUrls = <String>[];
                              try {
                                selectedUploadedFiles = selectedMedia
                                    .map((m) => FFUploadedFile(
                                          name: m.storagePath.split('/').last,
                                          bytes: m.bytes,
                                          height: m.dimensions?.height,
                                          width: m.dimensions?.width,
                                          blurHash: m.blurHash,
                                        ))
                                    .toList();
              
                                downloadUrls = (await Future.wait(
                                  selectedMedia.map(
                                    (m) async =>
                                        await uploadDataV(m.storagePath, m.bytes),
                                  ),
                                ))
                                    .where((u) => u != null)
                                    .map((u) => u!)
                                    .toList();
                              } finally {
                                _model.isDataUploading = false;
                              }
                              if (selectedUploadedFiles.length ==
                                      selectedMedia.length &&
                                  downloadUrls.length == selectedMedia.length) {
                                setState(() {
                                  _model.uploadedLocalFile =
                                      selectedUploadedFiles.first;
                                  _model.uploadedFileUrl = downloadUrls.first;
                                });
                              } else {
                                setState(() {});
                                return;
                              }
                            }
              
                            if (_model.uploadedFileUrl != '') {
                              await VerificationsRecord.collection
                                  .doc()
                                  .set(createVerificationsRecordData(
                                    userToVerify: currentUserReference,
                                    genderOfUserToVerify:
                                        currentUserDocument?.gender,
                                    selectedGesture:
                                        containerOptionsForSelectorsRecord
                                            .gestureOptions.first.number,
                                    handled: false,
                                    timeCreated: getCurrentTimestamp,
                                    name: valueOrDefault(
                                        currentUserDocument?.name, ''),
                                    uid: currentUserUid,
                                    selectedGestureDescription:
                                        containerOptionsForSelectorsRecord
                                            .gestureOptions.first.description,
                                    verificationPhoto: _model.uploadedFileUrl,
                                  ));
              
                              await currentUserReference!
                                  .update(createUsersRecordData(
                                verificationGoingOn: true,
                              ));
                              analytics.logEvent('Sign Up: Re-Requested Verification');
              
                              try {
                              unawaited(
                                () async {
                                  try {
                                    final result =
                                        await FirebaseFunctions.instanceFor(
                                                region: 'europe-west2')
                                            .httpsCallable(
                                                'createVerificationRequest')
                                            .call({
                                      "name": valueOrDefault(
                                          currentUserDocument?.name, ''),
                                      "gender": currentUserDocument!.gender!.name,
                                      "time": dateTimeFormat(
                                        "d/M H:mm",
                                        getCurrentTimestamp,
                                        locale: FFLocalizations.of(context)
                                            .languageCode,
                                      ),
                                      "userUID": currentUserUid,
                                    });
                                    _model.createverificationrequestCf2 =
                                        CreateVerificationRequestCloudFunctionCallResponse(
                                      succeeded: true,
                                    );
                                  } on FirebaseFunctionsException catch (error) {
                                    _model.createverificationrequestCf2 =
                                        CreateVerificationRequestCloudFunctionCallResponse(
                                      errorCode: error.code,
                                      succeeded: false,
                                    );
                                  }
                                }(),
                              );
                              shouldSetState = true;
                              Navigator.pop(context);
                              if (shouldSetState) setState(() {});
                              return;
                            } catch (error) {}
                          }}
                                        ),),
                  ),
              ),
                        ]),
            ),
        ));
      },
    );
  }
}
