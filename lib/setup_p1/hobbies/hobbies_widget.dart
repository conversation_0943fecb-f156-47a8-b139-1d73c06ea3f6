import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/gradient_button_disabled/gradient_button_disabled_widget.dart';
import '/profile_settings/settings_selector/kinks_subsection_for_setup/kinks_subsection_for_setup_widget.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'hobbies_model.dart';
export 'hobbies_model.dart';

class HobbiesWidget extends StatefulWidget {
  const HobbiesWidget({super.key});

  @override
  State<HobbiesWidget> createState() => _HobbiesWidgetState();
}

class _HobbiesWidgetState extends State<HobbiesWidget> {
  late HobbiesModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => HobbiesModel());
    logFirebaseEvent('screen_view', parameters: {'screen_name': 'Hobbies'});
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return StreamBuilder<List<OptionsForSelectorsRecord>>(
      stream: queryOptionsForSelectorsRecord(
        queryBuilder: (optionsForSelectorsRecord) =>
            optionsForSelectorsRecord.where(
          'name',
          isEqualTo: 'Hobbies',
        ),
        singleRecord: true,
      ),
      builder: (context, snapshot) {
        // Customize what your widget looks like when it's loading.
        if (!snapshot.hasData) {
          return Scaffold(
            backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
            body: Center(
              child: SizedBox(
                width: 50.0,
                height: 50.0,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    FlutterFlowTheme.of(context).accent2,
                  ),
                ),
              ),
            ),
          );
        }
        List<OptionsForSelectorsRecord> hobbiesOptionsForSelectorsRecordList =
            snapshot.data!;
        final hobbiesOptionsForSelectorsRecord =
            hobbiesOptionsForSelectorsRecordList.isNotEmpty
                ? hobbiesOptionsForSelectorsRecordList.first
                : null;
        return GestureDetector(
          onTap: () => _model.unfocusNode.canRequestFocus
              ? FocusScope.of(context).requestFocus(_model.unfocusNode)
              : FocusScope.of(context).unfocus(),
          child: Scaffold(
            key: scaffoldKey,
            backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
            body: SafeArea(
              top: true,
              child: AuthUserStreamWidget(
                builder: (context) => StreamBuilder<PublicProfileRecord>(
                  stream: PublicProfileRecord.getDocument(
                      currentUserDocument!.publicProfile!),
                  builder: (context, snapshot) {
                    // Customize what your widget looks like when it's loading.
                    if (!snapshot.hasData) {
                      return Center(
                        child: SizedBox(
                          width: 50.0,
                          height: 50.0,
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                              FlutterFlowTheme.of(context).accent2,
                            ),
                          ),
                        ),
                      );
                    }
                    final stackPublicProfileRecord = snapshot.data!;
                    return Stack(
                      alignment: const AlignmentDirectional(0.0, -1.0),
                      children: [
                        const Align(
                          alignment: AlignmentDirectional(0.0, -1.0),
                          child: Stack(
                            alignment: AlignmentDirectional(1.0, -1.0),
                            children: [],
                          ),
                        ),
                        Column(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Material(
                              color: Colors.transparent,
                              elevation: 3.0,
                              child: Container(
                                decoration: BoxDecoration(
                                  color: FlutterFlowTheme.of(context)
                                      .primaryBackground,
                                ),
                                child: Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      23.0, 0.0, 23.0, 0.0),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.stretch,
                                    children: [
                                      Padding(
                                        padding: const EdgeInsetsDirectional.fromSTEB(
                                            0.0, 70.0, 0.0, 16.0),
                                        child: Text(
                                          'What do you enjoy?',
                                          style: FlutterFlowTheme.of(context)
                                              .bodyMedium
                                              .override(
                                                fontFamily: 'BT Beau Sans',
                                                fontSize: 32.0,
                                                fontWeight: FontWeight.bold,
                                                useGoogleFonts: false,
                                                lineHeight: 1.29,
                                              ),
                                        ),
                                      ),
                                      Padding(
                                        padding: const EdgeInsetsDirectional.fromSTEB(
                                            0.0, 0.0, 0.0, 29.0),
                                        child: InkWell(
                                          splashColor: Colors.transparent,
                                          focusColor: Colors.transparent,
                                          hoverColor: Colors.transparent,
                                          highlightColor: Colors.transparent,
                                          child: Text(
                                            'Let others know more about yourself. \nThey are here for you.',
                                            style: FlutterFlowTheme.of(context)
                                                .bodyMedium
                                                .override(
                                                  fontFamily: 'BT Beau Sans',
                                                  fontSize: 16.0,
                                                  useGoogleFonts: false,
                                                  lineHeight: 1.21,
                                                ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            Expanded(
                              child: SingleChildScrollView(
                                child: Column(
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    Padding(
                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                          20.0, 0.0, 20.0, 0.0),
                                      child: wrapWithModel(
                                        model:
                                            _model.kinksSubsectionForSetupModel,
                                        updateCallback: () => setState(() {}),
                                        child: KinksSubsectionForSetupWidget(
                                          choices:
                                              hobbiesOptionsForSelectorsRecord!
                                                  .options,
                                          initialValues:
                                              stackPublicProfileRecord.hobbies,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            Align(
                              alignment: const AlignmentDirectional(0.0, 1.0),
                              child: Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    27.0, 30.0, 27.0, 47.0),
                                child: Column(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    if (_model.kinksSubsectionForSetupModel
                                            .choiceSet.isNotEmpty)
                                      wrapWithModel(
                                        model: _model.gradientButtonModel,
                                        updateCallback: () => setState(() {}),
                                        child: GradientButtonWidget(
                                          title:
                                              'Next ${_model.kinksSubsectionForSetupModel.choiceSet.length.toString()}/5',
                                          action: () async {
                                            // Set hobbies to public profile

                                            await currentUserDocument!
                                                .publicProfile!
                                                .update({
                                              ...mapToFirestore(
                                                {
                                                  'hobbies': _model
                                                      .kinksSubsectionForSetupModel
                                                      .choiceSet,
                                                },
                                              ),
                                            });
                                            // Set hobbies to public profile

                                            await currentUserReference!.update({
                                              ...mapToFirestore(
                                                {
                                                  'hobbies': _model
                                                      .kinksSubsectionForSetupModel
                                                      .choiceSet,
                                                },
                                              ),
                                            });
                                            if (getRemoteConfigBool(
                                                'showEdu')) {
                                              context.pushNamed('Education');
                                            } else {
                                              if (currentUserDocument?.gender ==
                                                  Gender.Male) {
                                                // Go to bio men page

                                                context.pushNamed('BioMen');
                                              } else {
                                                // Go to bio women page

                                                context.pushNamed('BioWomen');
                                              }
                                            }
                                          },
                                        ),
                                      ),
                                    if (_model.kinksSubsectionForSetupModel
                                            .choiceSet.isEmpty)
                                      wrapWithModel(
                                        model:
                                            _model.gradientButtonDisabledModel,
                                        updateCallback: () => setState(() {}),
                                        child: GradientButtonDisabledWidget(
                                          title: 'Next 0/5',
                                          action: () async {},
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                        Container(
                          width: MediaQuery.sizeOf(context).width * 1.0,
                          height: 5.0,
                          decoration: const BoxDecoration(
                            gradient: LinearGradient(
                              colors: [Color(0xFF67B0E5), Color(0xFFF49BD1)],
                              stops: [0.0, 1.0],
                              begin: AlignmentDirectional(1.0, 0.0),
                              end: AlignmentDirectional(-1.0, 0),
                            ),
                          ),
                        ),
                        Align(
                          alignment: const AlignmentDirectional(1.0, -1.0),
                          child: Container(
                            width: MediaQuery.sizeOf(context).width * 0.28,
                            height: 5.0,
                            decoration: const BoxDecoration(
                              color: Color(0xD4FFFFFF),
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              0.0, 25.0, 0.0, 0.0),
                          child: Row(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    20.0, 0.0, 0.0, 0.0),
                                child: InkWell(
                                  splashColor: Colors.transparent,
                                  focusColor: Colors.transparent,
                                  hoverColor: Colors.transparent,
                                  highlightColor: Colors.transparent,
                                  onTap: () async {
                                    context.goNamed(
                                      'RelationshipAims',
                                      extra: <String, dynamic>{
                                        kTransitionInfoKey: const TransitionInfo(
                                          hasTransition: true,
                                          transitionType:
                                              PageTransitionType.leftToRight,
                                        ),
                                      },
                                    );
                                  },
                                  child: FaIcon(
                                    FontAwesomeIcons.angleLeft,
                                    color: FlutterFlowTheme.of(context)
                                        .secondaryText,
                                    size: 30.0,
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 0.0, 18.0, 0.0),
                                child: InkWell(
                                  splashColor: Colors.transparent,
                                  focusColor: Colors.transparent,
                                  hoverColor: Colors.transparent,
                                  highlightColor: Colors.transparent,
                                  onTap: () async {
                                    if (getRemoteConfigBool('showEdu')) {
                                      context.pushNamed('Education');
                                    } else {
                                      if (currentUserDocument?.gender ==
                                          Gender.Male) {
                                        // Go to bio men page

                                        context.pushNamed('BioMen');
                                      } else {
                                        // Go to bio women page

                                        context.pushNamed('BioWomen');
                                      }
                                    }
                                  },
                                  child: Text(
                                    'Skip',
                                    style: FlutterFlowTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'BT Beau Sans',
                                          color: const Color(0xFF4F5765),
                                          fontSize: 18.0,
                                          fontWeight: FontWeight.bold,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
