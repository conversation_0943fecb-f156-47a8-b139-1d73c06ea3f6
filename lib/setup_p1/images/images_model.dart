import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/gradient_button_disabled/gradient_button_disabled_widget.dart';
import '/general/lightbulb_tip/lightbulb_tip_widget.dart';
import '/general/picture_upload/picture_upload_widget.dart';
import '/general/picture_upload_n_e/picture_upload_n_e_widget.dart';
import 'images_widget.dart' show ImagesWidget;
import 'package:flutter/material.dart';

class ImagesModel extends FlutterFlowModel<ImagesWidget> {
  ///  State fields for stateful widgets in this page.

  final unfocusNode = FocusNode();
  // Model for pictureUpload component.
  late PictureUploadModel pictureUploadModel1;
  // Model for pictureUploadNE component.
  late PictureUploadNEModel pictureUploadNEModel1;
  // Model for pictureUpload component.
  late PictureUploadModel pictureUploadModel2;
  // Model for pictureUploadNE component.
  late PictureUploadNEModel pictureUploadNEModel2;
  // Model for pictureUpload component.
  late PictureUploadModel pictureUploadModel3;
  // Model for pictureUploadNE component.
  late PictureUploadNEModel pictureUploadNEModel3;
  // Model for pictureUpload component.
  late PictureUploadModel pictureUploadModel4;
  // Model for pictureUploadNE component.
  late PictureUploadNEModel pictureUploadNEModel4;
  // Model for pictureUpload component.
  late PictureUploadModel pictureUploadModel5;
  // Model for pictureUploadNE component.
  late PictureUploadNEModel pictureUploadNEModel5;
  // Model for pictureUpload component.
  late PictureUploadModel pictureUploadModel6;
  // Model for pictureUploadNE component.
  late PictureUploadNEModel pictureUploadNEModel6;// Model for LightbulbTip component.
   late LightbulbTipModel lightbulbTipModel;
  // Model for GradientButton component.
  late GradientButtonModel gradientButtonModel;
  // Model for GradientButtonDisabled component.
  late GradientButtonDisabledModel gradientButtonDisabledModel;

  /// Initialization and disposal methods.

  @override
  void initState(BuildContext context) {
    pictureUploadModel1 = createModel(context, () => PictureUploadModel());
    pictureUploadNEModel1 = createModel(context, () => PictureUploadNEModel());
    pictureUploadModel2 = createModel(context, () => PictureUploadModel());
    pictureUploadNEModel2 = createModel(context, () => PictureUploadNEModel());
    pictureUploadModel3 = createModel(context, () => PictureUploadModel());
    pictureUploadNEModel3 = createModel(context, () => PictureUploadNEModel());
    pictureUploadModel4 = createModel(context, () => PictureUploadModel());
    pictureUploadNEModel4 = createModel(context, () => PictureUploadNEModel());
    pictureUploadModel5 = createModel(context, () => PictureUploadModel());
    pictureUploadNEModel5 = createModel(context, () => PictureUploadNEModel());    
    lightbulbTipModel = createModel(context, () => LightbulbTipModel());
    pictureUploadModel6 = createModel(context, () => PictureUploadModel());
    pictureUploadNEModel6 = createModel(context, () => PictureUploadNEModel());
    gradientButtonModel = createModel(context, () => GradientButtonModel());
    gradientButtonDisabledModel =
        createModel(context, () => GradientButtonDisabledModel());
  }

  @override
  void dispose() {
    unfocusNode.dispose();
    pictureUploadModel1.dispose();
    pictureUploadNEModel1.dispose();
    pictureUploadModel2.dispose();
    pictureUploadNEModel2.dispose();
    pictureUploadModel3.dispose();
    pictureUploadNEModel3.dispose();
    pictureUploadModel4.dispose();
    pictureUploadNEModel4.dispose();
    pictureUploadModel5.dispose();
    lightbulbTipModel.dispose();
    pictureUploadNEModel5.dispose();
    pictureUploadModel6.dispose();
    pictureUploadNEModel6.dispose();
    gradientButtonModel.dispose();
    gradientButtonDisabledModel.dispose();
  }

  /// Action blocks are added here.

  /// Additional helper methods are added here.
}
