import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/gradient_button_disabled/gradient_button_disabled_widget.dart';
import '/general/info_sheet_scrollable/info_sheet_scrollable_widget.dart';
import '/general/lightbulb_tip/lightbulb_tip_widget.dart';
import '/general/picture_upload/picture_upload_widget.dart';
import '/general/picture_upload_n_e/picture_upload_n_e_widget.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'images_model.dart';
export 'images_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';

class ImagesWidget extends StatefulWidget {
  const ImagesWidget({super.key});

  @override
  State<ImagesWidget> createState() => _ImagesWidgetState();
}

class _ImagesWidgetState extends State<ImagesWidget> {
  late ImagesModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => ImagesModel());
    logFirebaseEvent('screen_view',
        parameters: {'screen_name': 'ImagesSignup'});
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AuthUserStreamWidget(
      builder: (context) => StreamBuilder<PublicProfileRecord>(
        stream: PublicProfileRecord.getDocument(
            currentUserDocument!.publicProfile!),
        builder: (context, snapshot) {
          // Customize what your widget looks like when it's loading.
          if (!snapshot.hasData) {
            return Scaffold(
              backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
              body: Center(
                child: SizedBox(
                  width: 50.0,
                  height: 50.0,
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      FlutterFlowTheme.of(context).accent2,
                    ),
                  ),
                ),
              ),
            );
          }

          final imagesPublicProfileRecord = snapshot.data!;

          return GestureDetector(
            onTap: () => FocusScope.of(context).unfocus(),
            child: Scaffold(
              key: scaffoldKey,
              backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
              body: SafeArea(
                top: true,
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Stack(
                                  children: [
                                    Container(
                                      width: MediaQuery.sizeOf(context).width *
                                          1.0,
                                      height:
                                          MediaQuery.sizeOf(context).height *
                                              0.173,
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          colors: [
                                            FlutterFlowTheme.of(context)
                                                .primaryBackground,
                                            const Color(0x00FFFFFF)
                                          ],
                                          stops: const [0.9, 1.0],
                                          begin: const AlignmentDirectional(
                                              0.0, -1.0),
                                          end: const AlignmentDirectional(
                                              0, 1.0),
                                        ),
                                      ),
                                    ),
                                    Container(
                                      width: MediaQuery.sizeOf(context).width *
                                          1.0,
                                      height: 5.0,
                                      decoration: const BoxDecoration(
                                        gradient: LinearGradient(
                                          colors: [
                                            Color(0xFF67B0E5),
                                            Color(0xFFF49BD1)
                                          ],
                                          stops: [0.0, 1.0],
                                          begin: AlignmentDirectional(1.0, 0.0),
                                          end: AlignmentDirectional(-1.0, 0),
                                        ),
                                      ),
                                    ),
                                    Align(
                                      alignment:
                                          const AlignmentDirectional(1.0, -1.0),
                                      child: Container(
                                        width:
                                            MediaQuery.sizeOf(context).width *
                                                0.09,
                                        height: 5.0,
                                        decoration: const BoxDecoration(
                                          color: Color(0xD4FFFFFF),
                                        ),
                                      ),
                                    ),
                                    Column(
                                      mainAxisSize: MainAxisSize.max,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Padding(
                                          padding: const EdgeInsetsDirectional
                                              .fromSTEB(13.0, 70.0, 0.0, 0.0),
                                          child: Text(
                                            getRemoteConfigString(
                                                'images_headline'),
                                            style: FlutterFlowTheme.of(context)
                                                .bodyMedium
                                                .override(
                                                  fontFamily: 'BT Beau Sans',
                                                  fontSize: 32.0,
                                                  letterSpacing: 0.0,
                                                  fontWeight: FontWeight.bold,
                                                  useGoogleFonts: false,
                                                  lineHeight: 1.29,
                                                ),
                                          ),
                                        ),
                                        Align(
                                          alignment: const AlignmentDirectional(
                                              -1.0, -1.0),
                                          child: Padding(
                                            padding: const EdgeInsetsDirectional
                                                .fromSTEB(
                                                14.0, 14.0, 0.0, 11.0),
                                            child: Text(
                                              getRemoteConfigString(
                                                  'images_description'),
                                              style: FlutterFlowTheme.of(
                                                      context)
                                                  .bodyMedium
                                                  .override(
                                                    fontFamily: 'BT Beau Sans',
                                                    fontSize: 16.0,
                                                    letterSpacing: 0.0,
                                                    fontWeight: FontWeight.w500,
                                                    useGoogleFonts: false,
                                                  ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    Padding(
                                      padding:
                                          const EdgeInsetsDirectional.fromSTEB(
                                              0.0, 15.0, 0.0, 0.0),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.max,
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          FlutterFlowIconButton(
                                            borderColor:
                                                const Color(0x004B39EF),
                                            borderRadius: 20.0,
                                            borderWidth: 1.0,
                                            buttonSize: 40.0,
                                            fillColor: const Color(0x004B39EF),
                                            icon: FaIcon(
                                              FontAwesomeIcons.angleLeft,
                                              color:
                                                  FlutterFlowTheme.of(context)
                                                      .primaryText,
                                              size: 24.0,
                                            ),
                                            onPressed: () async {
                                              context.goNamed(
                                                'RelationshipAims',
                                                extra: <String, dynamic>{
                                                  kTransitionInfoKey:
                                                      const TransitionInfo(
                                                    hasTransition: true,
                                                    transitionType:
                                                        PageTransitionType
                                                            .leftToRight,
                                                  ),
                                                },
                                              );
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  10.0, 36.0, 10.0, 0.0),
                              child: SingleChildScrollView(
                                primary: false,
                                child: Column(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment:
                                      CrossAxisAlignment.stretch,
                                  children: [
                                    Flexible(
                                      child: GridView(
                                        padding: const EdgeInsets.fromLTRB(
                                          0,
                                          0,
                                          0,
                                          35.0,
                                        ),
                                        gridDelegate:
                                            const SliverGridDelegateWithFixedCrossAxisCount(
                                          crossAxisCount: 3,
                                          crossAxisSpacing: 10.0,
                                          mainAxisSpacing: 10.0,
                                          childAspectRatio: 0.9,
                                        ),
                                        primary: false,
                                        shrinkWrap: true,
                                        scrollDirection: Axis.vertical,
                                        children: [
                                          Row(
                                            mainAxisSize: MainAxisSize.max,
                                            children: [
                                              if (imagesPublicProfileRecord
                                                  .nPictures.isNotEmpty)
                                                wrapWithModel(
                                                  model: _model
                                                      .pictureUploadModel1,
                                                  updateCallback: () =>
                                                      safeSetState(() {}),
                                                  updateOnChange: true,
                                                  child: PictureUploadWidget(
                                                    signUp: true,
                                                    picturePosition: 1,
                                                    publicProfile:
                                                        imagesPublicProfileRecord,
                                                  ),
                                                ),
                                              if (imagesPublicProfileRecord
                                                  .nPictures.isEmpty)
                                                wrapWithModel(
                                                  model: _model
                                                      .pictureUploadNEModel1,
                                                  updateCallback: () =>
                                                      safeSetState(() {}),
                                                  updateOnChange: true,
                                                  child: PictureUploadNEWidget(
                                                    signUp: true,
                                                    picturePosition: 1,
                                                    publicProfile:
                                                        imagesPublicProfileRecord,
                                                  ),
                                                ),
                                            ],
                                          ),
                                          Row(
                                            mainAxisSize: MainAxisSize.max,
                                            children: [
                                              if (imagesPublicProfileRecord
                                                      .nPictures.length >=
                                                  2)
                                                wrapWithModel(
                                                  model: _model
                                                      .pictureUploadModel2,
                                                  updateCallback: () =>
                                                      safeSetState(() {}),
                                                  updateOnChange: true,
                                                  child: PictureUploadWidget(
                                                    signUp: true,
                                                    picturePosition: 2,
                                                    publicProfile:
                                                        imagesPublicProfileRecord,
                                                  ),
                                                ),
                                              if (imagesPublicProfileRecord
                                                      .nPictures.length <
                                                  2)
                                                wrapWithModel(
                                                  model: _model
                                                      .pictureUploadNEModel2,
                                                  updateCallback: () =>
                                                      safeSetState(() {}),
                                                  updateOnChange: true,
                                                  child: PictureUploadNEWidget(
                                                    signUp: true,
                                                    picturePosition: 2,
                                                    publicProfile:
                                                        imagesPublicProfileRecord,
                                                  ),
                                                ),
                                            ],
                                          ),
                                          Row(
                                            mainAxisSize: MainAxisSize.max,
                                            children: [
                                              if (imagesPublicProfileRecord
                                                      .nPictures.length >=
                                                  3)
                                                wrapWithModel(
                                                  model: _model
                                                      .pictureUploadModel3,
                                                  updateCallback: () =>
                                                      safeSetState(() {}),
                                                  updateOnChange: true,
                                                  child: PictureUploadWidget(
                                                    signUp: true,
                                                    picturePosition: 3,
                                                    publicProfile:
                                                        imagesPublicProfileRecord,
                                                  ),
                                                ),
                                              if (imagesPublicProfileRecord
                                                      .nPictures.length <
                                                  3)
                                                wrapWithModel(
                                                  model: _model
                                                      .pictureUploadNEModel3,
                                                  updateCallback: () =>
                                                      safeSetState(() {}),
                                                  updateOnChange: true,
                                                  child: PictureUploadNEWidget(
                                                    signUp: true,
                                                    picturePosition: 3,
                                                    publicProfile:
                                                        imagesPublicProfileRecord,
                                                  ),
                                                ),
                                            ],
                                          ),
                                          Row(
                                            mainAxisSize: MainAxisSize.max,
                                            children: [
                                              if (imagesPublicProfileRecord
                                                      .nPictures.length >=
                                                  4)
                                                wrapWithModel(
                                                  model: _model
                                                      .pictureUploadModel4,
                                                  updateCallback: () =>
                                                      safeSetState(() {}),
                                                  updateOnChange: true,
                                                  child: PictureUploadWidget(
                                                    signUp: true,
                                                    picturePosition: 4,
                                                    publicProfile:
                                                        imagesPublicProfileRecord,
                                                  ),
                                                ),
                                              if (imagesPublicProfileRecord
                                                      .nPictures.length <
                                                  4)
                                                wrapWithModel(
                                                  model: _model
                                                      .pictureUploadNEModel4,
                                                  updateCallback: () =>
                                                      safeSetState(() {}),
                                                  updateOnChange: true,
                                                  child: PictureUploadNEWidget(
                                                    signUp: true,
                                                    picturePosition: 4,
                                                    publicProfile:
                                                        imagesPublicProfileRecord,
                                                  ),
                                                ),
                                            ],
                                          ),
                                          Row(
                                            mainAxisSize: MainAxisSize.max,
                                            children: [
                                              if (imagesPublicProfileRecord
                                                      .nPictures.length >=
                                                  5)
                                                wrapWithModel(
                                                  model: _model
                                                      .pictureUploadModel5,
                                                  updateCallback: () =>
                                                      safeSetState(() {}),
                                                  updateOnChange: true,
                                                  child: PictureUploadWidget(
                                                    signUp: true,
                                                    picturePosition: 5,
                                                    publicProfile:
                                                        imagesPublicProfileRecord,
                                                  ),
                                                ),
                                              if (imagesPublicProfileRecord
                                                      .nPictures.length <
                                                  5)
                                                wrapWithModel(
                                                  model: _model
                                                      .pictureUploadNEModel5,
                                                  updateCallback: () =>
                                                      safeSetState(() {}),
                                                  updateOnChange: true,
                                                  child: PictureUploadNEWidget(
                                                    signUp: true,
                                                    picturePosition: 5,
                                                    publicProfile:
                                                        imagesPublicProfileRecord,
                                                  ),
                                                ),
                                            ],
                                          ),
                                          Row(
                                            mainAxisSize: MainAxisSize.max,
                                            children: [
                                              if (imagesPublicProfileRecord
                                                      .nPictures.length >=
                                                  6)
                                                wrapWithModel(
                                                  model: _model
                                                      .pictureUploadModel6,
                                                  updateCallback: () =>
                                                      safeSetState(() {}),
                                                  updateOnChange: true,
                                                  child: PictureUploadWidget(
                                                    signUp: true,
                                                    picturePosition: 6,
                                                    publicProfile:
                                                        imagesPublicProfileRecord,
                                                  ),
                                                ),
                                              if (imagesPublicProfileRecord
                                                      .nPictures.length <
                                                  6)
                                                wrapWithModel(
                                                  model: _model
                                                      .pictureUploadNEModel6,
                                                  updateCallback: () =>
                                                      safeSetState(() {}),
                                                  updateOnChange: true,
                                                  child: PictureUploadNEWidget(
                                                    signUp: true,
                                                    picturePosition: 6,
                                                    publicProfile:
                                                        imagesPublicProfileRecord,
                                                  ),
                                                ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                    FFButtonWidget(
                                      onPressed: () async {
                                        await showModalBottomSheet(
                                          isScrollControlled: true,
                                          backgroundColor: Colors.transparent,
                                          enableDrag: false,
                                          context: context,
                                          builder: (context) {
                                            return GestureDetector(
                                              onTap: () =>
                                                  FocusScope.of(context)
                                                      .unfocus(),
                                              child: Padding(
                                                padding:
                                                    MediaQuery.viewInsetsOf(
                                                        context),
                                                child:
                                                    InfoSheetScrollableWidget(
                                                  title: getRemoteConfigString(
                                                      'signup_images_ibutton_title'),
                                                  body: getRemoteConfigString(
                                                      'signup_images_info'),
                                                ),
                                              ),
                                            );
                                          },
                                        ).then((value) => safeSetState(() {}));
                                      },
                                      text: getRemoteConfigString(
                                          'signup_images_ibutton_title'),
                                      icon: Icon(
                                        Icons.info_outlined,
                                        color: FlutterFlowTheme.of(context)
                                            .secondaryText,
                                        size: 18.0,
                                      ),
                                      options: FFButtonOptions(
                                        width:
                                            MediaQuery.sizeOf(context).width *
                                                1.0,
                                        height: 40.0,
                                        padding: const EdgeInsetsDirectional
                                            .fromSTEB(15.0, 0.0, 15.0, 0.0),
                                        iconPadding: const EdgeInsetsDirectional
                                            .fromSTEB(0.0, 0.0, 0.0, 0.0),
                                        color: Colors.white,
                                        textStyle: FlutterFlowTheme.of(context)
                                            .titleSmall
                                            .override(
                                              fontFamily: 'BT Beau Sans',
                                              color:
                                                  FlutterFlowTheme.of(context)
                                                      .secondaryText,
                                              fontSize: 4.0,
                                              letterSpacing: 0.0,
                                              fontWeight: FontWeight.w500,
                                              useGoogleFonts: false,
                                            ),
                                        elevation: 0.0,
                                        borderSide: const BorderSide(
                                          color: Color(0x0057636C),
                                          width: 0.0,
                                        ),
                                        borderRadius:
                                            BorderRadius.circular(100.0),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Column(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Column(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  27.0, 0.0, 27.0, 33.0),
                              child: wrapWithModel(
                                model: _model.lightbulbTipModel,
                                updateCallback: () => safeSetState(() {}),
                                child: LightbulbTipWidget(
                                  tip: getRemoteConfigString('images_tips'),
                                ),
                              ),
                            ),
                          ],
                        ),
                        Align(
                          alignment: const AlignmentDirectional(0.0, 1.0),
                          child: Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                27.0, 0.0, 27.0, 15.0),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                if (imagesPublicProfileRecord
                                        .nPictures.length >=
                                    3)
                                  Align(
                                    alignment:
                                        const AlignmentDirectional(0.0, 1.0),
                                    child: Builder(
                                      builder: (context) => wrapWithModel(
                                        model: _model.gradientButtonModel,
                                        updateCallback: () =>
                                            safeSetState(() {}),
                                        child: GradientButtonWidget(
                                          title: 'Next',
                                          action: () async {
                                            if (imagesPublicProfileRecord
                                                    .nPictures.length >=
                                                3) {
                                              await currentUserReference!
                                                  .update(createUsersRecordData(
                                                incognito: false,
                                                paused: false,
                                                lowerAgeReq: 18,
                                                upperAgeReq: 99,
                                                lowerHeightReq: 75,
                                                upperHeightReq: 250,
                                                distanceReq: 50.1,
                                              ));
                                              if (currentUserDocument?.gender ==
                                                      Gender.Female
                                                  ? (valueOrDefault<bool>(
                                                          currentUserDocument
                                                              ?.bGroup,
                                                          false)
                                                      ? functions
                                                          .getStringListFromJson(
                                                              getRemoteConfigString(
                                                                  'signup_show_bio_w'))
                                                          .contains('bGroup')
                                                      : functions
                                                          .getStringListFromJson(
                                                              getRemoteConfigString(
                                                                  'signup_show_bio_w'))
                                                          .contains('aGroup'))
                                                  : (valueOrDefault<bool>(
                                                          currentUserDocument
                                                              ?.bGroup,
                                                          false)
                                                      ? functions
                                                          .getStringListFromJson(
                                                              getRemoteConfigString(
                                                                  'signup_show_bio_m'))
                                                          .contains('bGroup')
                                                      : functions
                                                          .getStringListFromJson(
                                                              getRemoteConfigString(
                                                                  'signup_show_bio_m'))
                                                          .contains(
                                                              'aGroup'))) {
                                                analytics.setUserProperties({
                                                  'Pictures':
                                                      imagesPublicProfileRecord
                                                          .nPictures.length
                                                });
                                                analytics.logEvent(
                                                    'Sign Up: Uploaded Pictures',
                                                    eventProperties: {
                                                      'Pictures Added Count':
                                                          imagesPublicProfileRecord
                                                              .nPictures.length
                                                    });
                                                context.goNamed('BioPrompts');

                                                return;
                                              } else {
                                                if (currentUserDocument
                                                        ?.location !=
                                                    null) {
                                                  analytics.setUserProperties({
                                                    'Pictures':
                                                        imagesPublicProfileRecord
                                                            .nPictures.length
                                                  });
                                                  analytics.logEvent(
                                                      'Sign Up: Uploaded Pictures',
                                                      eventProperties: {
                                                        'Pictures Added Count':
                                                            imagesPublicProfileRecord
                                                                .nPictures
                                                                .length
                                                      });
                                                  context.pushNamed(
                                                      'VerificationPrompt');

                                                  return;
                                                } else {
                                                  if (currentUserDocument
                                                          ?.signUpTestingCohort ==
                                                      SignUpTestingCohort
                                                          .Confirmation) {
                                                    analytics
                                                        .setUserProperties({
                                                      'Pictures':
                                                          imagesPublicProfileRecord
                                                              .nPictures.length
                                                    });
                                                    analytics.logEvent(
                                                        'Sign Up: Uploaded Pictures',
                                                        eventProperties: {
                                                          'Pictures Added Count':
                                                              imagesPublicProfileRecord
                                                                  .nPictures
                                                                  .length
                                                        });
                                                    context.pushNamed(
                                                        'BioConfirmationPage');

                                                    return;
                                                  } else {
                                                    analytics
                                                        .setUserProperties({
                                                      'Pictures':
                                                          imagesPublicProfileRecord
                                                              .nPictures.length
                                                    });
                                                    analytics.logEvent(
                                                        'Sign Up: Uploaded Pictures',
                                                        eventProperties: {
                                                          'Pictures Added Count':
                                                              imagesPublicProfileRecord
                                                                  .nPictures
                                                                  .length
                                                        });
                                                    context.pushNamed(
                                                        'LocationAllowingEntry');

                                                    return;
                                                  }
                                                }
                                              }
                                            } else {
                                              await showDialog(
                                                context: context,
                                                builder: (dialogContext) {
                                                  return Dialog(
                                                    elevation: 0,
                                                    insetPadding:
                                                        EdgeInsets.zero,
                                                    backgroundColor:
                                                        Colors.transparent,
                                                    alignment:
                                                        const AlignmentDirectional(
                                                                0.0, 0.0)
                                                            .resolve(
                                                                Directionality.of(
                                                                    context)),
                                                    child: GestureDetector(
                                                      onTap: () =>
                                                          FocusScope.of(
                                                                  dialogContext)
                                                              .unfocus(),
                                                      child:
                                                          const GeneralPopupWidget(
                                                        alertTitle:
                                                            '3 images are required',
                                                        alertText:
                                                            'Please upload at least 3 images before you continue',
                                                      ),
                                                    ),
                                                  );
                                                },
                                              );

                                              return;
                                            }
                                          },
                                        ),
                                      ),
                                    ),
                                  ),
                                if (imagesPublicProfileRecord.nPictures.length <
                                    3)
                                  wrapWithModel(
                                    model: _model.gradientButtonDisabledModel,
                                    updateCallback: () => safeSetState(() {}),
                                    child: GradientButtonDisabledWidget(
                                      title: 'Next',
                                      action: () async {},
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
