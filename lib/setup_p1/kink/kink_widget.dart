import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/gradient_button_disabled/gradient_button_disabled_widget.dart';
import '/general/info_sheet_scrollable_multi/info_sheet_scrollable_multi_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'kink_model.dart';
export 'kink_model.dart';
import 'package:upgrader/upgrader.dart';
import 'package:chyrpe/amplitudeConfig.dart';

  class KinkWidget extends StatefulWidget {
  const KinkWidget({super.key});

  @override
  State<KinkWidget> createState() => _KinkWidgetState();
}

class _KinkWidgetState extends State<KinkWidget> {
  late KinkModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => KinkModel());
    logFirebaseEvent('screen_view', parameters: {'screen_name': 'Role'});
  }
  
  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
     return UpgradeAlert(
      dialogStyle: UpgradeDialogStyle.cupertino,
      showLater: false,
      showIgnore: false,
      child: GestureDetector(
      onTap: () => _model.unfocusNode.canRequestFocus
          ? FocusScope.of(context).requestFocus(_model.unfocusNode)
          : FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        body: SafeArea(
          top: true,
          child: Stack(
            children: [
              Align(
                alignment: const AlignmentDirectional(0, -1),
                child: Stack(
                  alignment: const AlignmentDirectional(1, -1),
                  children: [
                    Container(
                      width: MediaQuery.sizeOf(context).width,
                      height: 5,
                      decoration: const BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Color(0xFF67B0E5), Color(0xFFF49BD1)],
                          stops: [0, 1],
                          begin: AlignmentDirectional(1, 0),
                          end: AlignmentDirectional(-1, 0),
                        ),
                      ),
                    ),
                    Container(
                      width: MediaQuery.sizeOf(context).width * 0.55,
                      height: 5,
                      decoration: const BoxDecoration(
                        color: Color(0xD4FFFFFF),
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(23, 0, 23, 0),
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Flexible(
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Padding(
                              padding:
                                  const EdgeInsetsDirectional.fromSTEB(0, 70, 0, 25),
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'What are you?',
                                    style: FlutterFlowTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'BT Beau Sans',
                                          fontSize: 32,
                                          letterSpacing: 0,
                                          fontWeight: FontWeight.bold,
                                          useGoogleFonts: false,
                                          lineHeight: 1.29,
                                        ),
                                  ),
                                ],
                              ),
                            ),
                            Padding(
                              padding:
                                  const EdgeInsetsDirectional.fromSTEB(0, 12, 0, 30),
                              child: Text(
                                'This app will help you find the relationship dynamic that suits you best.',
                                style: FlutterFlowTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'BT Beau Sans',
                                      fontSize: 16,
                                      letterSpacing: 0,
                                      useGoogleFonts: false,
                                      lineHeight: 1.21,
                                    ),
                              ),
                            ),
                            Flexible(
                              child: AuthUserStreamWidget(
                                builder: (context) => StreamBuilder<
                                    List<OptionsForSelectorsRecord>>(
                                  stream: queryOptionsForSelectorsRecord(
                                    queryBuilder: (optionsForSelectorsRecord) =>
                                        optionsForSelectorsRecord.where(
                                      'name',
                                      isEqualTo: valueOrDefault<bool>(
                                              currentUserDocument?.alternativeG,
                                              false)
                                          ? 'Alternative Roles'
                                          : '${currentUserDocument?.gender?.name} Roles',
                                    ),
                                    singleRecord: true,
                                  ),
                                  builder: (context, snapshot) {
                                    // Customize what your widget looks like when it's loading.
                                    if (!snapshot.hasData) {
                                      return Center(
                                        child: SizedBox(
                                          width: 40,
                                          height: 40,
                                          child: SpinKitCircle(
                                            color: FlutterFlowTheme.of(context)
                                                .secondaryText,
                                            size: 40,
                                          ),
                                        ),
                                      );
                                    }
                                    List<OptionsForSelectorsRecord>
                                        columnOptionsForSelectorsRecordList =
                                        snapshot.data!;
                                    // Return an empty Container when the item does not exist.
                                    if (snapshot.data!.isEmpty) {
                                      return Container();
                                    }
                                    final columnOptionsForSelectorsRecord =
                                        columnOptionsForSelectorsRecordList
                                                .isNotEmpty
                                            ? columnOptionsForSelectorsRecordList
                                                .first
                                            : null;
                                    return Builder(
                                      builder: (context) {
                                        final options =
                                            columnOptionsForSelectorsRecord
                                                    ?.options
                                                    .toList() ??
                                                [];
                                        return Column(
                                          mainAxisSize: MainAxisSize.max,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: List.generate(
                                              options.length, (optionsIndex) {
                                            final optionsItem =
                                                options[optionsIndex];
                                            return Padding(
                                              padding: const EdgeInsetsDirectional
                                                  .fromSTEB(0, 0, 0, 21),
                                              child: InkWell(
                                                splashColor: Colors.transparent,
                                                focusColor: Colors.transparent,
                                                hoverColor: Colors.transparent,
                                                highlightColor:
                                                    Colors.transparent,
                                                onTap: () async {
                                                  setState(() {
                                                    _model.selectedKink =
                                                        optionsItem;
                                                  });
                                                },
                                                child: Material(
                                                  color: Colors.transparent,
                                                  elevation: 0,
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            100),
                                                  ),
                                                  child: Container(
                                                    height: 52,
                                                    constraints: const BoxConstraints(
                                                      maxWidth: 400,
                                                    ),
                                                    decoration: BoxDecoration(
                                                      color: FlutterFlowTheme
                                                              .of(context)
                                                          .primaryBackground,
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              100),
                                                      border: Border.all(
                                                        color: _model
                                                                    .selectedKink ==
                                                                optionsItem
                                                            ? FlutterFlowTheme
                                                                    .of(context)
                                                                .accent2
                                                            : FlutterFlowTheme
                                                                    .of(context)
                                                                .secondaryText,
                                                        width: 2,
                                                      ),
                                                    ),
                                                    child: Align(
                                                      alignment:
                                                          const AlignmentDirectional(
                                                              0, 0),
                                                      child: Text(
                                                        optionsItem,
                                                        style:
                                                            FlutterFlowTheme.of(
                                                                    context)
                                                                .bodyMedium
                                                                .override(
                                                                  fontFamily:
                                                                      'BT Beau Sans',
                                                                  color: FlutterFlowTheme.of(
                                                                          context)
                                                                      .secondaryText,
                                                                  fontSize: 19,
                                                                  letterSpacing:
                                                                      0,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold,
                                                                  useGoogleFonts:
                                                                      false,
                                                                ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            );
                                          }),
                                        );
                                      },
                                    );
                                  },
                                ),
                              ),
                            ),
                            FFButtonWidget(
                              onPressed: () async {
                                await showModalBottomSheet(
                                  isScrollControlled: true,
                                  backgroundColor: Colors.transparent,
                                  enableDrag: false,
                                  context: context,
                                  builder: (context) {
                                    return GestureDetector(
                                      onTap: () =>
                                          FocusScope.of(context).unfocus(),
                                      child: Padding(
                                        padding:
                                            MediaQuery.viewInsetsOf(context),
                                        child: InfoSheetScrollableMultiWidget(
                                          title: 'Quick explanations',
                                          subtitle1: getRemoteConfigString(
                                              'dominant_term'),
                                          subbody1: getRemoteConfigString(
                                              'dominant_term_info_body'),
                                          subtitle2: getRemoteConfigString(
                                              'submissive_term'),
                                          subbody2: getRemoteConfigString(
                                              'submissive_term_info_body'),
                                          subtitle3: getRemoteConfigString(
                                              'switch_term'),
                                          subbody3: getRemoteConfigString(
                                              'switch_term_info_body'),
                                        ),
                                      ),
                                    );
                                  },
                                ).then((value) => safeSetState(() {}));
                              },
                              text: getRemoteConfigString(
                                  'signup_kink_ibutton_title'),
                              icon: Icon(
                                Icons.info_outlined,
                                color:
                                    FlutterFlowTheme.of(context).secondaryText,
                                size: 18.0,
                              ),
                              options: FFButtonOptions(
                                width: MediaQuery.sizeOf(context).width * 1.0,
                                height: 40.0,
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    15.0, 0.0, 15.0, 0.0),
                                iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 0.0, 0.0, 0.0),
                                color: Colors.white,
                                textStyle: FlutterFlowTheme.of(context)
                                    .titleSmall
                                    .override(
                                      fontFamily: 'BT Beau Sans',
                                      color: FlutterFlowTheme.of(context)
                                          .secondaryText,
                                      fontSize: 4.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.w500,
                                      useGoogleFonts: false,
                                    ),
                                elevation: 0.0,
                                borderSide: const BorderSide(
                                  color: Color(0x0057636C),
                                  width: 0.0,
                                ),
                                borderRadius: BorderRadius.circular(100.0),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Align(
                      alignment: const AlignmentDirectional(0, 1),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          const Divider(
                            thickness: 1,
                            color: Color(0xFFD4D8DE),
                          ),
                          Padding(
                            padding:
                                const EdgeInsetsDirectional.fromSTEB(0, 12, 0, 0),
                            child: Text(
                              'You can add your exact preferences later.',
                              textAlign: TextAlign.center,
                              style: FlutterFlowTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'BT Beau Sans',
                                    fontSize: 16,
                                    letterSpacing: 0,
                                    useGoogleFonts: false,
                                    lineHeight: 1.21,
                                  ),
                            ),
                          ),
                          Padding(
                            padding:
                                const EdgeInsetsDirectional.fromSTEB(27, 23, 27, 25),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Theme(
                                  data: ThemeData(
                                    checkboxTheme: CheckboxThemeData(
                                      visualDensity: VisualDensity.compact,
                                      materialTapTargetSize:
                                          MaterialTapTargetSize.shrinkWrap,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                    ),
                                    unselectedWidgetColor:
                                        FlutterFlowTheme.of(context)
                                            .secondaryText,
                                  ),
                                  child: Checkbox(
                                    value: _model.checkboxValue ??= false,
                                    onChanged: (newValue) async {
                                      setState(() =>
                                          _model.checkboxValue = newValue!);
                                    },
                                    side: BorderSide(
                                      width: 2,
                                      color: FlutterFlowTheme.of(context)
                                          .secondaryText,
                                    ),
                                    activeColor:
                                        FlutterFlowTheme.of(context).accent2,
                                    checkColor:
                                        FlutterFlowTheme.of(context).info,
                                  ),
                                ),
                                Text(
                                  'Hide in my profile',
                                  style: FlutterFlowTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        fontSize: 15,
                                        letterSpacing: 0,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding:
                                const EdgeInsetsDirectional.fromSTEB(27, 0, 27, 20),
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                if (_model.selectedKink != null &&
                                    _model.selectedKink != '')
                                  Builder(
                                    builder: (context) => wrapWithModel(
                                      model: _model.gradientButtonModel,
                                      updateCallback: () => setState(() {}),
                                      child: GradientButtonWidget(
                                        title: 'Next',
                                        action: () async {
                                          if (_model.selectedKink != null &&
                                              _model.selectedKink != '') {
                                            // Set role in user

                                            analytics.logEvent('Sign Up: Entered Role', eventProperties: {'Role Selected': _model.selectedKink, 'Role Hidden': _model.checkboxValue});
                                            analytics.setUserProperties({'Role': _model.selectedKink});


                                            await currentUserReference!
                                                .update(createUsersRecordData(
                                              position: _model.selectedKink,
                                            ));
                                            // Depending on choice, set public role

                                            await currentUserDocument!
                                                .publicProfile!
                                                .update(
                                                    createPublicProfileRecordData(
                                              publicRole: _model.checkboxValue!
                                                  ? ''
                                                  : _model.selectedKink,
                                              publicRoleShown:
                                                  !_model.checkboxValue!,
                                            ));
                                            if (getRemoteConfigBool(
                                                'hideXpForAll')) {
                                              context.pushNamed(
                                                  'RelationshipAims');
                                            } else {
                                              context
                                                  .pushNamed('ExperienceLevel');
                                            }
                                          } else {
                                            await showDialog(
                                              context: context,
                                              builder: (dialogContext) {
                                                return Dialog(
                                                  elevation: 0,
                                                  insetPadding: EdgeInsets.zero,
                                                  backgroundColor:
                                                      Colors.transparent,
                                                  alignment:
                                                      const AlignmentDirectional(0, 0)
                                                          .resolve(
                                                              Directionality.of(
                                                                  context)),
                                                  child: GestureDetector(
                                                    onTap: () => _model
                                                            .unfocusNode
                                                            .canRequestFocus
                                                        ? FocusScope.of(context)
                                                            .requestFocus(_model
                                                                .unfocusNode)
                                                        : FocusScope.of(context)
                                                            .unfocus(),
                                                    child: const GeneralPopupWidget(
                                                      alertTitle:
                                                          'No option selected',
                                                      alertText:
                                                          'Please select an option to continue',
                                                    ),
                                                  ),
                                                );
                                              },
                                            ).then((value) => setState(() {}));
                                          }
                                        },
                                      ),
                                    ),
                                  ),
                                if (_model.selectedKink == null ||
                                    _model.selectedKink == '')
                                  wrapWithModel(
                                    model: _model.gradientButtonDisabledModel,
                                    updateCallback: () => setState(() {}),
                                    child: GradientButtonDisabledWidget(
                                      title: 'Next',
                                      action: () async {},
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(15.0, 20.0, 0.0, 0.0),
                child: FlutterFlowIconButton(
                  borderColor: Colors.transparent,
                  borderRadius: 20.0,
                  borderWidth: 1.0,
                  buttonSize: 40.0,
                  icon: FaIcon(
                    FontAwesomeIcons.angleLeft,
                    color: FlutterFlowTheme.of(context).primaryText,
                    size: 24.0,
                  ),
                  onPressed: () async {
                    context.goNamed(
                      'PreferredGender',
                      extra: <String, dynamic>{
                        kTransitionInfoKey: const TransitionInfo(
                          hasTransition: true,
                          transitionType: PageTransitionType.leftToRight,
                        ),
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
      ),
    );
  }
}
