import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import 'men_extra_rules_new_widget.dart' show MenExtraRulesNewWidget;
import 'package:flutter/material.dart';

class MenExtraRulesNewModel extends FlutterFlowModel<MenExtraRulesNewWidget> {
  ///  State fields for stateful widgets in this component.

  // Model for GradientButton component.
  late GradientButtonModel gradientButtonModel;

  @override
  void initState(BuildContext context) {
    gradientButtonModel = createModel(context, () => GradientButtonModel());
  }

  @override
  void dispose() {
    gradientButtonModel.dispose();
  }
}