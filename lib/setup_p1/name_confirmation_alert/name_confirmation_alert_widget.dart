import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button_small/gradient_button_small_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'name_confirmation_alert_model.dart';
export 'name_confirmation_alert_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';

class NameConfirmationAlertWidget extends StatefulWidget {
  const NameConfirmationAlertWidget({
    super.key,
    required this.name,
  });

  final String? name;

  @override
  State<NameConfirmationAlertWidget> createState() =>
      _NameConfirmationAlertWidgetState();
}

class _NameConfirmationAlertWidgetState
    extends State<NameConfirmationAlertWidget> {
  late NameConfirmationAlertModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => NameConfirmationAlertModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: const AlignmentDirectional(0.0, 0.0),
      child: Padding(
        padding: const EdgeInsetsDirectional.fromSTEB(20.0, 0.0, 20.0, 0.0),
        child: Container(
          width: double.infinity,
          height: 400.0,
          constraints: const BoxConstraints(
            maxWidth: 400.0,
          ),
          decoration: BoxDecoration(
            color: FlutterFlowTheme.of(context).secondaryBackground,
            borderRadius: BorderRadius.circular(12.0),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(60.0, 0.0, 60.0, 0.0),
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(0.0, 37.0, 0.0, 0.0),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8.0),
                        child: SvgPicture.asset(
                          'assets/images/BlackCirclesOnly.svg',
                          width: 50.0,
                          height: 50.0,
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),
                    Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(0.0, 23.0, 0.0, 0.0),
                      child: Text(
                        'Welcome, ${widget.name}!',
                        textAlign: TextAlign.center,
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'BT Beau Sans',
                              fontSize: 20.0,
                              letterSpacing: 0.0,
                              fontWeight: FontWeight.bold,
                              useGoogleFonts: false,
                            ),
                      ),
                    ),
                    Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(0.0, 23.0, 0.0, 0.0),
                      child: Text(
                        'Real people have real names.\nEnjoy your experience!',
                        textAlign: TextAlign.center,
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'BT Beau Sans',
                              fontSize: 16.0,
                              letterSpacing: 0.0,
                              fontWeight: FontWeight.normal,
                              useGoogleFonts: false,
                              lineHeight: 1.55,
                            ),
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(52.0, 0.0, 52.0, 0.0),
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(0.0, 23.0, 0.0, 0.0),
                      child: wrapWithModel(
                        model: _model.gradientButtonSmallModel,
                        updateCallback: () => setState(() {}),
                        child: GradientButtonSmallWidget(
                          title: 'Get started',
                          action: () async {
                            if (currentUserDocument?.publicProfile != null) {
                              await currentUserDocument!.publicProfile!
                                  .update(createPublicProfileRecordData(
                                publicName: widget.name,
                                uid: currentUserUid,
                              ));

                              try {
                                              analytics.setUserProperties({'Referred Customer': ((currentUserDocument?.refCode != null) && (currentUserDocument?.refCode != "default")), 'Referrer String': currentUserDocument?.refCode ?? ''});
                                              analytics.logEvent('Sign Up: Entered Name');
                                              } catch(e) {}
                              
                              await currentUserReference!
                                  .update(createUsersRecordData(
                                name: widget.name,
                                publicName: widget.name,
                                nextSignUpStage: SetUpStage.birthday,
                              ));
                            } else {
                              // Set public name in public profile

                              var publicProfileRecordReference =
                                  PublicProfileRecord.createDoc(
                                      currentUserReference!);
                              await publicProfileRecordReference
                                  .set(createPublicProfileRecordData(
                                publicName: widget.name,
                                uid: currentUserUid,
                              ));
                              _model.publicDocument =
                                  PublicProfileRecord.getDocumentFromData(
                                      createPublicProfileRecordData(
                                        publicName: widget.name,
                                        uid: currentUserUid,
                                      ),
                                      publicProfileRecordReference);
                              // Save public profile document for user

                              await currentUserReference!
                                  .update(createUsersRecordData(
                                name: widget.name,
                                publicProfile: _model.publicDocument?.reference,
                                nextSignUpStage: SetUpStage.birthday,
                                publicName: widget.name,
                              ));
                            }

                            // Go to birthday

                            context.goNamed('Birthday');

                            setState(() {});
                          },
                        ),
                      ),
                    ),
                    Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(0.0, 26.0, 0.0, 0.0),
                      child: InkWell(
                        splashColor: Colors.transparent,
                        focusColor: Colors.transparent,
                        hoverColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        onTap: () async {
                          Navigator.pop(context);
                        },
                        child: Text(
                          'Edit Name',
                          style:
                              FlutterFlowTheme.of(context).bodyMedium.override(
                                    fontFamily: 'BT Beau Sans',
                                    color: const Color(0xFF4F5865),
                                    fontSize: 18.0,
                                    letterSpacing: 0.0,
                                    fontWeight: FontWeight.bold,
                                    useGoogleFonts: false,
                                  ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
