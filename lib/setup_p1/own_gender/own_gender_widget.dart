import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/gradient_button_disabled/gradient_button_disabled_widget.dart';
import '/general/info_sheet_scrollable/info_sheet_scrollable_widget.dart';
import '/setup_p1/men_extra_rules_new/men_extra_rules_new_widget.dart';
import '/setup_p1/women_extra_rules_new/women_extra_rules_new_widget.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'own_gender_model.dart';
export 'own_gender_model.dart';
import 'package:upgrader/upgrader.dart';
import 'package:chyrpe/amplitudeConfig.dart';

@Deprecated('This is an old sign up screen, no longer in use since summer 2024')
class OwnGenderWidget extends StatefulWidget {
  const OwnGenderWidget({super.key});

  @override
  State<OwnGenderWidget> createState() => _OwnGenderWidgetState();
}

class _OwnGenderWidgetState extends State<OwnGenderWidget> {
  late OwnGenderModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => OwnGenderModel());
    logFirebaseEvent('screen_view', parameters: {'screen_name': 'OwnGender'});
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

     return UpgradeAlert(
      dialogStyle: UpgradeDialogStyle.cupertino,
      showLater: false,
      showIgnore: false,
      child: GestureDetector(
      onTap: () => _model.unfocusNode.canRequestFocus
          ? FocusScope.of(context).requestFocus(_model.unfocusNode)
          : FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        body: SafeArea(
          top: true,
          child: Stack(
            children: [
              Align(
                alignment: const AlignmentDirectional(0.0, -1.0),
                child: Stack(
                  alignment: const AlignmentDirectional(1.0, -1.0),
                  children: [
                    Container(
                      width: MediaQuery.sizeOf(context).width * 1.0,
                      height: 5.0,
                      decoration: const BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Color(0xFF67B0E5), Color(0xFFF49BD1)],
                          stops: [0.0, 1.0],
                          begin: AlignmentDirectional(1.0, 0.0),
                          end: AlignmentDirectional(-1.0, 0),
                        ),
                      ),
                    ),
                    Container(
                      width: MediaQuery.sizeOf(context).width * 0.91,
                      height: 5.0,
                      decoration: const BoxDecoration(
                        color: Color(0xD4FFFFFF),
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(23.0, 0.0, 23.0, 0.0),
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(0.0, 70.0, 0.0, 25.0),
                      child: Text(
                        'What describes you best?',
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'BT Beau Sans',
                              fontSize: 32.0,
                              fontWeight: FontWeight.bold,
                              useGoogleFonts: false,
                              lineHeight: 1.29,
                            ),
                      ),
                    ),
                    Builder(
                      builder: (context) {
                        final options = GenderToShow.values.toList();
                        return Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children:
                              List.generate(options.length, (optionsIndex) {
                            final optionsItem = options[optionsIndex];
                            return Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 0.0, 0.0, 21.0),
                              child: InkWell(
                                splashColor: Colors.transparent,
                                focusColor: Colors.transparent,
                                hoverColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                onTap: () async {
                                  setState(() {
                                    _model.selectedGender = optionsItem;
                                  });
                                },
                                child: Material(
                                  color: Colors.transparent,
                                  elevation: 0.0,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(100.0),
                                  ),
                                  child: Container(
                                    height: 52.0,
                                    constraints: const BoxConstraints(
                                      maxWidth: 400.0,
                                    ),
                                    decoration: BoxDecoration(
                                      color: FlutterFlowTheme.of(context)
                                          .primaryBackground,
                                      borderRadius:
                                          BorderRadius.circular(100.0),
                                      border: Border.all(
                                        color:
                                            _model.selectedGender == optionsItem
                                                ? FlutterFlowTheme.of(context)
                                                    .accent2
                                                : FlutterFlowTheme.of(context)
                                                    .secondaryText,
                                        width: 2.0,
                                      ),
                                    ),
                                    child: Align(
                                      alignment: const AlignmentDirectional(0.0, 0.0),
                                      child: Text(
                                        optionsItem.name,
                                        style: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .override(
                                              fontFamily: 'BT Beau Sans',
                                              color:
                                                  FlutterFlowTheme.of(context)
                                                      .secondaryText,
                                              fontSize: 19.0,
                                              fontWeight: FontWeight.bold,
                                              useGoogleFonts: false,
                                            ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            );
                          }),
                        );
                      },
                    ),
                    FFButtonWidget(
                      onPressed: () async {
                        await showModalBottomSheet(
                          isScrollControlled: true,
                          backgroundColor: Colors.transparent,
                          enableDrag: false,
                          context: context,
                          builder: (context) {
                            return GestureDetector(
                              onTap: () => FocusScope.of(context).unfocus(),
                              child: Padding(
                                padding: MediaQuery.viewInsetsOf(context),
                                child: InfoSheetScrollableWidget(
                                  title: getRemoteConfigString(
                                      'signup_ownGender_sheet_title'),
                                  body: getRemoteConfigString(
                                      'signup_ownGender_info'),
                                ),
                              ),
                            );
                          },
                        ).then((value) => safeSetState(() {}));
                      },
                      text: getRemoteConfigString(
                          'signup_ownGender_info_button_title'),
                      icon: Icon(
                        Icons.info_outlined,
                        color: FlutterFlowTheme.of(context).secondaryText,
                        size: 18.0,
                      ),
                      options: FFButtonOptions(
                        height: 40.0,
                        padding: const EdgeInsetsDirectional.fromSTEB(
                            15.0, 0.0, 15.0, 0.0),
                        iconPadding:
                            const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                        color: Colors.white,
                        textStyle: FlutterFlowTheme.of(context)
                            .titleSmall
                            .override(
                              fontFamily: 'BT Beau Sans',
                              color: FlutterFlowTheme.of(context).secondaryText,
                              fontSize: 4.0,
                              letterSpacing: 0.0,
                              fontWeight: FontWeight.w500,
                              useGoogleFonts: false,
                            ),
                        elevation: 0.0,
                        borderSide: const BorderSide(
                          color: Color(0x0057636C),
                          width: 0.0,
                        ),
                        borderRadius: BorderRadius.circular(100.0),
                      ),
                    ),
                    const Spacer(),
                    Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 47.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          if (_model.selectedGender != null)
                            Builder(
                              builder: (context) => wrapWithModel(
                                model: _model.gradientButtonModel,
                                updateCallback: () => setState(() {}),
                                child: GradientButtonWidget(
                                  title: 'Next',
                                  action: () async {
                                    if (_model.selectedGender != null) {

                                      try {
                                      analytics.setUserProperties({'Gender': _model.selectedGender?.name ?? ''});
                                      analytics.logEvent('Sign Up: Selected Gender', eventProperties: {"Gender Selected": _model.selectedGender?.name ?? ''});
                                      } catch (error) {}

                                      // Set gender to user 
                                      await currentUserReference!
                                          .update(createUsersRecordData(
                                        gender: _model.selectedGender ==
                                                GenderToShow.Female
                                            ? Gender.Female
                                            : Gender.Male,
                                        nextSignUpStage: SetUpStage.name,
                                        alternativeG: (_model.selectedGender ==
                                                    GenderToShow.Male) ||
                                                (_model.selectedGender ==
                                                    GenderToShow.Female)
                                            ? false
                                            : true,
                                        signUpTestingCohort: functions
                                            .getRandomSignUpTestingCohort(functions
                                                .getNumberArrayFromJsonString(
                                                    getRemoteConfigString(
                                                        'signup_testing_cohorts_chances'))
                                                .toList()),
                                      ));

                                      try {
                                      if (currentUserDocument?.signUpTestingCohort == SignUpTestingCohort.Confirmation) {
                                      analytics.setUserProperties({'Sign Up Cohort': 'Confirmation'});
                                      analytics.logEvent('Sign Up Cohort Choice: Confirmation');
                                      } else if (currentUserDocument?.signUpTestingCohort == SignUpTestingCohort.NoConfirmation){
                                      analytics.setUserProperties({'Sign Up Cohort': 'No Confirmation'});
                                      analytics.logEvent('Sign Up Cohort Choice: No Confirmation');
                                      }
                                      } catch (error) {}

                                      if (currentUserDocument?.publicProfile !=
                                          null) {
                                        // Set gender to public profile

                                        await currentUserDocument!
                                            .publicProfile!
                                            .update(
                                                createPublicProfileRecordData(
                                          gender: _model.selectedGender ==
                                                  GenderToShow.Female
                                              ? Gender.Female
                                              : Gender.Male,
                                          alternativeG:
                                              (_model.selectedGender ==
                                                          GenderToShow.Male) ||
                                                      (_model.selectedGender ==
                                                          GenderToShow.Female)
                                                  ? false
                                                  : true,
                                          displayG: _model.selectedGender?.name,
                                        ));
                                      } else {
                                        // Set public name in public profile

                                        var publicProfileRecordReference =
                                            PublicProfileRecord.createDoc(
                                                currentUserReference!);
                                        await publicProfileRecordReference
                                            .set(createPublicProfileRecordData(
                                          uid: currentUserUid,
                                          gender: _model.selectedGender ==
                                                  GenderToShow.Female
                                              ? Gender.Female
                                              : Gender.Male,
                                          alternativeG:
                                              (_model.selectedGender ==
                                                          GenderToShow.Male) ||
                                                      (_model.selectedGender ==
                                                          GenderToShow.Female)
                                                  ? false
                                                  : true,
                                          displayG: _model.selectedGender?.name,
                                        ));
                                        _model.publicDocument = PublicProfileRecord
                                            .getDocumentFromData(
                                                createPublicProfileRecordData(
                                                  uid: currentUserUid,
                                                  gender: _model
                                                              .selectedGender ==
                                                          GenderToShow.Female
                                                      ? Gender.Female
                                                      : Gender.Male,
                                                  alternativeG:
                                                      (_model.selectedGender ==
                                                                  GenderToShow
                                                                      .Male) ||
                                                              (_model.selectedGender ==
                                                                  GenderToShow
                                                                      .Female)
                                                          ? false
                                                          : true,
                                                  displayG: _model
                                                      .selectedGender?.name,
                                                ),
                                                publicProfileRecordReference);
                                        // Save public profile document for user

                                        await currentUserReference!
                                            .update(createUsersRecordData(
                                          publicProfile:
                                              _model.publicDocument?.reference,
                                        ));
                                      }

                                      if (currentUserDocument?.gender ==
                                          Gender.Male) {
                                        if (getRemoteConfigBool(
                                            'show_additional_male_rules')) {
                                          await showModalBottomSheet(
                                            isScrollControlled: true,
                                            backgroundColor: Colors.transparent,
                                            isDismissible: false,
                                            enableDrag: false,
                                            useSafeArea: true,
                                            context: context,
                                            builder: (context) {
                                              return GestureDetector(
                                                onTap: () =>
                                                    FocusScope.of(context)
                                                        .unfocus(),
                                                child: Padding(
                                                  padding:
                                                      MediaQuery.viewInsetsOf(
                                                          context),
                                                  child: SizedBox(
                                                    height: MediaQuery.sizeOf(
                                                                context)
                                                            .height *
                                                        1.0,
                                                    child:
                                                        const MenExtraRulesNewWidget(),
                                                  ),
                                                ),
                                              );
                                            },
                                          ).then(
                                              (value) => safeSetState(() {}));
                                        } else {
                                          // Navigate to preferred gender

                                          context.goNamed('Name');
                                        }
                                      } else {
                                        if (getRemoteConfigBool(
                                            'show_additional_female_rules')) {
                                          await showModalBottomSheet(
                                            isScrollControlled: true,
                                            backgroundColor: Colors.transparent,
                                            isDismissible: false,
                                            enableDrag: false,
                                            useSafeArea: true,
                                            context: context,
                                            builder: (context) {
                                              return GestureDetector(
                                                onTap: () =>
                                                    FocusScope.of(context)
                                                        .unfocus(),
                                                child: Padding(
                                                  padding:
                                                      MediaQuery.viewInsetsOf(
                                                          context),
                                                  child: SizedBox(
                                                    height: MediaQuery.sizeOf(
                                                                context)
                                                            .height *
                                                        1.0,
                                                    child:
                                                        const WomenExtraRulesNewWidget(),
                                                  ),
                                                ),
                                              );
                                            },
                                          ).then(
                                              (value) => safeSetState(() {}));
                                        } else {
                                          // Navigate to preferred gender

                                          context.goNamed('Name');
                                        }
                                      }
                                    } else {
                                      await showDialog(
                                        context: context,
                                        builder: (dialogContext) {
                                          return Dialog(
                                            elevation: 0,
                                            insetPadding: EdgeInsets.zero,
                                            backgroundColor: Colors.transparent,
                                            alignment: const AlignmentDirectional(
                                                    0.0, 0.0)
                                                .resolve(
                                                    Directionality.of(context)),
                                            child: GestureDetector(
                                              onTap: () => _model.unfocusNode
                                                      .canRequestFocus
                                                  ? FocusScope.of(context)
                                                      .requestFocus(
                                                          _model.unfocusNode)
                                                  : FocusScope.of(context)
                                                      .unfocus(),
                                              child: const GeneralPopupWidget(
                                                alertTitle:
                                                    'No option selected',
                                                alertText:
                                                    'Please select an option to continue',
                                              ),
                                            ),
                                          );
                                        },
                                      ).then((value) => setState(() {}));
                                    }

                                    setState(() {});
                                  },
                                ),
                              ),
                            ),
                          if (_model.selectedGender == null)
                            wrapWithModel(
                              model: _model.gradientButtonDisabledModel,
                              updateCallback: () => setState(() {}),
                              child: GradientButtonDisabledWidget(
                                title: 'Next',
                                action: () async {},
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      ),
    );
  }
}
