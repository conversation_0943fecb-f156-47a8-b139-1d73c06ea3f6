import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/gradient_button_disabled/gradient_button_disabled_widget.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'preferred_gender_model.dart';
export 'preferred_gender_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';


class PreferredGenderWidget extends StatefulWidget {
  const PreferredGenderWidget({super.key});

  @override
  State<PreferredGenderWidget> createState() => _PreferredGenderWidgetState();
}

class _PreferredGenderWidgetState extends State<PreferredGenderWidget> {
  late PreferredGenderModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => PreferredGenderModel());
    logFirebaseEvent('screen_view', parameters: {'screen_name': 'PreferredGender'});
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return AuthUserStreamWidget(
      builder: (context) => StreamBuilder<List<OptionsForSelectorsRecord>>(
        stream: queryOptionsForSelectorsRecord(
          queryBuilder: (optionsForSelectorsRecord) =>
              optionsForSelectorsRecord.where(
            'name',
            isEqualTo: () {
              if (valueOrDefault<bool>(
                  currentUserDocument?.alternativeG, false)) {
                return 'Alternative Gender Reqs';
              } else if (currentUserDocument?.gender == Gender.Male) {
                return 'Male Gender Reqs';
              } else {
                return 'Female Gender Reqs';
              }
            }(),
          ),
          singleRecord: true,
        ),
        builder: (context, snapshot) {
          // Customize what your widget looks like when it's loading.
          if (!snapshot.hasData) {
            return Scaffold(
              backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
              body: Center(
                child: SizedBox(
                  width: 50.0,
                  height: 50.0,
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      FlutterFlowTheme.of(context).accent2,
                    ),
                  ),
                ),
              ),
            );
          }
          List<OptionsForSelectorsRecord>
              preferredGenderOptionsForSelectorsRecordList = snapshot.data!;
          final preferredGenderOptionsForSelectorsRecord =
              preferredGenderOptionsForSelectorsRecordList.isNotEmpty
                  ? preferredGenderOptionsForSelectorsRecordList.first
                  : null;
          return GestureDetector(
            onTap: () => _model.unfocusNode.canRequestFocus
                ? FocusScope.of(context).requestFocus(_model.unfocusNode)
                : FocusScope.of(context).unfocus(),
            child: Scaffold(
              key: scaffoldKey,
              backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
              body: SafeArea(
                top: true,
                child: Stack(
                  children: [
                    Align(
                      alignment: const AlignmentDirectional(0.0, -1.0),
                      child: Stack(
                        alignment: const AlignmentDirectional(1.0, -1.0),
                        children: [
                          Container(
                            width: MediaQuery.sizeOf(context).width * 1.0,
                            height: 5.0,
                            decoration: const BoxDecoration(
                              gradient: LinearGradient(
                                colors: [Color(0xFF67B0E5), Color(0xFFF49BD1)],
                                stops: [0.0, 1.0],
                                begin: AlignmentDirectional(1.0, 0.0),
                                end: AlignmentDirectional(-1.0, 0),
                              ),
                            ),
                          ),
                          Container(
                            width: MediaQuery.sizeOf(context).width * 0.64,
                            height: 5.0,
                            decoration: const BoxDecoration(
                              color: Color(0xD4FFFFFF),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(23.0, 0.0, 23.0, 0.0),
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 70.0, 0.0, 25.0),
                              child: Text(
                                'Who would you like to see?',
                                style: FlutterFlowTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'BT Beau Sans',
                                      fontSize: 32.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.bold,
                                      useGoogleFonts: false,
                                      lineHeight: 1.29,
                                    ),
                              ),
                            ),
                            Builder(
                              builder: (context) {
                                final preferredGenderVar =
                                    preferredGenderOptionsForSelectorsRecord
                                            ?.options
                                            .toList() ??
                                        [];
                                return Column(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children:
                                      List.generate(preferredGenderVar.length,
                                          (preferredGenderVarIndex) {
                                    final preferredGenderVarItem =
                                        preferredGenderVar[
                                            preferredGenderVarIndex];
                                    return Padding(
                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                          0.0, 0.0, 0.0, 21.0),
                                      child: InkWell(
                                        splashColor: Colors.transparent,
                                        focusColor: Colors.transparent,
                                        hoverColor: Colors.transparent,
                                        highlightColor: Colors.transparent,
                                        onTap: () async {
                                          setState(() {
                                            _model.selectedGender =
                                                preferredGenderVarItem;
                                          });
                                        },
                                        child: Material(
                                          color: Colors.transparent,
                                          elevation: 0.0,
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(100.0),
                                          ),
                                          child: Container(
                                            height: 52.0,
                                            constraints: const BoxConstraints(
                                              maxWidth: 400.0,
                                            ),
                                            decoration: BoxDecoration(
                                              color:
                                                  FlutterFlowTheme.of(context)
                                                      .primaryBackground,
                                              borderRadius:
                                                  BorderRadius.circular(100.0),
                                              border: Border.all(
                                                color: _model.selectedGender ==
                                                        preferredGenderVarItem
                                                    ? FlutterFlowTheme.of(
                                                            context)
                                                        .accent2
                                                    : FlutterFlowTheme.of(
                                                            context)
                                                        .secondaryText,
                                                width: 2.0,
                                              ),
                                            ),
                                            child: Align(
                                              alignment: const AlignmentDirectional(
                                                  0.0, 0.0),
                                              child: Text(
                                                preferredGenderVarItem,
                                                style:
                                                    FlutterFlowTheme.of(context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          color: FlutterFlowTheme
                                                                  .of(context)
                                                              .secondaryText,
                                                          fontSize: 19.0,
                                                          letterSpacing: 0.0,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                          useGoogleFonts: false,
                                                        ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    );
                                  }),
                                );
                              },
                            ),
                            if ((getRemoteConfigString(
                                            'signup_genderPref_info') !=
                                        '') &&
                                (getRemoteConfigString(
                                        'signup_genderPref_info') !=
                                    ' '))
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 12.0, 0.0, 25.0),
                                child: Text(
                                  getRemoteConfigString(
                                      'signup_genderPref_info'),
                                  style: FlutterFlowTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        fontSize: 15.0,
                                        letterSpacing: 0.0,
                                        useGoogleFonts: false,
                                        lineHeight: 1.21,
                                      ),
                                ),
                              ),
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 0.0, 0.0, 47.0),
                              child: Column(
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  if (_model.selectedGender != null &&
                                      _model.selectedGender != '')
                                    Builder(
                                      builder: (context) => wrapWithModel(
                                        model: _model.gradientButtonModel,
                                        updateCallback: () => setState(() {}),
                                        child: GradientButtonWidget(
                                          title: 'Next',
                                          action: () async {
                                            if (_model.selectedGender != null &&
                                                _model.selectedGender != '') {
                                                  analytics.logEvent('Sign Up: Entered Orientation', eventProperties: {'Orientation Selected': _model.selectedGender});
                                                  analytics.setUserProperties({'Ortientation': _model.selectedGender});
                                              await currentUserReference!
                                                  .update(createUsersRecordData(
                                                genderReq:
                                                    _model.selectedGender,
                                              ));
                                              if (getRemoteConfigBool(
                                                  'hideRoleForAll')) {
                                                if (getRemoteConfigBool(
                                                    'hideXpForAll')) {
                                                  context.pushNamed(
                                                      'RelationshipAims');
                                                } else {
                                                  context.pushNamed(
                                                      'ExperienceLevel');
                                                }
                                              } else {
                                                context.pushNamed('Kink');
                                              }
                                            } else {
                                              await showDialog(
                                                context: context,
                                                builder: (dialogContext) {
                                                  return Dialog(
                                                    elevation: 0,
                                                    insetPadding:
                                                        EdgeInsets.zero,
                                                    backgroundColor:
                                                        Colors.transparent,
                                                    alignment:
                                                        const AlignmentDirectional(
                                                                0.0, 0.0)
                                                            .resolve(
                                                                Directionality.of(
                                                                    context)),
                                                    child: GestureDetector(
                                                      onTap: () => _model
                                                              .unfocusNode
                                                              .canRequestFocus
                                                          ? FocusScope.of(
                                                                  context)
                                                              .requestFocus(_model
                                                                  .unfocusNode)
                                                          : FocusScope.of(
                                                                  context)
                                                              .unfocus(),
                                                      child: const GeneralPopupWidget(
                                                        alertTitle:
                                                            'No option selected',
                                                        alertText:
                                                            'Please select an option to continue',
                                                      ),
                                                    ),
                                                  );
                                                },
                                              ).then(
                                                  (value) => setState(() {}));
                                            }
                                          },
                                        ),
                                      ),
                                    ),
                                  if (_model.selectedGender == null ||
                                      _model.selectedGender == '')
                                    wrapWithModel(
                                      model: _model.gradientButtonDisabledModel,
                                      updateCallback: () => setState(() {}),
                                      child: GradientButtonDisabledWidget(
                                        title: 'Next',
                                        action: () async {},
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
