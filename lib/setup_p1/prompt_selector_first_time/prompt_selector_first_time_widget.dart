import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/setup_p1/prompt_editor_first_time/prompt_editor_first_time_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'prompt_selector_first_time_model.dart';
export 'prompt_selector_first_time_model.dart';

class PromptSelectorFirstTimeWidget extends StatefulWidget {
  const PromptSelectorFirstTimeWidget({
    super.key,
    this.prePrompt,
  });

  final String? prePrompt;

  @override
  State<PromptSelectorFirstTimeWidget> createState() =>
      _PromptSelectorFirstTimeWidgetState();
}

class _PromptSelectorFirstTimeWidgetState
    extends State<PromptSelectorFirstTimeWidget> {
  late PromptSelectorFirstTimeModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => PromptSelectorFirstTimeModel());

    // On component load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      if (widget.prePrompt != null && widget.prePrompt != '') {
        _model.result = widget.prePrompt!;
        _model.updatePage(() {});
      }
    });
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      elevation: 5.0,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(0.0),
          bottomRight: Radius.circular(0.0),
          topLeft: Radius.circular(16.0),
          topRight: Radius.circular(16.0),
        ),
      ),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: FlutterFlowTheme.of(context).primaryBackground,
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(0.0),
            bottomRight: Radius.circular(0.0),
            topLeft: Radius.circular(16.0),
            topRight: Radius.circular(16.0),
          ),
        ),
        child: Padding(
          padding: const EdgeInsetsDirectional.fromSTEB(17.0, 0.0, 17.0, 0.0),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(0.0, 12.0, 0.0, 0.0),
                child: Container(
                  width: 50.0,
                  height: 4.0,
                  decoration: BoxDecoration(
                    color: FlutterFlowTheme.of(context).alternate,
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                ),
              ),
              Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    splashColor: Colors.transparent,
                    focusColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    onTap: () async {
                      Navigator.pop(context);
                    },
                    child: Icon(
                      Icons.close_sharp,
                      color: FlutterFlowTheme.of(context).secondaryText,
                      size: 30.0,
                    ),
                  ),
                ],
              ),
              Align(
                alignment: const AlignmentDirectional(-1.0, 0.0),
                child: Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(0.0, 20.0, 0.0, 0.0),
                  child: Text(
                    'Select a prompt',
                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                          fontFamily: 'BT Beau Sans',
                          fontSize: 20.0,
                          letterSpacing: 0.0,
                          fontWeight: FontWeight.w600,
                          useGoogleFonts: false,
                        ),
                  ),
                ),
              ),
              Flexible(
                child: StreamBuilder<List<OptionsForSelectorsRecord>>(
                  stream: queryOptionsForSelectorsRecord(
                    queryBuilder: (optionsForSelectorsRecord) =>
                        optionsForSelectorsRecord.where(
                      'name',
                      isEqualTo: 'Bio Prompts incl. Categories',
                    ),
                    singleRecord: true,
                  ),
                  builder: (context, snapshot) {
                    // Customize what your widget looks like when it's loading.
                    if (!snapshot.hasData) {
                      return Center(
                        child: SizedBox(
                          width: 50.0,
                          height: 50.0,
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                              FlutterFlowTheme.of(context).accent2,
                            ),
                          ),
                        ),
                      );
                    }
                    List<OptionsForSelectorsRecord>
                        containerOptionsForSelectorsRecordList = snapshot.data!;
                    // Return an empty Container when the item does not exist.
                    if (snapshot.data!.isEmpty) {
                      return Container();
                    }
                    final containerOptionsForSelectorsRecord =
                        containerOptionsForSelectorsRecordList.isNotEmpty
                            ? containerOptionsForSelectorsRecordList.first
                            : null;

                    return Container(
                      decoration: const BoxDecoration(),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 20.0, 20.0, 0.0),
                            child: Builder(
                              builder: (context) {
                                final promptCategories =
                                    containerOptionsForSelectorsRecord
                                            ?.promptOptions
                                            .map((e) => e.category)
                                            .toList()
                                            .unique((e) => e)
                                            .toList() ??
                                        [];

                                return SingleChildScrollView(
                                  scrollDirection: Axis.horizontal,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children:
                                        List.generate(promptCategories.length,
                                            (promptCategoriesIndex) {
                                      final promptCategoriesItem =
                                          promptCategories[
                                              promptCategoriesIndex];
                                      return FFButtonWidget(
                                        onPressed: () async {
                                          _model.selectedCategory =
                                              promptCategoriesItem;
                                          setState(() {});
                                        },
                                        text: promptCategoriesItem,
                                        options: FFButtonOptions(
                                          height: 33.0,
                                          padding:
                                              const EdgeInsetsDirectional.fromSTEB(
                                                  24.0, 0.0, 24.0, 0.0),
                                          iconPadding:
                                              const EdgeInsetsDirectional.fromSTEB(
                                                  0.0, 0.0, 0.0, 0.0),
                                          color: _model.selectedCategory ==
                                                  promptCategoriesItem
                                              ? FlutterFlowTheme.of(context)
                                                  .secondaryText
                                              : const Color(0x00FFFFFF),
                                          textStyle:
                                              FlutterFlowTheme.of(context)
                                                  .titleSmall
                                                  .override(
                                                    fontFamily: 'BT Beau Sans',
                                                    color: _model
                                                                .selectedCategory ==
                                                            promptCategoriesItem
                                                        ? Colors.white
                                                        : FlutterFlowTheme.of(
                                                                context)
                                                            .secondaryText,
                                                    fontSize: 14.0,
                                                    letterSpacing: 0.0,
                                                    useGoogleFonts: false,
                                                  ),
                                          elevation: 0.0,
                                          borderSide: BorderSide(
                                            color: FlutterFlowTheme.of(context)
                                                .secondaryText,
                                            width: 1.0,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(100.0),
                                        ),
                                      );
                                    }).divide(const SizedBox(width: 8.0)),
                                  ),
                                );
                              },
                            ),
                          ),
                          Flexible(
                            child: Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 25.0, 0.0, 0.0),
                              child: Builder(
                                builder: (context) {
                                  final containerVar =
                                      containerOptionsForSelectorsRecord
                                              ?.promptOptions
                                              .where((e) =>
                                                  e.category ==
                                                  _model.selectedCategory)
                                              .toList()
                                              .map((e) => e.prompt)
                                              .toList()
                                              .toList() ??
                                          [];

                                  return SingleChildScrollView(
                                    child: Column(
                                      mainAxisSize: MainAxisSize.max,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.stretch,
                                      children:
                                          List.generate(containerVar.length,
                                              (containerVarIndex) {
                                        final containerVarItem =
                                            containerVar[containerVarIndex];
                                        return Column(
                                          mainAxisSize: MainAxisSize.max,
                                          children: [
                                            Padding(
                                              padding: const EdgeInsetsDirectional
                                                  .fromSTEB(0.0, 6.0, 0.0, 6.0),
                                              child: InkWell(
                                                splashColor: Colors.transparent,
                                                focusColor: Colors.transparent,
                                                hoverColor: Colors.transparent,
                                                highlightColor:
                                                    Colors.transparent,
                                                onTap: () async {
                                                  _model.result =
                                                      containerVarItem;
                                                  setState(() {});
                                                  Navigator.pop(context);
                                                  await showModalBottomSheet(
                                                    isScrollControlled: true,
                                                    backgroundColor:
                                                        Colors.transparent,
                                                    enableDrag: false,
                                                    useSafeArea: true,
                                                    context: context,
                                                    builder: (context) {
                                                      return Padding(
                                                        padding: MediaQuery
                                                            .viewInsetsOf(
                                                                context),
                                                        child: SizedBox(
                                                          height:
                                                              MediaQuery.sizeOf(
                                                                          context)
                                                                      .height *
                                                                  0.7,
                                                          child:
                                                              PromptEditorFirstTimeWidget(
                                                            prePrompt:
                                                                _model.result,
                                                          ),
                                                        ),
                                                      );
                                                    },
                                                  ).then((value) =>
                                                      safeSetState(() {}));
                                                },
                                                child: Row(
                                                  mainAxisSize:
                                                      MainAxisSize.max,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  children: [
                                                    Flexible(
                                                      child: Text(
                                                        containerVarItem,
                                                        style: FlutterFlowTheme
                                                                .of(context)
                                                            .bodyMedium
                                                            .override(
                                                              fontFamily:
                                                                  'BT Beau Sans',
                                                              fontSize: 15.0,
                                                              letterSpacing:
                                                                  0.0,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                              useGoogleFonts:
                                                                  false,
                                                            ),
                                                      ),
                                                    ),
                                                    Align(
                                                      alignment:
                                                          const AlignmentDirectional(
                                                              1.0, 0.0),
                                                      child: Column(
                                                        mainAxisSize:
                                                            MainAxisSize.max,
                                                        children: [
                                                          if (_model.result ==
                                                              containerVarItem)
                                                            const Icon(
                                                              Icons
                                                                  .check_rounded,
                                                              color: Color(
                                                                  0xFF9200D6),
                                                              size: 24.0,
                                                            ),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            Divider(
                                              thickness: 1.0,
                                              color:
                                                  FlutterFlowTheme.of(context)
                                                      .secondaryText,
                                            ),
                                          ],
                                        );
                                      }).addToEnd(const SizedBox(height: 100.0)),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}