import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/gradient_button_disabled/gradient_button_disabled_widget.dart';
import '/general/list_multiselect/list_multiselect_widget.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'relationship_aims_model.dart';
export 'relationship_aims_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';


class RelationshipAimsWidget extends StatefulWidget {
  const RelationshipAimsWidget({super.key});

  @override
  State<RelationshipAimsWidget> createState() => _RelationshipAimsWidgetState();
}

class _RelationshipAimsWidgetState extends State<RelationshipAimsWidget> {
  late RelationshipAimsModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => RelationshipAimsModel());
    logFirebaseEvent('screen_view', parameters: {'screen_name': 'RelationshipAims'});
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return StreamBuilder<List<OptionsForSelectorsRecord>>(
      stream: queryOptionsForSelectorsRecord(
        queryBuilder: (optionsForSelectorsRecord) =>
            optionsForSelectorsRecord.where(
          'name',
          isEqualTo: 'Relationship Aims',
        ),
        singleRecord: true,
      ),
      builder: (context, snapshot) {
        // Customize what your widget looks like when it's loading.
        if (!snapshot.hasData) {
          return Scaffold(
            backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
            body: Center(
              child: SizedBox(
                width: 50.0,
                height: 50.0,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    FlutterFlowTheme.of(context).accent2,
                  ),
                ),
              ),
            ),
          );
        }
        List<OptionsForSelectorsRecord>
            relationshipAimsOptionsForSelectorsRecordList = snapshot.data!;
        final relationshipAimsOptionsForSelectorsRecord =
            relationshipAimsOptionsForSelectorsRecordList.isNotEmpty
                ? relationshipAimsOptionsForSelectorsRecordList.first
                : null;
        return GestureDetector(
          onTap: () => _model.unfocusNode.canRequestFocus
              ? FocusScope.of(context).requestFocus(_model.unfocusNode)
              : FocusScope.of(context).unfocus(),
          child: Scaffold(
            key: scaffoldKey,
            backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
            body: SafeArea(
              top: true,
              child: AuthUserStreamWidget(
                builder: (context) => StreamBuilder<PublicProfileRecord>(
                  stream: PublicProfileRecord.getDocument(
                      currentUserDocument!.publicProfile!),
                  builder: (context, snapshot) {
                    // Customize what your widget looks like when it's loading.
                    if (!snapshot.hasData) {
                      return Center(
                        child: SizedBox(
                          width: 50.0,
                          height: 50.0,
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                              FlutterFlowTheme.of(context).accent2,
                            ),
                          ),
                        ),
                      );
                    }
                    final stackPublicProfileRecord = snapshot.data!;
                    return Stack(
                      children: [
                        Align(
                          alignment: const AlignmentDirectional(0.0, -1.0),
                          child: Stack(
                            alignment: const AlignmentDirectional(1.0, -1.0),
                            children: [
                              Container(
                                width: MediaQuery.sizeOf(context).width * 1.0,
                                height: 5.0,
                                decoration: const BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      Color(0xFF67B0E5),
                                      Color(0xFFF49BD1)
                                    ],
                                    stops: [0.0, 1.0],
                                    begin: AlignmentDirectional(1.0, 0.0),
                                    end: AlignmentDirectional(-1.0, 0),
                                  ),
                                ),
                              ),
                              Container(
                                width: MediaQuery.sizeOf(context).width * 0.37,
                                height: 5.0,
                                decoration: const BoxDecoration(
                                  color: Color(0xD4FFFFFF),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Column(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  23.0, 0.0, 23.0, 0.0),
                              child: Column(
                                mainAxisSize: MainAxisSize.max,
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                children: [
                                  Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        0.0, 70.0, 0.0, 30.0),
                                    child: Text(
                                      'I\'m looking for...',
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            fontFamily: 'BT Beau Sans',
                                            fontSize: 32.0,
                                            fontWeight: FontWeight.bold,
                                            useGoogleFonts: false,
                                            lineHeight: 1.29,
                                          ),
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        0.0, 0.0, 0.0, 30.0),
                                    child: wrapWithModel(
                                      model: _model.listMultiselectModel,
                                      updateCallback: () => setState(() {}),
                                      updateOnChange: true,
                                      child: ListMultiselectWidget(
                                        choices:
                                            relationshipAimsOptionsForSelectorsRecord!
                                                .options,
                                        previouslyMadeChoices:
                                            stackPublicProfileRecord
                                                .relationPreferences,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const Spacer(),
                            Align(
                              alignment: const AlignmentDirectional(0.0, 1.0),
                              child: Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    27.0, 0.0, 27.0, 47.0),
                                child: Column(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    if (_model.listMultiselectModel.choicesMade.isNotEmpty)
                                      wrapWithModel(
                                        model: _model.gradientButtonModel,
                                        updateCallback: () => setState(() {}),
                                        child: GradientButtonWidget(
                                          title: 'Next',
                                          action: () async {
                                            if (_model.listMultiselectModel
                                                .choicesMade.isNotEmpty) {
                                              // Set relationship preferences to public profile

                                              analytics.logEvent('Sign Up: Entered Relationship Preferences');
                                              analytics.setUserProperties({'Relationship Preferences': _model.listMultiselectModel.choicesMade});

                                              await currentUserDocument!
                                                  .publicProfile!
                                                  .update({
                                                ...mapToFirestore(
                                                  {
                                                    'relationPreferences': _model
                                                        .listMultiselectModel
                                                        .choicesMade,
                                                  },
                                                ),
                                              });
                                              if (getRemoteConfigBool(
                                                  'showHobbies')) {
                                                context.pushNamed('Hobbies');
                                              } else {
                                                if (getRemoteConfigBool(
                                                    'showEdu')) {
                                                  context
                                                      .pushNamed('Education');
                                                } else {
                                                  if (currentUserDocument
                                                          ?.signUpTestingCohort ==
                                                      SignUpTestingCohort
                                                          .Confirmation) {
                                                    context.pushNamed(
                                                        'BasicsConfirmationPage');
                                                  } else {
                                                    context.pushNamed('Images');
                                                  }
                                                }
                                              }
                                            } else {
                                              await showDialog(
                                                context: context,
                                                builder: (alertDialogContext) {
                                                  return AlertDialog(
                                                    title: const Text(
                                                        'No option selected'),
                                                    content: const Text(
                                                        'Please select at least one option to continue'),
                                                    actions: [
                                                      TextButton(
                                                        onPressed: () =>
                                                            Navigator.pop(
                                                                alertDialogContext),
                                                        child: const Text('Ok'),
                                                      ),
                                                    ],
                                                  );
                                                },
                                              );
                                            }
                                          },
                                        ),
                                      ),
                                    if (_model.listMultiselectModel.choicesMade.isEmpty)
                                      wrapWithModel(
                                        model:
                                            _model.gradientButtonDisabledModel,
                                        updateCallback: () => setState(() {}),
                                        child: GradientButtonDisabledWidget(
                                          title: 'Next',
                                          action: () async {},
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                        Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              10.0, 20.0, 0.0, 0.0),
                          child: FlutterFlowIconButton(
                            borderColor: Colors.transparent,
                            borderRadius: 20.0,
                            borderWidth: 1.0,
                            buttonSize: 40.0,
                            icon: FaIcon(
                              FontAwesomeIcons.angleLeft,
                              color: FlutterFlowTheme.of(context).primaryText,
                              size: 24.0,
                            ),
                            onPressed: () async {
                              if (getRemoteConfigBool('hideXpForAll')) {
                                if (getRemoteConfigBool('hideRoleForAll')) {
                                  context.goNamed(
                                    'PreferredGender',
                                    extra: <String, dynamic>{
                                      kTransitionInfoKey: const TransitionInfo(
                                        hasTransition: true,
                                        transitionType:
                                            PageTransitionType.leftToRight,
                                      ),
                                    },
                                  );
                                } else {
                                  context.goNamed(
                                    'Kink',
                                    extra: <String, dynamic>{
                                      kTransitionInfoKey: const TransitionInfo(
                                        hasTransition: true,
                                        transitionType:
                                            PageTransitionType.leftToRight,
                                      ),
                                    },
                                  );
                                }
                              } else {
                                context.goNamed(
                                  'ExperienceLevel',
                                  extra: <String, dynamic>{
                                    kTransitionInfoKey: const TransitionInfo(
                                      hasTransition: true,
                                      transitionType:
                                          PageTransitionType.leftToRight,
                                    ),
                                  },
                                );
                              }
                            },
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
