import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/components/notification_popup_widget.dart';
import 'package:chyrpe/discovery/popup/general_popup_new/general_popup_new_widget.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'images_verifications_model.dart';
export 'images_verifications_model.dart';
import 'package:verification/verification.dart';


class VeriImageFailedAuto extends StatelessWidget {
  final UsersRecord? currentUserDocument;

  const VeriImageFailedAuto({super.key, required this.currentUserDocument});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.max,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        VerificationHeading(
          text: currentUserDocument?.hasverificationCustomTitle() ?? false
              ? currentUserDocument!.verificationCustomTitle
              : getRemoteConfigString('images_verification_fail_title'),
          logoPath: "assets/images/purple_verifictions_logo.png",
          loaderPath: null,
          logoPadding: const EdgeInsets.only(bottom: 12),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(0, 18, 0, 0),
          child: VerificationSubtitle(
            text: currentUserDocument?.verificationCustomSubtitle ?? '',
            
          ),
        ),
        Expanded(
          child: Center(
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Padding(
                    padding: const EdgeInsets.fromLTRB(0, 20, 0, 0),
                    child: VerificationCheckList(
                      data: [
                        VerificationTileData(title: getRemoteConfigString('image_verification_fail_face_crtiterion'), isCompleted: currentUserDocument?.imageVeriHasFace ?? false),
                        VerificationTileData(title: getRemoteConfigString( 'image_verification_fail_explicit_crtiterion'), isCompleted: !(currentUserDocument?.imageVeriHasExplicit ?? false))
                      ]
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        VerificationButton(
          title: getRemoteConfigString('image_verification_fail_edit_btn'),
          isOutlined: currentUserDocument?.imageVeriMadeChanges ?? false,
          onTap: !(currentUserDocument?.imageVeriMadeChanges ?? false) ? () async {
            context.pushNamed('EditProfileNew');
          } : null,
        ),
        VerificationButton(
          title: !(currentUserDocument?.imageVeriMadeChanges ?? false) ? getRemoteConfigString("image_verification_fail_make_changes_btn") : getRemoteConfigString("image_verification_fail_recheck_btn"),
          isOutlined: !(currentUserDocument?.imageVeriMadeChanges ?? false),
          onTap: (currentUserDocument?.imageVeriMadeChanges ?? false) ? () async {
            await currentUserReference?.update(createUsersRecordData(autoVerificationStarted: false));
          } : null
        ),
        VerificationButton(
          title: getRemoteConfigString("verifications_request_manual_confirmation_btn"),
          isTertiary: true,
          onTap: () async {
            await showDialog(
                                    context: context,
                                    builder: (dialogContext) {
                                      return Dialog(
                                        elevation: 0,
                                        insetPadding: EdgeInsets.zero,
                                        backgroundColor: Colors.transparent,
                                        alignment:
                                            const AlignmentDirectional(0.0, 0.0)
                                                .resolve(
                                                    Directionality.of(context)),
                                        child: GeneralPopupNewWidget(
                                          title: getRemoteConfigString("verifications_request_manual_confirmation_title"),
                                          body:
                                              getRemoteConfigString("verifications_request_manual_confirmation_body"),
                                          buttonTitle: getRemoteConfigString("verifications_request_manual_confirmation_btn"),
                                          buttonAction: () async {
                                            await FirebaseFunctions.instanceFor(
                                                region: 'europe-west1')
                                            .httpsCallable('imageVerificationRequestReview')
                                            .call();

                                            Navigator.of(context).pop();
                                          },
                                        ),
                                      );
                                    },
                                  );
            
          },
        ),
      ],
    );
  }
}

class VeriImageInProgressManual extends StatelessWidget {
  final UsersRecord? currentUserDocument;

  const VeriImageInProgressManual({super.key, required this.currentUserDocument});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.max,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            children: [
              VerificationHeading(
                text: currentUserDocument?.hasverificationCustomTitle() ?? false
                    ? currentUserDocument!.verificationCustomTitle
                    : getRemoteConfigString('image_verification_in_progress_manual_finish_now'),
                logoPath: "assets/images/purple_verifictions_logo.png",
                loaderPath: "assets/images/purple_verifictions_loading_circle.png",
                logoPadding: const EdgeInsets.only(bottom: 12),
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(0, 18, 0, 0),
                child: VerificationSubtitle(
                  text: currentUserDocument?.verificationCustomSubtitle ?? '',
                  
                ),
              ),
            ],
          ),
        ),
        VerificationButton(
          title: (currentUserDocument?.pushNotificationAllow ?? false) ? getRemoteConfigString("image_verification_in_progress_pn_activated") : getRemoteConfigString("image_verification_in_progress_pn_activate"),
          isOutlined: currentUserDocument?.imageVeriMadeChanges ?? false,
          onTap: !(currentUserDocument?.pushNotificationAllow ?? false) ? () async {
            await showDialog(
                              context: context,
                              builder: (dialogContext) {
                                return Dialog(
                                  elevation: 0,
                                  insetPadding: EdgeInsets.zero,
                                  backgroundColor: Colors.transparent,
                                  alignment: const AlignmentDirectional(0.0, 0.0)
                                      .resolve(Directionality.of(context)),
                                  child: NotificationPopupWidget(
                                    alertTitle: getRemoteConfigString(
                                        'pn_allow_sl_question'),
                                    alertText: getRemoteConfigString(
                                        'pn_allow_sl_description'),
                                  ),
                                );
                              },
                            );
          } : null,
        )
       
            
          
      ],
    );
  }
}

class VeriImageFailedManual extends StatelessWidget {
  final UsersRecord? currentUserDocument;

  const VeriImageFailedManual({super.key, required this.currentUserDocument});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.max,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        VerificationHeading(
          text: currentUserDocument?.hasverificationCustomTitle() ?? false
              ? currentUserDocument!.verificationCustomTitle
              : getRemoteConfigString("image_verification_failed_manual_finish_now"),
          logoPath: "assets/images/purple_verifictions_logo.png",
          loaderPath: null,
          logoPadding: const EdgeInsets.only(bottom: 12),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(0, 18, 0, 0),
          child: VerificationSubtitle(
            text: currentUserDocument?.verificationCustomSubtitle ?? '',
            
          ),
        ),
        Expanded(
          child: Center(
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Padding(
                    padding: const EdgeInsets.fromLTRB(0, 20, 0, 0),
                    child: VerificationCheckList(
                      data: [
                        VerificationTileData(title: getRemoteConfigString("image_verification_fail_face_crtiterion"), isCompleted: currentUserDocument?.imageVeriHasFace ?? false),
                        VerificationTileData(title: getRemoteConfigString("image_verification_fail_explicit_crtiterion"), isCompleted: !(currentUserDocument?.imageVeriHasExplicit ?? false))
                      ]
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        VerificationButton(
          title: "Edit images",
          isOutlined: currentUserDocument?.imageVeriMadeChanges ?? false,
          onTap: !(currentUserDocument?.imageVeriMadeChanges ?? false) ? () async {
            context.pushNamed('EditProfileNew');
          } : null,
        ),
        !(currentUserDocument?.imageVeriMadeChanges ?? false) ? 
        
        Padding(
          padding: const EdgeInsets.fromLTRB(0, 14, 0, 0),
          child: Text(getRemoteConfigString("image_verification_failed_must_change_note"),
          style: TextStyle(fontSize: 11, fontFamily: "BT Beau Sans"), textAlign: TextAlign.center,),
        ) : VerificationButton(
          title: getRemoteConfigString("image_verification_failed_reattempt_now"),
          isOutlined: !(currentUserDocument?.imageVeriMadeChanges ?? false),
          onTap: () async {
            await FirebaseFunctions.instanceFor(
                                                region: 'europe-west1')
                                            .httpsCallable('imageVerificationRequestReview')
                                            .call();
            
          },
        ),
      ],
    );
  }
}
