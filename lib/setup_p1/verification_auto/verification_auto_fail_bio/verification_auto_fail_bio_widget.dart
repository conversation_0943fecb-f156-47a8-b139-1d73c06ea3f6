import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/button_no_outline/button_no_outline_widget.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import 'package:flutter/material.dart';
import 'verification_auto_fail_bio_model.dart';
export 'verification_auto_fail_bio_model.dart';

@Deprecated('No longer applied for new users. Still works for phasing out users. Bio now checked automatically, other fail widgets provide more customisation.')
class VerificationAutoFailBioWidget extends StatefulWidget {
  const VerificationAutoFailBioWidget({
    super.key,
    required this.mainCallback,
    required this.secondaryCallback,
  });

  final Future Function()? mainCallback;
  final Future Function()? secondaryCallback;

  @override
  State<VerificationAutoFailBioWidget> createState() =>
      _VerificationAutoFailBioWidgetState();
}

class _VerificationAutoFailBioWidgetState
    extends State<VerificationAutoFailBioWidget> {
  late VerificationAutoFailBioModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => VerificationAutoFailBioModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.sizeOf(context).width * 1.0,
      height: MediaQuery.sizeOf(context).height * 1.0,
      decoration: const BoxDecoration(),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Flexible(
            child: Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Flexible(
                  child: Padding(
                    padding:
                        const EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 10.0),
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          Align(
                            alignment: const AlignmentDirectional(0.0, 0.0),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(8.0),
                              child: Image.asset(
                                'assets/images/Grey_Verification_Failed_incl_halfcircle.png',
                                width: 63.0,
                                height: 63.0,
                                fit: BoxFit.contain,
                                alignment: const Alignment(0.0, 0.0),
                              ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 15.0, 0.0, 0.0),
                            child: Text(
                              getRemoteConfigString(
                                  'verification_auto_fail_bio_headline'),
                              textAlign: TextAlign.center,
                              style: FlutterFlowTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'BT Beau Sans',
                                    fontSize: 26.0,
                                    letterSpacing: 0.0,
                                    fontWeight: FontWeight.bold,
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 30.0, 0.0, 0.0),
                            child: Text(
                              getRemoteConfigString(
                                  'verification_auto_fail_bio_explanation'),
                              textAlign: TextAlign.center,
                              style: FlutterFlowTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'BT Beau Sans',
                                    letterSpacing: 0.0,
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              wrapWithModel(
                model: _model.gradientButtonModel,
                updateCallback: () => safeSetState(() {}),
                updateOnChange: true,
                child: GradientButtonWidget(
                  title: getRemoteConfigString(
                      'verification_auto_fail_try_again_btn'),
                  action: () async {
                    await widget.mainCallback?.call();
                  },
                ),
              ),
              wrapWithModel(
                model: _model.buttonNoOutlineModel,
                updateCallback: () => safeSetState(() {}),
                updateOnChange: true,
                child: ButtonNoOutlineWidget(
                  title: getRemoteConfigString(
                      'verification_auto_fail_req_review_btn'),
                  action: () async {
                    await widget.secondaryCallback?.call();
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
