import 'package:chyrpe/auth/firebase_auth/auth_util.dart';
import 'package:verification/verification.dart';

import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/button_no_outline/button_no_outline_widget.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import 'package:flutter/material.dart';
import 'verification_auto_fail_pic_model.dart';
export 'verification_auto_fail_pic_model.dart';

class VerificationAutoFailPicWidget extends StatefulWidget {
  const VerificationAutoFailPicWidget({
    super.key,
    required this.mainCallback,
    required this.secondaryCallback,
  });

  final Future Function()? mainCallback;
  final Future Function()? secondaryCallback;

  @override
  State<VerificationAutoFailPicWidget> createState() =>
      _VerificationAutoFailPicWidgetState();
}

class _VerificationAutoFailPicWidgetState
    extends State<VerificationAutoFailPicWidget> {
  late VerificationAutoFailPicModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => VerificationAutoFailPicModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.sizeOf(context).width * 1.0,
      height: MediaQuery.sizeOf(context).height * 1.0,
      decoration: const BoxDecoration(),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Flexible(
            child: Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Flexible(
                  child: Padding(
                    padding:
                        const EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 10.0),
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                           VerificationHeading(
                            text: currentUserDocument
                                        ?.hasverificationCustomTitle() ??
                                    false
                                ? currentUserDocument
                                        ?.verificationCustomTitle ??
                                    ''
                                : getRemoteConfigString(
                                    'verification_auto_rejection_headline'),
                            logoPath:
                                "assets/images/purple_verifictions_logo.png",
                            logoPadding: const EdgeInsets.only(bottom: 12),
                          ),
                          if (currentUserDocument
                                  ?.hasverificationCustomSubtitle() ??
                              false)
                            VerificationSubtitle(
                                text: currentUserDocument
                                        ?.verificationCustomSubtitle ??
                                    '',
                                padding: EdgeInsets.fromLTRB(0, 12, 0, 0)),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              wrapWithModel(
                model: _model.gradientButtonModel,
                updateCallback: () => safeSetState(() {}),
                updateOnChange: true,
                child: VerificationButton(
                  title: getRemoteConfigString(
                      'verification_auto_fail_try_again_btn'),
                  onTap: () async {
                    await widget.mainCallback?.call();
                  },
                ),
              ),
              if ((currentUserDocument?.previousVerifications.length ?? 0) >= 2)
              wrapWithModel(
                model: _model.buttonNoOutlineModel,
                updateCallback: () => safeSetState(() {}),
                updateOnChange: true,
                child: VerificationButton(
                  isTertiary: true,
                  title: getRemoteConfigString(
                      'verification_auto_fail_req_review_btn'),
                  onTap: () async {
                    await widget.secondaryCallback?.call();
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
