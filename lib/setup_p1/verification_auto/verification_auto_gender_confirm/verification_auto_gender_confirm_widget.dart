import 'package:chyrpe/flutter_flow/flutter_flow_widgets.dart';
import 'package:verification/verification.dart';

import '/auth/firebase_auth/auth_util.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'verification_auto_gender_confirm_model.dart';
export 'verification_auto_gender_confirm_model.dart';

class VerificationAutoGenderConfirmWidget extends StatefulWidget {
  const VerificationAutoGenderConfirmWidget({super.key});

  @override
  State<VerificationAutoGenderConfirmWidget> createState() =>
      _VerificationAutoGenderConfirmWidgetState();
}

class _VerificationAutoGenderConfirmWidgetState
    extends State<VerificationAutoGenderConfirmWidget> {
  late VerificationAutoGenderConfirmModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => VerificationAutoGenderConfirmModel());

    _model.textController ??=
        TextEditingController(text: currentUserDocument?.gender?.name);
    _model.textFieldFocusNode ??= FocusNode();
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.sizeOf(context).width * 1.0,
      height: MediaQuery.sizeOf(context).height * 1.0,
      decoration: const BoxDecoration(),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Flexible(
            child: Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Flexible(
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        VerificationHeading(
                          text:  getRemoteConfigString(
                                'verification_auto_gender_confirm_headline'),
                          logoPath:
                              "assets/images/purple_verifictions_logo.png",
                          logoPadding: const EdgeInsets.only(bottom: 12),
                        ),
                        VerificationSubtitle(
                            text: getRemoteConfigString(
                                'verification_auto_gender_confirm_subtitle'),
                            padding: EdgeInsets.fromLTRB(0, 18, 0, 0)
                            )
                        
                        
                      ],
                    ),
                  ),
                ),
                Flexible(
                  child: Padding(
                    padding:
                        const EdgeInsetsDirectional.fromSTEB(0.0, 31.0, 0.0, 0.0),
                    child: Stack(
                      children: [
                        Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              35.0, 0.0, 35.0, 0.0),
                          child: Container(
                            width: MediaQuery.sizeOf(context).width * 1.0,
                            constraints: const BoxConstraints(
                              minHeight: 51.0,
                              maxHeight: 200.0,
                            ),
                            decoration: BoxDecoration(
                              color: FlutterFlowTheme.of(context)
                                  .secondaryBackground,
                              borderRadius: BorderRadius.circular(11.0),
                              border: Border.all(
                                color: const Color(0xFFE9EAEE),
                                width: 1.0,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Flexible(
                                  child: Form(
                                    key: _model.formKey,
                                    autovalidateMode: AutovalidateMode.disabled,
                                    child: Padding(
                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                          10.0, 0.0, 10.0, 0.0),
                                      child: AuthUserStreamWidget(
                                        builder: (context) => TextFormField(
                                          controller: _model.textController,
                                          focusNode: _model.textFieldFocusNode,
                                          autofocus: false,
                                          obscureText: false,
                                          decoration: InputDecoration(
                                            hintText: 'Type here...',
                                            hintStyle:
                                                FlutterFlowTheme.of(context)
                                                    .labelMedium
                                                    .override(
                                                      fontFamily:
                                                          'BT Beau Sans',
                                                      letterSpacing: 0.0,
                                                      useGoogleFonts: false,
                                                    ),
                                            enabledBorder: InputBorder.none,
                                            focusedBorder: InputBorder.none,
                                            errorBorder: InputBorder.none,
                                            focusedErrorBorder:
                                                InputBorder.none,
                                          ),
                                          style: FlutterFlowTheme.of(context)
                                              .bodyMedium
                                              .override(
                                                fontFamily: 'BT Beau Sans',
                                                fontSize: 14.0,
                                                letterSpacing: 0.0,
                                                useGoogleFonts: false,
                                              ),
                                          maxLines: 5,
                                          minLines: 1,
                                          maxLength: 1000,
                                          maxLengthEnforcement:
                                              MaxLengthEnforcement.enforced,
                                          buildCounter: (context,
                                                  {required currentLength,
                                                  required isFocused,
                                                  maxLength}) =>
                                              null,
                                          validator: _model
                                              .textControllerValidator
                                              .asValidator(context),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              LayoutBuilder(
                builder: (BuildContext context, BoxConstraints constraints) => wrapWithModel(
                  model: _model.gradientButtonModel,
                  updateCallback: () => safeSetState(() {}),
                  updateOnChange: true,
                  child: FFButtonWidget(
                    options: FFButtonOptions(
                            borderRadius: BorderRadius.circular(100),
                            elevation: 0,
                            height: 42.0,
                            width: constraints.maxWidth,
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                20.0, 0.0, 20.0, 0.0),
                            iconPadding:
                                const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                            color: const Color(0xFFB99CE4),
                            textStyle:
                                FlutterFlowTheme.of(context).titleSmall.override(
                                      fontFamily: 'BT Beau Sans',
                                      color: Colors.white,
                                      fontSize: 12.0,
                                      letterSpacing: 0.0,
                                      useGoogleFonts: false,
                                    ),
                    ),
                    text: getRemoteConfigString(
                        'verification_auto_gender_confirm_btn'),
                    onPressed: () async {
                      var shouldSetState = false;
                      if (_model.formKey.currentState == null ||
                          !_model.formKey.currentState!.validate()) {
                        return;
                      }
                      try {
                        final result = await FirebaseFunctions.instanceFor(
                                region: 'europe-west2')
                            .httpsCallable('genderConfirm')
                            .call({
                          "confirmedGender": _model.textController.text,
                        });
                        _model.confirmGenderCf =
                            GenderConfirmCloudFunctionCallResponse(
                          succeeded: true,
                        );
                      } on FirebaseFunctionsException catch (error) {
                        _model.confirmGenderCf =
                            GenderConfirmCloudFunctionCallResponse(
                          errorCode: error.code,
                          succeeded: false,
                        );
                      }

                      shouldSetState = true;
                      if (!_model.confirmGenderCf!.succeeded!) {
                        await showDialog(
                          context: context,
                          builder: (dialogContext) {
                            return Dialog(
                              elevation: 0,
                              insetPadding: EdgeInsets.zero,
                              backgroundColor: Colors.transparent,
                              alignment: const AlignmentDirectional(0.0, 0.0)
                                  .resolve(Directionality.of(context)),
                              child: const GeneralPopupWidget(
                                alertText: 'Something went wrong',
                                alertTitle:
                                    'Please try again <NAME_EMAIL>',
                              ),
                            );
                          },
                        );

                        if (shouldSetState) safeSetState(() {});
                        return;
                      }
                      FFAppState().saveHintShown = false;
                      FFAppState().introCompleted = false;
                      FFAppState().update(() {});

                      context.goNamed('TempLoaderScreen');

                      if (shouldSetState) safeSetState(() {});
                    },
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
