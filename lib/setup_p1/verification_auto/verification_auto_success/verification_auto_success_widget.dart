import 'package:verification/verification.dart';

import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'verification_auto_success_model.dart';
export 'verification_auto_success_model.dart';

class VerificationAutoSuccessWidget extends StatefulWidget {
  const VerificationAutoSuccessWidget({super.key});

  @override
  State<VerificationAutoSuccessWidget> createState() =>
      _VerificationAutoSuccessWidgetState();
}

class _VerificationAutoSuccessWidgetState
    extends State<VerificationAutoSuccessWidget> {
  late VerificationAutoSuccessModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => VerificationAutoSuccessModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.sizeOf(context).width * 1.0,
      height: MediaQuery.sizeOf(context).height * 1.0,
      decoration: const BoxDecoration(),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Flexible(
            child: Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Flexible(
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        VerificationHeading(
                          text: currentUserDocument
                                      ?.hasverificationCustomTitle() ??
                                  false
                              ? currentUserDocument?.verificationCustomTitle ??
                                  ''
                              : getRemoteConfigString(
                                  'verification_success_headline'),
                          logoPath:
                              "assets/images/purple_verifictions_logo.png",
                          logoPadding: const EdgeInsets.only(bottom: 12),
                        ),
                        VerificationSubtitle(
                            text: currentUserDocument
                                        ?.hasverificationCustomSubtitle() ??
                                    false
                                ? currentUserDocument
                                        ?.verificationCustomSubtitle ??
                                    ''
                                : getRemoteConfigString(
                                    'verification_success_description'),
                            padding: EdgeInsets.fromLTRB(0, 33, 0, 0)
                            )
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              Builder(
                builder: (context) => wrapWithModel(
                  model: _model.gradientButtonModel,
                  updateCallback: () => safeSetState(() {}),
                  child: VerificationButton(
                    title: getRemoteConfigString(
                        'verification_success_getstarted_button_title'),
                    onTap: () async {
                      var shouldSetState = false;
                      try {
                        final result = await FirebaseFunctions.instanceFor(
                                region: 'europe-west1')
                            .httpsCallable('generateMatchesAdvanced')
                            .call({
                          "append": false,
                           "smoothSwiping": true,
                        });
                        _model.generateMatchesCf =
                            GenerateMatchesCloudFunctionCallResponse(
                          succeeded: true,
                        );
                      } on FirebaseFunctionsException catch (error) {
                        _model.generateMatchesCf =
                            GenerateMatchesCloudFunctionCallResponse(
                          errorCode: error.code,
                          succeeded: false,
                        );
                      }

                      if (!_model.generateMatchesCf!.succeeded!) {
                        await showDialog(
                          context: context,
                          builder: (dialogContext) {
                            return Dialog(
                              elevation: 0,
                              insetPadding: EdgeInsets.zero,
                              backgroundColor: Colors.transparent,
                              alignment: const AlignmentDirectional(0.0, 0.0)
                                  .resolve(Directionality.of(context)),
                              child: const GeneralPopupWidget(
                                alertTitle: 'Something went wrong',
                                alertText:
                                    'Please try again <NAME_EMAIL>',
                              ),
                            );
                          },
                        );

                        if (shouldSetState) safeSetState(() {});
                        return;
                      }
                      FFAppState().saveHintShown = false;
                      FFAppState().introCompleted = false;

                      try {
                        currentUserReference!.update(createUsersRecordData(
                          firstVerificationDone: true,
                        ));
                      } catch (e) {}

                      context.goNamed('TempLoaderScreen');

                      if (shouldSetState) safeSetState(() {});
                    },
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
