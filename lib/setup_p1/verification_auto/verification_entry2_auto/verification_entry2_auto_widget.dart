import 'package:verification/verification.dart';

import '/backend/schema/structs/index.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import 'package:flutter/material.dart';
import 'verification_entry2_auto_model.dart';
export 'verification_entry2_auto_model.dart';

class VerificationEntry2AutoWidget extends StatefulWidget {
  const VerificationEntry2AutoWidget({
    super.key,
    required this.callback,
    required this.title,
    required this.benefits,
    required this.btnTitle,
  });

  final Future Function()? callback;
  final String? title;
  final List<TitleDescriptionStruct>? benefits;
  final String? btnTitle;

  @override
  State<VerificationEntry2AutoWidget> createState() =>
      _VerificationEntry2AutoWidgetState();
}

class _VerificationEntry2AutoWidgetState
    extends State<VerificationEntry2AutoWidget> {
  late VerificationEntry2AutoModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => VerificationEntry2AutoModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.sizeOf(context).width * 1.0,
      height: MediaQuery.sizeOf(context).height * 1.0,
      decoration: const BoxDecoration(),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Flexible(
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        VerificationHeading(
                          text:valueOrDefault<String>(
                              widget.title,
                              getRemoteConfigString('verification_entry_2_auto_new_title'),
                            ),
                          logoPath:
                              "assets/images/purple_verifictions_logo.png",
                          loaderPath: null,
                          logoPadding: const EdgeInsets.only(bottom: 12),
                        ),
                        VerificationSubtitle(
                          text: getRemoteConfigString('verification_entry_2_auto_new_subtitle'),
                          padding: EdgeInsets.fromLTRB(0, 12, 0, 0),
                         
                        ),
                       
                      ],
                    ),
                  ),
                  Padding(
                    padding:
                        const EdgeInsetsDirectional.fromSTEB(0.0, 37.0, 0.0, 0.0),
                    child: Center(
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                             VerificationExplanationList(
                        data: (jsonDecode(getRemoteConfigString(
                                'image_verification_start_map_2')) as List)
                            .map((item) => VerificationTileData.fromMap(
                                item as Map<String, dynamic>))
                            .toList()
                            .cast<VerificationTileData>(),
                      ),
                      
                          ].divide(const SizedBox(height: 25.0)),
                        ),
                      ),
                    ),
                  ),
                ].addToEnd(const SizedBox(height: 30.0)),
              ),
            ),
          ),
          Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              wrapWithModel(
                model: _model.gradientButtonModel,
                updateCallback: () => safeSetState(() {}),
                updateOnChange: true,
                child: VerificationButton(
                  title: widget.btnTitle!,
                  onTap: () async {
                    await widget.callback?.call();
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
