import 'package:verification/verification.dart';
import '/auth/firebase_auth/auth_util.dart';
import '/backend/schema/structs/index.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import 'package:flutter/material.dart';
import 'verification_entry_auto_model.dart';
export 'verification_entry_auto_model.dart';

class VerificationEntryAutoWidget extends StatefulWidget {
  const VerificationEntryAutoWidget({
    super.key,
    required this.callback,
    required this.title,
    required this.benefits,
    required this.btnTitle,
  });

  final Future Function()? callback;
  final String? title;
  final List<TitleDescriptionStruct>? benefits;
  final String? btnTitle;

  @override
  State<VerificationEntryAutoWidget> createState() =>
      _VerificationEntryAutoWidgetState();
}

class _VerificationEntryAutoWidgetState
    extends State<VerificationEntryAutoWidget> {
  late VerificationEntryAutoModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => VerificationEntryAutoModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.sizeOf(context).width * 1.0,
      height: MediaQuery.sizeOf(context).height * 1.0,
      decoration: const BoxDecoration(),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Flexible(
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        VerificationHeading(
                          text: valueOrDefault<String>(
                              widget.title,
                              getRemoteConfigString(
                                  'verification_entry_auto_title')),
                          logoPath:
                              "assets/images/purple_verifictions_logo.png",
                          loaderPath: null,
                          logoPadding: const EdgeInsets.only(bottom: 12),
                        ),
                        VerificationSubtitle(
                          text: getRemoteConfigString(
                              'verification_entry_auto_subtitle'),
                          padding: EdgeInsets.fromLTRB(0, 12, 0, 0),
                          leading: Icon(
                            Icons.schedule,
                            color: Colors.black,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsetsDirectional.fromSTEB(
                        0.0, 37.0, 0.0, 0.0),
                    child: Builder(
                      builder: (context) {
                        final benefits = widget.benefits!.toList();

                        return SingleChildScrollView(
                          child: MultiStageList(
                            activeIndex: (!(currentUserDocument
                                            ?.imageVeriHasExplicit ??
                                        false) &&
                                    (currentUserDocument?.imageVeriHasFace ??
                                        false))
                                ? 2
                                : 1,
                            data: (jsonDecode(getRemoteConfigString(
                                    'image_verification_start_map')) as List)
                                .map((item) => VerificationTileData.fromMap(
                                    item as Map<String, dynamic>))
                                .toList()
                                .cast<VerificationTileData>(),
                          ),
                        );
                      },
                    ),
                  ),
                ].addToEnd(const SizedBox(height: 30.0)),
              ),
            ),
          ),
          Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              wrapWithModel(
                model: _model.gradientButtonModel,
                updateCallback: () => safeSetState(() {}),
                updateOnChange: true,
                child: VerificationButton(
                  title: widget.btnTitle!,
                  onTap: () async {
                    await widget.callback?.call();
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
