import 'package:chyrpe/components/popup_for_failed_verification_widget.dart';
import 'package:chyrpe/flutter_flow/remote_config/firebase_remote_config_util.dart';
import 'package:chyrpe/flutter_flow/flutter_flow_widgets.dart';
import 'package:verification/verification.dart';
import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import 'dart:async';
import '/custom_code/actions/index.dart' as actions;
import '/custom_code/widgets/index.dart' as custom_widgets;
import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'verification_get_ready_auto_model.dart';
export 'verification_get_ready_auto_model.dart';
import 'package:flutter_branch_sdk/flutter_branch_sdk.dart';
import '/backend/schema/enums/enums.dart';

class VerificationGetReadyAutoWidget extends StatefulWidget {
  const VerificationGetReadyAutoWidget({super.key});

  @override
  State<VerificationGetReadyAutoWidget> createState() =>
      _VerificationGetReadyAutoWidgetState();
}

class _VerificationGetReadyAutoWidgetState
    extends State<VerificationGetReadyAutoWidget> {
  late VerificationGetReadyAutoModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => VerificationGetReadyAutoModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(2.0, 0.0, 0.0, 0.0),
      child: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
        ),
        child: Padding(
          padding: const EdgeInsetsDirectional.fromSTEB(20.0, 20.0, 20.0, 20.0),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Flexible(
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Padding(
                        padding: const EdgeInsetsDirectional.fromSTEB(
                            0.0, 29.0, 0.0, 0.0),
                        child: Text(
                          getRemoteConfigString(
                              'verification_auto_get_ready_title'),
                          textAlign: TextAlign.center,
                          style:
                              FlutterFlowTheme.of(context).bodyMedium.override(
                                    fontFamily: 'BT Beau Sans',
                                    fontSize: 26.0,
                                    letterSpacing: 0.0,
                                    fontWeight: FontWeight.bold,
                                    useGoogleFonts: false,
                                  ),
                        ),
                      ),
                      if (getRemoteConfigString(
                              'verification_auto_get_ready_subtitle') !=
                          '')
                        Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              10.0, 20.0, 10.0, 10.0),
                          child: Text(
                            getRemoteConfigString(
                                'verification_auto_get_ready_subtitle'),
                            textAlign: TextAlign.center,
                            style: FlutterFlowTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'BT Beau Sans',
                                  fontSize: 14.0,
                                  letterSpacing: 0.0,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                      LayoutBuilder(builder:
                          (BuildContext context, BoxConstraints constraints) {
                        return Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              0.0, 0.0, 0.0, 0.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Padding(
                                padding: const EdgeInsets.all(40.0),
                                child: SizedBox(
                                  width: 200,
                                  height: 350,
                                  child: custom_widgets.SelfiePreviewOval(
                                    width: 200.0,
                                    height: 350.0,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      }),
                    ],
                  ),
                ),
                Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    LayoutBuilder(
                      builder:
                          (BuildContext context, BoxConstraints constraints) =>
                              wrapWithModel(
                        model: _model.gradientButtonModel,
                        updateCallback: () => safeSetState(() {}),
                        updateOnChange: true,
                        child: FFButtonWidget(
                          options: FFButtonOptions(
                            height: 42.0,
                            width: constraints.maxWidth,
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                20.0, 0.0, 20.0, 0.0),
                            iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 0.0, 0.0, 0.0),
                            color: const Color(0xFFB99CE4),
                            textStyle: FlutterFlowTheme.of(context)
                                .titleSmall
                                .override(
                                  fontFamily: 'BT Beau Sans',
                                  color: Colors.white,
                                  fontSize: 12.0,
                                  letterSpacing: 0.0,
                                  useGoogleFonts: false,
                                ),
                            elevation: 0.0,
                            borderRadius: BorderRadius.circular(100.0),
                          ),
                          text: getRemoteConfigString(
                              'verification_auto_get_ready_btn'),
                          onPressed: () async {
                            var shouldSetState = false;
                            try {
                              if (valueOrDefault(
                                  currentUserDocument?.previousVerifications,
                                  []).isEmpty) {
                                BranchEvent eventVeri0 =
                                    BranchEvent.customEvent(
                                        'pre_verification_requested');
                                FlutterBranchSdk.trackContentWithoutBuo(
                                    branchEvent: eventVeri0);

                                if (currentUserDocument?.gender ==
                                    Gender.Female) {
                                  BranchEvent eventVeri0Femme =
                                      BranchEvent.customEvent(
                                          'pre_verification_requested_female');
                                  FlutterBranchSdk.trackContentWithoutBuo(
                                      branchEvent: eventVeri0Femme);
                                }
                              }
                            } catch (e) {}

                            try {
                              final result =
                                  await FirebaseFunctions.instanceFor(
                                          region: 'europe-west2')
                                      .httpsCallable('getVerificationSessionID')
                                      .call({});
                              _model.sessionIDCf =
                                  GetVerificationSessionIDCloudFunctionCallResponse(
                                data:
                                    AwsSessionRegionStruct.fromMap(result.data),
                                succeeded: true,
                                resultAsString: result.data.toString(),
                                jsonBody: result.data,
                              );
                            } on FirebaseFunctionsException catch (error) {
                              _model.sessionIDCf =
                                  GetVerificationSessionIDCloudFunctionCallResponse(
                                errorCode: error.code,
                                succeeded: false,
                              );
                            }

                            shouldSetState = true;
                            if (_model.sessionIDCf!.succeeded!) {
                              _model.livenessDetect =
                                  await actions.livenessDetect(
                                _model.sessionIDCf!.data!.session,
                                _model.sessionIDCf!.data!.region,
                              );
                              shouldSetState = true;
                              if (_model.livenessDetect!) {
                                await currentUserReference!
                                    .update(createUsersRecordData(
                                  verificationGoingOn: true,
                                  autoVerificationStarted: true,
                                ));
                                unawaited(
                                  () async {
                                    try {
                                      final result = await FirebaseFunctions
                                              .instanceFor(
                                                  region: currentUserDocument
                                                              ?.newStyleVerification ??
                                                          false
                                                      ? 'europe-west1'
                                                      : 'europe-west2')
                                          .httpsCallable(currentUserDocument
                                                      ?.newStyleVerification ??
                                                  false
                                              ? 'verifySelfServiceAdvanced'
                                              : 'verifySelfService')
                                          .call({});
                                      _model.verifySelfServiceCf =
                                          VerifySelfServiceCloudFunctionCallResponse(
                                        succeeded: true,
                                      );
                                    } on FirebaseFunctionsException catch (error) {
                                      _model.verifySelfServiceCf =
                                          VerifySelfServiceCloudFunctionCallResponse(
                                        errorCode: error.code,
                                        succeeded: false,
                                      );
                                    }
                                  }(),
                                );
                                shouldSetState = true;
                                Navigator.pop(context);
                                if (shouldSetState) safeSetState(() {});
                                return;
                              } else {
                                await showDialog(
                                  context: context,
                                  builder: (dialogContext) {
                                    return Dialog(
                                        elevation: 0,
                                        insetPadding: EdgeInsets.zero,
                                        backgroundColor: Colors.transparent,
                                        alignment:
                                            const AlignmentDirectional(0.0, 0.0)
                                                .resolve(
                                                    Directionality.of(context)),
                                        child:
                                            PopupForFailedVerificationWidget());
                                  },
                                );

                                if (shouldSetState) safeSetState(() {});
                                return;
                              }
                            } else {
                              await showDialog(
                                context: context,
                                builder: (dialogContext) {
                                  return Dialog(
                                      elevation: 0,
                                      insetPadding: EdgeInsets.zero,
                                      backgroundColor: Colors.transparent,
                                      alignment: const AlignmentDirectional(
                                              0.0, 0.0)
                                          .resolve(Directionality.of(context)),
                                      child:
                                          PopupForFailedVerificationWidget());
                                },
                              );

                              if (shouldSetState) safeSetState(() {});
                              return;
                            }

                            if (shouldSetState) safeSetState(() {});
                          },
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(
                          10.0, 20.0, 10.0, 10.0),
                      child: Text(
                        getRemoteConfigString(
                            'verification_auto_photosensitivity_warning'),
                        textAlign: TextAlign.center,
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'BT Beau Sans',
                              fontSize: 11.0,
                              letterSpacing: 0.0,
                              useGoogleFonts: false,
                            ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
