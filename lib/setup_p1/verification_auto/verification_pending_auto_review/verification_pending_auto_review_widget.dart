import '/auth/firebase_auth/auth_util.dart';
import '/components/notification_popup_widget.dart';
import '/flutter_flow/flutter_flow_animations.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/gradient_button_disabled/gradient_button_disabled_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'verification_pending_auto_review_model.dart';
export 'verification_pending_auto_review_model.dart';
import 'package:verification/verification.dart';

class VerificationPendingAutoReviewWidget extends StatefulWidget {
  const VerificationPendingAutoReviewWidget({super.key});

  @override
  State<VerificationPendingAutoReviewWidget> createState() =>
      _VerificationPendingAutoReviewWidgetState();
}

class _VerificationPendingAutoReviewWidgetState
    extends State<VerificationPendingAutoReviewWidget>
    with TickerProviderStateMixin {
  late VerificationPendingAutoReviewModel _model;

  final animationsMap = <String, AnimationInfo>{};

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => VerificationPendingAutoReviewModel());

    animationsMap.addAll({
      'imageOnPageLoadAnimation': AnimationInfo(
        loop: true,
        trigger: AnimationTrigger.onPageLoad,
        effectsBuilder: () => [
          RotateEffect(
            curve: Curves.easeInOut,
            delay: 0.0.ms,
            duration: 1000.0.ms,
            begin: 0.0,
            end: 1.0,
          ),
        ],
      ),
    });
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.sizeOf(context).width * 1.0,
      height: MediaQuery.sizeOf(context).height * 1.0,
      decoration: const BoxDecoration(),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Flexible(
            child: Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Flexible(
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Align(
                          alignment: const AlignmentDirectional(0.0, 0.0),
                          child: Stack(
                            alignment: const AlignmentDirectional(0.0, 0.0),
                            children: [
                              VerificationHeading(
                                text: getRemoteConfigString(
                                    'verification_auto_processing_title'),
                                logoPath:
                                    "assets/images/purple_verifictions_logo.png",
                                loaderPath:
                                    "assets/images/purple_verifictions_loading_circle.png",
                                logoPadding: const EdgeInsets.only(bottom: 12),
                              )
                            ],
                          ),
                        ),
                        VerificationSubtitle(
                            text: getRemoteConfigString(
                                'verification_auto_processing_description'),
                            padding: EdgeInsets.fromLTRB(0, 12, 0, 0))
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              Builder(
                builder: (context) {
                  return Builder(
                    builder: (context) => wrapWithModel(
                      model: _model.gradientButtonModel,
                      updateCallback: () => safeSetState(() {}),
                      updateOnChange: true,
                      child: VerificationButton(
                        title: currentUserDocument?.pushNotificationAllow ??
                                false
                            ? getRemoteConfigString(
                                'verification_in_review_allowancegranted_button_title')
                            : getRemoteConfigString(
                                'verification_inreview_button_title'),
                        onTap: currentUserDocument?.pushNotificationAllow ??
                                false
                            ? null
                            : () async {
                                await showDialog(
                                  context: context,
                                  builder: (dialogContext) {
                                    return Dialog(
                                      elevation: 0,
                                      insetPadding: EdgeInsets.zero,
                                      backgroundColor: Colors.transparent,
                                      alignment: const AlignmentDirectional(
                                              0.0, 0.0)
                                          .resolve(Directionality.of(context)),
                                      child: NotificationPopupWidget(
                                        alertTitle: getRemoteConfigString(
                                            'pn_allow_sl_question'),
                                        alertText: getRemoteConfigString(
                                            'pn_allow_sl_description'),
                                      ),
                                    );
                                  },
                                );
                              },
                      ),
                    ),
                  );
                  // } else if (valueOrDefault<bool>(
                  //     currentUserDocument?.pushNotificationAllow, false)) {
                  //   return wrapWithModel(
                  //     model: _model.gradientButtonDisabledModel,
                  //     updateCallback: () => safeSetState(() {}),
                  //     updateOnChange: true,
                  //     child: GradientButtonDisabledWidget(
                  //       title: getRemoteConfigString(
                  //           'verification_in_review_allowancegranted_button_title'),
                  //       action: () async {},
                  //     ),
                  //   );
                  // } else {
                  //   return Container(
                  //     width: 1.0,
                  //     height: 1.0,
                  //     decoration: const BoxDecoration(
                  //       color: Color(0x00FFFFFF),
                  //     ),
                  //   );
                  // }
                },
              ),
            ],
          ),
        ],
      ),
    );
  }
}
