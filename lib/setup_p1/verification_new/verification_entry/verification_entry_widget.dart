import 'package:chyrpe/components/general_popup_widget.dart';
import 'package:chyrpe/flutter_flow/flutter_flow_widgets.dart';
import 'package:verification/verification.dart';

import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/backend/firebase_storage/storage.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/upload_data.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/flutter_flow/permissions_util.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'verification_entry_model.dart';
export 'verification_entry_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';
import 'package:flutter_branch_sdk/flutter_branch_sdk.dart';
import '/backend/schema/enums/enums.dart';

class VerificationEntryWidget extends StatefulWidget {
  const VerificationEntryWidget({super.key});

  @override
  State<VerificationEntryWidget> createState() =>
      _VerificationEntryWidgetState();
}

class _VerificationEntryWidgetState extends State<VerificationEntryWidget> {
  late VerificationEntryModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => VerificationEntryModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<OptionsForSelectorsRecord>>(
      stream: queryOptionsForSelectorsRecord(
        queryBuilder: (optionsForSelectorsRecord) =>
            optionsForSelectorsRecord.where(
          'name',
          isEqualTo: 'Gestures for Verifications',
        ),
        singleRecord: true,
      ),
      builder: (context, snapshot) {
        // Customize what your widget looks like when it's loading.
        if (!snapshot.hasData) {
          return Center(
            child: SizedBox(
              width: 50.0,
              height: 50.0,
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  FlutterFlowTheme.of(context).accent2,
                ),
              ),
            ),
          );
        }
        List<OptionsForSelectorsRecord> containerOptionsForSelectorsRecordList =
            snapshot.data!;
        final containerOptionsForSelectorsRecord =
            containerOptionsForSelectorsRecordList.isNotEmpty
                ? containerOptionsForSelectorsRecordList.first
                : null;

        return Container(
          width: MediaQuery.sizeOf(context).width * 1.0,
          height: MediaQuery.sizeOf(context).height * 1.0,
          decoration: const BoxDecoration(),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Flexible(
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Flexible(
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                             VerificationHeading(
                          text: getRemoteConfigString(
                                    'verification_entry_headline'),
                          logoPath:
                              "assets/images/purple_verifictions_logo.png",
                          logoPadding: const EdgeInsets.only(bottom: 12),
                        ),
                        VerificationSubtitle(
                            text: currentUserDocument?.idVerification ?? false ? getRemoteConfigString('verification_id_request_title') :  getRemoteConfigString(
                                  'verification_entry_description'),
                            padding: EdgeInsets.fromLTRB(0, 18, 0, 0)
                            )
                            ,
                           
                           
                            if (!(valueOrDefault<bool>(
                                    currentUserDocument?.verificationGoingOn,
                                    false) &&
                                valueOrDefault<bool>(
                                    currentUserDocument?.verified, false)) && (!(currentUserDocument?.idVerification ?? false)))
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 39.0, 0.0, 0.0),
                                child: AuthUserStreamWidget(
                                  builder: (context) => Stack(
                                    children: [
                                      Padding(
                                        padding: const EdgeInsetsDirectional.fromSTEB(
                                            0.0, 13.0, 0.0, 0.0),
                                        child: Container(
                                          width:
                                              MediaQuery.sizeOf(context).width *
                                                  1.0,
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(11.0),
                                            border: Border.all(
                                              color: const Color(0xFFB9BFC8),
                                              width: 1.0,
                                            ),
                                          ),
                                          child: Column(
                                            mainAxisSize: MainAxisSize.max,
                                            children: [
                                              Padding(
                                                padding: const EdgeInsetsDirectional
                                                    .fromSTEB(
                                                        35.0, 30.0, 35.0, 0.0),
                                                child: Row(
                                                  mainAxisSize:
                                                      MainAxisSize.max,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Flexible(
                                                      child: Column(
                                                        mainAxisSize:
                                                            MainAxisSize.max,
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .center,
                                                        children: [
                                                          Padding(
                                                            padding:
                                                                const EdgeInsetsDirectional
                                                                    .fromSTEB(
                                                                        0.0,
                                                                        3.0,
                                                                        0.0,
                                                                        0.0),
                                                            child: Text(
                                                              getRemoteConfigString(
                                                                  'verification_entry_step1_title'),
                                                              style: FlutterFlowTheme
                                                                      .of(context)
                                                                  .bodyMedium
                                                                  .override(
                                                                    fontFamily:
                                                                        'BT Beau Sans',
                                                                    fontSize:
                                                                        16.0,
                                                                    letterSpacing:
                                                                        0.0,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold,
                                                                    useGoogleFonts:
                                                                        false,
                                                                  ),
                                                            ),
                                                          ),
                                                          Padding(
                                                            padding:
                                                                const EdgeInsetsDirectional
                                                                    .fromSTEB(
                                                                        0.0,
                                                                        3.0,
                                                                        0.0,
                                                                        0.0),
                                                            child: Text(
                                                              getRemoteConfigString(
                                                                  'verification_entry_step1_description'),
                                                              textAlign:
                                                                  TextAlign
                                                                      .center,
                                                              style: FlutterFlowTheme
                                                                      .of(context)
                                                                  .bodyMedium
                                                                  .override(
                                                                    fontFamily:
                                                                        'BT Beau Sans',
                                                                    fontSize:
                                                                        12.0,
                                                                    letterSpacing:
                                                                        0.0,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w500,
                                                                    useGoogleFonts:
                                                                        false,
                                                                  ),
                                                            ),
                                                          ),
                                                          Padding(
                                                            padding:
                                                                const EdgeInsetsDirectional
                                                                    .fromSTEB(
                                                                        0.0,
                                                                        15.0,
                                                                        0.0,
                                                                        0.0),
                                                            child: ClipRRect(
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          8.0),
                                                              child:
                                                                  Image.network(
                                                                containerOptionsForSelectorsRecord!
                                                                    .gestureOptions
                                                                    .first
                                                                    .pictureURL,
                                                                width: 122.0,
                                                                height: 161.0,
                                                                fit: BoxFit
                                                                    .cover,
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              Padding(
                                                padding: const EdgeInsetsDirectional
                                                    .fromSTEB(
                                                        35.0, 30.0, 35.0, 30.0),
                                                child: Row(
                                                  mainAxisSize:
                                                      MainAxisSize.max,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Flexible(
                                                      child: Column(
                                                        mainAxisSize:
                                                            MainAxisSize.max,
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .center,
                                                        children: [
                                                          Padding(
                                                            padding:
                                                                const EdgeInsetsDirectional
                                                                    .fromSTEB(
                                                                        0.0,
                                                                        3.0,
                                                                        0.0,
                                                                        0.0),
                                                            child: Text(
                                                              getRemoteConfigString(
                                                                  'verification_entry_step2_title'),
                                                              textAlign:
                                                                  TextAlign
                                                                      .center,
                                                              style: FlutterFlowTheme
                                                                      .of(context)
                                                                  .bodyMedium
                                                                  .override(
                                                                    fontFamily:
                                                                        'BT Beau Sans',
                                                                    fontSize:
                                                                        16.0,
                                                                    letterSpacing:
                                                                        0.0,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold,
                                                                    useGoogleFonts:
                                                                        false,
                                                                  ),
                                                            ),
                                                          ),
                                                          Padding(
                                                            padding:
                                                                const EdgeInsetsDirectional
                                                                    .fromSTEB(
                                                                        0.0,
                                                                        3.0,
                                                                        0.0,
                                                                        0.0),
                                                            child: Text(
                                                              getRemoteConfigString(
                                                                  'verification_entry_step2_description'),
                                                              textAlign:
                                                                  TextAlign
                                                                      .center,
                                                              style: FlutterFlowTheme
                                                                      .of(context)
                                                                  .bodyMedium
                                                                  .override(
                                                                    fontFamily:
                                                                        'BT Beau Sans',
                                                                    fontSize:
                                                                        12.0,
                                                                    letterSpacing:
                                                                        0.0,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w500,
                                                                    useGoogleFonts:
                                                                        false,
                                                                  ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                      Align(
                                        alignment:
                                            const AlignmentDirectional(0.0, 0.0),
                                        child: Container(
                                          width: 260.0,
                                          height: 25.0,
                                          decoration: BoxDecoration(
                                            color: FlutterFlowTheme.of(context)
                                                .primaryBackground,
                                            borderRadius:
                                                BorderRadius.circular(100.0),
                                            border: Border.all(
                                              color: const Color(0xFFB9BFC8),
                                            ),
                                          ),
                                          child: Align(
                                            alignment:
                                                const AlignmentDirectional(0.0, 0.0),
                                            child: Text(
                                              'How it works',
                                              style: FlutterFlowTheme.of(
                                                      context)
                                                  .bodyMedium
                                                  .override(
                                                    fontFamily: 'BT Beau Sans',
                                                    fontSize: 13.0,
                                                    letterSpacing: 0.0,
                                                    fontWeight: FontWeight.w500,
                                                    useGoogleFonts: false,
                                                  ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ].addToEnd(const SizedBox(height: 30.0)),
                ),
              ),
              Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  wrapWithModel(
                    model: _model.gradientButtonModel,
                    updateCallback: () => setState(() {}),
                    updateOnChange: true,
                    child: LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return FFButtonWidget(
                        options: FFButtonOptions(
                                borderRadius: BorderRadius.circular(100),
                                elevation: 0,
                                height: 42.0,
                                width: constraints.maxWidth,
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    20.0, 0.0, 20.0, 0.0),
                                iconPadding:
                                    const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                                color: const Color(0xFFB99CE4),
                                textStyle:
                                    FlutterFlowTheme.of(context).titleSmall.override(
                                          fontFamily: 'BT Beau Sans',
                                          color: Colors.white,
                                          fontSize: 12.0,
                                          letterSpacing: 0.0,
                                          useGoogleFonts: false,
                                        ),
                        ),
                          text: currentUserDocument?.idVerification ?? false ? "Take photo" : getRemoteConfigString(
                              'verification_entry_selfie_button_title'),
                          onPressed: () async {
                        
                            var shouldSetState = false;
                            if (!(await getPermissionStatus(cameraPermission))) {
                              await requestPermission(cameraPermission);
                            }
                            final selectedMedia = await selectMedia(
                              maxWidth: 800.00,
                              imageQuality: 70,
                              multiImage: false,
                            );
                            if (selectedMedia != null &&
                                selectedMedia.every((m) =>
                                    validateFileFormat(m.storagePath, context))) {
                              setState(() => _model.isDataUploading = true);
                              var selectedUploadedFiles = <FFUploadedFile>[];
                        
                              var downloadUrls = <String>[];
                              try {
                                selectedUploadedFiles = selectedMedia
                                    .map((m) => FFUploadedFile(
                                          name: m.storagePath.split('/').last,
                                          bytes: m.bytes,
                                          height: m.dimensions?.height,
                                          width: m.dimensions?.width,
                                          blurHash: m.blurHash,
                                        ))
                                    .toList();
                        
                                downloadUrls = (await Future.wait(
                                  selectedMedia.map(
                                    (m) async =>
                                        await uploadDataV(m.storagePath, m.bytes),
                                  ),
                                ))
                                    .where((u) => u != null)
                                    .map((u) => u!)
                                    .toList();
                              } finally {
                                _model.isDataUploading = false;
                              }
                              if (selectedUploadedFiles.length ==
                                      selectedMedia.length &&
                                  downloadUrls.length == selectedMedia.length) {
                                setState(() {
                                  _model.uploadedLocalFile =
                                      selectedUploadedFiles.first;
                                  _model.uploadedFileUrl = downloadUrls.first;
                                });
                              } else {
                                setState(() {});
                                return;
                              }
                            }
                        
                            if (_model.uploadedFileUrl != '') {
                             
                             try {
                        
                                analytics.logEvent('Sign Up: Requested Verification');
                        
                              
                              if ((currentUserDocument?.previousVerifications.length ?? 0) > 0 && !(currentUserDocument?.hasVerificationFailed() ?? false)) {
                        
                                BranchEvent eventVeri0 = BranchEvent.customEvent('pre_verification_requested'); 
                                FlutterBranchSdk.trackContentWithoutBuo(branchEvent: eventVeri0);
                        
                                if (currentUserDocument?.gender == Gender.Female) {
                                BranchEvent eventVeri0Femme = BranchEvent.customEvent('pre_verification_requested_female'); 
                                FlutterBranchSdk.trackContentWithoutBuo(branchEvent: eventVeri0Femme);
                        
                                }
                          }
                        
                              final result = await FirebaseFunctions
                                                      .instanceFor(
                                                          region: 'europe-west1')
                                                  .httpsCallable(
                                                      'requestManualVeriGeneral')
                                                  .call({"verificationPhotoUrl": _model.uploadedFileUrl});
                                              _model.cloudFunctionx23 =
                                                  RequestManualVeriReviewCloudFunctionCallResponse(
                                                succeeded: true,
                                              );
                                            } on FirebaseFunctionsException catch (error) {
                                              _model.cloudFunctionx23 =
                                                  RequestManualVeriReviewCloudFunctionCallResponse(
                                                errorCode: error.code,
                                                succeeded: false,
                                              );
                                            }
                        
                                            if (_model
                                                .cloudFunctionx23!.succeeded!) {
                                              FFAppState().update(() {});
                                            } else {
                                              await showDialog(
                                                context: context,
                                                builder: (dialogContext) {
                                                  return Dialog(
                                                    elevation: 0,
                                                    insetPadding: EdgeInsets.zero,
                                                    backgroundColor:
                                                        Colors.transparent,
                                                    alignment: const AlignmentDirectional(
                                                            0.0, 0.0)
                                                        .resolve(Directionality.of(
                                                            context)),
                                                    child: const GeneralPopupWidget(
                                                      alertTitle:
                                                          'Something went wrong',
                                                      alertText:
                                                          'Please try again <NAME_EMAIL>',
                                                    ),
                                                  );
                                                }
                                              );
                                      
                                    }
                            } else {
                              if (shouldSetState) setState(() {});
                              return;
                            }
                        
                            if (shouldSetState) setState(() {});
                          },
                        );
                      }
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
