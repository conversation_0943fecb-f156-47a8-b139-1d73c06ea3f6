import 'package:chyrpe/backend/firebase_storage/storage.dart';
import 'package:chyrpe/components/general_popup_widget.dart';
import 'package:chyrpe/flutter_flow/flutter_flow_widgets.dart';
import 'package:chyrpe/flutter_flow/permissions_util.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:verification/verification.dart';
import '/flutter_flow/upload_data.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';

import '/auth/firebase_auth/auth_util.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/setup_p1/get_verified_popup/get_verified_popup_widget.dart';
import 'package:flutter/material.dart';
import 'verification_fail_model.dart';
export 'verification_fail_model.dart';

class VerificationFailWidget extends StatefulWidget {
  const VerificationFailWidget({super.key});

  @override
  State<VerificationFailWidget> createState() => _VerificationFailWidgetState();
}

class _VerificationFailWidgetState extends State<VerificationFailWidget> {
  late VerificationFailModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => VerificationFailModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.sizeOf(context).width * 1.0,
      height: MediaQuery.sizeOf(context).height * 1.0,
      decoration: const BoxDecoration(),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Flexible(
            child: Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Flexible(
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                         VerificationHeading(
                          text: currentUserDocument
                                      ?.hasverificationCustomTitle() ??
                                  false
                              ? currentUserDocument?.verificationCustomTitle ??
                                  ''
                              : getRemoteConfigString('verification_fail_headline'),
                          logoPath:
                              "assets/images/purple_verifictions_logo.png",
                          logoPadding: const EdgeInsets.only(bottom: 12),
                        ),
                        VerificationSubtitle(
                            text: currentUserDocument
                                        ?.verificationCustomSubtitle ??
                                     currentUserDocument?.verificationFailReason ?? ''
                                ,
                            padding: EdgeInsets.fromLTRB(0, 18, 0, 0)
                            )
                        
                      ]
                    ),
                  ),
                ),
              ],
            ),
          ),
          Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              wrapWithModel(
                model: _model.gradientButtonModel,
                updateCallback: () => setState(() {}),
                updateOnChange: true,
                child: LayoutBuilder(
                  builder: (BuildContext context, BoxConstraints constraints) {
                    return FFButtonWidget(
                      options: FFButtonOptions(
                                borderRadius: BorderRadius.circular(100),
                                elevation: 0,
                                height: 42.0,
                                width: constraints.maxWidth,
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    20.0, 0.0, 20.0, 0.0),
                                iconPadding:
                                    const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                                color: const Color(0xFFB99CE4),
                                textStyle:
                                    FlutterFlowTheme.of(context).titleSmall.override(
                                          fontFamily: 'BT Beau Sans',
                                          color: Colors.white,
                                          fontSize: 12.0,
                                          letterSpacing: 0.0,
                                          useGoogleFonts: false,
                                        ),
                        ),
                      text: getRemoteConfigString(
                          'verification_fail_tryagain_button_title'),
                      onPressed: () async {
                              try{
                    
                              final result = await FirebaseFunctions
                                                      .instanceFor(
                                                          region: 'europe-west1')
                                                  .httpsCallable(
                                                      'resetManualVeri')
                                                  .call({});
                                              _model.cloudFunctionx23 =
                                                  RequestManualVeriReviewCloudFunctionCallResponse(
                                                succeeded: true,
                                              );
                                            } on FirebaseFunctionsException catch (error) {
                                              _model.cloudFunctionx23 =
                                                  RequestManualVeriReviewCloudFunctionCallResponse(
                                                errorCode: error.code,
                                                succeeded: false,
                                              );
                                            }
                    
                                            if (_model
                                                .cloudFunctionx23!.succeeded!) {
                                              FFAppState().update(() {});
                                            } else {
                                              await showDialog(
                                                context: context,
                                                builder: (dialogContext) {
                                                  return Dialog(
                                                    elevation: 0,
                                                    insetPadding: EdgeInsets.zero,
                                                    backgroundColor:
                                                        Colors.transparent,
                                                    alignment: const AlignmentDirectional(
                                                            0.0, 0.0)
                                                        .resolve(Directionality.of(
                                                            context)),
                                                    child: const GeneralPopupWidget(
                                                      alertTitle:
                                                          'Something went wrong',
                                                      alertText:
                                                          'Please try again <NAME_EMAIL>',
                                                    ),
                                                  );
                                                }
                                              );
                                      
                                    }
                                    }
                    );
                  }
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
