import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/gradient_button_grey_dark/gradient_button_grey_dark_widget.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'verification_fail_gender_reclass_model.dart';
export 'verification_fail_gender_reclass_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';

class VerificationFailGenderReclassWidget extends StatefulWidget {
  const VerificationFailGenderReclassWidget({super.key});

  @override
  State<VerificationFailGenderReclassWidget> createState() =>
      _VerificationFailGenderReclassWidgetState();
}

class _VerificationFailGenderReclassWidgetState
    extends State<VerificationFailGenderReclassWidget> {
  late VerificationFailGenderReclassModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => VerificationFailGenderReclassModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.sizeOf(context).width * 1.0,
      height: MediaQuery.sizeOf(context).height * 1.0,
      decoration: const BoxDecoration(),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Flexible(
            child: Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Flexible(
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Align(
                          alignment: const AlignmentDirectional(0.0, 0.0),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8.0),
                            child: Image.asset(
                              'assets/images/Gradient_Verification_Incl_Circle.png',
                              width: 63.0,
                              height: 63.0,
                              fit: BoxFit.contain,
                              alignment: const Alignment(0.0, 0.0),
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              0.0, 15.0, 0.0, 0.0),
                          child: Text(
                            getRemoteConfigString('gender_reclass_headline'),
                            textAlign: TextAlign.center,
                            style: FlutterFlowTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'BT Beau Sans',
                                  fontSize: 26.0,
                                  letterSpacing: 0.0,
                                  fontWeight: FontWeight.bold,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              0.0, 15.0, 0.0, 0.0),
                          child: Text(
                            getRemoteConfigString(
                                'verification_genderreclass_description'),
                            textAlign: TextAlign.center,
                            style: FlutterFlowTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'BT Beau Sans',
                                  letterSpacing: 0.0,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              Builder(
                builder: (context) => wrapWithModel(
                  model: _model.gradientButtonModel1,
                  updateCallback: () => setState(() {}),
                  updateOnChange: true,
                  child: GradientButtonWidget(
                    title: getRemoteConfigString(
                        'gender_reclass_alternative_button_title'),
                    action: () async {
                      analytics.logEvent('Sign Up: Requested Gender Reclass Alternative');
                      try {
                        final result = await FirebaseFunctions.instanceFor(
                                region: 'europe-west2')
                            .httpsCallable('changeGender')
                            .call({
                          "newGender": 'Alternative (Female)',
                        });
                        _model.changeGenderMaleCf =
                            ChangeGenderCloudFunctionCallResponse(
                          succeeded: true,
                        );
                      } on FirebaseFunctionsException catch (error) {
                        _model.changeGenderMaleCf =
                            ChangeGenderCloudFunctionCallResponse(
                          errorCode: error.code,
                          succeeded: false,
                        );
                      }

                      if (_model.changeGenderMaleCf!.succeeded!) {
                        _model.updatePage(() {});
                      } else {
                        await showDialog(
                          context: context,
                          builder: (dialogContext) {
                            return Dialog(
                              elevation: 0,
                              insetPadding: EdgeInsets.zero,
                              backgroundColor: Colors.transparent,
                              alignment: const AlignmentDirectional(0.0, 0.0)
                                  .resolve(Directionality.of(context)),
                              child: const GeneralPopupWidget(
                                alertTitle: 'Something went wrong',
                                alertText:
                                    'Please try again <NAME_EMAIL>',
                              ),
                            );
                          },
                        );
                      }

                      setState(() {});
                    },
                  ),
                ),
              ),
              Builder(
                builder: (context) => wrapWithModel(
                  model: _model.gradientButtonModel2,
                  updateCallback: () => setState(() {}),
                  updateOnChange: true,
                  child: GradientButtonWidget(
                    title: getRemoteConfigString(
                        'gender_reclass_male_button_title'),
                    action: () async {
                      analytics.logEvent('Sign Up: Requested Gender Reclass Male');
                      try {
                        final result = await FirebaseFunctions.instanceFor(
                                region: 'europe-west2')
                            .httpsCallable('changeGender')
                            .call({
                          "newGender": 'Male',
                        });
                        _model.changeGenderMale2CfCopy =
                            ChangeGenderCloudFunctionCallResponse(
                          succeeded: true,
                        );
                      } on FirebaseFunctionsException catch (error) {
                        _model.changeGenderMale2CfCopy =
                            ChangeGenderCloudFunctionCallResponse(
                          errorCode: error.code,
                          succeeded: false,
                        );
                      }

                      if (_model.changeGenderMale2CfCopy!.succeeded!) {
                        _model.updatePage(() {});
                      } else {
                        await showDialog(
                          context: context,
                          builder: (dialogContext) {
                            return Dialog(
                              elevation: 0,
                              insetPadding: EdgeInsets.zero,
                              backgroundColor: Colors.transparent,
                              alignment: const AlignmentDirectional(0.0, 0.0)
                                  .resolve(Directionality.of(context)),
                              child: const GeneralPopupWidget(
                                alertTitle: 'Something went wrong',
                                alertText:
                                    'Please try again <NAME_EMAIL>',
                              ),
                            );
                          },
                        );
                      }

                      setState(() {});
                    },
                  ),
                ),
              ),
              wrapWithModel(
                model: _model.gradientButtonGreyDarkModel,
                updateCallback: () => setState(() {}),
                updateOnChange: true,
                child: GradientButtonGreyDarkWidget(
                  title: getRemoteConfigString(
                      'gender_reclass_evidence_button_title'),
                  action: () async {
                    analytics.logEvent('Sign Up: Requested Gender Reclasss Email Evidence Sending');
                    await launchUrl(Uri(
                        scheme: 'mailto',
                        path: '<EMAIL>',
                        query: {
                          'subject': 'Gender Evidence',
                        }
                            .entries
                            .map((MapEntry<String, String> e) =>
                                '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
                            .join('&')));
                  },
                ),
              ),
            ].divide(const SizedBox(height: 8.0)),
          ),
        ],
      ),
    );
  }
}
