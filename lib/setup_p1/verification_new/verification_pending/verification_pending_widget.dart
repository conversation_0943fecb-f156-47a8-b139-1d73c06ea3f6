import '/auth/firebase_auth/auth_util.dart';
import '/components/notification_popup_widget.dart';
import '/flutter_flow/flutter_flow_animations.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/gradient_button_disabled/gradient_button_disabled_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'verification_pending_model.dart';
export 'verification_pending_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';
import 'package:verification/verification.dart';

class VerificationPendingWidget extends StatefulWidget {
  const VerificationPendingWidget({super.key});

  @override
  State<VerificationPendingWidget> createState() =>
      _VerificationPendingWidgetState();
}

class _VerificationPendingWidgetState extends State<VerificationPendingWidget>
    with TickerProviderStateMixin {
  late VerificationPendingModel _model;

  final animationsMap = <String, AnimationInfo>{};

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => VerificationPendingModel());

    animationsMap.addAll({
      'imageOnPageLoadAnimation': AnimationInfo(
        loop: true,
        trigger: AnimationTrigger.onPageLoad,
        effectsBuilder: () => [
          RotateEffect(
            curve: Curves.easeInOut,
            delay: 0.0.ms,
            duration: 1000.0.ms,
            begin: 0.0,
            end: 1.0,
          ),
        ],
      ),
    });
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.sizeOf(context).width * 1.0,
      height: MediaQuery.sizeOf(context).height * 1.0,
      decoration: const BoxDecoration(),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Flexible(
            child: Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Flexible(
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Align(
                          alignment: const AlignmentDirectional(0.0, 0.0),
                          child: Stack(
                            alignment: const AlignmentDirectional(0.0, 0.0),
                            children: [
                              VerificationHeading(
                                text: currentUserDocument?.hasverificationCustomTitle() ?? false ? currentUserDocument?.verificationCustomTitle ?? '' : getRemoteConfigString(
                                'verification_inreview_headline'),
                                logoPath:
                                    "assets/images/purple_verifictions_logo.png",
                                loaderPath:
                                    "assets/images/purple_verifictions_loading_circle.png",
                                logoPadding: const EdgeInsets.only(bottom: 12),
                              )
                            ],
                          ),
                        ),
                        VerificationSubtitle(
                            text: currentUserDocument?.hasverificationCustomSubtitle() ?? false ? currentUserDocument?.verificationCustomSubtitle ?? '' : getRemoteConfigString(
                                'verification_inreview_description'),
                            padding: EdgeInsets.fromLTRB(0, 12, 0, 0))
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              Builder(
                builder: (context) {
                 
                    return Builder(
                      builder: (context) => wrapWithModel(
                        model: _model.gradientButtonModel,
                        updateCallback: () => safeSetState(() {}),
                        updateOnChange: true,
                        child: VerificationButton(
                          title: currentUserDocument?.pushNotificationAllow ?? false ?  getRemoteConfigString(
                            'verification_in_review_allowancegranted_button_title') : getRemoteConfigString(
                              'verification_inreview_button_title'),
                          onTap: currentUserDocument?.pushNotificationAllow ?? false ? null : () async {
                            await showDialog(
                              context: context,
                              builder: (dialogContext) {
                                return Dialog(
                                  elevation: 0,
                                  insetPadding: EdgeInsets.zero,
                                  backgroundColor: Colors.transparent,
                                  alignment: const AlignmentDirectional(0.0, 0.0)
                                      .resolve(Directionality.of(context)),
                                  child: NotificationPopupWidget(
                                    alertTitle: getRemoteConfigString(
                                        'pn_allow_sl_question'),
                                    alertText: getRemoteConfigString(
                                        'pn_allow_sl_description'),
                                  ),
                                );
                              },
                            );
                          },
                        ),
                      ),
                    );
                },
              ),
            ],
          ),
        ],
      ),
    );
    return Container(
      width: MediaQuery.sizeOf(context).width * 1.0,
      height: MediaQuery.sizeOf(context).height * 1.0,
      decoration: const BoxDecoration(),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Flexible(
            child: Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Flexible(
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Align(
                          alignment: const AlignmentDirectional(0.0, 0.0),
                          child: Stack(
                            alignment: const AlignmentDirectional(0.0, 0.0),
                            children: [
                              Align(
                                alignment: const AlignmentDirectional(0.0, 0.0),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(8.0),
                                  child: Image.asset(
                                    'assets/images/checkmark.seal.fill_3.png',
                                    width: 36.0,
                                    height: 36.0,
                                    fit: BoxFit.contain,
                                    alignment: const Alignment(0.0, 0.0),
                                  ),
                                ),
                              ),
                              Align(
                                alignment: const AlignmentDirectional(0.0, 0.0),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(8.0),
                                  child: Image.asset(
                                    'assets/images/Gradient_Loading_Circle.png',
                                    width: 63.0,
                                    height: 63.0,
                                    fit: BoxFit.contain,
                                    alignment: const Alignment(0.0, 0.0),
                                  ),
                                ).animateOnPageLoad(
                                    animationsMap['imageOnPageLoadAnimation']!),
                              ),
                            ],
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              0.0, 15.0, 0.0, 0.0),
                          child: Text(
                            getRemoteConfigString(
                                'verification_inreview_headline'),
                            textAlign: TextAlign.center,
                            style: FlutterFlowTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'BT Beau Sans',
                                  fontSize: 26.0,
                                  letterSpacing: 0.0,
                                  fontWeight: FontWeight.bold,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              0.0, 15.0, 0.0, 0.0),
                          child: Text(
                            getRemoteConfigString(
                                'verification_inreview_description'),
                            textAlign: TextAlign.center,
                            style: FlutterFlowTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'BT Beau Sans',
                                  letterSpacing: 0.0,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              Builder(
                builder: (context) {
                  if (!valueOrDefault<bool>(
                          currentUserDocument?.pushNotificationAllow, false) &&
                      !valueOrDefault<bool>(
                          currentUserDocument?.pushNotificationDecision,
                          false)) {
                    return Builder(
                      builder: (context) => wrapWithModel(
                        model: _model.gradientButtonModel,
                        updateCallback: () => setState(() {}),
                        updateOnChange: true,
                        child: GradientButtonWidget(
                          title: getRemoteConfigString(
                              'verification_inreview_button_title'),
                          action: () async {
                            try {
                                analytics.logEvent('Verification Pending: Tapped Allow Notifications');
                                } catch(e) {}
                            await showDialog(
                              context: context,
                              builder: (dialogContext) {
                                return Dialog(
                                  elevation: 0,
                                  insetPadding: EdgeInsets.zero,
                                  backgroundColor: Colors.transparent,
                                  alignment: const AlignmentDirectional(0.0, 0.0)
                                      .resolve(Directionality.of(context)),
                                  child: NotificationPopupWidget(
                                    alertTitle: getRemoteConfigString(
                                        'pn_allow_sl_question'),
                                    alertText: getRemoteConfigString(
                                        'pn_allow_sl_description'),
                                  ),
                                );
                              },
                            );
                          },
                        ),
                      ),
                    );
                  } else if (valueOrDefault<bool>(
                      currentUserDocument?.pushNotificationAllow, false)) {
                    return wrapWithModel(
                      model: _model.gradientButtonDisabledModel,
                      updateCallback: () => setState(() {}),
                      updateOnChange: true,
                      child: GradientButtonDisabledWidget(
                        title: getRemoteConfigString(
                            'verification_in_review_allowancegranted_button_title'),
                        action: () async {},
                      ),
                    );
                  } else {
                    return Container(
                      width: 1.0,
                      height: 1.0,
                      decoration: const BoxDecoration(
                        color: Color(0x00FFFFFF),
                      ),
                    );
                  }
                },
              ),
            ],
          ),
        ],
      ),
    );
  }
}
