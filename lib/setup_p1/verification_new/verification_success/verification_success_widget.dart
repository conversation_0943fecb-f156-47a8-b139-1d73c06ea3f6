import 'package:chyrpe/setup_p1/verification_auto/verification_auto_success/verification_auto_success_widget.dart';

import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'verification_success_model.dart';
export 'verification_success_model.dart';


class VerificationSuccessWidget extends StatefulWidget {
  const VerificationSuccessWidget({super.key});

  @override
  State<VerificationSuccessWidget> createState() =>
      _VerificationSuccessWidgetState();
}

class _VerificationSuccessWidgetState extends State<VerificationSuccessWidget> {
  late VerificationSuccessModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => VerificationSuccessModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.sizeOf(context).width * 1.0,
      height: MediaQuery.sizeOf(context).height * 1.0,
      decoration: const BoxDecoration(),
      child: VerificationAutoSuccessWidget()
    );
  }
}
