import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/setup_p1/verification_new/verification_entry/verification_entry_widget.dart';
import '/setup_p1/verification_new/verification_fail/verification_fail_widget.dart';
import '/setup_p1/verification_new/verification_fail_gender_reclass/verification_fail_gender_reclass_widget.dart';
import '/setup_p1/verification_new/verification_pending/verification_pending_widget.dart';
import '/setup_p1/verification_new/verification_success/verification_success_widget.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'verification_prompt_model.dart';
export 'verification_prompt_model.dart';
import 'package:upgrader/upgrader.dart';

class VerificationPromptWidget extends StatefulWidget {
  const VerificationPromptWidget({super.key});

  @override
  State<VerificationPromptWidget> createState() =>
      _VerificationPromptWidgetState();
}

class _VerificationPromptWidgetState extends State<VerificationPromptWidget> {
  late VerificationPromptModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => VerificationPromptModel());
    logFirebaseEvent('screen_view', parameters: {'screen_name': 'VerificationScreen'});

    // On page load action.
    
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return UpgradeAlert(
      dialogStyle: UpgradeDialogStyle.cupertino,
      showLater: false,
      showIgnore: false,
      child: GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        body: SafeArea(
          top: true,
          child: Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(18.0, 18.0, 18.0, 60.0),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                Align(
                  alignment: const AlignmentDirectional(-1.0, 0.0),
                  child: FlutterFlowIconButton(
                    borderColor: const Color(0x00FFFFFF),
                    borderRadius: 20.0,
                    borderWidth: 1.0,
                    buttonSize: 40.0,
                    fillColor: const Color(0x00FFFFFF),
                    icon: FaIcon(
                      FontAwesomeIcons.angleLeft,
                      color: FlutterFlowTheme.of(context).primaryText,
                      size: 24.0,
                    ),
                    onPressed: () async {
                      if (currentUserDocument?.gender == Gender.Female
                          ? (valueOrDefault<bool>(
                                  currentUserDocument?.bGroup, false)
                              ? functions
                                  .getStringListFromJson(getRemoteConfigString(
                                      'signup_show_bio_w'))
                                  .contains('bGroup')
                              : functions
                                  .getStringListFromJson(getRemoteConfigString(
                                      'signup_show_bio_w'))
                                  .contains('aGroup'))
                          : (valueOrDefault<bool>(
                                  currentUserDocument?.bGroup, false)
                              ? functions
                                  .getStringListFromJson(getRemoteConfigString(
                                      'signup_show_bio_m'))
                                  .contains('bGroup')
                              : functions
                                  .getStringListFromJson(getRemoteConfigString(
                                      'signup_show_bio_m'))
                                  .contains('aGroup'))) {
                        context.goNamed('BioPrompts');

                        return;
                      } else {
                        context.pushNamed('Images');

                        return;
                      }
                    },
                  ),
                ),
                StreamBuilder<UsersRecord>(
                  stream: UsersRecord.getDocument(currentUserReference!)
                    ..listen((containerUsersRecord) async {
                      if (_model.containerPreviousSnapshot != null &&
                          !const UsersRecordDocumentEquality().equals(
                              containerUsersRecord,
                              _model.containerPreviousSnapshot)) {
                        setState(() {});

                        setState(() {});
                      }
                      _model.containerPreviousSnapshot = containerUsersRecord;
                    }),
                  builder: (context, snapshot) {
                    // Customize what your widget looks like when it's loading.
                    if (!snapshot.hasData) {
                      return Center(
                        child: SizedBox(
                          width: 1.0,
                          height: 1.0,
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                              FlutterFlowTheme.of(context).primaryBackground,
                            ),
                          ),
                        ),
                      );
                    }

                    final containerUsersRecord = snapshot.data!;

                    return Container(
                      width: 1.0,
                      height: 1.0,
                      decoration: BoxDecoration(
                        color: FlutterFlowTheme.of(context).secondaryBackground,
                      ),
                    );
                  },
                ),
                Flexible(
                  child: Builder(
                    builder: (context) {
                      if (valueOrDefault<bool>(
                              currentUserDocument?.verified, false) &&
                          valueOrDefault<bool>(
                              currentUserDocument?.genderIssue, false)) {
                        return Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              20.0, 0.0, 20.0, 0.0),
                          child: wrapWithModel(
                            model: _model.verificationFailGenderReclassModel,
                            updateCallback: () => setState(() {}),
                            updateOnChange: true,
                            child: const VerificationFailGenderReclassWidget(),
                          ),
                        );
                      } else if (valueOrDefault<bool>(
                              currentUserDocument?.verified, false) &&
                          !valueOrDefault<bool>(
                              currentUserDocument?.genderIssue, false)) {
                        return Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              20.0, 0.0, 20.0, 0.0),
                          child: wrapWithModel(
                            model: _model.verificationSuccessModel,
                            updateCallback: () => setState(() {}),
                            updateOnChange: true,
                            child: const VerificationSuccessWidget(),
                          ),
                        );
                      } else if (!valueOrDefault<bool>(
                              currentUserDocument?.verified, false) &&
                          valueOrDefault<bool>(
                              currentUserDocument?.verificationFailed, false) &&
                          !valueOrDefault<bool>(
                              currentUserDocument?.verificationGoingOn,
                              false)) {
                        return Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              20.0, 0.0, 20.0, 0.0),
                          child: wrapWithModel(
                            model: _model.verificationFailModel,
                            updateCallback: () => setState(() {}),
                            updateOnChange: true,
                            child: const VerificationFailWidget(),
                          ),
                        );
                      } else if (valueOrDefault<bool>(
                          currentUserDocument?.verificationGoingOn, false)) {
                        return Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              20.0, 0.0, 20.0, 0.0),
                          child: wrapWithModel(
                            model: _model.verificationPendingModel,
                            updateCallback: () => setState(() {}),
                            updateOnChange: true,
                            child: const VerificationPendingWidget(),
                          ),
                        );
                      } else {
                        return wrapWithModel(
                          model: _model.verificationEntryModel,
                          updateCallback: () => setState(() {}),
                          updateOnChange: true,
                          child: const VerificationEntryWidget(),
                        );
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      ),
    );
  }
}