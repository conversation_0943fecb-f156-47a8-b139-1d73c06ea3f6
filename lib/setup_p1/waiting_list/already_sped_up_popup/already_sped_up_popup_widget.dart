import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'already_sped_up_popup_model.dart';
export 'already_sped_up_popup_model.dart';
import '/discovery/popup/general_popup_new/general_popup_new_widget.dart';

class AlreadySpedUpPopupWidget extends StatefulWidget {
  const AlreadySpedUpPopupWidget({super.key});

  @override
  State<AlreadySpedUpPopupWidget> createState() =>
      _AlreadySpedUpPopupWidgetState();
}

class _AlreadySpedUpPopupWidgetState extends State<AlreadySpedUpPopupWidget> {
  late AlreadySpedUpPopupModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => AlreadySpedUpPopupModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

   @override
   Widget build(BuildContext context) {
    return wrapWithModel(
      model: GeneralPopupNewModel(),
      updateCallback: () {
        safeSetState(() {});
      },
      child: GeneralPopupNewWidget(
        title: 'You already purchased a speed-up',
        body:  'You can change your subscription in your app store\'s settings.',
        buttonTitle: 'Continue',
        buttonAction: () async { Navigator.pop(context);
        }
      ),
    );
   }

  // @override
  // Widget build(BuildContext context) {
  //   return Align(
  //     alignment: AlignmentDirectional(0.0, 0.0),
  //     child: Padding(
  //       padding: EdgeInsetsDirectional.fromSTEB(10.0, 0.0, 10.0, 0.0),
  //       child: Container(
  //         width: double.infinity,
  //         constraints: BoxConstraints(
  //           maxWidth: 400.0,
  //         ),
  //         decoration: BoxDecoration(
  //           color: FlutterFlowTheme.of(context).primaryBackground,
  //           borderRadius: BorderRadius.circular(12.0),
  //         ),
  //         child: Padding(
  //           padding: EdgeInsetsDirectional.fromSTEB(30.0, 28.0, 30.0, 40.0),
  //           child: Column(
  //             mainAxisSize: MainAxisSize.min,
  //             children: [
  //               Row(
  //                 mainAxisSize: MainAxisSize.max,
  //                 mainAxisAlignment: MainAxisAlignment.end,
  //                 children: [
  //                   InkWell(
  //                     splashColor: Colors.transparent,
  //                     focusColor: Colors.transparent,
  //                     hoverColor: Colors.transparent,
  //                     highlightColor: Colors.transparent,
  //                     onTap: () async {
  //                       FFAppState().update(() {});
  //                       Navigator.pop(context);
  //                     },
  //                     child: Icon(
  //                       Icons.close_rounded,
  //                       color: FlutterFlowTheme.of(context).secondaryText,
  //                       size: 26.0,
  //                     ),
  //                   ),
  //                 ],
  //               ),
  //               Padding(
  //                 padding: EdgeInsetsDirectional.fromSTEB(0.0, 28.0, 0.0, 0.0),
  //                 child: Text(
  //                   'You already purchased a speed-up',
  //                   textAlign: TextAlign.center,
  //                   style: FlutterFlowTheme.of(context).bodyMedium.override(
  //                         fontFamily: 'BT Beau Sans',
  //                         fontSize: 20.0,
  //                         letterSpacing: 0.0,
  //                         fontWeight: FontWeight.bold,
  //                         useGoogleFonts: false,
  //                       ),
  //                 ),
  //               ),
  //               Padding(
  //                 padding: EdgeInsetsDirectional.fromSTEB(0.0, 11.0, 0.0, 0.0),
  //                 child: Text(
  //                   'You can change your subscription in your app store\'s settings.',
  //                   textAlign: TextAlign.center,
  //                   style: FlutterFlowTheme.of(context).bodyMedium.override(
  //                         fontFamily: 'BT Beau Sans',
  //                         fontSize: 16.0,
  //                         letterSpacing: 0.0,
  //                         fontWeight: FontWeight.normal,
  //                         useGoogleFonts: false,
  //                         lineHeight: 1.55,
  //                       ),
  //                 ),
  //               ),
  //               Padding(
  //                 padding:
  //                     EdgeInsetsDirectional.fromSTEB(30.0, 27.0, 30.0, 0.0),
  //                 child: wrapWithModel(
  //                   model: _model.gradientButtonModel,
  //                   updateCallback: () => setState(() {}),
  //                   child: GradientButtonWidget(
  //                     title: 'OK',
  //                     action: () async {
  //                       Navigator.pop(context);
  //                     },
  //                   ),
  //                 ),
  //               ),
  //             ],
  //           ),
  //         ),
  //       ),
  //     ),
  //   );
  // }
}
