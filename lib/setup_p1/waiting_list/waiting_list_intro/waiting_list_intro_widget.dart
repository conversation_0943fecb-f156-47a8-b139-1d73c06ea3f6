import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/flutter_flow/instant_timer.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/gradient_button_outline/gradient_button_outline_widget.dart';
import '/general/gradient_button_small/gradient_button_small_widget.dart';
import '/general/info_sheet_scrollable/info_sheet_scrollable_widget.dart';
import '/setup_p1/waiting_list/already_sped_up_popup/already_sped_up_popup_widget.dart';
import '/flutter_flow/revenue_cat_util.dart' as revenue_cat;
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:lottie/lottie.dart';
import 'package:provider/provider.dart';
import 'waiting_list_intro_model.dart';
export 'waiting_list_intro_model.dart';
import 'package:upgrader/upgrader.dart';
import 'package:chyrpe/amplitudeConfig.dart';

class WaitingListIntroWidget extends StatefulWidget {
  const WaitingListIntroWidget({super.key});

  @override
  State<WaitingListIntroWidget> createState() => _WaitingListIntroWidgetState();
}

class _WaitingListIntroWidgetState extends State<WaitingListIntroWidget> {
  late WaitingListIntroModel _model;
  

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => WaitingListIntroModel());

    // On page load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      setState(() {
        _model.waitingListSwitchValue2 = (revenue_cat.activeEntitlementIds
                .contains('150_waitinglist_speed') ||
            revenue_cat.activeEntitlementIds
                .contains('200_waitinglist_speed') ||
            revenue_cat.activeEntitlementIds.contains('300_waitinglist_speed'));
      });
      _model.instantTimer = InstantTimer.periodic(
        duration: const Duration(milliseconds: 60000),
        callback: (timer) async {
          setState(() {});
        },
        startImmediately: true,
      );
    });
  }

  @override
  void dispose() {
    _model.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return UpgradeAlert(
      dialogStyle: UpgradeDialogStyle.cupertino,
      showLater: false,
      showIgnore: false,
      child: GestureDetector(
      onTap: () => _model.unfocusNode.canRequestFocus
          ? FocusScope.of(context).requestFocus(_model.unfocusNode)
          : FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        body: Align(
          alignment: const AlignmentDirectional(0.0, 0.0),
          child: AuthUserStreamWidget(
            builder: (context) => StreamBuilder<List<WlAvailabilityRecord>>(
              stream: queryWlAvailabilityRecord(
                queryBuilder: (wlAvailabilityRecord) => wlAvailabilityRecord
                    .where(
                      'name',
                      isEqualTo:
                          valueOrDefault(currentUserDocument?.wlRegion, ''),
                    )
                    .where(
                      'time',
                      isLessThan: getCurrentTimestamp,
                    ),
                singleRecord: true,
              ),
              builder: (context, snapshot) {
                // Customize what your widget looks like when it's loading.
                if (!snapshot.hasData) {
                  return Center(
                    child: SizedBox(
                      width: 50.0,
                      height: 50.0,
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                          FlutterFlowTheme.of(context).accent2,
                        ),
                      ),
                    ),
                  );
                }
                List<WlAvailabilityRecord>
                    conditionalBuilderWlAvailabilityRecordList = snapshot.data!;
                final conditionalBuilderWlAvailabilityRecord =
                    conditionalBuilderWlAvailabilityRecordList.isNotEmpty
                        ? conditionalBuilderWlAvailabilityRecordList.first
                        : null;
                return Builder(
                  builder: (context) {
                    if (valueOrDefault<bool>(
                        currentUserDocument?.localWlAdmitted, false)) {
                      return Stack(
                        children: [
                          Lottie.asset(
                            'assets/lottie_animations/gradient_background_2.json',
                            width: MediaQuery.sizeOf(context).width * 1.0,
                            height: MediaQuery.sizeOf(context).height * 2.0,
                            fit: BoxFit.cover,
                            animate: true,
                          ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                80.0, 0.0, 80.0, 0.0),
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  width: MediaQuery.sizeOf(context).width * 0.7,
                                  decoration: const BoxDecoration(),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    children: [
                                      Align(
                                        alignment:
                                            const AlignmentDirectional(0.0, -1.0),
                                        child: Padding(
                                          padding:
                                              const EdgeInsetsDirectional.fromSTEB(
                                                  10.0, 0.0, 10.0, 0.0),
                                          child: Text(
                                            getRemoteConfigString(
                                                'waitinglist_local_accepted_title'),
                                            textAlign: TextAlign.center,
                                            style: FlutterFlowTheme.of(context)
                                                .bodyMedium
                                                .override(
                                                  fontFamily: 'BT Beau Sans',
                                                  color: FlutterFlowTheme.of(
                                                          context)
                                                      .info,
                                                  fontSize: 26.0,
                                                  letterSpacing: 0.0,
                                                  fontWeight: FontWeight.bold,
                                                  useGoogleFonts: false,
                                                  lineHeight: 1.2,
                                                ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      0.0, 99.0, 0.0, 99.0),
                                  child: FaIcon(
                                    FontAwesomeIcons.check,
                                    color: FlutterFlowTheme.of(context).info,
                                    size: 74.0,
                                  ),
                                ),
                                wrapWithModel(
                                  model: _model.gradientButtonOutlineModel,
                                  updateCallback: () => setState(() {}),
                                  updateOnChange: true,
                                  child: GradientButtonOutlineWidget(
                                    title: 'Continue',
                                    action: () async {
                                      context.pushNamed(
                                        'Discovery',
                                        extra: <String, dynamic>{
                                          kTransitionInfoKey: const TransitionInfo(
                                            hasTransition: true,
                                            transitionType:
                                                PageTransitionType.topToBottom,
                                          ),
                                        },
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      );
                    } else {
                      return SizedBox(
                        width: MediaQuery.sizeOf(context).width * 1.0,
                        height: MediaQuery.sizeOf(context).height * 1.0,
                        child: Stack(
                          alignment: const AlignmentDirectional(0.0, 0.0),
                          children: [
                            Builder(
                              builder: (context) {
                                if (!(revenue_cat.activeEntitlementIds
                                        .contains('150_waitinglist_speed') ||
                                    revenue_cat.activeEntitlementIds
                                        .contains('200_waitinglist_speed') ||
                                    revenue_cat.activeEntitlementIds
                                        .contains('300_waitinglist_speed'))) {
                                  return Lottie.asset(
                                    'assets/lottie_animations/gradient_background_-_pink.json',
                                    width:
                                        MediaQuery.sizeOf(context).width * 1.0,
                                    height:
                                        MediaQuery.sizeOf(context).height * 2.0,
                                    fit: BoxFit.cover,
                                    reverse: true,
                                    animate: true,
                                  );
                                } else if (revenue_cat.activeEntitlementIds
                                    .contains('150_waitinglist_speed')) {
                                  return Lottie.asset(
                                    'assets/lottie_animations/gradient_background_-_purple.json',
                                    width:
                                        MediaQuery.sizeOf(context).width * 1.0,
                                    height:
                                        MediaQuery.sizeOf(context).height * 2.0,
                                    fit: BoxFit.cover,
                                    animate: true,
                                  );
                                } else if (revenue_cat.activeEntitlementIds
                                    .contains('200_waitinglist_speed')) {
                                  return Lottie.asset(
                                    'assets/lottie_animations/gradient_background_-_red_2.json',
                                    width:
                                        MediaQuery.sizeOf(context).width * 1.0,
                                    height:
                                        MediaQuery.sizeOf(context).height * 2.0,
                                    fit: BoxFit.cover,
                                    animate: true,
                                  );
                                } else {
                                  return Lottie.asset(
                                    'assets/lottie_animations/gradient_background_-_gold.json',
                                    width:
                                        MediaQuery.sizeOf(context).width * 1.0,
                                    height:
                                        MediaQuery.sizeOf(context).height * 2.0,
                                    fit: BoxFit.cover,
                                    animate: true,
                                  );
                                }
                              },
                            ),
                            if (revenue_cat.activeEntitlementIds
                                .contains('200_waitinglist_speed'))
                              Align(
                                alignment: const AlignmentDirectional(0.0, 1.0),
                                child: Container(
                                  width: MediaQuery.sizeOf(context).width * 1.0,
                                  height:
                                      MediaQuery.sizeOf(context).height * 1.0,
                                  decoration: const BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        Color(0x51F0F2F4),
                                        Color(0xD2FFFFFF)
                                      ],
                                      stops: [0.0, 1.0],
                                      begin: AlignmentDirectional(0.0, -1.0),
                                      end: AlignmentDirectional(0, 1.0),
                                    ),
                                  ),
                                ),
                              ),
                            if (!revenue_cat.activeEntitlementIds
                                .contains('200_waitinglist_speed'))
                              Align(
                                alignment: const AlignmentDirectional(0.0, 1.0),
                                child: Container(
                                  width: MediaQuery.sizeOf(context).width * 1.0,
                                  height:
                                      MediaQuery.sizeOf(context).height * 1.0,
                                  decoration: const BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        Color(0x00FFFFFF),
                                        Color(0xE5FFFFFF)
                                      ],
                                      stops: [0.0, 0.6],
                                      begin: AlignmentDirectional(0.0, -1.0),
                                      end: AlignmentDirectional(0, 1.0),
                                    ),
                                  ),
                                ),
                              ),
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 70.0, 0.0, 70.0),
                              child: Column(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Align(
                                    alignment: const AlignmentDirectional(0.0, 0.0),
                                    child: Padding(
                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                          0.0, 0.0, 0.0, 20.0),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.max,
                                        mainAxisAlignment:
                                            MainAxisAlignment.end,
                                        children: [
                                          if (getRemoteConfigBool(
                                              'local_waitingIntro_showInfo'))
                                            Padding(
                                              padding: const EdgeInsetsDirectional
                                                  .fromSTEB(0.0, 0.0, 5.0, 0.0),
                                              child: InkWell(
                                                splashColor: Colors.transparent,
                                                focusColor: Colors.transparent,
                                                hoverColor: Colors.transparent,
                                                highlightColor:
                                                    Colors.transparent,
                                                onTap: () async {
                                                  await showModalBottomSheet(
                                                    isScrollControlled: true,
                                                    backgroundColor:
                                                        Colors.transparent,
                                                    enableDrag: false,
                                                    context: context,
                                                    builder: (context) {
                                                      return GestureDetector(
                                                        onTap: () => _model
                                                                .unfocusNode
                                                                .canRequestFocus
                                                            ? FocusScope.of(
                                                                    context)
                                                                .requestFocus(_model
                                                                    .unfocusNode)
                                                            : FocusScope.of(
                                                                    context)
                                                                .unfocus(),
                                                        child: Padding(
                                                          padding: MediaQuery
                                                              .viewInsetsOf(
                                                                  context),
                                                          child:
                                                              InfoSheetScrollableWidget(
                                                            title: getRemoteConfigString(
                                                                'local_waitingIntro_sheetInfo_title'),
                                                            body: getRemoteConfigString(
                                                                'local_waitingIntro_sheetInfo_body'),
                                                          ),
                                                        ),
                                                      );
                                                    },
                                                  ).then((value) =>
                                                      safeSetState(() {}));
                                                },
                                                child: Container(
                                                  width: 40.0,
                                                  height: 40.0,
                                                  decoration: const BoxDecoration(
                                                    color: Color(0x3D57636C),
                                                    shape: BoxShape.circle,
                                                  ),
                                                  child: Icon(
                                                    Icons.info_outline_rounded,
                                                    color: FlutterFlowTheme.of(
                                                            context)
                                                        .info,
                                                    size: 28.0,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          Align(
                                            alignment:
                                                const AlignmentDirectional(1.0, 0.0),
                                            child: Padding(
                                              padding: const EdgeInsetsDirectional
                                                  .fromSTEB(
                                                      0.0, 0.0, 20.0, 0.0),
                                              child: FFButtonWidget(
                                                onPressed: () async {
                                                  context.pushNamed(
                                                      'EditProfileSignUp');
                                                },
                                                text: 'Edit Profile',
                                                options: FFButtonOptions(
                                                  height: 40.0,
                                                  padding: const EdgeInsetsDirectional
                                                      .fromSTEB(
                                                          24.0, 0.0, 24.0, 0.0),
                                                  iconPadding:
                                                      const EdgeInsetsDirectional
                                                          .fromSTEB(0.0, 0.0,
                                                              0.0, 0.0),
                                                  color: const Color(0x3D57636C),
                                                  textStyle: FlutterFlowTheme
                                                          .of(context)
                                                      .titleSmall
                                                      .override(
                                                        fontFamily:
                                                            'BT Beau Sans',
                                                        color: Colors.white,
                                                        fontSize: 14.0,
                                                        letterSpacing: 0.0,
                                                        useGoogleFonts: false,
                                                      ),
                                                  elevation: 0.0,
                                                  borderSide: const BorderSide(
                                                    color: Colors.transparent,
                                                    width: 1.0,
                                                  ),
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          24.0),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  Flexible(
                                    child: Container(
                                      width: MediaQuery.sizeOf(context).width *
                                          0.7,
                                      decoration: const BoxDecoration(),
                                      child: SingleChildScrollView(
                                        child: Column(
                                          mainAxisSize: MainAxisSize.max,
                                          children: [
                                            Align(
                                              alignment: const AlignmentDirectional(
                                                  0.0, -1.0),
                                              child: Padding(
                                                padding: const EdgeInsetsDirectional
                                                    .fromSTEB(
                                                        10.0, 0.0, 10.0, 0.0),
                                                child: Text(
                                                  getRemoteConfigString(
                                                      'waitingList_localTitle'),
                                                  textAlign: TextAlign.center,
                                                  style: FlutterFlowTheme.of(
                                                          context)
                                                      .bodyMedium
                                                      .override(
                                                        fontFamily:
                                                            'BT Beau Sans',
                                                        fontSize: 26.0,
                                                        letterSpacing: 0.0,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        useGoogleFonts: false,
                                                        lineHeight: 1.2,
                                                      ),
                                                ),
                                              ),
                                            ),
                                            Padding(
                                              padding: const EdgeInsetsDirectional
                                                  .fromSTEB(
                                                      0.0, 37.0, 0.0, 0.0),
                                              child: Container(
                                                decoration: const BoxDecoration(),
                                                child: Stack(
                                                  children: [
                                                    if (valueOrDefault<bool>(
                                                        currentUserDocument
                                                            ?.onWaitingList,
                                                        false))
                                                      Align(
                                                        alignment:
                                                            const AlignmentDirectional(
                                                                0.0, 0.0),
                                                        child: Container(
                                                          width:
                                                              MediaQuery.sizeOf(
                                                                          context)
                                                                      .width *
                                                                  1.0,
                                                          decoration:
                                                              BoxDecoration(
                                                            gradient:
                                                                const LinearGradient(
                                                              colors: [
                                                                Color(
                                                                    0xFF91B2FB),
                                                                Color(
                                                                    0xFFFF9EDE)
                                                              ],
                                                              stops: [0.0, 1.0],
                                                              begin:
                                                                  AlignmentDirectional(
                                                                      1.0,
                                                                      0.17),
                                                              end:
                                                                  AlignmentDirectional(
                                                                      -1.0,
                                                                      -0.17),
                                                            ),
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        11.0),
                                                            border: Border.all(
                                                              color: Colors
                                                                  .transparent,
                                                              width: 1.0,
                                                            ),
                                                          ),
                                                          child: Visibility(
                                                            visible: valueOrDefault<
                                                                    bool>(
                                                                currentUserDocument
                                                                    ?.onWaitingList,
                                                                false),
                                                            child: Align(
                                                              alignment:
                                                                  const AlignmentDirectional(
                                                                      0.0, 0.0),
                                                              child: Padding(
                                                                padding:
                                                                    const EdgeInsetsDirectional
                                                                        .fromSTEB(
                                                                            1.0,
                                                                            1.0,
                                                                            1.0,
                                                                            1.0),
                                                                child:
                                                                    Container(
                                                                  width: MediaQuery.sizeOf(
                                                                              context)
                                                                          .width *
                                                                      1.0,
                                                                  decoration:
                                                                      BoxDecoration(
                                                                    color: FlutterFlowTheme.of(
                                                                            context)
                                                                        .info,
                                                                    borderRadius:
                                                                        BorderRadius.circular(
                                                                            11.0),
                                                                    border:
                                                                        Border
                                                                            .all(
                                                                      color: Colors
                                                                          .transparent,
                                                                      width:
                                                                          1.0,
                                                                    ),
                                                                  ),
                                                                  child:
                                                                      Padding(
                                                                    padding: const EdgeInsetsDirectional
                                                                        .fromSTEB(
                                                                            5.0,
                                                                            5.0,
                                                                            5.0,
                                                                            5.0),
                                                                    child:
                                                                        Column(
                                                                      mainAxisSize:
                                                                          MainAxisSize
                                                                              .max,
                                                                      mainAxisAlignment:
                                                                          MainAxisAlignment
                                                                              .center,
                                                                      children: [
                                                                        Text(
                                                                          getRemoteConfigString(
                                                                              'waitinglist_currentPosition_heading'),
                                                                          textAlign:
                                                                              TextAlign.center,
                                                                          style: FlutterFlowTheme.of(context)
                                                                              .bodyMedium
                                                                              .override(
                                                                                fontFamily: 'BT Beau Sans',
                                                                                color: FlutterFlowTheme.of(context).secondaryText,
                                                                                fontSize: 14.0,
                                                                                letterSpacing: 0.0,
                                                                                fontWeight: FontWeight.normal,
                                                                                useGoogleFonts: false,
                                                                              ),
                                                                        ),
                                                                        Padding(
                                                                          padding: const EdgeInsetsDirectional.fromSTEB(
                                                                              0.0,
                                                                              8.0,
                                                                              0.0,
                                                                              0.0),
                                                                          child:
                                                                              Text(
                                                                            getRemoteConfigBool('waitinglist_showInterval')
                                                                                ? valueOrDefault(currentUserDocument?.waitingListInterval, '')
                                                                                : getRemoteConfigString('waitinglist_currentPosition_fallbackText'),
                                                                            textAlign:
                                                                                TextAlign.center,
                                                                            style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                  fontFamily: 'BT Beau Sans',
                                                                                  color: FlutterFlowTheme.of(context).primaryText,
                                                                                  fontSize: 20.0,
                                                                                  letterSpacing: 0.0,
                                                                                  fontWeight: FontWeight.bold,
                                                                                  useGoogleFonts: false,
                                                                                ),
                                                                          ),
                                                                        ),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            if (!(conditionalBuilderWlAvailabilityRecord !=
                                                null))
                                              Padding(
                                                padding: const EdgeInsetsDirectional
                                                    .fromSTEB(
                                                        0.0, 15.0, 0.0, 0.0),
                                                child: Container(
                                                  decoration: const BoxDecoration(),
                                                  child: Stack(
                                                    children: [
                                                      if (valueOrDefault<bool>(
                                                          currentUserDocument
                                                              ?.onWaitingList,
                                                          false))
                                                        Align(
                                                          alignment:
                                                              const AlignmentDirectional(
                                                                  0.0, 0.0),
                                                          child: Container(
                                                            width: MediaQuery
                                                                        .sizeOf(
                                                                            context)
                                                                    .width *
                                                                1.0,
                                                            decoration:
                                                                BoxDecoration(
                                                              gradient:
                                                                  const LinearGradient(
                                                                colors: [
                                                                  Color(
                                                                      0xFF91B2FB),
                                                                  Color(
                                                                      0xFFFF9EDE)
                                                                ],
                                                                stops: [
                                                                  0.0,
                                                                  1.0
                                                                ],
                                                                begin:
                                                                    AlignmentDirectional(
                                                                        1.0,
                                                                        0.17),
                                                                end:
                                                                    AlignmentDirectional(
                                                                        -1.0,
                                                                        -0.17),
                                                              ),
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          11.0),
                                                              border:
                                                                  Border.all(
                                                                color: Colors
                                                                    .transparent,
                                                                width: 1.0,
                                                              ),
                                                            ),
                                                            child: Padding(
                                                              padding:
                                                                  const EdgeInsetsDirectional
                                                                      .fromSTEB(
                                                                          1.0,
                                                                          1.0,
                                                                          1.0,
                                                                          1.0),
                                                              child: Container(
                                                                decoration:
                                                                    BoxDecoration(
                                                                  color: const Color(
                                                                      0xFFD8DADB),
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              10.0),
                                                                ),
                                                                child: Padding(
                                                                  padding: const EdgeInsetsDirectional
                                                                      .fromSTEB(
                                                                          16.0,
                                                                          0.0,
                                                                          16.0,
                                                                          0.0),
                                                                  child: Row(
                                                                    mainAxisSize:
                                                                        MainAxisSize
                                                                            .max,
                                                                    mainAxisAlignment:
                                                                        MainAxisAlignment
                                                                            .spaceBetween,
                                                                    children: [
                                                                      Flexible(
                                                                        child:
                                                                            Text(
                                                                          getRemoteConfigString(
                                                                              'speedup_boolean_name'),
                                                                          style: FlutterFlowTheme.of(context)
                                                                              .bodyMedium
                                                                              .override(
                                                                                fontFamily: 'BT Beau Sans',
                                                                                fontSize: 16.0,
                                                                                letterSpacing: 0.0,
                                                                                useGoogleFonts: false,
                                                                              ),
                                                                        ),
                                                                      ),
                                                                      Flexible(
                                                                        child:
                                                                            Align(
                                                                          alignment: const AlignmentDirectional(
                                                                              1.0,
                                                                              0.0),
                                                                          child:
                                                                              Stack(
                                                                            alignment:
                                                                                const AlignmentDirectional(1.0, 0.0),
                                                                            children: [
                                                                              Switch.adaptive(
                                                                                value: _model.waitingListSwitchValue1 ??= false,
                                                                                onChanged: (true == true)
                                                                                    ? null
                                                                                    : (newValue) async {
                                                                                        setState(() => _model.waitingListSwitchValue1 = newValue);
                                                                                      },
                                                                                activeColor: const Color(0xFF15ACDC),
                                                                                activeTrackColor: FlutterFlowTheme.of(context).accent1,
                                                                                inactiveTrackColor: (true == true) ? const Color(0xFFC4C5C6) : FlutterFlowTheme.of(context).alternate,
                                                                                inactiveThumbColor: (true == true) ? const Color(0xFF464646) : FlutterFlowTheme.of(context).secondaryText,
                                                                              ),
                                                                            ],
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            if (conditionalBuilderWlAvailabilityRecord !=
                                                null)
                                              Padding(
                                                padding: const EdgeInsetsDirectional
                                                    .fromSTEB(
                                                        0.0, 15.0, 0.0, 0.0),
                                                child: Container(
                                                  decoration: const BoxDecoration(),
                                                  child: Stack(
                                                    children: [
                                                      if (valueOrDefault<bool>(
                                                          currentUserDocument
                                                              ?.onWaitingList,
                                                          false))
                                                        Align(
                                                          alignment:
                                                              const AlignmentDirectional(
                                                                  0.0, 0.0),
                                                          child: Container(
                                                            width: MediaQuery
                                                                        .sizeOf(
                                                                            context)
                                                                    .width *
                                                                1.0,
                                                            decoration:
                                                                BoxDecoration(
                                                              gradient:
                                                                  const LinearGradient(
                                                                colors: [
                                                                  Color(
                                                                      0xFF91B2FB),
                                                                  Color(
                                                                      0xFFFF9EDE)
                                                                ],
                                                                stops: [
                                                                  0.0,
                                                                  1.0
                                                                ],
                                                                begin:
                                                                    AlignmentDirectional(
                                                                        1.0,
                                                                        0.17),
                                                                end:
                                                                    AlignmentDirectional(
                                                                        -1.0,
                                                                        -0.17),
                                                              ),
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          11.0),
                                                              border:
                                                                  Border.all(
                                                                color: Colors
                                                                    .transparent,
                                                                width: 1.0,
                                                              ),
                                                            ),
                                                            child: Padding(
                                                              padding:
                                                                  const EdgeInsetsDirectional
                                                                      .fromSTEB(
                                                                          1.0,
                                                                          1.0,
                                                                          1.0,
                                                                          1.0),
                                                              child: Container(
                                                                decoration:
                                                                    BoxDecoration(
                                                                  color: FlutterFlowTheme.of(
                                                                          context)
                                                                      .primaryBackground,
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              10.0),
                                                                ),
                                                                child: Padding(
                                                                  padding: const EdgeInsetsDirectional
                                                                      .fromSTEB(
                                                                          16.0,
                                                                          0.0,
                                                                          16.0,
                                                                          0.0),
                                                                  child: Row(
                                                                    mainAxisSize:
                                                                        MainAxisSize
                                                                            .max,
                                                                    mainAxisAlignment:
                                                                        MainAxisAlignment
                                                                            .spaceBetween,
                                                                    children: [
                                                                      Flexible(
                                                                        child:
                                                                            Text(
                                                                          getRemoteConfigString(
                                                                              'speedup_boolean_name'),
                                                                          style: FlutterFlowTheme.of(context)
                                                                              .bodyMedium
                                                                              .override(
                                                                                fontFamily: 'BT Beau Sans',
                                                                                fontSize: 16.0,
                                                                                letterSpacing: 0.0,
                                                                                useGoogleFonts: false,
                                                                              ),
                                                                        ),
                                                                      ),
                                                                      Flexible(
                                                                        child:
                                                                            Align(
                                                                          alignment: const AlignmentDirectional(
                                                                              1.0,
                                                                              0.0),
                                                                          child:
                                                                              Stack(
                                                                            alignment:
                                                                                const AlignmentDirectional(1.0, 0.0),
                                                                            children: [
                                                                              Builder(
                                                                                builder: (context) => Switch.adaptive(
                                                                                  value: _model.waitingListSwitchValue2 ??= revenue_cat.activeEntitlementIds.contains('150_waitinglist_speed') || revenue_cat.activeEntitlementIds.contains('200_waitinglist_speed') || revenue_cat.activeEntitlementIds.contains('300_waitinglist_speed'),
                                                                                  onChanged: (newValue) async {
                                                                                    setState(() => _model.waitingListSwitchValue2 = newValue);
                                                                                    if (newValue) {
                                                                                      if (revenue_cat.activeEntitlementIds.contains('150_waitinglist_speed') || revenue_cat.activeEntitlementIds.contains('200_waitinglist_speed') || revenue_cat.activeEntitlementIds.contains('300_waitinglist_speed')) {
                                                                                        await showDialog(
                                                                                          context: context,
                                                                                          builder: (dialogContext) {
                                                                                            return Dialog(
                                                                                              elevation: 0,
                                                                                              insetPadding: EdgeInsets.zero,
                                                                                              backgroundColor: Colors.transparent,
                                                                                              alignment: const AlignmentDirectional(0.0, 0.0).resolve(Directionality.of(context)),
                                                                                              child: GestureDetector(
                                                                                                onTap: () => _model.unfocusNode.canRequestFocus ? FocusScope.of(context).requestFocus(_model.unfocusNode) : FocusScope.of(context).unfocus(),
                                                                                                child: const AlreadySpedUpPopupWidget(),
                                                                                              ),
                                                                                            );
                                                                                          },
                                                                                        ).then((value) => setState(() {}));
                                                                                      } else {
                                                                                        try {
                       analytics.logEvent('Navigated to Speed Up from Waiting List Intro');
                       } catch(e) {}
                                                                                        context.pushNamed(
                                                                                          'WaitingListSpeedUpPurchase',
                                                                                          extra: <String, dynamic>{
                                                                                            kTransitionInfoKey: const TransitionInfo(
                                                                                              hasTransition: true,
                                                                                              transitionType: PageTransitionType.bottomToTop,
                                                                                            ),
                                                                                          },
                                                                                        );
                                                                                      }
                                                                                    } else {
                                                                                      setState(() {
                                                                                        _model.waitingListSwitchValue2 = (revenue_cat.activeEntitlementIds.contains('150_waitinglist_speed') || revenue_cat.activeEntitlementIds.contains('200_waitinglist_speed') || revenue_cat.activeEntitlementIds.contains('300_waitinglist_speed'));
                                                                                      });
                                                                                    }
                                                                                  },
                                                                                  activeColor: const Color(0xFF15ACDC),
                                                                                  activeTrackColor: FlutterFlowTheme.of(context).accent1,
                                                                                  inactiveTrackColor: FlutterFlowTheme.of(context).alternate,
                                                                                  inactiveThumbColor: FlutterFlowTheme.of(context).secondaryText,
                                                                                ),
                                                                              ),
                                                                              if (!(revenue_cat.activeEntitlementIds.contains('150_waitinglist_speed') || revenue_cat.activeEntitlementIds.contains('200_waitinglist_speed') || revenue_cat.activeEntitlementIds.contains('300_waitinglist_speed')))
                                                                                InkWell(
                                                                                  splashColor: Colors.transparent,
                                                                                  focusColor: Colors.transparent,
                                                                                  hoverColor: Colors.transparent,
                                                                                  highlightColor: Colors.transparent,
                                                                                  onTap: () async {
                                                                                    context.pushNamed(
                                                                                      'WaitingListSpeedUpPurchase',
                                                                                      extra: <String, dynamic>{
                                                                                        kTransitionInfoKey: const TransitionInfo(
                                                                                          hasTransition: true,
                                                                                          transitionType: PageTransitionType.bottomToTop,
                                                                                        ),
                                                                                      },
                                                                                    );
                                                                                  },
                                                                                  child: Container(
                                                                                    width: 70.0,
                                                                                    height: 45.0,
                                                                                    decoration: const BoxDecoration(),
                                                                                  ),
                                                                                ),
                                                                            ],
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            if (!(conditionalBuilderWlAvailabilityRecord !=
                                                null))
                                              Align(
                                                alignment: const AlignmentDirectional(
                                                    0.0, -1.0),
                                                child: Padding(
                                                  padding: const EdgeInsetsDirectional
                                                      .fromSTEB(
                                                          0.0, 15.0, 0.0, 0.0),
                                                  child: Text(
                                                    getRemoteConfigString(
                                                        'waitingList_notAvailable_caption'),
                                                    textAlign: TextAlign.center,
                                                    style: FlutterFlowTheme.of(
                                                            context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          fontSize: 14.0,
                                                          letterSpacing: 0.0,
                                                          fontWeight:
                                                              FontWeight.normal,
                                                          useGoogleFonts: false,
                                                          lineHeight: 1.2,
                                                        ),
                                                  ),
                                                ),
                                              ),
                                            StreamBuilder<
                                                List<
                                                    GlobalWlAvailabilityRecord>>(
                                              stream:
                                                  queryGlobalWlAvailabilityRecord(
                                                queryBuilder:
                                                    (globalWlAvailabilityRecord) =>
                                                        globalWlAvailabilityRecord
                                                            .where(
                                                  'name',
                                                  isEqualTo: valueOrDefault(
                                                      currentUserDocument
                                                          ?.wlRegion,
                                                      ''),
                                                ),
                                                singleRecord: true,
                                              ),
                                              builder: (context, snapshot) {
                                                // Customize what your widget looks like when it's loading.
                                                if (!snapshot.hasData) {
                                                  return Center(
                                                    child: SizedBox(
                                                      width: 50.0,
                                                      height: 50.0,
                                                      child:
                                                          CircularProgressIndicator(
                                                        valueColor:
                                                            AlwaysStoppedAnimation<
                                                                Color>(
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .accent2,
                                                        ),
                                                      ),
                                                    ),
                                                  );
                                                }
                                                List<GlobalWlAvailabilityRecord>
                                                    containerGlobalWlAvailabilityRecordList =
                                                    snapshot.data!;
                                                final containerGlobalWlAvailabilityRecord =
                                                    containerGlobalWlAvailabilityRecordList
                                                            .isNotEmpty
                                                        ? containerGlobalWlAvailabilityRecordList
                                                            .first
                                                        : null;
                                                return Container(
                                                  decoration: const BoxDecoration(),
                                                  child: Visibility(
                                                    visible: getRemoteConfigBool(
                                                            'show_globalWaitingListAscent_button') ||
                                                        (containerGlobalWlAvailabilityRecord !=
                                                            null),
                                                    child: Padding(
                                                      padding:
                                                          const EdgeInsetsDirectional
                                                              .fromSTEB(
                                                                  30.0,
                                                                  15.0,
                                                                  30.0,
                                                                  0.0),
                                                      child: wrapWithModel(
                                                        model: _model
                                                            .gradientButtonModel,
                                                        updateCallback: () =>
                                                            setState(() {}),
                                                        child:
                                                            GradientButtonWidget(
                                                          title: getRemoteConfigString(
                                                              'waitinglist_goto_global_cta'),
                                                          action: () async {
                                                            context.pushNamed(
                                                              'GlobalMatchingInfo',
                                                              extra: <String,
                                                                  dynamic>{
                                                                kTransitionInfoKey:
                                                                    const TransitionInfo(
                                                                  hasTransition:
                                                                      true,
                                                                  transitionType:
                                                                      PageTransitionType
                                                                          .bottomToTop,
                                                                ),
                                                              },
                                                            );
                                                          },
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                );
                                              },
                                            ),
                                            Align(
                                              alignment: const AlignmentDirectional(
                                                  0.0, -1.0),
                                              child: Padding(
                                                padding: const EdgeInsetsDirectional
                                                    .fromSTEB(
                                                        10.0, 50.0, 10.0, 0.0),
                                                child: Text(
                                                  getRemoteConfigString(
                                                      'waitinglist_moreInfo_subtitle'),
                                                  textAlign: TextAlign.center,
                                                  style: FlutterFlowTheme.of(
                                                          context)
                                                      .bodyMedium
                                                      .override(
                                                        fontFamily:
                                                            'BT Beau Sans',
                                                        fontSize: 16.0,
                                                        letterSpacing: 0.0,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        useGoogleFonts: false,
                                                        lineHeight: 1.2,
                                                      ),
                                                ),
                                              ),
                                            ),
                                            Padding(
                                              padding: const EdgeInsetsDirectional
                                                  .fromSTEB(
                                                      0.0, 27.0, 0.0, 0.0),
                                              child: Builder(
                                                builder: (context) {
                                                  if (conditionalBuilderWlAvailabilityRecord !=
                                                      null) {
                                                    return SizedBox(
                                                      width: double.infinity,
                                                      height: 220.0,
                                                      child: CarouselSlider(
                                                        items: [
                                                          Stack(
                                                            children: [
                                                              if (valueOrDefault<
                                                                      bool>(
                                                                  currentUserDocument
                                                                      ?.onWaitingList,
                                                                  false))
                                                                Align(
                                                                  alignment:
                                                                      const AlignmentDirectional(
                                                                          0.0,
                                                                          0.0),
                                                                  child:
                                                                      Container(
                                                                    width: MediaQuery.sizeOf(context)
                                                                            .width *
                                                                        1.0,
                                                                    decoration:
                                                                        BoxDecoration(
                                                                      gradient:
                                                                          const LinearGradient(
                                                                        colors: [
                                                                          Color(
                                                                              0xFF91B2FB),
                                                                          Color(
                                                                              0xFFFF9EDE)
                                                                        ],
                                                                        stops: [
                                                                          0.0,
                                                                          1.0
                                                                        ],
                                                                        begin: AlignmentDirectional(
                                                                            1.0,
                                                                            0.17),
                                                                        end: AlignmentDirectional(
                                                                            -1.0,
                                                                            -0.17),
                                                                      ),
                                                                      borderRadius:
                                                                          BorderRadius.circular(
                                                                              11.0),
                                                                      border:
                                                                          Border
                                                                              .all(
                                                                        color: Colors
                                                                            .transparent,
                                                                        width:
                                                                            1.0,
                                                                      ),
                                                                    ),
                                                                    child:
                                                                        Visibility(
                                                                      visible: valueOrDefault<
                                                                              bool>(
                                                                          currentUserDocument
                                                                              ?.onWaitingList,
                                                                          false),
                                                                      child:
                                                                          Align(
                                                                        alignment: const AlignmentDirectional(
                                                                            0.0,
                                                                            0.0),
                                                                        child:
                                                                            Padding(
                                                                          padding: const EdgeInsetsDirectional.fromSTEB(
                                                                              1.0,
                                                                              1.0,
                                                                              1.0,
                                                                              1.0),
                                                                          child:
                                                                              Container(
                                                                            width:
                                                                                MediaQuery.sizeOf(context).width * 1.0,
                                                                            height:
                                                                                MediaQuery.sizeOf(context).height * 1.0,
                                                                            decoration:
                                                                                BoxDecoration(
                                                                              color: FlutterFlowTheme.of(context).info,
                                                                              borderRadius: BorderRadius.circular(11.0),
                                                                              border: Border.all(
                                                                                color: Colors.transparent,
                                                                                width: 1.0,
                                                                              ),
                                                                            ),
                                                                            alignment:
                                                                                const AlignmentDirectional(0.0, 0.0),
                                                                            child:
                                                                                Padding(
                                                                              padding: const EdgeInsetsDirectional.fromSTEB(20.0, 20.0, 20.0, 20.0),
                                                                              child: SingleChildScrollView(
                                                                                child: Column(
                                                                                  mainAxisSize: MainAxisSize.max,
                                                                                  mainAxisAlignment: MainAxisAlignment.center,
                                                                                  children: [
                                                                                    Text(
                                                                                      getRemoteConfigString('waitinglist_p1'),
                                                                                      textAlign: TextAlign.center,
                                                                                      style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                            fontFamily: 'BT Beau Sans',
                                                                                            color: FlutterFlowTheme.of(context).secondaryText,
                                                                                            fontSize: 14.0,
                                                                                            letterSpacing: 0.0,
                                                                                            fontWeight: FontWeight.normal,
                                                                                            useGoogleFonts: false,
                                                                                          ),
                                                                                    ),
                                                                                  ],
                                                                                ),
                                                                              ),
                                                                            ),
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ),
                                                            ],
                                                          ),
                                                          Stack(
                                                            children: [
                                                              if (valueOrDefault<
                                                                      bool>(
                                                                  currentUserDocument
                                                                      ?.onWaitingList,
                                                                  false))
                                                                Align(
                                                                  alignment:
                                                                      const AlignmentDirectional(
                                                                          0.0,
                                                                          0.0),
                                                                  child:
                                                                      Container(
                                                                    width: MediaQuery.sizeOf(context)
                                                                            .width *
                                                                        1.0,
                                                                    decoration:
                                                                        BoxDecoration(
                                                                      gradient:
                                                                          const LinearGradient(
                                                                        colors: [
                                                                          Color(
                                                                              0xFF91B2FB),
                                                                          Color(
                                                                              0xFFFF9EDE)
                                                                        ],
                                                                        stops: [
                                                                          0.0,
                                                                          1.0
                                                                        ],
                                                                        begin: AlignmentDirectional(
                                                                            1.0,
                                                                            0.17),
                                                                        end: AlignmentDirectional(
                                                                            -1.0,
                                                                            -0.17),
                                                                      ),
                                                                      borderRadius:
                                                                          BorderRadius.circular(
                                                                              11.0),
                                                                      border:
                                                                          Border
                                                                              .all(
                                                                        color: Colors
                                                                            .transparent,
                                                                        width:
                                                                            1.0,
                                                                      ),
                                                                    ),
                                                                    child:
                                                                        Visibility(
                                                                      visible: valueOrDefault<
                                                                              bool>(
                                                                          currentUserDocument
                                                                              ?.onWaitingList,
                                                                          false),
                                                                      child:
                                                                          Align(
                                                                        alignment: const AlignmentDirectional(
                                                                            0.0,
                                                                            0.0),
                                                                        child:
                                                                            Padding(
                                                                          padding: const EdgeInsetsDirectional.fromSTEB(
                                                                              1.0,
                                                                              1.0,
                                                                              1.0,
                                                                              1.0),
                                                                          child:
                                                                              Container(
                                                                            width:
                                                                                MediaQuery.sizeOf(context).width * 1.0,
                                                                            height:
                                                                                MediaQuery.sizeOf(context).height * 1.0,
                                                                            decoration:
                                                                                BoxDecoration(
                                                                              color: FlutterFlowTheme.of(context).info,
                                                                              borderRadius: BorderRadius.circular(11.0),
                                                                              border: Border.all(
                                                                                color: Colors.transparent,
                                                                                width: 1.0,
                                                                              ),
                                                                            ),
                                                                            alignment:
                                                                                const AlignmentDirectional(0.0, 0.0),
                                                                            child:
                                                                                Padding(
                                                                              padding: const EdgeInsetsDirectional.fromSTEB(20.0, 20.0, 20.0, 20.0),
                                                                              child: SingleChildScrollView(
                                                                                child: Column(
                                                                                  mainAxisSize: MainAxisSize.max,
                                                                                  mainAxisAlignment: MainAxisAlignment.center,
                                                                                  children: [
                                                                                    Text(
                                                                                      getRemoteConfigString('waitinglist_p2'),
                                                                                      textAlign: TextAlign.center,
                                                                                      style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                            fontFamily: 'BT Beau Sans',
                                                                                            color: FlutterFlowTheme.of(context).secondaryText,
                                                                                            fontSize: 14.0,
                                                                                            letterSpacing: 0.0,
                                                                                            fontWeight: FontWeight.normal,
                                                                                            useGoogleFonts: false,
                                                                                          ),
                                                                                    ),
                                                                                  ],
                                                                                ),
                                                                              ),
                                                                            ),
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ),
                                                            ],
                                                          ),
                                                          Stack(
                                                            children: [
                                                              if (valueOrDefault<
                                                                      bool>(
                                                                  currentUserDocument
                                                                      ?.onWaitingList,
                                                                  false))
                                                                Align(
                                                                  alignment:
                                                                      const AlignmentDirectional(
                                                                          0.0,
                                                                          0.0),
                                                                  child:
                                                                      Container(
                                                                    width: MediaQuery.sizeOf(context)
                                                                            .width *
                                                                        1.0,
                                                                    decoration:
                                                                        BoxDecoration(
                                                                      gradient:
                                                                          const LinearGradient(
                                                                        colors: [
                                                                          Color(
                                                                              0xFF91B2FB),
                                                                          Color(
                                                                              0xFFFF9EDE)
                                                                        ],
                                                                        stops: [
                                                                          0.0,
                                                                          1.0
                                                                        ],
                                                                        begin: AlignmentDirectional(
                                                                            1.0,
                                                                            0.17),
                                                                        end: AlignmentDirectional(
                                                                            -1.0,
                                                                            -0.17),
                                                                      ),
                                                                      borderRadius:
                                                                          BorderRadius.circular(
                                                                              11.0),
                                                                      border:
                                                                          Border
                                                                              .all(
                                                                        color: Colors
                                                                            .transparent,
                                                                        width:
                                                                            1.0,
                                                                      ),
                                                                    ),
                                                                    child:
                                                                        Visibility(
                                                                      visible: valueOrDefault<
                                                                              bool>(
                                                                          currentUserDocument
                                                                              ?.onWaitingList,
                                                                          false),
                                                                      child:
                                                                          Align(
                                                                        alignment: const AlignmentDirectional(
                                                                            0.0,
                                                                            0.0),
                                                                        child:
                                                                            Padding(
                                                                          padding: const EdgeInsetsDirectional.fromSTEB(
                                                                              1.0,
                                                                              1.0,
                                                                              1.0,
                                                                              1.0),
                                                                          child:
                                                                              Container(
                                                                            width:
                                                                                MediaQuery.sizeOf(context).width * 1.0,
                                                                            height:
                                                                                MediaQuery.sizeOf(context).height * 1.0,
                                                                            decoration:
                                                                                BoxDecoration(
                                                                              color: FlutterFlowTheme.of(context).info,
                                                                              borderRadius: BorderRadius.circular(11.0),
                                                                              border: Border.all(
                                                                                color: Colors.transparent,
                                                                                width: 1.0,
                                                                              ),
                                                                            ),
                                                                            alignment:
                                                                                const AlignmentDirectional(0.0, 0.0),
                                                                            child:
                                                                                Padding(
                                                                              padding: const EdgeInsetsDirectional.fromSTEB(20.0, 20.0, 20.0, 20.0),
                                                                              child: SingleChildScrollView(
                                                                                child: Column(
                                                                                  mainAxisSize: MainAxisSize.max,
                                                                                  mainAxisAlignment: MainAxisAlignment.center,
                                                                                  children: [
                                                                                    Text(
                                                                                      getRemoteConfigString('waitinglist_p3'),
                                                                                      textAlign: TextAlign.center,
                                                                                      style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                            fontFamily: 'BT Beau Sans',
                                                                                            color: FlutterFlowTheme.of(context).secondaryText,
                                                                                            fontSize: 14.0,
                                                                                            letterSpacing: 0.0,
                                                                                            fontWeight: FontWeight.normal,
                                                                                            useGoogleFonts: false,
                                                                                          ),
                                                                                    ),
                                                                                  ],
                                                                                ),
                                                                              ),
                                                                            ),
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ),
                                                            ],
                                                          ),
                                                          Stack(
                                                            children: [
                                                              if (valueOrDefault<
                                                                      bool>(
                                                                  currentUserDocument
                                                                      ?.onWaitingList,
                                                                  false))
                                                                Align(
                                                                  alignment:
                                                                      const AlignmentDirectional(
                                                                          0.0,
                                                                          0.0),
                                                                  child:
                                                                      Container(
                                                                    width: MediaQuery.sizeOf(context)
                                                                            .width *
                                                                        1.0,
                                                                    decoration:
                                                                        BoxDecoration(
                                                                      gradient:
                                                                          const LinearGradient(
                                                                        colors: [
                                                                          Color(
                                                                              0xFF91B2FB),
                                                                          Color(
                                                                              0xFFFF9EDE)
                                                                        ],
                                                                        stops: [
                                                                          0.0,
                                                                          1.0
                                                                        ],
                                                                        begin: AlignmentDirectional(
                                                                            1.0,
                                                                            0.17),
                                                                        end: AlignmentDirectional(
                                                                            -1.0,
                                                                            -0.17),
                                                                      ),
                                                                      borderRadius:
                                                                          BorderRadius.circular(
                                                                              11.0),
                                                                      border:
                                                                          Border
                                                                              .all(
                                                                        color: Colors
                                                                            .transparent,
                                                                        width:
                                                                            1.0,
                                                                      ),
                                                                    ),
                                                                    child:
                                                                        Visibility(
                                                                      visible: valueOrDefault<
                                                                              bool>(
                                                                          currentUserDocument
                                                                              ?.onWaitingList,
                                                                          false),
                                                                      child:
                                                                          Align(
                                                                        alignment: const AlignmentDirectional(
                                                                            0.0,
                                                                            0.0),
                                                                        child:
                                                                            Padding(
                                                                          padding: const EdgeInsetsDirectional.fromSTEB(
                                                                              1.0,
                                                                              1.0,
                                                                              1.0,
                                                                              1.0),
                                                                          child:
                                                                              Container(
                                                                            width:
                                                                                MediaQuery.sizeOf(context).width * 1.0,
                                                                            height:
                                                                                MediaQuery.sizeOf(context).height * 1.0,
                                                                            decoration:
                                                                                BoxDecoration(
                                                                              color: FlutterFlowTheme.of(context).info,
                                                                              borderRadius: BorderRadius.circular(11.0),
                                                                              border: Border.all(
                                                                                color: Colors.transparent,
                                                                                width: 1.0,
                                                                              ),
                                                                            ),
                                                                            alignment:
                                                                                const AlignmentDirectional(0.0, 0.0),
                                                                            child:
                                                                                Padding(
                                                                              padding: const EdgeInsetsDirectional.fromSTEB(20.0, 20.0, 20.0, 20.0),
                                                                              child: SingleChildScrollView(
                                                                                child: Column(
                                                                                  mainAxisSize: MainAxisSize.max,
                                                                                  mainAxisAlignment: MainAxisAlignment.center,
                                                                                  children: [
                                                                                    if (revenue_cat.activeEntitlementIds.contains('150_waitinglist_speed') || revenue_cat.activeEntitlementIds.contains('200_waitinglist_speed') || revenue_cat.activeEntitlementIds.contains('300_waitinglist_speed'))
                                                                                      Column(
                                                                                        mainAxisSize: MainAxisSize.max,
                                                                                        mainAxisAlignment: MainAxisAlignment.center,
                                                                                        children: [
                                                                                          Text(
                                                                                            getRemoteConfigString('waitinglist_ppaywlink_cursub'),
                                                                                            textAlign: TextAlign.center,
                                                                                            style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                                  fontFamily: 'BT Beau Sans',
                                                                                                  color: FlutterFlowTheme.of(context).secondaryText,
                                                                                                  fontSize: 14.0,
                                                                                                  letterSpacing: 0.0,
                                                                                                  fontWeight: FontWeight.normal,
                                                                                                  useGoogleFonts: false,
                                                                                                ),
                                                                                          ),
                                                                                        ],
                                                                                      ),
                                                                                    if (!(revenue_cat.activeEntitlementIds.contains('150_waitinglist_speed') || revenue_cat.activeEntitlementIds.contains('200_waitinglist_speed') || revenue_cat.activeEntitlementIds.contains('300_waitinglist_speed')))
                                                                                      Column(
                                                                                        mainAxisSize: MainAxisSize.max,
                                                                                        mainAxisAlignment: MainAxisAlignment.center,
                                                                                        children: [
                                                                                          Text(
                                                                                            getRemoteConfigString('waitinglist_ppaywlink'),
                                                                                            textAlign: TextAlign.center,
                                                                                            style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                                  fontFamily: 'BT Beau Sans',
                                                                                                  color: FlutterFlowTheme.of(context).secondaryText,
                                                                                                  fontSize: 14.0,
                                                                                                  letterSpacing: 0.0,
                                                                                                  fontWeight: FontWeight.normal,
                                                                                                  useGoogleFonts: false,
                                                                                                ),
                                                                                          ),
                                                                                          Padding(
                                                                                            padding: const EdgeInsetsDirectional.fromSTEB(15.0, 23.0, 15.0, 0.0),
                                                                                            child: Builder(
                                                                                              builder: (context) {
                                                                                                return Column(
                                                                                                  mainAxisSize: MainAxisSize.max,
                                                                                                  children: [
                                                                                                    wrapWithModel(
                                                                                                      model: _model.gradientButtonSmallModel,
                                                                                                      updateCallback: () => setState(() {}),
                                                                                                      child: GradientButtonSmallWidget(
                                                                                                        title: getRemoteConfigString('speedup_cta_name'),
                                                                                                        action: () async {
                                                                                                          context.pushNamed('WaitingListSpeedUpPurchase');
                                                                                                        },
                                                                                                      ),
                                                                                                    ),
                                                                                                    if (!(revenue_cat.activeEntitlementIds.contains('150_waitinglist_speed') || revenue_cat.activeEntitlementIds.contains('200_waitinglist_speed') || revenue_cat.activeEntitlementIds.contains('300_waitinglist_speed')))
                                                                                                      Align(
                                                                                                        alignment: const AlignmentDirectional(0.0, 1.0),
                                                                                                        child: Padding(
                                                                                                          padding: const EdgeInsetsDirectional.fromSTEB(0.0, 8.0, 0.0, 0.0),
                                                                                                          child: InkWell(
                                                                                                            splashColor: Colors.transparent,
                                                                                                            focusColor: Colors.transparent,
                                                                                                            hoverColor: Colors.transparent,
                                                                                                            highlightColor: Colors.transparent,
                                                                                                            onTap: () async {
                                                                                                              await revenue_cat.restorePurchases();
                                                                                                            },
                                                                                                            child: Text(
                                                                                                              'Restore purchase',
                                                                                                              textAlign: TextAlign.center,
                                                                                                              style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                                                    fontFamily: 'BT Beau Sans',
                                                                                                                    color: FlutterFlowTheme.of(context).primaryText,
                                                                                                                    fontSize: 11.0,
                                                                                                                    letterSpacing: 0.0,
                                                                                                                    fontWeight: FontWeight.normal,
                                                                                                                    useGoogleFonts: false,
                                                                                                                  ),
                                                                                                            ),
                                                                                                          ),
                                                                                                        ),
                                                                                                      ),
                                                                                                  ],
                                                                                                );
                                                                                                                                                                                            },
                                                                                            ),
                                                                                          ),
                                                                                        ],
                                                                                      ),
                                                                                  ],
                                                                                ),
                                                                              ),
                                                                            ),
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ),
                                                            ],
                                                          ),
                                                        ],
                                                        carouselController: _model
                                                                .carouselController1 ??=
                                                            CarouselController(),
                                                        options:
                                                            CarouselOptions(
                                                          initialPage: 0,
                                                          viewportFraction: 1.0,
                                                          disableCenter: true,
                                                          enlargeCenterPage:
                                                              true,
                                                          enlargeFactor: 1.0,
                                                          enableInfiniteScroll:
                                                              true,
                                                          scrollDirection:
                                                              Axis.horizontal,
                                                          autoPlay: true,
                                                          autoPlayAnimationDuration:
                                                              const Duration(
                                                                  milliseconds:
                                                                      800),
                                                          autoPlayInterval:
                                                              const Duration(
                                                                  milliseconds:
                                                                      (800 +
                                                                          9000)),
                                                          autoPlayCurve:
                                                              Curves.linear,
                                                          pauseAutoPlayInFiniteScroll:
                                                              true,
                                                          onPageChanged: (index,
                                                                  _) =>
                                                              _model.carouselCurrentIndex1 =
                                                                  index,
                                                        ),
                                                      ),
                                                    );
                                                  } else {
                                                    return SizedBox(
                                                      width: double.infinity,
                                                      height: 220.0,
                                                      child: CarouselSlider(
                                                        items: [
                                                          Stack(
                                                            children: [
                                                              if (valueOrDefault<
                                                                      bool>(
                                                                  currentUserDocument
                                                                      ?.onWaitingList,
                                                                  false))
                                                                Align(
                                                                  alignment:
                                                                      const AlignmentDirectional(
                                                                          0.0,
                                                                          0.0),
                                                                  child:
                                                                      Container(
                                                                    width: MediaQuery.sizeOf(context)
                                                                            .width *
                                                                        1.0,
                                                                    decoration:
                                                                        BoxDecoration(
                                                                      gradient:
                                                                          const LinearGradient(
                                                                        colors: [
                                                                          Color(
                                                                              0xFF91B2FB),
                                                                          Color(
                                                                              0xFFFF9EDE)
                                                                        ],
                                                                        stops: [
                                                                          0.0,
                                                                          1.0
                                                                        ],
                                                                        begin: AlignmentDirectional(
                                                                            1.0,
                                                                            0.17),
                                                                        end: AlignmentDirectional(
                                                                            -1.0,
                                                                            -0.17),
                                                                      ),
                                                                      borderRadius:
                                                                          BorderRadius.circular(
                                                                              11.0),
                                                                      border:
                                                                          Border
                                                                              .all(
                                                                        color: Colors
                                                                            .transparent,
                                                                        width:
                                                                            1.0,
                                                                      ),
                                                                    ),
                                                                    child:
                                                                        Visibility(
                                                                      visible: valueOrDefault<
                                                                              bool>(
                                                                          currentUserDocument
                                                                              ?.onWaitingList,
                                                                          false),
                                                                      child:
                                                                          Align(
                                                                        alignment: const AlignmentDirectional(
                                                                            0.0,
                                                                            0.0),
                                                                        child:
                                                                            Padding(
                                                                          padding: const EdgeInsetsDirectional.fromSTEB(
                                                                              1.0,
                                                                              1.0,
                                                                              1.0,
                                                                              1.0),
                                                                          child:
                                                                              Container(
                                                                            width:
                                                                                MediaQuery.sizeOf(context).width * 1.0,
                                                                            height:
                                                                                MediaQuery.sizeOf(context).height * 1.0,
                                                                            decoration:
                                                                                BoxDecoration(
                                                                              color: FlutterFlowTheme.of(context).info,
                                                                              borderRadius: BorderRadius.circular(11.0),
                                                                              border: Border.all(
                                                                                color: Colors.transparent,
                                                                                width: 1.0,
                                                                              ),
                                                                            ),
                                                                            alignment:
                                                                                const AlignmentDirectional(0.0, 0.0),
                                                                            child:
                                                                                Padding(
                                                                              padding: const EdgeInsetsDirectional.fromSTEB(20.0, 20.0, 20.0, 20.0),
                                                                              child: SingleChildScrollView(
                                                                                child: Column(
                                                                                  mainAxisSize: MainAxisSize.max,
                                                                                  mainAxisAlignment: MainAxisAlignment.center,
                                                                                  children: [
                                                                                    Text(
                                                                                      getRemoteConfigString('waitinglist_prelaunch_p1'),
                                                                                      textAlign: TextAlign.center,
                                                                                      style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                            fontFamily: 'BT Beau Sans',
                                                                                            color: FlutterFlowTheme.of(context).secondaryText,
                                                                                            fontSize: 14.0,
                                                                                            letterSpacing: 0.0,
                                                                                            fontWeight: FontWeight.normal,
                                                                                            useGoogleFonts: false,
                                                                                          ),
                                                                                    ),
                                                                                  ],
                                                                                ),
                                                                              ),
                                                                            ),
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ),
                                                            ],
                                                          ),
                                                          Stack(
                                                            children: [
                                                              if (valueOrDefault<
                                                                      bool>(
                                                                  currentUserDocument
                                                                      ?.onWaitingList,
                                                                  false))
                                                                Align(
                                                                  alignment:
                                                                      const AlignmentDirectional(
                                                                          0.0,
                                                                          0.0),
                                                                  child:
                                                                      Container(
                                                                    width: MediaQuery.sizeOf(context)
                                                                            .width *
                                                                        1.0,
                                                                    decoration:
                                                                        BoxDecoration(
                                                                      gradient:
                                                                          const LinearGradient(
                                                                        colors: [
                                                                          Color(
                                                                              0xFF91B2FB),
                                                                          Color(
                                                                              0xFFFF9EDE)
                                                                        ],
                                                                        stops: [
                                                                          0.0,
                                                                          1.0
                                                                        ],
                                                                        begin: AlignmentDirectional(
                                                                            1.0,
                                                                            0.17),
                                                                        end: AlignmentDirectional(
                                                                            -1.0,
                                                                            -0.17),
                                                                      ),
                                                                      borderRadius:
                                                                          BorderRadius.circular(
                                                                              11.0),
                                                                      border:
                                                                          Border
                                                                              .all(
                                                                        color: Colors
                                                                            .transparent,
                                                                        width:
                                                                            1.0,
                                                                      ),
                                                                    ),
                                                                    child:
                                                                        Visibility(
                                                                      visible: valueOrDefault<
                                                                              bool>(
                                                                          currentUserDocument
                                                                              ?.onWaitingList,
                                                                          false),
                                                                      child:
                                                                          Align(
                                                                        alignment: const AlignmentDirectional(
                                                                            0.0,
                                                                            0.0),
                                                                        child:
                                                                            Padding(
                                                                          padding: const EdgeInsetsDirectional.fromSTEB(
                                                                              1.0,
                                                                              1.0,
                                                                              1.0,
                                                                              1.0),
                                                                          child:
                                                                              Container(
                                                                            width:
                                                                                MediaQuery.sizeOf(context).width * 1.0,
                                                                            height:
                                                                                MediaQuery.sizeOf(context).height * 1.0,
                                                                            decoration:
                                                                                BoxDecoration(
                                                                              color: FlutterFlowTheme.of(context).info,
                                                                              borderRadius: BorderRadius.circular(11.0),
                                                                              border: Border.all(
                                                                                color: Colors.transparent,
                                                                                width: 1.0,
                                                                              ),
                                                                            ),
                                                                            alignment:
                                                                                const AlignmentDirectional(0.0, 0.0),
                                                                            child:
                                                                                Padding(
                                                                              padding: const EdgeInsetsDirectional.fromSTEB(20.0, 20.0, 20.0, 20.0),
                                                                              child: SingleChildScrollView(
                                                                                child: Column(
                                                                                  mainAxisSize: MainAxisSize.max,
                                                                                  mainAxisAlignment: MainAxisAlignment.center,
                                                                                  children: [
                                                                                    Text(
                                                                                      getRemoteConfigString('waitinglist_prelaunch_p2'),
                                                                                      textAlign: TextAlign.center,
                                                                                      style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                            fontFamily: 'BT Beau Sans',
                                                                                            color: FlutterFlowTheme.of(context).secondaryText,
                                                                                            fontSize: 14.0,
                                                                                            letterSpacing: 0.0,
                                                                                            fontWeight: FontWeight.normal,
                                                                                            useGoogleFonts: false,
                                                                                          ),
                                                                                    ),
                                                                                  ],
                                                                                ),
                                                                              ),
                                                                            ),
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ),
                                                            ],
                                                          ),
                                                          Stack(
                                                            children: [
                                                              if (valueOrDefault<
                                                                      bool>(
                                                                  currentUserDocument
                                                                      ?.onWaitingList,
                                                                  false))
                                                                Align(
                                                                  alignment:
                                                                      const AlignmentDirectional(
                                                                          0.0,
                                                                          0.0),
                                                                  child:
                                                                      Container(
                                                                    width: MediaQuery.sizeOf(context)
                                                                            .width *
                                                                        1.0,
                                                                    decoration:
                                                                        BoxDecoration(
                                                                      gradient:
                                                                          const LinearGradient(
                                                                        colors: [
                                                                          Color(
                                                                              0xFF91B2FB),
                                                                          Color(
                                                                              0xFFFF9EDE)
                                                                        ],
                                                                        stops: [
                                                                          0.0,
                                                                          1.0
                                                                        ],
                                                                        begin: AlignmentDirectional(
                                                                            1.0,
                                                                            0.17),
                                                                        end: AlignmentDirectional(
                                                                            -1.0,
                                                                            -0.17),
                                                                      ),
                                                                      borderRadius:
                                                                          BorderRadius.circular(
                                                                              11.0),
                                                                      border:
                                                                          Border
                                                                              .all(
                                                                        color: Colors
                                                                            .transparent,
                                                                        width:
                                                                            1.0,
                                                                      ),
                                                                    ),
                                                                    child:
                                                                        Visibility(
                                                                      visible: valueOrDefault<
                                                                              bool>(
                                                                          currentUserDocument
                                                                              ?.onWaitingList,
                                                                          false),
                                                                      child:
                                                                          Align(
                                                                        alignment: const AlignmentDirectional(
                                                                            0.0,
                                                                            0.0),
                                                                        child:
                                                                            Padding(
                                                                          padding: const EdgeInsetsDirectional.fromSTEB(
                                                                              1.0,
                                                                              1.0,
                                                                              1.0,
                                                                              1.0),
                                                                          child:
                                                                              Container(
                                                                            width:
                                                                                MediaQuery.sizeOf(context).width * 1.0,
                                                                            height:
                                                                                MediaQuery.sizeOf(context).height * 1.0,
                                                                            decoration:
                                                                                BoxDecoration(
                                                                              color: FlutterFlowTheme.of(context).info,
                                                                              borderRadius: BorderRadius.circular(11.0),
                                                                              border: Border.all(
                                                                                color: Colors.transparent,
                                                                                width: 1.0,
                                                                              ),
                                                                            ),
                                                                            alignment:
                                                                                const AlignmentDirectional(0.0, 0.0),
                                                                            child:
                                                                                Padding(
                                                                              padding: const EdgeInsetsDirectional.fromSTEB(20.0, 20.0, 20.0, 20.0),
                                                                              child: SingleChildScrollView(
                                                                                child: Column(
                                                                                  mainAxisSize: MainAxisSize.max,
                                                                                  mainAxisAlignment: MainAxisAlignment.center,
                                                                                  children: [
                                                                                    Text(
                                                                                      getRemoteConfigString('waitinglist_p3'),
                                                                                      textAlign: TextAlign.center,
                                                                                      style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                                            fontFamily: 'BT Beau Sans',
                                                                                            color: FlutterFlowTheme.of(context).secondaryText,
                                                                                            fontSize: 14.0,
                                                                                            letterSpacing: 0.0,
                                                                                            fontWeight: FontWeight.normal,
                                                                                            useGoogleFonts: false,
                                                                                          ),
                                                                                    ),
                                                                                  ],
                                                                                ),
                                                                              ),
                                                                            ),
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ),
                                                            ],
                                                          ),
                                                        ],
                                                        carouselController: _model
                                                                .carouselController2 ??=
                                                            CarouselController(),
                                                        options:
                                                            CarouselOptions(
                                                          initialPage: 0,
                                                          viewportFraction: 1.0,
                                                          disableCenter: true,
                                                          enlargeCenterPage:
                                                              true,
                                                          enlargeFactor: 1.0,
                                                          enableInfiniteScroll:
                                                              true,
                                                          scrollDirection:
                                                              Axis.horizontal,
                                                          autoPlay: true,
                                                          autoPlayAnimationDuration:
                                                              const Duration(
                                                                  milliseconds:
                                                                      800),
                                                          autoPlayInterval:
                                                              const Duration(
                                                                  milliseconds:
                                                                      (800 +
                                                                          9000)),
                                                          autoPlayCurve:
                                                              Curves.linear,
                                                          pauseAutoPlayInFiniteScroll:
                                                              true,
                                                          onPageChanged: (index,
                                                                  _) =>
                                                              _model.carouselCurrentIndex2 =
                                                                  index,
                                                        ),
                                                      ),
                                                    );
                                                  }
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      );
                    }
                  },
                );
              },
            ),
          ),
        ),
      ),
      ),
    );
  }
}
