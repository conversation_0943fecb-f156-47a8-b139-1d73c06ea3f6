import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/info_sheet_scrollable_multi/info_sheet_scrollable_multi_widget.dart';
import '/general/skip_button_grey_async/skip_button_grey_async_widget.dart';
import '/setup_p2/contacts/contact_sheet/contact_sheet_widget.dart';
import '/custom_code/actions/index.dart' as actions;
import '/flutter_flow/custom_functions.dart' as functions;
import '/flutter_flow/permissions_util.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'contact_blocking_entry_model.dart';
export 'contact_blocking_entry_model.dart';

class ContactBlockingEntryWidget extends StatefulWidget {
  const ContactBlockingEntryWidget({super.key});

  @override
  State<ContactBlockingEntryWidget> createState() =>
      _ContactBlockingEntryWidgetState();
}

class _ContactBlockingEntryWidgetState
    extends State<ContactBlockingEntryWidget> {
  late ContactBlockingEntryModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => ContactBlockingEntryModel());
    logFirebaseEvent('screen_view', parameters: {'screen_name': 'ContactBlockingEntry'});
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return GestureDetector(
      onTap: () => _model.unfocusNode.canRequestFocus
          ? FocusScope.of(context).requestFocus(_model.unfocusNode)
          : FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        appBar: AppBar(
          backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
          automaticallyImplyLeading: false,
          title: Stack(
            alignment: const AlignmentDirectional(1.0, 0.0),
            children: [
              Align(
                alignment: const AlignmentDirectional(1.0, 0.0),
                child: Container(
                  height: 30.0,
                  decoration: const BoxDecoration(),
                  child: wrapWithModel(
                    model: _model.skipButtonGreyAsyncModel,
                    updateCallback: () => setState(() {}),
                    child: SkipButtonGreyAsyncWidget(
                      action: () async {
                        await currentUserReference!.update({
                          ...createUsersRecordData(
                            paused: false,
                            incognito: false,
                            likesScore: 0,
                            randomIndex: functions.getRandomNumber(),
                            latestNPSprovided: getCurrentTimestamp,
                          ),
                          ...mapToFirestore(
                            {
                              'blockedContactNumberOnly':
                                  FFAppState().blockedClientNumbersOnly,
                              'blockedContacts':
                                  getContactForBlockingListFirestoreData(
                                FFAppState().blockedContacts,
                              ),
                            },
                          ),
                        });
                        try {
                          final result = await FirebaseFunctions.instanceFor(
                                  region: 'europe-west1')
                              .httpsCallable('generateMatchesAdvanced')
                              .call({
                            "append": false,
                             "smoothSwiping": true,
                          });
                          _model.matches1 =
                              GenerateMatchesCloudFunctionCallResponse(
                            succeeded: true,
                          );
                        } on FirebaseFunctionsException catch (error) {
                          _model.matches1 =
                              GenerateMatchesCloudFunctionCallResponse(
                            errorCode: error.code,
                            succeeded: false,
                          );
                        }

                        context.goNamed('Discovery');

                        setState(() {});
                      },
                    ),
                  ),
                ),
              ),
            ],
          ),
          actions: const [],
          centerTitle: true,
          elevation: 0.0,
        ),
        body: SafeArea(
          top: true,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(25.0, 0.0, 50.0, 0.0),
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Text(
                      'Is there someone you would like to avoid?',
                      style: FlutterFlowTheme.of(context).bodyMedium.override(
                            fontFamily: 'BT Beau Sans',
                            fontSize: 30.0,
                            fontWeight: FontWeight.bold,
                            useGoogleFonts: false,
                            lineHeight: 1.2,
                          ),
                    ),
                    Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(0.0, 19.0, 0.0, 0.0),
                      child: Text(
                        'By providing your contacts we can hide you from people with information resembling what you have provided.',
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'BT Beau Sans',
                              fontSize: 16.0,
                              fontWeight: FontWeight.normal,
                              useGoogleFonts: false,
                            ),
                      ),
                    ),
                    Align(
                      alignment: const AlignmentDirectional(-1.0, 0.0),
                      child: Padding(
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(0.0, 4.0, 0.0, 0.0),
                        child: InkWell(
                          splashColor: Colors.transparent,
                          focusColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          onTap: () async {
                            await showModalBottomSheet(
                              isScrollControlled: true,
                              backgroundColor: Colors.transparent,
                              enableDrag: false,
                              context: context,
                              builder: (context) {
                                return GestureDetector(
                                  onTap: () =>
                                      _model.unfocusNode.canRequestFocus
                                          ? FocusScope.of(context)
                                              .requestFocus(_model.unfocusNode)
                                          : FocusScope.of(context).unfocus(),
                                  child: Padding(
                                    padding: MediaQuery.viewInsetsOf(context),
                                    child: InfoSheetScrollableMultiWidget(
                                      title: getRemoteConfigString(
                                          'phone_blocking_info_title'),
                                      subtitle1: getRemoteConfigString(
                                          'phone_blocking_info_subtitle_1'),
                                      subbody1: getRemoteConfigString(
                                          'phone_blocking_info_subbody_1'),
                                      subtitle2: getRemoteConfigString(
                                          'phone_blocking_info_subtitle_2'),
                                      subbody2: getRemoteConfigString(
                                          'phone_blocking_info_subbody_2'),
                                    ),
                                  ),
                                );
                              },
                            ).then((value) => safeSetState(() {}));
                          },
                          child: Text(
                            'This is how we will process your information',
                            style: FlutterFlowTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'BT Beau Sans',
                                  color: const Color(0xFF0E6BD5),
                                  letterSpacing: 0.0,
                                  fontWeight: FontWeight.w600,
                                  decoration: TextDecoration.underline,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(),
              Builder(
                builder: (context) => Padding(
                  padding:
                      const EdgeInsetsDirectional.fromSTEB(25.0, 0.0, 25.0, 64.0),
                  child: wrapWithModel(
                    model: _model.gradientButtonModel,
                    updateCallback: () => setState(() {}),
                    updateOnChange: true,
                    child: GradientButtonWidget(
                      title: 'Import Contacts',
                      action: () async {
                        await requestPermission(contactsPermission);
                        if (await getPermissionStatus(contactsPermission)) {
                          _model.contacts =
                              await actions.fetchAndCompareContacts();
                          await showModalBottomSheet(
                            isScrollControlled: true,
                            backgroundColor: Colors.transparent,
                            enableDrag: false,
                            context: context,
                            builder: (context) {
                              return GestureDetector(
                                onTap: () => _model.unfocusNode.canRequestFocus
                                    ? FocusScope.of(context)
                                        .requestFocus(_model.unfocusNode)
                                    : FocusScope.of(context).unfocus(),
                                child: Padding(
                                  padding: MediaQuery.viewInsetsOf(context),
                                  child: SizedBox(
                                    height:
                                        MediaQuery.sizeOf(context).height * 0.9,
                                    child: ContactSheetWidget(
                                      contacts: _model.contacts!
                                          .sortedList((e) => e.name),
                                      callback: () async {
                                        await currentUserReference!.update({
                                          ...createUsersRecordData(
                                            paused: false,
                                            incognito: false,
                                            likesScore: 0,
                                            randomIndex:
                                                functions.getRandomNumber(),
                                            latestNPSprovided:
                                                getCurrentTimestamp,
                                          ),
                                          ...mapToFirestore(
                                            {
                                              'blockedContactNumberOnly':
                                                  FFAppState()
                                                      .blockedClientNumbersOnly,
                                              'blockedContacts':
                                                  getContactForBlockingListFirestoreData(
                                                FFAppState().blockedContacts,
                                              ),
                                            },
                                          ),
                                        });
                                        try {
                                          final result = await FirebaseFunctions
                                                  .instanceFor(
                                                      region: 'europe-west1')
                                              .httpsCallable('generateMatchesAdvanced')
                                              .call({
                                            "append": false,
                                             "smoothSwiping": true,
                                          });
                                          _model.cloudFunctiont6yz =
                                              GenerateMatchesCloudFunctionCallResponse(
                                            succeeded: true,
                                          );
                                        } on FirebaseFunctionsException catch (error) {
                                          _model.cloudFunctiont6yz =
                                              GenerateMatchesCloudFunctionCallResponse(
                                            errorCode: error.code,
                                            succeeded: false,
                                          );
                                        }

                                        context.goNamed('Discovery');
                                      },
                                    ),
                                  ),
                                ),
                              );
                            },
                          ).then((value) => safeSetState(() {}));
                        } else {
                          await showDialog(
                            context: context,
                            builder: (dialogContext) {
                              return Dialog(
                                elevation: 0,
                                insetPadding: EdgeInsets.zero,
                                backgroundColor: Colors.transparent,
                                alignment: const AlignmentDirectional(0.0, 0.0)
                                    .resolve(Directionality.of(context)),
                                child: GestureDetector(
                                  onTap: () =>
                                      _model.unfocusNode.canRequestFocus
                                          ? FocusScope.of(context)
                                              .requestFocus(_model.unfocusNode)
                                          : FocusScope.of(context).unfocus(),
                                  child: const GeneralPopupWidget(
                                    alertTitle: 'Permission needed',
                                    alertText:
                                        'To block contacts, you first need to grant Chyrpe permission to access them. Please try it again.',
                                  ),
                                ),
                              );
                            },
                          ).then((value) => setState(() {}));
                        }

                        setState(() {});
                      },
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
