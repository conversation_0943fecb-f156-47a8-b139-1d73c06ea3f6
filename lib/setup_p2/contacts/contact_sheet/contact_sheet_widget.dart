import '/backend/backend.dart';
import '/backend/schema/structs/index.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:text_search/text_search.dart';
import 'contact_sheet_model.dart';
export 'contact_sheet_model.dart';

class ContactSheetWidget extends StatefulWidget {
  const ContactSheetWidget({
    super.key,
    required this.contacts,
    required this.callback,
  });

  final List<ContactForBlockingStruct>? contacts;
  final Future Function()? callback;

  @override
  State<ContactSheetWidget> createState() => _ContactSheetWidgetState();
}

class _ContactSheetWidgetState extends State<ContactSheetWidget> {
  late ContactSheetModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => ContactSheetModel());
    logFirebaseEvent('screen_view', parameters: {'screen_name': 'ContactBlockingSheet'});

    _model.textController ??= TextEditingController();
    _model.textFieldFocusNode ??= FocusNode();
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return Container(
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).primaryBackground,
        boxShadow: const [
          BoxShadow(
            blurRadius: 5.0,
            color: Color(0x3B1D2429),
            offset: Offset(0.0, -3.0),
          )
        ],
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(0.0),
          bottomRight: Radius.circular(0.0),
          topLeft: Radius.circular(0.0),
          topRight: Radius.circular(0.0),
        ),
      ),
      child: Align(
        alignment: const AlignmentDirectional(0.0, 1.0),
        child: SafeArea(
          child: Stack(
            alignment: const AlignmentDirectional(0.0, 1.0),
            children: [
              Align(
                alignment: const AlignmentDirectional(0.0, 0.0),
                child: Stack(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 5.0, 0.0, 0.0),
                            child: Stack(
                              children: [
                                Align(
                                  alignment: const AlignmentDirectional(0.0, 0.0),
                                  child: Text(
                                    'Select who to block',
                                    style: FlutterFlowTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'BT Beau Sans',
                                          fontSize: 16.0,
                                          fontWeight: FontWeight.bold,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ),
                                Align(
                                  alignment: const AlignmentDirectional(-1.0, 0.0),
                                  child: InkWell(
                                    splashColor: Colors.transparent,
                                    focusColor: Colors.transparent,
                                    hoverColor: Colors.transparent,
                                    highlightColor: Colors.transparent,
                                    onTap: () async {
                                      Navigator.pop(context);
                                    },
                                    child: Icon(
                                      Icons.close,
                                      color: FlutterFlowTheme.of(context)
                                          .secondaryText,
                                      size: 24.0,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SingleChildScrollView(
                            primary: false,
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      0.0, 26.0, 0.0, 0.0),
                                  child: Text(
                                    'An easy way to stay discrete in \nyour own social circle.',
                                    textAlign: TextAlign.center,
                                    style: FlutterFlowTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'BT Beau Sans',
                                          fontWeight: FontWeight.normal,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      0.0, 47.0, 0.0, 12.0),
                                  child: Text(
                                    'Contacts',
                                    textAlign: TextAlign.center,
                                    style: FlutterFlowTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'BT Beau Sans',
                                          fontWeight: FontWeight.bold,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ),
                                Divider(
                                  thickness: 1.0,
                                  color:
                                      FlutterFlowTheme.of(context).secondaryText,
                                ),
                                Column(
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    Padding(
                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                          0.0, 17.0, 0.0, 0.0),
                                      child: Column(
                                        mainAxisSize: MainAxisSize.max,
                                        children: [
                                          Container(
                                            height: 42.0,
                                            decoration: BoxDecoration(
                                              color: const Color(0xFFD8DBDF),
                                              borderRadius:
                                                  BorderRadius.circular(4.0),
                                            ),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                const Padding(
                                                  padding: EdgeInsetsDirectional
                                                      .fromSTEB(
                                                          10.0, 0.0, 0.0, 0.0),
                                                  child: Icon(
                                                    Icons.search_outlined,
                                                    color: Color(0xFF747E90),
                                                    size: 28.0,
                                                  ),
                                                ),
                                                Expanded(
                                                  child: Align(
                                                    alignment:
                                                        const AlignmentDirectional(
                                                            0.0, 0.0),
                                                    child: Padding(
                                                      padding:
                                                          const EdgeInsetsDirectional
                                                              .fromSTEB(8.0, 0.0,
                                                                  8.0, 0.0),
                                                      child: TextFormField(
                                                        controller:
                                                            _model.textController,
                                                        focusNode: _model
                                                            .textFieldFocusNode,
                                                        onChanged: (_) =>
                                                            EasyDebounce.debounce(
                                                          '_model.textController',
                                                          const Duration(
                                                              milliseconds: 1),
                                                          () async {
                                                            safeSetState(() {
                                                              _model
                                                                  .simpleSearchResults = TextSearch(widget
                                                                      .contacts!
                                                                      .map((e) =>
                                                                          e.name)
                                                                      .toList()
                                                                      .map((str) =>
                                                                          TextSearchItem.fromTerms(
                                                                              str,
                                                                              [
                                                                                str
                                                                              ]))
                                                                      .toList())
                                                                  .search(_model
                                                                      .textController
                                                                      .text)
                                                                  .map((r) =>
                                                                      r.object)
                                                                  .toList();
                                                            });
                                                            setState(() {
                                                              _model.searchActive =
                                                                  _model
                                                                          .textController
                                                                          .text.isNotEmpty;
                                                            });
                                                          },
                                                        ),
                                                        textCapitalization:
                                                            TextCapitalization
                                                                .none,
                                                        obscureText: false,
                                                        decoration:
                                                            InputDecoration(
                                                          labelText:
                                                              'Search for a name',
                                                          labelStyle:
                                                              FlutterFlowTheme.of(
                                                                      context)
                                                                  .labelMedium
                                                                  .override(
                                                                    fontFamily:
                                                                        'BT Beau Sans',
                                                                    fontSize:
                                                                        14.0,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w500,
                                                                    useGoogleFonts:
                                                                        false,
                                                                  ),
                                                          hintStyle:
                                                              FlutterFlowTheme.of(
                                                                      context)
                                                                  .labelMedium,
                                                          enabledBorder:
                                                              InputBorder.none,
                                                          focusedBorder:
                                                              InputBorder.none,
                                                          errorBorder:
                                                              InputBorder.none,
                                                          focusedErrorBorder:
                                                              InputBorder.none,
                                                        ),
                                                        style:
                                                            FlutterFlowTheme.of(
                                                                    context)
                                                                .bodyMedium,
                                                        textAlign:
                                                            TextAlign.start,
                                                        maxLength: 100,
                                                        maxLengthEnforcement:
                                                            MaxLengthEnforcement
                                                                .enforced,
                                                        buildCounter: (context,
                                                                {required currentLength,
                                                                required isFocused,
                                                                maxLength}) =>
                                                            null,
                                                        validator: _model
                                                            .textControllerValidator
                                                            .asValidator(context),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          Padding(
                                            padding:
                                                const EdgeInsetsDirectional.fromSTEB(
                                                    0.0, 11.0, 0.0, 15.0),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.max,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                Theme(
                                                  data: ThemeData(
                                                    checkboxTheme:
                                                        CheckboxThemeData(
                                                      visualDensity:
                                                          VisualDensity.compact,
                                                      materialTapTargetSize:
                                                          MaterialTapTargetSize
                                                              .shrinkWrap,
                                                      shape:
                                                          RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius.circular(
                                                                4.0),
                                                      ),
                                                    ),
                                                    unselectedWidgetColor:
                                                        FlutterFlowTheme.of(
                                                                context)
                                                            .secondaryText,
                                                  ),
                                                  child: Checkbox(
                                                    value: _model
                                                        .checkboxValue ??= false,
                                                    onChanged: (newValue) async {
                                                      setState(() =>
                                                          _model.checkboxValue =
                                                              newValue!);
                                                      if (newValue!) {
                                                        setState(() {
                                                          FFAppState()
                                                                  .blockedContacts =
                                                              widget.contacts!
                                                                  .toList()
                                                                  .cast<
                                                                      ContactForBlockingStruct>();
                                                          FFAppState()
                                                                  .blockedClientNumbersOnly =
                                                              widget.contacts!
                                                                  .map((e) => e
                                                                      .clientNumber)
                                                                  .toList()
                                                                  .cast<String>();
                                                        });
                                                      } else {
                                                        setState(() {
                                                          FFAppState()
                                                              .blockedContacts = [];
                                                          FFAppState()
                                                              .blockedClientNumbersOnly = [];
                                                        });
                                                      }
                                                    },
                                                    activeColor:
                                                        const Color(0xFFAE34E8),
                                                    checkColor:
                                                        FlutterFlowTheme.of(
                                                                context)
                                                            .info,
                                                  ),
                                                ),
                                                Text(
                                                  'Select all',
                                                  style:
                                                      FlutterFlowTheme.of(context)
                                                          .bodyMedium
                                                          .override(
                                                            fontFamily:
                                                                'BT Beau Sans',
                                                            fontWeight:
                                                                FontWeight.normal,
                                                            useGoogleFonts: false,
                                                          ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    SingleChildScrollView(
                                      child: Column(
                                        mainAxisSize: MainAxisSize.max,
                                        children: [
                                          if (!_model.searchActive)
                                            Container(
                                              constraints: BoxConstraints(
                                                maxHeight:
                                                    MediaQuery.sizeOf(context)
                                                            .height *
                                                        0.7,
                                              ),
                                              decoration: const BoxDecoration(),
                                              child: Builder(
                                                builder: (context) {
                                                  final contacts = widget
                                                      .contacts!
                                                      .sortedList((e) => e.name)
                                                      .toList();
                                                  return ListView.builder(
                                                    padding: const EdgeInsets.fromLTRB(
                                                      0,
                                                      0,
                                                      0,
                                                      400.0,
                                                    ),
                                                    primary: false,
                                                    scrollDirection:
                                                        Axis.vertical,
                                                    itemCount: contacts.length,
                                                    itemBuilder:
                                                        (context, contactsIndex) {
                                                      final contactsItem =
                                                          contacts[contactsIndex];
                                                      return Card(
                                                        clipBehavior: Clip
                                                            .antiAliasWithSaveLayer,
                                                        color: FlutterFlowTheme
                                                                .of(context)
                                                            .primaryBackground,
                                                        elevation: 0.0,
                                                        shape:
                                                            RoundedRectangleBorder(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(0.0),
                                                        ),
                                                        child: Column(
                                                          mainAxisSize:
                                                              MainAxisSize.max,
                                                          children: [
                                                            InkWell(
                                                              splashColor: Colors
                                                                  .transparent,
                                                              focusColor: Colors
                                                                  .transparent,
                                                              hoverColor: Colors
                                                                  .transparent,
                                                              highlightColor:
                                                                  Colors
                                                                      .transparent,
                                                              onTap: () async {
                                                                if (FFAppState()
                                                                    .blockedClientNumbersOnly
                                                                    .contains(
                                                                        contactsItem
                                                                            .clientNumber)) {
                                                                  setState(() {
                                                                    FFAppState()
                                                                        .removeFromBlockedContacts(
                                                                            contactsItem);
                                                                    FFAppState().removeFromBlockedClientNumbersOnly(
                                                                        contactsItem
                                                                            .clientNumber);
                                                                  });
                                                                } else {
                                                                  setState(() {
                                                                    FFAppState()
                                                                        .addToBlockedContacts(
                                                                            contactsItem);
                                                                    FFAppState().addToBlockedClientNumbersOnly(
                                                                        contactsItem
                                                                            .clientNumber);
                                                                  });
                                                                }
                                                              },
                                                              child: Row(
                                                                mainAxisSize:
                                                                    MainAxisSize
                                                                        .max,
                                                                mainAxisAlignment:
                                                                    MainAxisAlignment
                                                                        .spaceBetween,
                                                                children: [
                                                                  Column(
                                                                    mainAxisSize:
                                                                        MainAxisSize
                                                                            .max,
                                                                    crossAxisAlignment:
                                                                        CrossAxisAlignment
                                                                            .start,
                                                                    children: [
                                                                      Padding(
                                                                        padding: const EdgeInsetsDirectional.fromSTEB(
                                                                            0.0,
                                                                            21.0,
                                                                            0.0,
                                                                            0.0),
                                                                        child:
                                                                            Text(
                                                                          contactsItem
                                                                              .name,
                                                                          style: FlutterFlowTheme.of(context)
                                                                              .bodyMedium
                                                                              .override(
                                                                                fontFamily: 'BT Beau Sans',
                                                                                fontSize: 15.0,
                                                                                fontWeight: FontWeight.bold,
                                                                                useGoogleFonts: false,
                                                                              ),
                                                                        ),
                                                                      ),
                                                                      Padding(
                                                                        padding: const EdgeInsetsDirectional.fromSTEB(
                                                                            0.0,
                                                                            13.0,
                                                                            0.0,
                                                                            21.0),
                                                                        child:
                                                                            Text(
                                                                          contactsItem
                                                                              .clientNumber,
                                                                          style: FlutterFlowTheme.of(context)
                                                                              .bodyMedium
                                                                              .override(
                                                                                fontFamily: 'BT Beau Sans',
                                                                                fontSize: 12.0,
                                                                                fontWeight: FontWeight.normal,
                                                                                useGoogleFonts: false,
                                                                              ),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                  Align(
                                                                    alignment:
                                                                        const AlignmentDirectional(
                                                                            1.0,
                                                                            0.0),
                                                                    child: Column(
                                                                      mainAxisSize:
                                                                          MainAxisSize
                                                                              .max,
                                                                      children: [
                                                                        if (FFAppState()
                                                                            .blockedClientNumbersOnly
                                                                            .contains(
                                                                                contactsItem.clientNumber))
                                                                          const Icon(
                                                                            Icons
                                                                                .check_rounded,
                                                                            color:
                                                                                Color(0xFF9200D6),
                                                                            size:
                                                                                24.0,
                                                                          ),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                            Divider(
                                                              thickness: 1.0,
                                                              color: FlutterFlowTheme
                                                                      .of(context)
                                                                  .secondaryText,
                                                            ),
                                                          ],
                                                        ),
                                                      );
                                                    },
                                                  );
                                                },
                                              ),
                                            ),
                                          if (_model.searchActive)
                                            Container(
                                              constraints: BoxConstraints(
                                                maxHeight:
                                                    MediaQuery.sizeOf(context)
                                                            .height *
                                                        0.7,
                                              ),
                                              decoration: const BoxDecoration(),
                                              child: Builder(
                                                builder: (context) {
                                                  final contactsSearch = widget
                                                      .contacts!
                                                      .where((e) => _model
                                                          .simpleSearchResults
                                                          .contains(e.name))
                                                      .toList();
                                                  return ListView.builder(
                                                    padding: const EdgeInsets.fromLTRB(
                                                      0,
                                                      0,
                                                      0,
                                                      400.0,
                                                    ),
                                                    primary: false,
                                                    shrinkWrap: true,
                                                    scrollDirection:
                                                        Axis.vertical,
                                                    itemCount:
                                                        contactsSearch.length,
                                                    itemBuilder: (context,
                                                        contactsSearchIndex) {
                                                      final contactsSearchItem =
                                                          contactsSearch[
                                                              contactsSearchIndex];
                                                      return Card(
                                                        clipBehavior: Clip
                                                            .antiAliasWithSaveLayer,
                                                        color: FlutterFlowTheme
                                                                .of(context)
                                                            .primaryBackground,
                                                        elevation: 0.0,
                                                        shape:
                                                            RoundedRectangleBorder(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(0.0),
                                                        ),
                                                        child: Column(
                                                          mainAxisSize:
                                                              MainAxisSize.max,
                                                          children: [
                                                            InkWell(
                                                              splashColor: Colors
                                                                  .transparent,
                                                              focusColor: Colors
                                                                  .transparent,
                                                              hoverColor: Colors
                                                                  .transparent,
                                                              highlightColor:
                                                                  Colors
                                                                      .transparent,
                                                              onTap: () async {
                                                                if (FFAppState()
                                                                    .blockedClientNumbersOnly
                                                                    .contains(
                                                                        contactsSearchItem
                                                                            .clientNumber)) {
                                                                  setState(() {
                                                                    FFAppState()
                                                                        .removeFromBlockedContacts(
                                                                            contactsSearchItem);
                                                                    FFAppState().removeFromBlockedClientNumbersOnly(
                                                                        contactsSearchItem
                                                                            .clientNumber);
                                                                  });
                                                                } else {
                                                                  setState(() {
                                                                    FFAppState()
                                                                        .addToBlockedContacts(
                                                                            contactsSearchItem);
                                                                    FFAppState().addToBlockedClientNumbersOnly(
                                                                        contactsSearchItem
                                                                            .clientNumber);
                                                                  });
                                                                }
                                                              },
                                                              child: Row(
                                                                mainAxisSize:
                                                                    MainAxisSize
                                                                        .max,
                                                                mainAxisAlignment:
                                                                    MainAxisAlignment
                                                                        .spaceBetween,
                                                                children: [
                                                                  Column(
                                                                    mainAxisSize:
                                                                        MainAxisSize
                                                                            .max,
                                                                    crossAxisAlignment:
                                                                        CrossAxisAlignment
                                                                            .start,
                                                                    children: [
                                                                      Padding(
                                                                        padding: const EdgeInsetsDirectional.fromSTEB(
                                                                            0.0,
                                                                            21.0,
                                                                            0.0,
                                                                            0.0),
                                                                        child:
                                                                            Text(
                                                                          contactsSearchItem
                                                                              .name,
                                                                          style: FlutterFlowTheme.of(context)
                                                                              .bodyMedium
                                                                              .override(
                                                                                fontFamily: 'BT Beau Sans',
                                                                                fontSize: 15.0,
                                                                                fontWeight: FontWeight.bold,
                                                                                useGoogleFonts: false,
                                                                              ),
                                                                        ),
                                                                      ),
                                                                      Padding(
                                                                        padding: const EdgeInsetsDirectional.fromSTEB(
                                                                            0.0,
                                                                            13.0,
                                                                            0.0,
                                                                            21.0),
                                                                        child:
                                                                            Text(
                                                                          contactsSearchItem
                                                                              .clientNumber,
                                                                          style: FlutterFlowTheme.of(context)
                                                                              .bodyMedium
                                                                              .override(
                                                                                fontFamily: 'BT Beau Sans',
                                                                                fontSize: 12.0,
                                                                                fontWeight: FontWeight.normal,
                                                                                useGoogleFonts: false,
                                                                              ),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                  Align(
                                                                    alignment:
                                                                        const AlignmentDirectional(
                                                                            1.0,
                                                                            0.0),
                                                                    child: Column(
                                                                      mainAxisSize:
                                                                          MainAxisSize
                                                                              .max,
                                                                      children: [
                                                                        if (FFAppState()
                                                                            .blockedClientNumbersOnly
                                                                            .contains(
                                                                                contactsSearchItem.clientNumber))
                                                                          const Icon(
                                                                            Icons
                                                                                .check_rounded,
                                                                            color:
                                                                                Color(0xFF9200D6),
                                                                            size:
                                                                                24.0,
                                                                          ),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                            Divider(
                                                              thickness: 1.0,
                                                              color: FlutterFlowTheme
                                                                      .of(context)
                                                                  .secondaryText,
                                                            ),
                                                          ],
                                                        ),
                                                      );
                                                    },
                                                  );
                                                },
                                              ),
                                            ),
                                        ].addToEnd(const SizedBox(height: 400.0)),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(17.0, 0.0, 17.0, 27.0),
                child: wrapWithModel(
                  model: _model.gradientButtonModel,
                  updateCallback: () => setState(() {}),
                  child: GradientButtonWidget(
                    title:
                        'Block ${FFAppState().blockedClientNumbersOnly.length.toString()} Contacts',
                    action: () async {
                      await widget.callback?.call();
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
