import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:provider/provider.dart';
import 'change_role_model.dart';
export 'change_role_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';

class ChangeRoleWidget extends StatefulWidget {
  const ChangeRoleWidget({
    super.key,
    required this.hiddenInProfile,
  });

  final bool? hiddenInProfile;

  @override
  State<ChangeRoleWidget> createState() => _ChangeRoleWidgetState();
}

class _ChangeRoleWidgetState extends State<ChangeRoleWidget> {
  late ChangeRoleModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => ChangeRoleModel());

    // On component load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      if (valueOrDefault<bool>(currentUserDocument?.alternativeG, false)) {
        // Already for next page: get the right options for roles
        _model.alternativeRoles = await queryOptionsForSelectorsRecordOnce(
          queryBuilder: (optionsForSelectorsRecord) =>
              optionsForSelectorsRecord.where(
            'name',
            isEqualTo: 'Alternative Roles',
          ),
          singleRecord: true,
        ).then((s) => s.firstOrNull);
        setState(() {
          _model.kinks =
              _model.alternativeRoles!.options.toList().cast<String>();
        });
      } else {
        if (currentUserDocument?.gender == Gender.Female) {
          // Already for next page: get the right options for roles
          _model.femaleRoles = await queryOptionsForSelectorsRecordOnce(
            queryBuilder: (optionsForSelectorsRecord) =>
                optionsForSelectorsRecord.where(
              'name',
              isEqualTo: 'Female Roles',
            ),
            singleRecord: true,
          ).then((s) => s.firstOrNull);
          setState(() {
            _model.kinks = _model.femaleRoles!.options.toList().cast<String>();
          });
        } else {
          // Already for next page: get the right options for roles
          _model.maleRoles = await queryOptionsForSelectorsRecordOnce(
            queryBuilder: (optionsForSelectorsRecord) =>
                optionsForSelectorsRecord.where(
              'name',
              isEqualTo: 'Male Roles',
            ),
            singleRecord: true,
          ).then((s) => s.firstOrNull);
          setState(() {
            _model.kinks = _model.maleRoles!.options.toList().cast<String>();
          });
        }
      }

      setState(() {
        _model.selectedKink = valueOrDefault(currentUserDocument?.position, '');
      });
    });
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return Container(
      width: MediaQuery.sizeOf(context).width * 1.0,
      height: MediaQuery.sizeOf(context).height * 1.0,
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).primaryBackground,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(0.0),
          bottomRight: Radius.circular(0.0),
          topLeft: Radius.circular(20.0),
          topRight: Radius.circular(20.0),
        ),
      ),
      child: SafeArea(
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(23.0, 0.0, 23.0, 0.0),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Padding(
                    padding: const EdgeInsetsDirectional.fromSTEB(0.0, 80.0, 0.0, 25.0),
                    child: Text(
                      'What are you?',
                      style: FlutterFlowTheme.of(context).bodyMedium.override(
                            fontFamily: 'BT Beau Sans',
                            fontSize: 32.0,
                            fontWeight: FontWeight.bold,
                            useGoogleFonts: false,
                            lineHeight: 1.29,
                          ),
                    ),
                  ),
                  Flexible(
                    child: Builder(
                      builder: (context) {
                        final options = _model.kinks.toList();
                        return SingleChildScrollView(
                          child: Column(
                            mainAxisSize: MainAxisSize.max,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children:
                                List.generate(options.length, (optionsIndex) {
                              final optionsItem = options[optionsIndex];
                              return Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 0.0, 0.0, 21.0),
                                child: InkWell(
                                  splashColor: Colors.transparent,
                                  focusColor: Colors.transparent,
                                  hoverColor: Colors.transparent,
                                  highlightColor: Colors.transparent,
                                  onTap: () async {
                                    setState(() {
                                      _model.selectedKink = optionsItem;
                                    });
                                  },
                                  child: Material(
                                    color: Colors.transparent,
                                    elevation: 0.0,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(100.0),
                                    ),
                                    child: Container(
                                      height: 52.0,
                                      constraints: const BoxConstraints(
                                        maxWidth: 400.0,
                                      ),
                                      decoration: BoxDecoration(
                                        color: FlutterFlowTheme.of(context)
                                            .primaryBackground,
                                        borderRadius:
                                            BorderRadius.circular(100.0),
                                        border: Border.all(
                                          color:
                                              _model.selectedKink == optionsItem
                                                  ? FlutterFlowTheme.of(context)
                                                      .accent2
                                                  : FlutterFlowTheme.of(context)
                                                      .secondaryText,
                                          width: 2.0,
                                        ),
                                      ),
                                      child: Align(
                                        alignment: const AlignmentDirectional(0.0, 0.0),
                                        child: Text(
                                          optionsItem,
                                          style: FlutterFlowTheme.of(context)
                                              .bodyMedium
                                              .override(
                                                fontFamily: 'BT Beau Sans',
                                                color:
                                                    FlutterFlowTheme.of(context)
                                                        .secondaryText,
                                                fontSize: 19.0,
                                                letterSpacing: 0.0,
                                                fontWeight: FontWeight.bold,
                                                useGoogleFonts: false,
                                              ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            }),
                          ),
                        );
                      },
                    ),
                  ),
                  Align(
                    alignment: const AlignmentDirectional(0.0, 1.0),
                    child: Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(0.0, 30.0, 0.0, 0.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          const Divider(
                            thickness: 1.0,
                            color: Color(0xFFD4D8DE),
                          ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                27.0, 23.0, 27.0, 25.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Theme(
                                  data: ThemeData(
                                    checkboxTheme: CheckboxThemeData(
                                      visualDensity: VisualDensity.compact,
                                      materialTapTargetSize:
                                          MaterialTapTargetSize.shrinkWrap,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(4.0),
                                      ),
                                    ),
                                    unselectedWidgetColor:
                                        FlutterFlowTheme.of(context)
                                            .secondaryText,
                                  ),
                                  child: Checkbox(
                                    value: _model.checkboxValue ??=
                                        widget.hiddenInProfile!,
                                    onChanged: (newValue) async {
                                      setState(
                                          () => _model.checkboxValue = newValue!);
                                    },
                                    activeColor:
                                        FlutterFlowTheme.of(context).accent2,
                                    checkColor: FlutterFlowTheme.of(context).info,
                                  ),
                                ),
                                Text(
                                  'Hide in my profile',
                                  style: FlutterFlowTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        fontSize: 15.0,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                27.0, 0.0, 27.0, 47.0),
                            child: wrapWithModel(
                              model: _model.gradientButtonModel,
                              updateCallback: () => setState(() {}),
                              child: GradientButtonWidget(
                                title: 'Update',
                                action: () async {
                                  // Set role in user
        
                                  analytics.setUserProperties({'Role': _model.selectedKink});
        
                                  await currentUserReference!
                                      .update(createUsersRecordData(
                                    position: _model.selectedKink,
                                  ));
                                  // Depending on choice, set public role
        
                                  await currentUserDocument!.publicProfile!
                                      .update(createPublicProfileRecordData(
                                    publicRole: _model.checkboxValue!
                                        ? ''
                                        : _model.selectedKink,
                                    publicRoleShown: !_model.checkboxValue!,
                                  ));
                                  Navigator.pop(context);
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Align(
              alignment: const AlignmentDirectional(-1.0, -1.0),
              child: Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(18.0, 30.0, 0.0, 0.0),
                child: InkWell(
                  splashColor: Colors.transparent,
                  focusColor: Colors.transparent,
                  hoverColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  onTap: () async {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close_rounded,
                    color: FlutterFlowTheme.of(context).secondaryText,
                    size: 35.0,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
