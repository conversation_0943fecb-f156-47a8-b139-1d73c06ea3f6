import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/custom_code/actions/index.dart' as actions;
import '/flutter_flow/custom_functions.dart' as functions;
import '/flutter_flow/permissions_util.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'location_allowing_entry_model.dart';
export 'location_allowing_entry_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';

class LocationAllowingEntryWidget extends StatefulWidget {
  const LocationAllowingEntryWidget({super.key});

  @override
  State<LocationAllowingEntryWidget> createState() =>
      _LocationAllowingEntryWidgetState();
}

class _LocationAllowingEntryWidgetState
    extends State<LocationAllowingEntryWidget> {
  late LocationAllowingEntryModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();
  LatLng? currentUserLocationValue;

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => LocationAllowingEntryModel());
    logFirebaseEvent('screen_view',
        parameters: {'screen_name': 'LocationAllowingEntry'});
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return FutureBuilder<List<AKeyRecord>>(
      future: queryAKeyRecordOnce(
        queryBuilder: (aKeyRecord) => aKeyRecord.where(
          'name',
          isEqualTo: 'Here',
        ),
        singleRecord: true,
      ),
      builder: (context, snapshot) {
        // Customize what your widget looks like when it's loading.
        if (!snapshot.hasData) {
          return Scaffold(
            backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
            body: Center(
              child: SizedBox(
                width: 50.0,
                height: 50.0,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    FlutterFlowTheme.of(context).accent2,
                  ),
                ),
              ),
            ),
          );
        }
        List<AKeyRecord> locationAllowingEntryAKeyRecordList = snapshot.data!;
        final locationAllowingEntryAKeyRecord =
            locationAllowingEntryAKeyRecordList.isNotEmpty
                ? locationAllowingEntryAKeyRecordList.first
                : null;
        return GestureDetector(
          onTap: () => FocusScope.of(context).unfocus(),
          child: Scaffold(
            key: scaffoldKey,
            backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
            body: SafeArea(
              top: true,
              child: Padding(
                padding:
                    const EdgeInsetsDirectional.fromSTEB(25.0, 0.0, 25.0, 0.0),
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Align(
                      alignment: const AlignmentDirectional(-1.0, -1.0),
                      child: Padding(
                        padding: const EdgeInsetsDirectional.fromSTEB(
                            0.0, 20.0, 0.0, 0.0),
                        child: FlutterFlowIconButton(
                          borderColor: Colors.transparent,
                          borderRadius: 20.0,
                          borderWidth: 1.0,
                          buttonSize: 40.0,
                          icon: FaIcon(
                            FontAwesomeIcons.angleLeft,
                            color: FlutterFlowTheme.of(context).primaryText,
                            size: 24.0,
                          ),
                          onPressed: () async {
                            if (currentUserDocument?.gender == Gender.Female
                                ? (valueOrDefault<bool>(
                                        currentUserDocument?.bGroup, false)
                                    ? functions
                                        .getStringListFromJson(
                                            getRemoteConfigString(
                                                'signup_show_bio_w'))
                                        .contains('bGroup')
                                    : functions
                                        .getStringListFromJson(
                                            getRemoteConfigString(
                                                'signup_show_bio_w'))
                                        .contains('aGroup'))
                                : (valueOrDefault<bool>(
                                        currentUserDocument?.bGroup, false)
                                    ? functions
                                        .getStringListFromJson(
                                            getRemoteConfigString(
                                                'signup_show_bio_m'))
                                        .contains('bGroup')
                                    : functions
                                        .getStringListFromJson(
                                            getRemoteConfigString(
                                                'signup_show_bio_m'))
                                        .contains('aGroup'))) {
                              context.goNamed('BioPrompts');

                              return;
                            } else {
                              context.pushNamed('Images');

                              return;
                            }
                          },
                        ),
                      ),
                    ),
                    Flexible(
                      child: Align(
                        alignment: const AlignmentDirectional(0.0, 0.0),
                        child: SingleChildScrollView(
                          child: Column(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 0.0, 10.0, 20.0),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(8.0),
                                  child: Image.asset(
                                    'assets/images/Location_Icon_Art.png',
                                    width: 300.0,
                                    height: 200.0,
                                    fit: BoxFit.contain,
                                  ),
                                ),
                              ),
                              Text(
                                getRemoteConfigString(
                                    'location_allowing_headline'),
                                textAlign: TextAlign.center,
                                style: FlutterFlowTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'BT Beau Sans',
                                      fontSize: 34.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.bold,
                                      useGoogleFonts: false,
                                    ),
                              ),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 13.0, 0.0, 0.0),
                                child: Text(
                                  getRemoteConfigString(
                                      'location_allowing_explanation'),
                                  textAlign: TextAlign.center,
                                  style: FlutterFlowTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        fontSize: 17.0,
                                        letterSpacing: 0.0,
                                        fontWeight: FontWeight.normal,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    Align(
                      alignment: const AlignmentDirectional(0.0, 1.0),
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Builder(
                              builder: (context) => wrapWithModel(
                                model: _model.gradientButtonModel,
                                updateCallback: () => setState(() {}),
                                child: GradientButtonWidget(
                                  title: getRemoteConfigString(
                                      'location_allowing_button_title'),
                                  action: () async {
                                    currentUserLocationValue =
                                        await getCurrentUserLocation(
                                            defaultLocation:
                                                const LatLng(0.0, 0.0));
                                    await requestPermission(locationPermission);
                                    if (await getPermissionStatus(
                                        locationPermission)) {
                                      await actions.getLocationData(
                                        currentUserLocationValue!,
                                        locationAllowingEntryAKeyRecord!.key,
                                      );

                                      analytics.logEvent(
                                          'Sign Up: Gave Location Permission');
                                      analytics.setUserProperties({
                                        'City(Here)': FFAppState().hereLocation,
                                        "Country (Here)":
                                            valueOrDefault<String>(
                                          FFAppState().regionName,
                                          'Other',
                                        )
                                      });
                                      // Save coordinates to user document
                                      await currentUserReference!
                                          .update(createUsersRecordData(
                                        location: FFAppState().currentLocation,
                                        city: FFAppState().hereLocation,
                                        wlRegion: valueOrDefault<String>(
                                          FFAppState().regionName,
                                          'Other',
                                        ),
                                        nextSignUpStage:
                                            SetUpStage.verification,
                                      ));
                                      // Save city to public profile
                                      await currentUserDocument!.publicProfile!
                                          .update(createPublicProfileRecordData(
                                        city: FFAppState().hereLocation,
                                      ));
                                      if (currentUserDocument
                                              ?.signUpTestingCohort ==
                                          SignUpTestingCohort.Confirmation) {
                                        context.goNamed(
                                            'LastStepConfirmationPage');
                                      } else {
                                        context.goNamed('VerificationPrompt');
                                      }
                                    } else {
                                      await showDialog(
                                        barrierDismissible: false,
                                        context: context,
                                        builder: (dialogContext) {
                                          return Dialog(
                                            elevation: 0,
                                            insetPadding: EdgeInsets.zero,
                                            backgroundColor: Colors.transparent,
                                            alignment:
                                                const AlignmentDirectional(
                                                        0.0, 0.0)
                                                    .resolve(Directionality.of(
                                                        context)),
                                            child: GestureDetector(
                                              onTap: () =>
                                                  FocusScope.of(dialogContext)
                                                      .unfocus(),
                                              child: const GeneralPopupWidget(
                                                alertTitle:
                                                    'Location access needed',
                                                alertText:
                                                    'In order to use chyrpe, you need to grant us access to your location. You can do so in your settings app.',
                                              ),
                                            ),
                                          );
                                        },
                                      );
                                    }
                                  },
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 31.0, 0.0, 20.0),
                              child: InkWell(
                                splashColor: Colors.transparent,
                                focusColor: Colors.transparent,
                                hoverColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                onTap: () async {
                                  analytics.logEvent(
                                      'Sign Up: Accessed Extra Location Information');
                                  context.pushNamed(
                                    'LocationAllowingUsageNotice',
                                    extra: <String, dynamic>{
                                      kTransitionInfoKey: const TransitionInfo(
                                        hasTransition: true,
                                        transitionType:
                                            PageTransitionType.bottomToTop,
                                      ),
                                    },
                                  );
                                },
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      'How is my location used?',
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            fontFamily: 'BT Beau Sans',
                                            fontSize: 18.0,
                                            letterSpacing: 0.0,
                                            fontWeight: FontWeight.bold,
                                            useGoogleFonts: false,
                                          ),
                                    ),
                                    Padding(
                                      padding:
                                          const EdgeInsetsDirectional.fromSTEB(
                                              10.0, 0.0, 0.0, 3.0),
                                      child: FaIcon(
                                        FontAwesomeIcons.chevronDown,
                                        color: FlutterFlowTheme.of(context)
                                            .primaryText,
                                        size: 24.0,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
