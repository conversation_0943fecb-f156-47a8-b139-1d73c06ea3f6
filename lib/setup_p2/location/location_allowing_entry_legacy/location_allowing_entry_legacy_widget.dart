import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/backend/schema/enums/enums.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/custom_code/actions/index.dart' as actions;
import '/flutter_flow/permissions_util.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'location_allowing_entry_legacy_model.dart';
export 'location_allowing_entry_legacy_model.dart';

class LocationAllowingEntryLegacyWidget extends StatefulWidget {
  const LocationAllowingEntryLegacyWidget({super.key});

  @override
  State<LocationAllowingEntryLegacyWidget> createState() =>
      _LocationAllowingEntryLegacyWidgetState();
}

class _LocationAllowingEntryLegacyWidgetState
    extends State<LocationAllowingEntryLegacyWidget> {
  late LocationAllowingEntryLegacyModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();
  LatLng? currentUserLocationValue;

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => LocationAllowingEntryLegacyModel());
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return FutureBuilder<List<AKeyRecord>>(
      future: queryAKeyRecordOnce(
        queryBuilder: (aKeyRecord) => aKeyRecord.where(
          'name',
          isEqualTo: 'Here',
        ),
        singleRecord: true,
      ),
      builder: (context, snapshot) {
        // Customize what your widget looks like when it's loading.
        if (!snapshot.hasData) {
          return Scaffold(
            backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
            body: Center(
              child: SizedBox(
                width: 50.0,
                height: 50.0,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    FlutterFlowTheme.of(context).accent2,
                  ),
                ),
              ),
            ),
          );
        }
        List<AKeyRecord> locationAllowingEntryLegacyAKeyRecordList =
            snapshot.data!;
        final locationAllowingEntryLegacyAKeyRecord =
            locationAllowingEntryLegacyAKeyRecordList.isNotEmpty
                ? locationAllowingEntryLegacyAKeyRecordList.first
                : null;

        return GestureDetector(
          onTap: () => FocusScope.of(context).unfocus(),
          child: Scaffold(
            key: scaffoldKey,
            backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
            body: SafeArea(
              top: true,
              child: Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(25.0, 0.0, 25.0, 0.0),
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: Align(
                        alignment: const AlignmentDirectional(0.0, 0.0),
                        child: SingleChildScrollView(
                          child: Column(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 0.0, 10.0, 20.0),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(8.0),
                                  child: Image.asset(
                                    'assets/images/Location_Icon_Art.png',
                                    width: 300.0,
                                    height: 200.0,
                                    fit: BoxFit.contain,
                                  ),
                                ),
                              ),
                              Text(
                                getRemoteConfigString(
                                    'location_allowing_headline'),
                                textAlign: TextAlign.center,
                                style: FlutterFlowTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'BT Beau Sans',
                                      fontSize: 34.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.bold,
                                      useGoogleFonts: false,
                                    ),
                              ),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 13.0, 0.0, 0.0),
                                child: Text(
                                  getRemoteConfigString(
                                      'location_allowing_explanation'),
                                  textAlign: TextAlign.center,
                                  style: FlutterFlowTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        fontSize: 17.0,
                                        letterSpacing: 0.0,
                                        fontWeight: FontWeight.normal,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    Align(
                      alignment: const AlignmentDirectional(0.0, 1.0),
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Builder(
                              builder: (context) => wrapWithModel(
                                model: _model.gradientButtonModel,
                                updateCallback: () => setState(() {}),
                                child: GradientButtonWidget(
                                  title: getRemoteConfigString(
                                      'location_allowing_button_title'),
                                  action: () async {
                                    currentUserLocationValue =
                                        await getCurrentUserLocation(
                                            defaultLocation: const LatLng(0.0, 0.0));
                                    await requestPermission(locationPermission);
                                    if (await getPermissionStatus(
                                        locationPermission)) {
                                      await actions.getLocationData(
                                        currentUserLocationValue!,
                                        locationAllowingEntryLegacyAKeyRecord!
                                            .key,
                                      );
                                      // Save coordinates to user document

                                      await currentUserReference!
                                          .update(createUsersRecordData(
                                        location: FFAppState().currentLocation,
                                        city: FFAppState().hereLocation,
                                        wlRegion: valueOrDefault<String>(
                                          FFAppState().regionName,
                                          'Other',
                                        ),
                                        nextSignUpStage:
                                            SetUpStage.verification,
                                      ));
                                      // Save city to public profile

                                      await currentUserDocument!.publicProfile!
                                          .update(createPublicProfileRecordData(
                                        city: FFAppState().hereLocation,
                                      ));
                                      FFAppState().saveHintShown = false;
                                      FFAppState().introCompleted = false;
                                      setState(() {});
                                      if (currentUserDocument?.gender ==
                                          Gender.Female) {
                                        try {
                                          final result = await FirebaseFunctions
                                                  .instanceFor(
                                                      region: 'europe-west2')
                                              .httpsCallable(
                                                  'profileSetUpWomen')
                                              .call({});
                                          _model.profileSetupWomenCf =
                                              ProfileSetUpWomenCloudFunctionCallResponse(
                                            succeeded: true,
                                          );
                                        } on FirebaseFunctionsException catch (error) {
                                          _model.profileSetupWomenCf =
                                              ProfileSetUpWomenCloudFunctionCallResponse(
                                            errorCode: error.code,
                                            succeeded: false,
                                          );
                                        }

                                        if (_model
                                            .profileSetupWomenCf!.succeeded!) {
                                          await Future.delayed(const Duration(
                                              milliseconds: 1000));
                                        } else {
                                          await showDialog(
                                            context: context,
                                            builder: (dialogContext) {
                                              return Dialog(
                                                elevation: 0,
                                                insetPadding: EdgeInsets.zero,
                                                backgroundColor:
                                                    Colors.transparent,
                                                alignment: const AlignmentDirectional(
                                                        0.0, 0.0)
                                                    .resolve(Directionality.of(
                                                        context)),
                                                child: GestureDetector(
                                                  onTap: () => FocusScope.of(
                                                          dialogContext)
                                                      .unfocus(),
                                                  child: const GeneralPopupWidget(
                                                    alertTitle:
                                                        'Something went wrong',
                                                    alertText:
                                                        'Please try again. If the issue persists, please contact <NAME_EMAIL>.',
                                                  ),
                                                ),
                                              );
                                            },
                                          );
                                        }
                                      } else {
                                        try {
                                          final result = await FirebaseFunctions
                                                  .instanceFor(
                                                      region: 'europe-west2')
                                              .httpsCallable(
                                                  'addNewUserWaitingList')
                                              .call({});
                                          _model.addNewUserWaitingListCf =
                                              AddNewUserWaitingListCloudFunctionCallResponse(
                                            succeeded: true,
                                          );
                                        } on FirebaseFunctionsException catch (error) {
                                          _model.addNewUserWaitingListCf =
                                              AddNewUserWaitingListCloudFunctionCallResponse(
                                            errorCode: error.code,
                                            succeeded: false,
                                          );
                                        }

                                        if (_model.addNewUserWaitingListCf!
                                            .succeeded!) {
                                          await Future.delayed(const Duration(
                                              milliseconds: 1000));
                                        } else {
                                          await showDialog(
                                            context: context,
                                            builder: (dialogContext) {
                                              return Dialog(
                                                elevation: 0,
                                                insetPadding: EdgeInsets.zero,
                                                backgroundColor:
                                                    Colors.transparent,
                                                alignment: const AlignmentDirectional(
                                                        0.0, 0.0)
                                                    .resolve(Directionality.of(
                                                        context)),
                                                child: GestureDetector(
                                                  onTap: () => FocusScope.of(
                                                          dialogContext)
                                                      .unfocus(),
                                                  child: const GeneralPopupWidget(
                                                    alertTitle:
                                                        'Something went wrong',
                                                    alertText:
                                                        'Please try again. If the issue persists, please contact <NAME_EMAIL>.',
                                                  ),
                                                ),
                                              );
                                            },
                                          );
                                        }
                                      }

                                      context.goNamed('Discovery');
                                    } else {
                                      await showDialog(
                                        barrierDismissible: false,
                                        context: context,
                                        builder: (dialogContext) {
                                          return Dialog(
                                            elevation: 0,
                                            insetPadding: EdgeInsets.zero,
                                            backgroundColor: Colors.transparent,
                                            alignment: const AlignmentDirectional(
                                                    0.0, 0.0)
                                                .resolve(
                                                    Directionality.of(context)),
                                            child: GestureDetector(
                                              onTap: () =>
                                                  FocusScope.of(dialogContext)
                                                      .unfocus(),
                                              child: const GeneralPopupWidget(
                                                alertTitle:
                                                    'Location access needed',
                                                alertText:
                                                    'In order to use chyrpe, you need to grant us access to your location. You can do so in your settings app.',
                                              ),
                                            ),
                                          );
                                        },
                                      );
                                    }

                                    setState(() {});
                                  },
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 31.0, 0.0, 20.0),
                              child: InkWell(
                                splashColor: Colors.transparent,
                                focusColor: Colors.transparent,
                                hoverColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                onTap: () async {
                                  context.pushNamed(
                                    'LocationAllowingUsageNotice',
                                    extra: <String, dynamic>{
                                      kTransitionInfoKey: const TransitionInfo(
                                        hasTransition: true,
                                        transitionType:
                                            PageTransitionType.bottomToTop,
                                      ),
                                    },
                                  );
                                },
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      'How is my location used?',
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            fontFamily: 'BT Beau Sans',
                                            fontSize: 18.0,
                                            letterSpacing: 0.0,
                                            fontWeight: FontWeight.bold,
                                            useGoogleFonts: false,
                                          ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsetsDirectional.fromSTEB(
                                          10.0, 0.0, 0.0, 3.0),
                                      child: FaIcon(
                                        FontAwesomeIcons.chevronDown,
                                        color: FlutterFlowTheme.of(context)
                                            .primaryText,
                                        size: 24.0,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
