import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/single_purchases/single_purchase_spotlight_boost/single_purchase_spotlight_boost_widget.dart';
import '/flutter_flow/revenue_cat_util.dart' as revenue_cat;
import 'package:carousel_slider/carousel_slider.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'boost_purchase_component_model.dart';
export 'boost_purchase_component_model.dart';

class BoostPurchaseComponentWidget extends StatefulWidget {
  const BoostPurchaseComponentWidget({super.key});

  @override
  State<BoostPurchaseComponentWidget> createState() =>
      _BoostPurchaseComponentWidgetState();
}

class _BoostPurchaseComponentWidgetState
    extends State<BoostPurchaseComponentWidget> {
  late BoostPurchaseComponentModel _model;
  var currency = revenue_cat
                                                        .offerings!.current!
                                                        .getPackage('chyrpe_boosts_1')!
                                                        .storeProduct
                                                        .currencyCode;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => BoostPurchaseComponentModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.sizeOf(context).width * 1.0,
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).primaryBackground,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(0.0),
          bottomRight: Radius.circular(0.0),
          topLeft: Radius.circular(10.0),
          topRight: Radius.circular(10.0),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(0.0, 20.0, 20.0, 0.0),
                child: FlutterFlowIconButton(
                  borderRadius: 100.0,
                  buttonSize: 35.0,
                  fillColor: const Color(0x00353535),
                  icon: Icon(
                    Icons.close_rounded,
                    color: FlutterFlowTheme.of(context).primaryText,
                    size: 18.0,
                  ),
                  onPressed: () async {
                    Navigator.pop(context);
                  },
                ),
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(58.0, 0.0, 58.0, 0.0),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                Text(
                  getRemoteConfigString('iap_boost_purchase_title'),
                  textAlign: TextAlign.center,
                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                        fontFamily: 'BT Beau Sans',
                        fontSize: 26.0,
                        letterSpacing: 0.0,
                        fontWeight: FontWeight.bold,
                        useGoogleFonts: false,
                        lineHeight: 1.2,
                      ),
                ),
                Text(
                  getRemoteConfigString('iap_boost_purchase_description'),
                  textAlign: TextAlign.center,
                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                        fontFamily: 'BT Beau Sans',
                        letterSpacing: 0.0,
                        useGoogleFonts: false,
                        lineHeight: 1.2,
                      ),
                ),
              ].divide(const SizedBox(height: 28.0)),
            ),
          ),
          Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(0.0, 42.0, 0.0, 80.0),
            child: SizedBox(
              width: double.infinity,
              height: 300.0,
              child: CarouselSlider(
                items: [
                  Builder(
                    builder: (context) => wrapWithModel(
                      model: _model.singlePurchaseSpotlightBoostModel1,
                      updateCallback: () => safeSetState(() {}),
                      child: SinglePurchaseSpotlightBoostWidget(
                        bigTitle: '1 Boost',
                        smallCaption: '$currency ${formatNumber(
                          revenue_cat.offerings!.current!
                              .getPackage('chyrpe_boosts_1')!
                              .storeProduct
                              .price,
                          formatType: FormatType.custom,
                          format: '##.##',
                          locale: '',
                        )} each',
                        showSave: false,
                        saveValue: '0',
                        btnTitle:
                            'Get it for ${revenue_cat.offerings!.current!.getPackage('chyrpe_boosts_1')!.storeProduct.priceString}',
                        btnAction: () async {
                          var shouldSetState = false;
                          _model.purchase = await revenue_cat
                              .purchasePackage('chyrpe_boosts_1');
                          shouldSetState = true;
                          if (_model.purchase!) {
                            try {
                              final result =
                                  await FirebaseFunctions.instanceFor(
                                          region: 'europe-west2')
                                      .httpsCallable('syncAfterOneTimePurchase')
                                      .call({});
                              _model.syncAfterBoostPurchase =
                                  SyncAfterOneTimePurchaseCloudFunctionCallResponse(
                                succeeded: true,
                              );
                            } on FirebaseFunctionsException catch (error) {
                              _model.syncAfterBoostPurchase =
                                  SyncAfterOneTimePurchaseCloudFunctionCallResponse(
                                errorCode: error.code,
                                succeeded: false,
                              );
                            }

                            shouldSetState = true;
                            if (_model.syncAfterBoostPurchase!.succeeded!) {
                              Navigator.pop(context);
                              if (shouldSetState) safeSetState(() {});
                              return;
                            } else {
                              await showDialog(
                                context: context,
                                builder: (dialogContext) {
                                  return Dialog(
                                    elevation: 0,
                                    insetPadding: EdgeInsets.zero,
                                    backgroundColor: Colors.transparent,
                                    alignment: const AlignmentDirectional(0.0, 0.0)
                                        .resolve(Directionality.of(context)),
                                    child: const GeneralPopupWidget(
                                      alertTitle: 'Something went wrong',
                                      alertText:
                                          'Please try again <NAME_EMAIL>',
                                    ),
                                  );
                                },
                              );

                              if (shouldSetState) safeSetState(() {});
                              return;
                            }
                          } else {
                            await showDialog(
                              context: context,
                              builder: (dialogContext) {
                                return Dialog(
                                  elevation: 0,
                                  insetPadding: EdgeInsets.zero,
                                  backgroundColor: Colors.transparent,
                                  alignment: const AlignmentDirectional(0.0, 0.0)
                                      .resolve(Directionality.of(context)),
                                  child: const GeneralPopupWidget(
                                    alertTitle: 'Something went wrong',
                                    alertText:
                                        'Please try again <NAME_EMAIL>',
                                  ),
                                );
                              },
                            );

                            if (shouldSetState) safeSetState(() {});
                            return;
                          }

                          if (shouldSetState) safeSetState(() {});
                        },
                      ),
                    ),
                  ),
                  Builder(
                    builder: (context) => wrapWithModel(
                      model: _model.singlePurchaseSpotlightBoostModel2,
                      updateCallback: () => safeSetState(() {}),
                      child: SinglePurchaseSpotlightBoostWidget(
                        bigTitle: '5 Boosts',
                        smallCaption: '$currency ${formatNumber(
                          revenue_cat.offerings!.current!
                                  .getPackage('chyrpe_boosts_5')!
                                  .storeProduct
                                  .price /
                              5,
                          formatType: FormatType.custom,
                          format: '##.##',
                          locale: '',
                        )} each',
                        showSave: true,
                        saveValue: formatNumber(
                          100 - ((revenue_cat.offerings!.current!
                                          .getPackage('chyrpe_boosts_5')!
                                          .storeProduct
                                          .price /
                                      5) /
                                  (revenue_cat.offerings!.current!
                                      .getPackage('chyrpe_boosts_1')!
                                      .storeProduct
                                      .price)) *
                              100,
                          formatType: FormatType.custom,
                          format: '##',
                          locale: '',
                        ),
                        btnTitle:
                            'Get 5 for ${revenue_cat.offerings!.current!.getPackage('chyrpe_boosts_5')!.storeProduct.priceString}',
                        btnAction: () async {
                          var shouldSetState = false;
                          _model.purchase1 = await revenue_cat
                              .purchasePackage('chyrpe_boosts_5');
                          shouldSetState = true;
                          if (_model.purchase1!) {
                            try {
                              final result =
                                  await FirebaseFunctions.instanceFor(
                                          region: 'europe-west2')
                                      .httpsCallable('syncAfterOneTimePurchase')
                                      .call({});
                              _model.syncAfterBoostPurchase1 =
                                  SyncAfterOneTimePurchaseCloudFunctionCallResponse(
                                succeeded: true,
                              );
                            } on FirebaseFunctionsException catch (error) {
                              _model.syncAfterBoostPurchase1 =
                                  SyncAfterOneTimePurchaseCloudFunctionCallResponse(
                                errorCode: error.code,
                                succeeded: false,
                              );
                            }

                            shouldSetState = true;
                            if (_model.syncAfterBoostPurchase1!.succeeded!) {
                              Navigator.pop(context);
                              if (shouldSetState) safeSetState(() {});
                              return;
                            } else {
                              await showDialog(
                                context: context,
                                builder: (dialogContext) {
                                  return Dialog(
                                    elevation: 0,
                                    insetPadding: EdgeInsets.zero,
                                    backgroundColor: Colors.transparent,
                                    alignment: const AlignmentDirectional(0.0, 0.0)
                                        .resolve(Directionality.of(context)),
                                    child: const GeneralPopupWidget(
                                      alertTitle: 'Something went wrong',
                                      alertText:
                                          'Please try again <NAME_EMAIL>',
                                    ),
                                  );
                                },
                              );

                              if (shouldSetState) safeSetState(() {});
                              return;
                            }
                          } else {
                            await showDialog(
                              context: context,
                              builder: (dialogContext) {
                                return Dialog(
                                  elevation: 0,
                                  insetPadding: EdgeInsets.zero,
                                  backgroundColor: Colors.transparent,
                                  alignment: const AlignmentDirectional(0.0, 0.0)
                                      .resolve(Directionality.of(context)),
                                  child: const GeneralPopupWidget(
                                    alertTitle: 'Something went wrong',
                                    alertText:
                                        'Please try again <NAME_EMAIL>',
                                  ),
                                );
                              },
                            );

                            if (shouldSetState) safeSetState(() {});
                            return;
                          }

                          if (shouldSetState) safeSetState(() {});
                        },
                      ),
                    ),
                  ),
                  Builder(
                    builder: (context) => wrapWithModel(
                      model: _model.singlePurchaseSpotlightBoostModel3,
                      updateCallback: () => safeSetState(() {}),
                      child: SinglePurchaseSpotlightBoostWidget(
                        bigTitle: '10 Boosts',
                        smallCaption: '$currency ${formatNumber(
                          revenue_cat.offerings!.current!
                                  .getPackage('chyrpe_boosts_10')!
                                  .storeProduct
                                  .price /
                              10,
                          formatType: FormatType.custom,
                          format: '##.##',
                          locale: '',
                        )} each',
                        showSave: true,
                        saveValue: formatNumber(
                          100 - ((revenue_cat.offerings!.current!
                                          .getPackage('chyrpe_boosts_10')!
                                          .storeProduct
                                          .price /
                                      10) /
                                  (revenue_cat.offerings!.current!
                                      .getPackage('chyrpe_boosts_1')!
                                      .storeProduct
                                      .price)) *
                              100,
                          formatType: FormatType.custom,
                          format: '##',
                          locale: '',
                        ),
                        btnTitle:
                            'Get 10 for ${revenue_cat.offerings!.current!.getPackage('chyrpe_boosts_10')!.storeProduct.priceString}',
                        btnAction: () async {
                          var shouldSetState = false;
                          _model.purchase3 = await revenue_cat
                              .purchasePackage('chyrpe_boosts_10');
                          shouldSetState = true;
                          if (_model.purchase3!) {
                            try {
                              final result =
                                  await FirebaseFunctions.instanceFor(
                                          region: 'europe-west2')
                                      .httpsCallable('syncAfterOneTimePurchase')
                                      .call({});
                              _model.syncAfterBoostPurchase3 =
                                  SyncAfterOneTimePurchaseCloudFunctionCallResponse(
                                succeeded: true,
                              );
                            } on FirebaseFunctionsException catch (error) {
                              _model.syncAfterBoostPurchase3 =
                                  SyncAfterOneTimePurchaseCloudFunctionCallResponse(
                                errorCode: error.code,
                                succeeded: false,
                              );
                            }

                            shouldSetState = true;
                            if (_model.syncAfterBoostPurchase3!.succeeded!) {
                              Navigator.pop(context);
                              if (shouldSetState) safeSetState(() {});
                              return;
                            } else {
                              await showDialog(
                                context: context,
                                builder: (dialogContext) {
                                  return Dialog(
                                    elevation: 0,
                                    insetPadding: EdgeInsets.zero,
                                    backgroundColor: Colors.transparent,
                                    alignment: const AlignmentDirectional(0.0, 0.0)
                                        .resolve(Directionality.of(context)),
                                    child: const GeneralPopupWidget(
                                      alertTitle: 'Something went wrong',
                                      alertText:
                                          'Please try again <NAME_EMAIL>',
                                    ),
                                  );
                                },
                              );

                              if (shouldSetState) safeSetState(() {});
                              return;
                            }
                          } else {
                            await showDialog(
                              context: context,
                              builder: (dialogContext) {
                                return Dialog(
                                  elevation: 0,
                                  insetPadding: EdgeInsets.zero,
                                  backgroundColor: Colors.transparent,
                                  alignment: const AlignmentDirectional(0.0, 0.0)
                                      .resolve(Directionality.of(context)),
                                  child: const GeneralPopupWidget(
                                    alertTitle: 'Something went wrong',
                                    alertText:
                                        'Please try again <NAME_EMAIL>',
                                  ),
                                );
                              },
                            );

                            if (shouldSetState) safeSetState(() {});
                            return;
                          }

                          if (shouldSetState) safeSetState(() {});
                        },
                      ),
                    ),
                  ),
                ],
                carouselController: _model.carouselController ??=
                    CarouselController(),
                options: CarouselOptions(
                  initialPage: 1,
                  viewportFraction: 0.7,
                  disableCenter: true,
                  enlargeCenterPage: true,
                  enlargeFactor: 0.25,
                  enableInfiniteScroll: false,
                  scrollDirection: Axis.horizontal,
                  autoPlay: false,
                  onPageChanged: (index, _) =>
                      _model.carouselCurrentIndex = index,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
