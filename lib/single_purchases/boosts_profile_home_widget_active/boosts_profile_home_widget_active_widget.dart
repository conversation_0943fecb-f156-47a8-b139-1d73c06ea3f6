import '/auth/firebase_auth/auth_util.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/custom_code/widgets/index.dart' as custom_widgets;
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'boosts_profile_home_widget_active_model.dart';
export 'boosts_profile_home_widget_active_model.dart';

class BoostsProfileHomeWidgetActiveWidget extends StatefulWidget {
  const BoostsProfileHomeWidgetActiveWidget({super.key});

  @override
  State<BoostsProfileHomeWidgetActiveWidget> createState() =>
      _BoostsProfileHomeWidgetActiveWidgetState();
}

class _BoostsProfileHomeWidgetActiveWidgetState
    extends State<BoostsProfileHomeWidgetActiveWidget> {
  late BoostsProfileHomeWidgetActiveModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => BoostsProfileHomeWidgetActiveModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.sizeOf(context).width * 1.0,
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).primaryBackground,
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(
          color: const Color(0xC3C3C3C3),
        ),
      ),
      child: Padding(
        padding: const EdgeInsetsDirectional.fromSTEB(12.0, 10.0, 12.0, 10.0),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 13.0, 0.0),
              child: Stack(
                alignment: const AlignmentDirectional(1.0, -1.0),
                children: [
                  Padding(
                    padding:
                        const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 10.0, 0.0),
                    child: Container(
                      width: 47.0,
                      height: 47.0,
                      decoration: const BoxDecoration(
                        color: Color(0xFF923DCB),
                        shape: BoxShape.circle,
                      ),
                      child: Padding(
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(3.0, 3.0, 3.0, 3.0),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8.0),
                          child: SvgPicture.asset(
                            'assets/images/Artboard_1_11Boost.svg',
                            width: 37.0,
                            height: 37.0,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ),
                  ),
                  Container(
                    width: 18.0,
                    height: 18.0,
                    decoration: BoxDecoration(
                      color: const Color(0xFF923DCB),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: FlutterFlowTheme.of(context).primaryBackground,
                        width: 2.0,
                      ),
                    ),
                    alignment: const AlignmentDirectional(0.0, 0.0),
                    child: Align(
                      alignment: const AlignmentDirectional(0.0, 0.0),
                      child: AuthUserStreamWidget(
                        builder: (context) => Text(
                          valueOrDefault(currentUserDocument?.boostsLeft, 0)
                              .toString(),
                          style:
                              FlutterFlowTheme.of(context).bodyMedium.override(
                                    fontFamily: 'BT Beau Sans',
                                    color: FlutterFlowTheme.of(context)
                                        .primaryBackground,
                                    fontSize: 9.0,
                                    letterSpacing: 0.0,
                                    fontWeight: FontWeight.bold,
                                    useGoogleFonts: false,
                                  ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  getRemoteConfigString('iap_boost_home_active_title'),
                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                        fontFamily: 'BT Beau Sans',
                        letterSpacing: 0.0,
                        fontWeight: FontWeight.bold,
                        useGoogleFonts: false,
                      ),
                ),
                AuthUserStreamWidget(
                  builder: (context) => SizedBox(
                    height: 20.0,
                    child: custom_widgets.TimerWidgetPurpleText(
                      height: 10.0,
                      initialTime: currentUserDocument!
                              .boostedUntil!.millisecondsSinceEpoch -
                          DateTime.now().millisecondsSinceEpoch,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
