import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/single_purchases/single_purchase_spotlight_diamond/single_purchase_spotlight_diamond_widget.dart';
import 'diamond_like_purchase_component_widget.dart'
    show DiamondLikePurchaseComponentWidget;
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';

class DiamondLikePurchaseComponentModel
    extends FlutterFlowModel<DiamondLikePurchaseComponentWidget> {
  ///  State fields for stateful widgets in this component.

  // State field(s) for Carousel widget.
  CarouselController? carouselController;
  int carouselCurrentIndex = 1;

  // Model for SinglePurchaseSpotlightDiamond component.
  late SinglePurchaseSpotlightDiamondModel singlePurchaseSpotlightDiamondModel1;
  // Stores action output result for [RevenueCat - Purchase] action in SinglePurchaseSpotlightDiamond widget.
  bool? purchase;
  // Stores action output result for [Cloud Function - syncAfterOneTimePurchase] action in SinglePurchaseSpotlightDiamond widget.
  SyncAfterOneTimePurchaseCloudFunctionCallResponse? syncAfterBoostPurchase;
  // Model for SinglePurchaseSpotlightDiamond component.
  late SinglePurchaseSpotlightDiamondModel singlePurchaseSpotlightDiamondModel2;
  // Stores action output result for [RevenueCat - Purchase] action in SinglePurchaseSpotlightDiamond widget.
  bool? purchase2;
  // Stores action output result for [Cloud Function - syncAfterOneTimePurchase] action in SinglePurchaseSpotlightDiamond widget.
  SyncAfterOneTimePurchaseCloudFunctionCallResponse? syncAfterBoostPurchase2;
  // Model for SinglePurchaseSpotlightDiamond component.
  late SinglePurchaseSpotlightDiamondModel singlePurchaseSpotlightDiamondModel3;
  // Stores action output result for [RevenueCat - Purchase] action in SinglePurchaseSpotlightDiamond widget.
  bool? purchase3;
  // Stores action output result for [Cloud Function - syncAfterOneTimePurchase] action in SinglePurchaseSpotlightDiamond widget.
  SyncAfterOneTimePurchaseCloudFunctionCallResponse? syncAfterBoostPurchase3;

  @override
  void initState(BuildContext context) {
    singlePurchaseSpotlightDiamondModel1 =
        createModel(context, () => SinglePurchaseSpotlightDiamondModel());
    singlePurchaseSpotlightDiamondModel2 =
        createModel(context, () => SinglePurchaseSpotlightDiamondModel());
    singlePurchaseSpotlightDiamondModel3 =
        createModel(context, () => SinglePurchaseSpotlightDiamondModel());
  }

  @override
  void dispose() {
    singlePurchaseSpotlightDiamondModel1.dispose();
    singlePurchaseSpotlightDiamondModel2.dispose();
    singlePurchaseSpotlightDiamondModel3.dispose();
  }
}