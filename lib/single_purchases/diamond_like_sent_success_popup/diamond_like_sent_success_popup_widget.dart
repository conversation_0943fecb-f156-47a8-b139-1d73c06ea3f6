import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:rive/rive.dart' hide LinearGradient;
import 'package:flutter/material.dart';
import 'diamond_like_sent_success_popup_model.dart';
export 'diamond_like_sent_success_popup_model.dart';

class DiamondLikeSentSuccessPopupWidget extends StatefulWidget {
  const DiamondLikeSentSuccessPopupWidget({super.key});

  @override
  State<DiamondLikeSentSuccessPopupWidget> createState() =>
      _DiamondLikeSentSuccessPopupWidgetState();
}

class _DiamondLikeSentSuccessPopupWidgetState
    extends State<DiamondLikeSentSuccessPopupWidget> {
  late DiamondLikeSentSuccessPopupModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => DiamondLikeSentSuccessPopupModel());
  }

  late StateMachineController _controller;
  void _onInit(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 2');
  if (ctrl != null) {
    art.addController(ctrl);
    _controller = ctrl;
    _controller.isActive = true;
    
      }
     // Keep it inactive initially
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 300.0,
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).primaryText,
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Padding(
        padding: const EdgeInsetsDirectional.fromSTEB(10.0, 20.0, 10.0, 30.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 55.0,
              height: 55.0,
              child: RiveAnimation.asset(
                'assets/rive_animations/diamond_animation.riv',
                artboard: 'Artboard',
                fit: BoxFit.cover,
                animations: const ['spinning'],
                onInit: _onInit,
              ),
            ),
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
              child: Text(
                getRemoteConfigString('iap_diamond_confirmation_title'),
                textAlign: TextAlign.center,
                style: FlutterFlowTheme.of(context).bodyMedium.override(
                      fontFamily: 'BT Beau Sans',
                      color: FlutterFlowTheme.of(context).primaryBackground,
                      fontSize: 14.0,
                      letterSpacing: 0.0,
                      fontWeight: FontWeight.bold,
                      useGoogleFonts: false,
                    ),
              ),
            ),
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(0.0, 5.0, 0.0, 0.0),
              child: Text(
                getRemoteConfigString('iap_diamond_confirmation_subtitle'),
                style: FlutterFlowTheme.of(context).bodyMedium.override(
                      fontFamily: 'BT Beau Sans',
                      color: FlutterFlowTheme.of(context).primaryBackground,
                      fontSize: 12.0,
                      letterSpacing: 0.0,
                      useGoogleFonts: false,
                    ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
