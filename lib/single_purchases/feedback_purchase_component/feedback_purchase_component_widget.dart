import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/single_purchases/single_purchase_spotlight_feedback/single_purchase_spotlight_feedback_widget.dart';
import '/single_purchases/single_purchase_subscription_suggestion_plus/single_purchase_subscription_suggestion_plus_widget.dart';
import '/flutter_flow/revenue_cat_util.dart' as revenue_cat;
import 'package:carousel_slider/carousel_slider.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'feedback_purchase_component_model.dart';
export 'feedback_purchase_component_model.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:chyrpe/amplitudeConfig.dart';

class FeedbackPurchaseComponentWidget extends StatefulWidget {
  const FeedbackPurchaseComponentWidget({super.key});

  @override
  State<FeedbackPurchaseComponentWidget> createState() =>
      _FeedbackPurchaseComponentWidgetState();
}

class _FeedbackPurchaseComponentWidgetState
    extends State<FeedbackPurchaseComponentWidget> {
  late FeedbackPurchaseComponentModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => FeedbackPurchaseComponentModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.sizeOf(context).width * 1.0,
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).primaryBackground,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(0.0),
          bottomRight: Radius.circular(0.0),
          topLeft: Radius.circular(10.0),
          topRight: Radius.circular(10.0),
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(0.0, 20.0, 20.0, 0.0),
                  child: FlutterFlowIconButton(
                    borderRadius: 100.0,
                    buttonSize: 35.0,
                    fillColor: const Color(0x00353535),
                    icon: Icon(
                      Icons.close_rounded,
                      color: FlutterFlowTheme.of(context).primaryText,
                      size: 18.0,
                    ),
                    onPressed: () async {
                      Navigator.pop(context);
                    },
                  ),
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(58.0, 0.0, 58.0, 0.0),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  Text(
                    getRemoteConfigString('iap_feedback_title'),
                    textAlign: TextAlign.center,
                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                          fontFamily: 'BT Beau Sans',
                          fontSize: 26.0,
                          letterSpacing: 0.0,
                          fontWeight: FontWeight.bold,
                          useGoogleFonts: false,
                          lineHeight: 1.2,
                        ),
                  ),
                  Text(
                    getRemoteConfigString('iap_feedback_description'),
                    textAlign: TextAlign.center,
                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                          fontFamily: 'BT Beau Sans',
                          letterSpacing: 0.0,
                          useGoogleFonts: false,
                          lineHeight: 1.2,
                        ),
                  ),
                ].divide(const SizedBox(height: 28.0)),
              ),
            ),
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(0.0, 42.0, 0.0, 0.0),
              child: SizedBox(
                width: double.infinity,
                height: 300.0,
                child: CarouselSlider(
                  items: [
                    Column(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Builder(
                          builder: (context) => wrapWithModel(
                            model: _model.singlePurchaseSpotlightFeedbackModel,
                            updateCallback: () => safeSetState(() {}),
                            child: SinglePurchaseSpotlightFeedbackWidget(
                              bigTitle: '1 Week Access',
                              btnTitle:
                                  'Get it for ${revenue_cat.offerings!.current!.getPackage('chyrpe_feedback_1w')!.storeProduct.priceString}',
                              btnAction: () async {
                                var shouldSetState = false;
                                _model.purchase = await revenue_cat
                                    .purchasePackage('chyrpe_feedback_1w');
                                shouldSetState = true;
                                if (_model.purchase!) {
                                  try {
                                    final result =
                                        await FirebaseFunctions.instanceFor(
                                                region: 'europe-west2')
                                            .httpsCallable('redeemFeedback1week')
                                            .call({});
                                    _model.redeemFeedback =
                                        RedeemFeedback1weekCloudFunctionCallResponse(
                                      succeeded: true,
                                    );
                                  } on FirebaseFunctionsException catch (error) {
                                    _model.redeemFeedback =
                                        RedeemFeedback1weekCloudFunctionCallResponse(
                                      errorCode: error.code,
                                      succeeded: false,
                                    );
                                  }
        
                                  shouldSetState = true;
                                  if (_model.redeemFeedback!.succeeded!) {
                                    await revenue_cat.restorePurchases();
                                    await Purchases.syncPurchases();
                                    Navigator.pop(context);
                                  } else {
                                    await showDialog(
                                      context: context,
                                      builder: (dialogContext) {
                                        return Dialog(
                                          elevation: 0,
                                          insetPadding: EdgeInsets.zero,
                                          backgroundColor: Colors.transparent,
                                          alignment:
                                              const AlignmentDirectional(0.0, 0.0)
                                                  .resolve(
                                                      Directionality.of(context)),
                                          child: const GeneralPopupWidget(
                                            alertTitle: 'Something went wrong',
                                            alertText:
                                                '<NAME_EMAIL>',
                                          ),
                                        );
                                      },
                                    );
                                  }
                                } else {
                                  await showDialog(
                                    context: context,
                                    builder: (dialogContext) {
                                      return Dialog(
                                        elevation: 0,
                                        insetPadding: EdgeInsets.zero,
                                        backgroundColor: Colors.transparent,
                                        alignment: const AlignmentDirectional(0.0, 0.0)
                                            .resolve(Directionality.of(context)),
                                        child: const GeneralPopupWidget(
                                          alertTitle: 'Something went wrong',
                                          alertText:
                                              'Please try again <NAME_EMAIL>',
                                        ),
                                      );
                                    },
                                  );
        
                                  if (shouldSetState) safeSetState(() {});
                                  return;
                                }
        
                                if (shouldSetState) safeSetState(() {});
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                  carouselController: _model.carouselController ??=
                      CarouselController(),
                  options: CarouselOptions(
                    initialPage: 0,
                    viewportFraction: 0.7,
                    disableCenter: true,
                    enlargeCenterPage: true,
                    enlargeFactor: 0.25,
                    enableInfiniteScroll: false,
                    scrollDirection: Axis.horizontal,
                    autoPlay: false,
                    onPageChanged: (index, _) =>
                        _model.carouselCurrentIndex = index,
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(15.0, 35.0, 15.0, 50.0),
              child: wrapWithModel(
                model: _model.singlePurchaseSubscriptionSuggestionPlusModel,
                updateCallback: () => safeSetState(() {}),
                child: SinglePurchaseSubscriptionSuggestionPlusWidget(
                  tagline: getRemoteConfigString('iap_feedback_pupgrade'),
                  btnTitle:
                      'Plus from ${revenue_cat.offerings!.current!.getPackage('chyrpe_plus_6m')!.storeProduct.currencyCode} ${(revenue_cat.offerings!.current!.getPackage('chyrpe_plus_6m')!.storeProduct.price / 12).toStringAsFixed(2)}/week',
                  btnAction: () async {
                    Navigator.pop(context);
                    try {
                       analytics.logEvent('Navigated to Plus Evolved from Feedback Purchase');
                       } catch(e) {}
        
                    context.pushNamed(
                      'PlusEvolvedSubscriptionNew',
                      queryParameters: {
                        'initialSubscription': serializeParam(
                          'Plus',
                          ParamType.String,
                        ),
                      }.withoutNulls,
                      extra: <String, dynamic>{
                        kTransitionInfoKey: const TransitionInfo(
                          hasTransition: true,
                          transitionType: PageTransitionType.bottomToTop,
                        ),
                      },
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
