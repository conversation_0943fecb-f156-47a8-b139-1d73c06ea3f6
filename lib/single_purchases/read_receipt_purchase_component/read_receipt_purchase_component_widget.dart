import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/single_purchases/single_purchase_spotlight_read_receipt/single_purchase_spotlight_read_receipt_widget.dart';
import '/flutter_flow/revenue_cat_util.dart' as revenue_cat;
import 'package:carousel_slider/carousel_slider.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'read_receipt_purchase_component_model.dart';
export 'read_receipt_purchase_component_model.dart';

class ReadReceiptPurchaseComponentWidget extends StatefulWidget {
  const ReadReceiptPurchaseComponentWidget({super.key});

  @override
  State<ReadReceiptPurchaseComponentWidget> createState() =>
      _ReadReceiptPurchaseComponentWidgetState();
}

class _ReadReceiptPurchaseComponentWidgetState
    extends State<ReadReceiptPurchaseComponentWidget> {
  late ReadReceiptPurchaseComponentModel _model;
  var currency = revenue_cat
                                                        .offerings!.current!
                                                        .getPackage('chyrpe_rreceipt_1')!
                                                        .storeProduct
                                                        .currencyCode;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => ReadReceiptPurchaseComponentModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: const AlignmentDirectional(0.0, 1.0),
      child: Container(
        width: MediaQuery.sizeOf(context).width * 1.0,
        decoration: BoxDecoration(
          color: FlutterFlowTheme.of(context).primaryBackground,
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(0.0),
            bottomRight: Radius.circular(0.0),
            topLeft: Radius.circular(10.0),
            topRight: Radius.circular(10.0),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(0.0, 20.0, 20.0, 0.0),
                  child: FlutterFlowIconButton(
                    borderRadius: 100.0,
                    buttonSize: 35.0,
                    fillColor: const Color(0x00353535),
                    icon: Icon(
                      Icons.close_rounded,
                      color: FlutterFlowTheme.of(context).primaryText,
                      size: 18.0,
                    ),
                    onPressed: () async {
                      Navigator.pop(context);
                    },
                  ),
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(58.0, 0.0, 58.0, 0.0),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  Text(
                    getRemoteConfigString('iap_rreceipt_title'),
                    textAlign: TextAlign.center,
                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                          fontFamily: 'BT Beau Sans',
                          fontSize: 26.0,
                          letterSpacing: 0.0,
                          fontWeight: FontWeight.bold,
                          useGoogleFonts: false,
                          lineHeight: 1.2,
                        ),
                  ),
                  Text(
                    getRemoteConfigString('iap_rreceipt_description'),
                    textAlign: TextAlign.center,
                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                          fontFamily: 'BT Beau Sans',
                          letterSpacing: 0.0,
                          useGoogleFonts: false,
                          lineHeight: 1.2,
                        ),
                  ),
                ].divide(const SizedBox(height: 28.0)),
              ),
            ),
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(0.0, 42.0, 0.0, 80.0),
              child: SizedBox(
                width: double.infinity,
                height: 300.0,
                child: CarouselSlider(
                  items: [
                    Builder(
                      builder: (context) => wrapWithModel(
                        model: _model.singlePurchaseSpotlightReadReceiptModel1,
                        updateCallback: () => safeSetState(() {}),
                        child: SinglePurchaseSpotlightReadReceiptWidget(
                          bigTitle: '1 Read Receipt',
                          smallCaption: '$currency ${formatNumber(
                            revenue_cat.offerings!.current!
                                .getPackage('chyrpe_rreceipt_1')!
                                .storeProduct
                                .price,
                            formatType: FormatType.custom,
                            locale: '',
                          )} each',
                          showSave: false,
                          saveValue: '0',
                          btnTitle:
                              'Get it for ${revenue_cat.offerings!.current!.getPackage('chyrpe_rreceipt_1')!.storeProduct.priceString}',
                          btnAction: () async {
                            var shouldSetState = false;
                            _model.purchase = await revenue_cat
                                .purchasePackage('chyrpe_rreceipt_1');
                            shouldSetState = true;
                            if (_model.purchase!) {
                              try {
                                final result = await FirebaseFunctions
                                        .instanceFor(region: 'europe-west2')
                                    .httpsCallable('syncAfterOneTimePurchase')
                                    .call({});
                                _model.syncAfterBoostPurchase =
                                    SyncAfterOneTimePurchaseCloudFunctionCallResponse(
                                  succeeded: true,
                                );
                              } on FirebaseFunctionsException catch (error) {
                                _model.syncAfterBoostPurchase =
                                    SyncAfterOneTimePurchaseCloudFunctionCallResponse(
                                  errorCode: error.code,
                                  succeeded: false,
                                );
                              }

                              shouldSetState = true;
                              if (_model.syncAfterBoostPurchase!.succeeded!) {
                                Navigator.pop(context);
                                if (shouldSetState) safeSetState(() {});
                                return;
                              } else {
                                await showDialog(
                                  context: context,
                                  builder: (dialogContext) {
                                    return Dialog(
                                      elevation: 0,
                                      insetPadding: EdgeInsets.zero,
                                      backgroundColor: Colors.transparent,
                                      alignment: const AlignmentDirectional(0.0, 0.0)
                                          .resolve(Directionality.of(context)),
                                      child: const GeneralPopupWidget(
                                        alertTitle: 'Something went wrong',
                                        alertText:
                                            'Please try again <NAME_EMAIL>',
                                      ),
                                    );
                                  },
                                );

                                if (shouldSetState) safeSetState(() {});
                                return;
                              }
                            } else {
                              await showDialog(
                                context: context,
                                builder: (dialogContext) {
                                  return Dialog(
                                    elevation: 0,
                                    insetPadding: EdgeInsets.zero,
                                    backgroundColor: Colors.transparent,
                                    alignment: const AlignmentDirectional(0.0, 0.0)
                                        .resolve(Directionality.of(context)),
                                    child: const GeneralPopupWidget(
                                      alertTitle: 'Something went wrong',
                                      alertText:
                                          'Please try again <NAME_EMAIL>',
                                    ),
                                  );
                                },
                              );

                              if (shouldSetState) safeSetState(() {});
                              return;
                            }

                            if (shouldSetState) safeSetState(() {});
                          },
                        ),
                      ),
                    ),
                    Builder(
                      builder: (context) => wrapWithModel(
                        model: _model.singlePurchaseSpotlightReadReceiptModel2,
                        updateCallback: () => safeSetState(() {}),
                        child: SinglePurchaseSpotlightReadReceiptWidget(
                          bigTitle: '3 Read Receipts',
                          smallCaption: '$currency ${formatNumber(
                            revenue_cat.offerings!.current!
                                    .getPackage('chyrpe_rreceipt_3')!
                                    .storeProduct
                                    .price /
                                3,
                            formatType: FormatType.custom,
                            format: '##.##',
                            locale: '',
                          )} each',
                          showSave: true,
                          saveValue: formatNumber(
                            100 - ((revenue_cat.offerings!.current!
                                            .getPackage('chyrpe_rreceipt_3')!
                                            .storeProduct
                                            .price /
                                        3) /
                                    (revenue_cat.offerings!.current!
                                        .getPackage('chyrpe_rreceipt_1')!
                                        .storeProduct
                                        .price)) *
                                100,
                            formatType: FormatType.custom,
                            format: '##',
                            locale: '',
                          ),
                          btnTitle:
                              'Get 3 for ${revenue_cat.offerings!.current!.getPackage('chyrpe_rreceipt_3')!.storeProduct.priceString}',
                          btnAction: () async {
                            var shouldSetState = false;
                            _model.purchase2 = await revenue_cat
                                .purchasePackage('chyrpe_rreceipt_3');
                            shouldSetState = true;
                            if (_model.purchase2!) {
                              try {
                                final result = await FirebaseFunctions
                                        .instanceFor(region: 'europe-west2')
                                    .httpsCallable('syncAfterOneTimePurchase')
                                    .call({});
                                _model.syncAfterBoostPurchase2 =
                                    SyncAfterOneTimePurchaseCloudFunctionCallResponse(
                                  succeeded: true,
                                );
                              } on FirebaseFunctionsException catch (error) {
                                _model.syncAfterBoostPurchase2 =
                                    SyncAfterOneTimePurchaseCloudFunctionCallResponse(
                                  errorCode: error.code,
                                  succeeded: false,
                                );
                              }

                              shouldSetState = true;
                              if (_model.syncAfterBoostPurchase2!.succeeded!) {
                                Navigator.pop(context);
                                if (shouldSetState) safeSetState(() {});
                                return;
                              } else {
                                await showDialog(
                                  context: context,
                                  builder: (dialogContext) {
                                    return Dialog(
                                      elevation: 0,
                                      insetPadding: EdgeInsets.zero,
                                      backgroundColor: Colors.transparent,
                                      alignment: const AlignmentDirectional(0.0, 0.0)
                                          .resolve(Directionality.of(context)),
                                      child: const GeneralPopupWidget(
                                        alertTitle: 'Something went wrong',
                                        alertText:
                                            'Please try again <NAME_EMAIL>',
                                      ),
                                    );
                                  },
                                );

                                if (shouldSetState) safeSetState(() {});
                                return;
                              }
                            } else {
                              await showDialog(
                                context: context,
                                builder: (dialogContext) {
                                  return Dialog(
                                    elevation: 0,
                                    insetPadding: EdgeInsets.zero,
                                    backgroundColor: Colors.transparent,
                                    alignment: const AlignmentDirectional(0.0, 0.0)
                                        .resolve(Directionality.of(context)),
                                    child: const GeneralPopupWidget(
                                      alertTitle: 'Something went wrong',
                                      alertText:
                                          'Please try again <NAME_EMAIL>',
                                    ),
                                  );
                                },
                              );

                              if (shouldSetState) safeSetState(() {});
                              return;
                            }

                            if (shouldSetState) safeSetState(() {});
                          },
                        ),
                      ),
                    ),
                    Builder(
                      builder: (context) => wrapWithModel(
                        model: _model.singlePurchaseSpotlightReadReceiptModel3,
                        updateCallback: () => safeSetState(() {}),
                        child: SinglePurchaseSpotlightReadReceiptWidget(
                          bigTitle: '5 Read Receipts',
                          smallCaption: '$currency ${formatNumber(
                            revenue_cat.offerings!.current!
                                    .getPackage('chyrpe_rreceipt_5')!
                                    .storeProduct
                                    .price /
                                5,
                            formatType: FormatType.custom,
                            format: '##.##',
                            locale: '',
                          )} each',
                          showSave: true,
                          saveValue: formatNumber(
                            100 - ((revenue_cat.offerings!.current!
                                            .getPackage('chyrpe_rreceipt_5')!
                                            .storeProduct
                                            .price /
                                        5) /
                                    (revenue_cat.offerings!.current!
                                        .getPackage('chyrpe_rreceipt_1')!
                                        .storeProduct
                                        .price)) *
                                100,
                            formatType: FormatType.custom,
                            format: '##',
                            locale: '',
                          ),
                          btnTitle:
                              'Get 5 for ${revenue_cat.offerings!.current!.getPackage('chyrpe_rreceipt_5')!.storeProduct.priceString}',
                          btnAction: () async {
                            var shouldSetState = false;
                            _model.purchase3 = await revenue_cat
                                .purchasePackage('chyrpe_rreceipt_5');
                            shouldSetState = true;
                            if (_model.purchase3!) {
                              try {
                                final result = await FirebaseFunctions
                                        .instanceFor(region: 'europe-west2')
                                    .httpsCallable('syncAfterOneTimePurchase')
                                    .call({});
                                _model.syncAfterBoostPurchase3 =
                                    SyncAfterOneTimePurchaseCloudFunctionCallResponse(
                                  succeeded: true,
                                );
                              } on FirebaseFunctionsException catch (error) {
                                _model.syncAfterBoostPurchase3 =
                                    SyncAfterOneTimePurchaseCloudFunctionCallResponse(
                                  errorCode: error.code,
                                  succeeded: false,
                                );
                              }

                              shouldSetState = true;
                              if (_model.syncAfterBoostPurchase3!.succeeded!) {
                                Navigator.pop(context);
                                if (shouldSetState) safeSetState(() {});
                                return;
                              } else {
                                await showDialog(
                                  context: context,
                                  builder: (dialogContext) {
                                    return Dialog(
                                      elevation: 0,
                                      insetPadding: EdgeInsets.zero,
                                      backgroundColor: Colors.transparent,
                                      alignment: const AlignmentDirectional(0.0, 0.0)
                                          .resolve(Directionality.of(context)),
                                      child: const GeneralPopupWidget(
                                        alertTitle: 'Something went wrong',
                                        alertText:
                                            'Please try again <NAME_EMAIL>',
                                      ),
                                    );
                                  },
                                );

                                if (shouldSetState) safeSetState(() {});
                                return;
                              }
                            } else {
                              await showDialog(
                                context: context,
                                builder: (dialogContext) {
                                  return Dialog(
                                    elevation: 0,
                                    insetPadding: EdgeInsets.zero,
                                    backgroundColor: Colors.transparent,
                                    alignment: const AlignmentDirectional(0.0, 0.0)
                                        .resolve(Directionality.of(context)),
                                    child: const GeneralPopupWidget(
                                      alertTitle: 'Something went wrong',
                                      alertText:
                                          'Please try again <NAME_EMAIL>',
                                    ),
                                  );
                                },
                              );

                              if (shouldSetState) safeSetState(() {});
                              return;
                            }

                            if (shouldSetState) safeSetState(() {});
                          },
                        ),
                      ),
                    ),
                  ],
                  carouselController: _model.carouselController ??=
                      CarouselController(),
                  options: CarouselOptions(
                    initialPage: 1,
                    viewportFraction: 0.7,
                    disableCenter: true,
                    enlargeCenterPage: true,
                    enlargeFactor: 0.25,
                    enableInfiniteScroll: false,
                    scrollDirection: Axis.horizontal,
                    autoPlay: false,
                    onPageChanged: (index, _) =>
                        _model.carouselCurrentIndex = index,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
