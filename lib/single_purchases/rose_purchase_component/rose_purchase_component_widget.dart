import '/auth/firebase_auth/auth_util.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/backend/schema/enums/enums.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/single_purchases/single_purchase_spotlight_rose/single_purchase_spotlight_rose_widget.dart';
import '/single_purchases/single_purchase_subscription_suggestion_evolved/single_purchase_subscription_suggestion_evolved_widget.dart';
import '/flutter_flow/revenue_cat_util.dart' as revenue_cat;
import 'package:carousel_slider/carousel_slider.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'rose_purchase_component_model.dart';
export 'rose_purchase_component_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';
import '/discovery/discovery/utils/daily_like_limit.dart';

class RosePurchaseComponentWidget extends StatefulWidget {
  const RosePurchaseComponentWidget({super.key});

  @override
  State<RosePurchaseComponentWidget> createState() =>
      _RosePurchaseComponentWidgetState();
}

class _RosePurchaseComponentWidgetState
    extends State<RosePurchaseComponentWidget> with DailyLikeLimit, WeeklyLikeLimit, LikeLimitInterpolator {
  late RosePurchaseComponentModel _model;
  var currency = revenue_cat
                                                        .offerings!.current!
                                                        .getPackage('chyrpe_roses_3')!
                                                        .storeProduct
                                                        .currencyCode;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => RosePurchaseComponentModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.sizeOf(context).width * 1.0,
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).primaryBackground,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(0.0),
          bottomRight: Radius.circular(0.0),
          topLeft: Radius.circular(10.0),
          topRight: Radius.circular(10.0),
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(0.0, 20.0, 20.0, 0.0),
                  child: FlutterFlowIconButton(
                    borderRadius: 100.0,
                    buttonSize: 35.0,
                    fillColor: const Color(0x00353535),
                    icon: Icon(
                      Icons.close_rounded,
                      color: FlutterFlowTheme.of(context).primaryText,
                      size: 18.0,
                    ),
                    onPressed: () async {
                      Navigator.pop(context);
                    },
                  ),
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(58.0, 0.0, 58.0, 0.0),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  Text(
                    getRemoteConfigString('iap_rose_purchase_title'),
                    textAlign: TextAlign.center,
                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                          fontFamily: 'BT Beau Sans',
                          fontSize: 26.0,
                          letterSpacing: 0.0,
                          fontWeight: FontWeight.bold,
                          useGoogleFonts: false,
                          lineHeight: 1.2,
                        ),
                  ),
                  Text(
                    getRemoteConfigString('iap_rose_purchase_description'),
                    textAlign: TextAlign.center,
                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                          fontFamily: 'BT Beau Sans',
                          letterSpacing: 0.0,
                          useGoogleFonts: false,
                          lineHeight: 1.2,
                        ),
                  ),
                ].divide(const SizedBox(height: 28.0)),
              ),
            ),
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(0.0, 42.0, 0.0, 0.0),
              child: SizedBox(
                width: double.infinity,
                height: 300.0,
                child: CarouselSlider(
                  items: [
                    Builder(
                      builder: (context) => wrapWithModel(
                        model: _model.singlePurchaseSpotlightRoseModel1,
                        updateCallback: () => safeSetState(() {}),
                        child: SinglePurchaseSpotlightRoseWidget(
                          bigTitle: '3 Roses',
                          smallCaption: '$currency ${formatNumber(
                            revenue_cat.offerings!.current!
                                    .getPackage('chyrpe_roses_3')!
                                    .storeProduct
                                    .price /
                                3,
                            formatType: FormatType.custom,
                            format: '##.##',
                            locale: '',

                          )} each',
                          showSave: false,
                          saveValue: '0',
                          btnTitle:
                              'Get 3 for ${revenue_cat.offerings!.current!.getPackage('chyrpe_roses_3')!.storeProduct.priceString}',
                          btnAction: () async {
                            var shouldSetState = false;
                            _model.purchase = await revenue_cat
                                .purchasePackage('chyrpe_roses_3');
                            shouldSetState = true;
                            if (_model.purchase!) {
                              try {
                                final result =
                                    await FirebaseFunctions.instanceFor(
                                            region: 'europe-west2')
                                        .httpsCallable('syncAfterOneTimePurchase')
                                        .call({});
                                _model.syncAfterOneTimePurchase =
                                    SyncAfterOneTimePurchaseCloudFunctionCallResponse(
                                  succeeded: true,
                                );
                              } on FirebaseFunctionsException catch (error) {
                                _model.syncAfterOneTimePurchase =
                                    SyncAfterOneTimePurchaseCloudFunctionCallResponse(
                                  errorCode: error.code,
                                  succeeded: false,
                                );
                              }
        
                              shouldSetState = true;
                              if (_model.syncAfterOneTimePurchase!.succeeded!) {
                                Navigator.pop(context);
                                if (shouldSetState) safeSetState(() {});
                                return;
                              } else {
                                await showDialog(
                                  context: context,
                                  builder: (dialogContext) {
                                    return Dialog(
                                      elevation: 0,
                                      insetPadding: EdgeInsets.zero,
                                      backgroundColor: Colors.transparent,
                                      alignment: const AlignmentDirectional(0.0, 0.0)
                                          .resolve(Directionality.of(context)),
                                      child: const GeneralPopupWidget(
                                        alertTitle: 'Something went wrong',
                                        alertText:
                                            'Please try again <NAME_EMAIL>',
                                      ),
                                    );
                                  },
                                );
        
                                if (shouldSetState) safeSetState(() {});
                                return;
                              }
                            } else {
                              await showDialog(
                                context: context,
                                builder: (dialogContext) {
                                  return Dialog(
                                    elevation: 0,
                                    insetPadding: EdgeInsets.zero,
                                    backgroundColor: Colors.transparent,
                                    alignment: const AlignmentDirectional(0.0, 0.0)
                                        .resolve(Directionality.of(context)),
                                    child: const GeneralPopupWidget(
                                      alertTitle: 'Something went wrong',
                                      alertText:
                                          'Please try again <NAME_EMAIL>',
                                    ),
                                  );
                                },
                              );
        
                              if (shouldSetState) safeSetState(() {});
                              return;
                            }
        
                            if (shouldSetState) safeSetState(() {});
                          },
                        ),
                      ),
                    ),
                    Builder(
                      builder: (context) => wrapWithModel(
                        model: _model.singlePurchaseSpotlightRoseModel2,
                        updateCallback: () => safeSetState(() {}),
                        child: SinglePurchaseSpotlightRoseWidget(
                          bigTitle: '12 Roses',
                          smallCaption: '$currency ${formatNumber(
                            revenue_cat.offerings!.current!
                                    .getPackage('chyrpe_roses_12')!
                                    .storeProduct
                                    .price /
                                12,
                            formatType: FormatType.custom,
                            format: '##.##',
                            locale: '',
                          )} each',
                          showSave: true,
                          saveValue: formatNumber(
                            100 - ((revenue_cat.offerings!.current!
                                            .getPackage('chyrpe_roses_12')!
                                            .storeProduct
                                            .price /
                                        12) /
                                    (revenue_cat.offerings!.current!
                                            .getPackage('chyrpe_roses_3')!
                                            .storeProduct
                                            .price /
                                        3)) *
                                100,
                            formatType: FormatType.custom,
                            format: '##',
                            locale: '',
                          ),
                          btnTitle:
                              'Get 12 for ${revenue_cat.offerings!.current!.getPackage('chyrpe_roses_12')!.storeProduct.priceString}',
                          btnAction: () async {
                            var shouldSetState = false;
                            _model.purchase2 = await revenue_cat
                                .purchasePackage('chyrpe_roses_12');
                            shouldSetState = true;
                            if (_model.purchase2!) {
                              try {
                                final result =
                                    await FirebaseFunctions.instanceFor(
                                            region: 'europe-west2')
                                        .httpsCallable('syncAfterOneTimePurchase')
                                        .call({});
                                _model.syncAfterOneTimePurchase2 =
                                    SyncAfterOneTimePurchaseCloudFunctionCallResponse(
                                  succeeded: true,
                                );
                              } on FirebaseFunctionsException catch (error) {
                                _model.syncAfterOneTimePurchase2 =
                                    SyncAfterOneTimePurchaseCloudFunctionCallResponse(
                                  errorCode: error.code,
                                  succeeded: false,
                                );
                              }
        
                              shouldSetState = true;
                              if (_model.syncAfterOneTimePurchase2!.succeeded!) {
                                Navigator.pop(context);
                                if (shouldSetState) safeSetState(() {});
                                return;
                              } else {
                                await showDialog(
                                  context: context,
                                  builder: (dialogContext) {
                                    return Dialog(
                                      elevation: 0,
                                      insetPadding: EdgeInsets.zero,
                                      backgroundColor: Colors.transparent,
                                      alignment: const AlignmentDirectional(0.0, 0.0)
                                          .resolve(Directionality.of(context)),
                                      child: const GeneralPopupWidget(
                                        alertTitle: 'Something went wrong',
                                        alertText:
                                            'Please try again <NAME_EMAIL>',
                                      ),
                                    );
                                  },
                                );
        
                                if (shouldSetState) safeSetState(() {});
                                return;
                              }
                            } else {
                              await showDialog(
                                context: context,
                                builder: (dialogContext) {
                                  return Dialog(
                                    elevation: 0,
                                    insetPadding: EdgeInsets.zero,
                                    backgroundColor: Colors.transparent,
                                    alignment: const AlignmentDirectional(0.0, 0.0)
                                        .resolve(Directionality.of(context)),
                                    child: const GeneralPopupWidget(
                                      alertTitle: 'Something went wrong',
                                      alertText:
                                          'Please try again <NAME_EMAIL>',
                                    ),
                                  );
                                },
                              );
        
                              if (shouldSetState) safeSetState(() {});
                              return;
                            }
        
                            if (shouldSetState) safeSetState(() {});
                          },
                        ),
                      ),
                    ),
                    Builder(
                      builder: (context) => wrapWithModel(
                        model: _model.singlePurchaseSpotlightRoseModel3,
                        updateCallback: () => safeSetState(() {}),
                        child: SinglePurchaseSpotlightRoseWidget(
                          bigTitle: '25 Roses',
                          smallCaption: '$currency ${formatNumber(
                            revenue_cat.offerings!.current!
                                    .getPackage('chyrpe_roses_25')!
                                    .storeProduct
                                    .price /
                                25,
                            formatType: FormatType.custom,
                            format: '##.##',
                            locale: '',
                          )} each',
                          showSave: true,
                          saveValue: formatNumber(
                            100 - ((revenue_cat.offerings!.current!
                                            .getPackage('chyrpe_roses_25')!
                                            .storeProduct
                                            .price /
                                        25) /
                                    (revenue_cat.offerings!.current!
                                            .getPackage('chyrpe_roses_3')!
                                            .storeProduct
                                            .price /
                                        3)) *
                                100,
                            formatType: FormatType.custom,
                            format: '##',
                            locale: '',
                          ),
                          btnTitle:
                              'Get 25 for ${revenue_cat.offerings!.current!.getPackage('chyrpe_roses_25')!.storeProduct.priceString}',
                          btnAction: () async {
                            var shouldSetState = false;
                            _model.purchase3 = await revenue_cat
                                .purchasePackage('chyrpe_roses_25');
                            shouldSetState = true;
                            if (_model.purchase3!) {
                              try {
                                final result =
                                    await FirebaseFunctions.instanceFor(
                                            region: 'europe-west2')
                                        .httpsCallable('syncAfterOneTimePurchase')
                                        .call({});
                                _model.syncAfterOneTimePurchase3 =
                                    SyncAfterOneTimePurchaseCloudFunctionCallResponse(
                                  succeeded: true,
                                );
                              } on FirebaseFunctionsException catch (error) {
                                _model.syncAfterOneTimePurchase3 =
                                    SyncAfterOneTimePurchaseCloudFunctionCallResponse(
                                  errorCode: error.code,
                                  succeeded: false,
                                );
                              }
        
                              shouldSetState = true;
                              if (_model.syncAfterOneTimePurchase3!.succeeded!) {
                                Navigator.pop(context);
                                if (shouldSetState) safeSetState(() {});
                                return;
                              } else {
                                await showDialog(
                                  context: context,
                                  builder: (dialogContext) {
                                    return Dialog(
                                      elevation: 0,
                                      insetPadding: EdgeInsets.zero,
                                      backgroundColor: Colors.transparent,
                                      alignment: const AlignmentDirectional(0.0, 0.0)
                                          .resolve(Directionality.of(context)),
                                      child: const GeneralPopupWidget(
                                        alertTitle: 'Something went wrong',
                                        alertText:
                                            'Please try again <NAME_EMAIL>',
                                      ),
                                    );
                                  },
                                );
        
                                if (shouldSetState) safeSetState(() {});
                                return;
                              }
                            } else {
                              await showDialog(
                                context: context,
                                builder: (dialogContext) {
                                  return Dialog(
                                    elevation: 0,
                                    insetPadding: EdgeInsets.zero,
                                    backgroundColor: Colors.transparent,
                                    alignment: const AlignmentDirectional(0.0, 0.0)
                                        .resolve(Directionality.of(context)),
                                    child: const GeneralPopupWidget(
                                      alertTitle: 'Something went wrong',
                                      alertText:
                                          'Please try again <NAME_EMAIL>',
                                    ),
                                  );
                                },
                              );
        
                              if (shouldSetState) safeSetState(() {});
                              return;
                            }
        
                            if (shouldSetState) safeSetState(() {});
                          },
                        ),
                      ),
                    ),
                  ],
                  carouselController: _model.carouselController ??=
                      CarouselController(),
                  options: CarouselOptions(
                    initialPage: 1,
                    viewportFraction: 0.7,
                    disableCenter: true,
                    enlargeCenterPage: true,
                    enlargeFactor: 0.25,
                    enableInfiniteScroll: false,
                    scrollDirection: Axis.horizontal,
                    autoPlay: false,
                    onPageChanged: (index, _) =>
                        _model.carouselCurrentIndex = index,
                  ),
                ),
              ),
            ),
            if (!(revenue_cat.activeEntitlementIds.contains('evolved_access') || revenue_cat.activeEntitlementIds.contains('divine_access')))
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(15.0, 50.0, 15.0, 0.0),
                child: wrapWithModel(
                  model:
                      _model.singlePurchaseSubscriptionSuggestionEvolvedModel,
                  updateCallback: () => safeSetState(() {}),
                  child: SinglePurchaseSubscriptionSuggestionEvolvedWidget(
                    tagline: (currentUserDocument?.gender == Gender.Female &&
      valueOrDefault(currentUserDocument?.fiveTestGroup, 0) >
          valueOrDefault(getRemoteConfigInt('divine_legacy_t'), 0)) ? interpolateLikeLimitPlaceholders(getRemoteConfigString('iap_purchase_eupgrade_tagline_divine_placeholder')) :
                        interpolateLikeLimitPlaceholders(getRemoteConfigString('iap_purchase_eupgrade_tagline_placeholder')),
                    btnTitle:
                    (currentUserDocument?.gender == Gender.Female &&
      valueOrDefault(currentUserDocument?.fiveTestGroup, 0) >
          valueOrDefault(getRemoteConfigInt('divine_legacy_t'), 0)) ?
                      'Divine from $currency ${(revenue_cat.offerings!.current!.getPackage('chyrpe_divine_3m')!.storeProduct.price / 12).toStringAsFixed(2)}/week' : 'Evolved from $currency ${(revenue_cat.offerings!.current!.getPackage('chyrpe_evolved_6m')!.storeProduct.price / 12).toStringAsFixed(2)}/week',
                    btnAction: () async {
                      Navigator.pop(context);
                      if (!revenue_cat.activeEntitlementIds
                              .contains('evolved_access') &&
                          revenue_cat.activeEntitlementIds
                              .contains('gold_access')) {
                                if (currentUserDocument?.gender == Gender.Female && 
                                              valueOrDefault(currentUserDocument?.fiveTestGroup, 0) > valueOrDefault(getRemoteConfigInt('divine_legacy_t'), 0)) {
                                              analytics.logEvent('Navigated to Divine from Roses Purchase');
                                              context.pushNamed(
                                                'DivineSubscription',
                                                extra: <String, dynamic>{
                                                  kTransitionInfoKey: const TransitionInfo(
                                                    hasTransition: true,
                                                    transitionType: PageTransitionType.bottomToTop,
                                                    duration: Duration(milliseconds: 200),
                                                  ),
                                                },
                                              );
                                              return;
                                            }
                                try {
                       analytics.logEvent('Navigated to Evolved from Roses Purchase');
                       } catch(e) {}
                        context.pushNamed('EvolvedSubscriptionNew');
                      } else {
                        try {
                       analytics.logEvent('Navigated to Gold Evolved from Roses Purchase');
                       } catch(e) {}
                        context.pushNamed(
                          'GoldEvolvedSubscriptionNew',
                          queryParameters: {
                            'initialSubscription': serializeParam(
                              'Evolved',
                              ParamType.String,
                            ),
                          }.withoutNulls,
                        );
                      }
                    },
                  ),
                ),
              ),
          ].addToEnd(const SizedBox(height: 50.0)),
        ),
      ),
    );
  }
}
