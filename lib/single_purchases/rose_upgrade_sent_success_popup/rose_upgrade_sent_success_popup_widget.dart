import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:rive/rive.dart' hide LinearGradient;
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'rose_upgrade_sent_success_popup_model.dart';
export 'rose_upgrade_sent_success_popup_model.dart';

class RoseUpgradeSentSuccessPopupWidget extends StatefulWidget {
  const RoseUpgradeSentSuccessPopupWidget({super.key});

  @override
  State<RoseUpgradeSentSuccessPopupWidget> createState() =>
      _RoseUpgradeSentSuccessPopupWidgetState();
}

class _RoseUpgradeSentSuccessPopupWidgetState
    extends State<RoseUpgradeSentSuccessPopupWidget> {
  late RoseUpgradeSentSuccessPopupModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => RoseUpgradeSentSuccessPopupModel());

    // On component load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      await Future.delayed(const Duration(milliseconds: 3000));
      Navigator.pop(context);
    });
  
  }
  
  late StateMachineController _controller;
  void _onInit(Artboard art) {
  var ctrl = StateMachineController.fromArtboard(art, 'State Machine 1');
  if (ctrl != null) {
    art.addController(ctrl);
    _controller = ctrl;
    _controller.isActive = true;
    
      }
     // Keep it inactive initially
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 300.0,
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).primaryBackground,
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Padding(
        padding: const EdgeInsetsDirectional.fromSTEB(10.0, 20.0, 10.0, 30.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 55.0,
              height: 55.0,
              child: RiveAnimation.asset(
                'assets/rive_animations/rose_animation.riv',
                artboard: 'rose artborad',
                fit: BoxFit.cover,
                onInit: _onInit,
              ),
            ),
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(0.0, 5.0, 0.0, 0.0),
              child: Text(
                getRemoteConfigString('iap_rose_confirmation_title'),
                textAlign: TextAlign.center,
                style: FlutterFlowTheme.of(context).bodyMedium.override(
                      fontFamily: 'BT Beau Sans',
                      color: FlutterFlowTheme.of(context).primaryText,
                      fontSize: 14.0,
                      letterSpacing: 0.0,
                      fontWeight: FontWeight.bold,
                      useGoogleFonts: false,
                    ),
              ),
            ),
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(0.0, 5.0, 0.0, 0.0),
              child: Text(
                getRemoteConfigString('iap_rose_confirmation_subtitle'),
                style: FlutterFlowTheme.of(context).bodyMedium.override(
                      fontFamily: 'BT Beau Sans',
                      color: FlutterFlowTheme.of(context).primaryText,
                      fontSize: 12.0,
                      letterSpacing: 0.0,
                      useGoogleFonts: false,
                    ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
