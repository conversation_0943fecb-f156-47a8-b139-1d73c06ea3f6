import '/auth/firebase_auth/auth_util.dart';
import '/backend/schema/enums/enums.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/revenue_cat_util.dart' as revenue_cat;
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'roses_profile_home_widget_model.dart';
export 'roses_profile_home_widget_model.dart';

class RosesProfileHomeWidgetWidget extends StatefulWidget {
  const RosesProfileHomeWidgetWidget({
    super.key,
    required this.callback,
  });

  final Future Function()? callback;

  @override
  State<RosesProfileHomeWidgetWidget> createState() =>
      _RosesProfileHomeWidgetWidgetState();
}

class _RosesProfileHomeWidgetWidgetState
    extends State<RosesProfileHomeWidgetWidget> {
  late RosesProfileHomeWidgetModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => RosesProfileHomeWidgetModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return InkWell(
      splashColor: Colors.transparent,
      focusColor: Colors.transparent,
      hoverColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: () async {
        await widget.callback?.call();
      },
      child: Container(
        width: MediaQuery.sizeOf(context).width * 1.0,
        decoration: BoxDecoration(
          color: FlutterFlowTheme.of(context).primaryBackground,
          borderRadius: BorderRadius.circular(12.0),
          border: Border.all(
            color: const Color(0xC3C3C3C3),
          ),
        ),
        child: Padding(
          padding: const EdgeInsetsDirectional.fromSTEB(12.0, 10.0, 12.0, 10.0),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            children: [
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 13.0, 0.0),
                child: Stack(
                  alignment: const AlignmentDirectional(1.0, -1.0),
                  children: [
                    Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 10.0, 0.0),
                      child: Container(
                        width: 47.0,
                        height: 47.0,
                        decoration: const BoxDecoration(
                          color: Color(0xFFCB3D8E),
                          shape: BoxShape.circle,
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8.0),
                          child: SvgPicture.asset(
                            'assets/images/Artboard_1_12ThickRose2_2.svg',
                            width: 37.0,
                            height: 37.0,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: 18.0,
                      height: 18.0,
                      decoration: BoxDecoration(
                        color: const Color(0xFFCB3D8E),
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: FlutterFlowTheme.of(context).primaryBackground,
                          width: 2.0,
                        ),
                      ),
                      alignment: const AlignmentDirectional(0.0, 0.0),
                      child: Align(
                        alignment: const AlignmentDirectional(0.0, 0.0),
                        child: AuthUserStreamWidget(
                          builder: (context) => Text(
                            () {
                              if ((currentUserDocument
                                                              ?.gender ==
                                                          Gender.Female) &&
                                                      revenue_cat
                                                          .activeEntitlementIds
                                                          .contains(
                                                              'divine_access')) {
                                                               return (valueOrDefault(
                                            currentUserDocument
                                                ?.aEvolvedLikesLeft,
                                            0) +
                                        getRemoteConfigInt(
                                            'weekly_elikes_divine') -
                                        (FFAppState().mondayString ==
                                                valueOrDefault(
                                                    currentUserDocument
                                                        ?.latestWeekOfEvolvedLinkingString,
                                                    '')
                                            ? valueOrDefault(
                                                currentUserDocument
                                                    ?.evolvedLikesDoneThisWeek,
                                                0)
                                            : 0))
                                    .toString();
                                                              } else
                              if (revenue_cat.activeEntitlementIds
                                  .contains('evolved_access')) {
                                return (valueOrDefault(
                                            currentUserDocument
                                                ?.aEvolvedLikesLeft,
                                            0) +
                                        getRemoteConfigInt(
                                            'weekly_elikes_evolved') -
                                        (FFAppState().mondayString ==
                                                valueOrDefault(
                                                    currentUserDocument
                                                        ?.latestWeekOfEvolvedLinkingString,
                                                    '')
                                            ? valueOrDefault(
                                                currentUserDocument
                                                    ?.evolvedLikesDoneThisWeek,
                                                0)
                                            : 0))
                                    .toString();
                              } else if (revenue_cat.activeEntitlementIds
                                  .contains('gold_access')) {
                                return (valueOrDefault(
                                            currentUserDocument
                                                ?.aEvolvedLikesLeft,
                                            0) +
                                        getRemoteConfigInt(
                                            'weekly_elikes_gold') -
                                        (FFAppState().mondayString ==
                                                valueOrDefault(
                                                    currentUserDocument
                                                        ?.latestWeekOfEvolvedLinkingString,
                                                    '')
                                            ? valueOrDefault(
                                                currentUserDocument
                                                    ?.evolvedLikesDoneThisWeek,
                                                0)
                                            : 0))
                                    .toString();
                              } else {
                                return valueOrDefault(
                                        currentUserDocument?.aEvolvedLikesLeft,
                                        0)
                                    .toString();
                              }
                            }(),
                            style: FlutterFlowTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'BT Beau Sans',
                                  color: FlutterFlowTheme.of(context)
                                      .primaryBackground,
                                  fontSize: 9.0,
                                  letterSpacing: 0.0,
                                  fontWeight: FontWeight.bold,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Roses',
                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                          fontFamily: 'BT Beau Sans',
                          letterSpacing: 0.0,
                          fontWeight: FontWeight.bold,
                          useGoogleFonts: false,
                        ),
                  ),
                  Text(
                    getRemoteConfigString('iap_roses_home_tagline'),
                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                          fontFamily: 'BT Beau Sans',
                          letterSpacing: 0.0,
                          fontWeight: FontWeight.normal,
                          useGoogleFonts: false,
                        ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
