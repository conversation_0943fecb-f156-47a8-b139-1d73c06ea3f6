import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'single_purchase_spotlight_swipe_reset_model.dart';
export 'single_purchase_spotlight_swipe_reset_model.dart';

class SinglePurchaseSpotlightSwipeResetWidget extends StatefulWidget {
  const SinglePurchaseSpotlightSwipeResetWidget({
    super.key,
    required this.bigTitle,
    required this.smallCaption,
    required this.showSave,
    required this.saveValue,
    required this.btnTitle,
    required this.btnAction,
  });

  final String? bigTitle;
  final String? smallCaption;
  final bool? showSave;
  final String? saveValue;
  final String? btnTitle;
  final Future Function()? btnAction;

  @override
  State<SinglePurchaseSpotlightSwipeResetWidget> createState() =>
      _SinglePurchaseSpotlightSwipeResetWidgetState();
}

class _SinglePurchaseSpotlightSwipeResetWidgetState
    extends State<SinglePurchaseSpotlightSwipeResetWidget> {
  late SinglePurchaseSpotlightSwipeResetModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model =
        createModel(context, () => SinglePurchaseSpotlightSwipeResetModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Padding(
          padding: const EdgeInsetsDirectional.fromSTEB(0.0, 11.0, 0.0, 0.0),
          child: Container(
            width: MediaQuery.sizeOf(context).width * 1.0,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12.0),
              border: Border.all(
                color: const Color(0xFFC3C3C3),
              ),
            ),
            child: Padding(
              padding:
                  const EdgeInsetsDirectional.fromSTEB(25.0, 30.0, 25.0, 25.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    valueOrDefault<String>(
                      widget.bigTitle,
                      'Roses',
                    ),
                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                          fontFamily: 'BT Beau Sans',
                          fontSize: 22.0,
                          letterSpacing: 0.0,
                          fontWeight: FontWeight.bold,
                          useGoogleFonts: false,
                        ),
                  ),
                  Padding(
                    padding: const EdgeInsetsDirectional.fromSTEB(
                        0.0, 15.0, 0.0, 0.0),
                    child: FaIcon(
                      FontAwesomeIcons.undoAlt,
                      color: FlutterFlowTheme.of(context).primaryText,
                      size: 56.0,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsetsDirectional.fromSTEB(
                        0.0, 15.0, 0.0, 35.0),
                    child: Text(
                      valueOrDefault<String>(
                        widget.smallCaption,
                        'Price',
                      ),
                      style: FlutterFlowTheme.of(context).bodyMedium.override(
                            fontFamily: 'BT Beau Sans',
                            fontSize: 15.0,
                            letterSpacing: 0.0,
                            fontWeight: FontWeight.bold,
                            useGoogleFonts: false,
                          ),
                    ),
                  ),
                  FFButtonWidget(
                    onPressed: () async {
                      await widget.btnAction?.call();
                    },
                    text: widget.btnTitle!,
                    options: FFButtonOptions(
                      width: MediaQuery.sizeOf(context).width * 1.0,
                      height: 53.0,
                      padding: const EdgeInsetsDirectional.fromSTEB(
                          16.0, 0.0, 16.0, 0.0),
                      iconPadding: const EdgeInsetsDirectional.fromSTEB(
                          0.0, 0.0, 0.0, 0.0),
                      color: const Color(0xFF7A8690),
                      textStyle:
                          FlutterFlowTheme.of(context).titleSmall.override(
                                fontFamily: 'BT Beau Sans',
                                color: Colors.white,
                                fontSize: 12.0,
                                letterSpacing: 0.0,
                                fontWeight: FontWeight.bold,
                                useGoogleFonts: false,
                              ),
                      elevation: 0.0,
                      borderRadius: BorderRadius.circular(100.0),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
