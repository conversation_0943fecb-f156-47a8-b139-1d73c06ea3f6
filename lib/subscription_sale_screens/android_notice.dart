import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';

class AndroidNotice extends StatelessWidget {
  const AndroidNotice({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ((!isiOS) &&
            getRemoteConfigBool('subscription_disclaimer_android_show'))
        ? Text(getRemoteConfigString("subscription_disclaimer_android"),
            textAlign: TextAlign.center,
            style: FlutterFlowTheme.of(context).bodySmall.override(
                  fontFamily: 'BT Beau Sans',
                  color: FlutterFlowTheme.of(context).primaryText,
                  fontSize: 10.0,
                  letterSpacing: 0.0,
                  fontWeight: FontWeight.normal,
                  fontStyle: FontStyle.normal,
                  useGoogleFonts: false,
                ))
        : Container();
  }
}
