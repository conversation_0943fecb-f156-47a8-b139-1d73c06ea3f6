import '/backend/schema/enums/enums.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/info_sheet_scrollable/info_sheet_scrollable_widget.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import '/flutter_flow/revenue_cat_util.dart' as revenue_cat;
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';
import 'chyrpe_standard_new_model.dart';
export 'chyrpe_standard_new_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';

class ChyrpeStandardNewWidget extends StatefulWidget {
  const ChyrpeStandardNewWidget({super.key});

  @override
  State<ChyrpeStandardNewWidget> createState() =>
      _ChyrpeStandardNewWidgetState();
}

class _ChyrpeStandardNewWidgetState extends State<ChyrpeStandardNewWidget> {
  late ChyrpeStandardNewModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => ChyrpeStandardNewModel());
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _model.unfocusNode.canRequestFocus
          ? FocusScope.of(context).requestFocus(_model.unfocusNode)
          : FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        body: Stack(
          children: [
            Container(
              width: MediaQuery.sizeOf(context).width * 1.0,
              height: MediaQuery.sizeOf(context).height * 1.0,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [Color(0xFFFF9EDE), Color(0xFF91B2FB)],
                  stops: [0.0, 1.0],
                  begin: AlignmentDirectional(-1.0, 0.0),
                  end: AlignmentDirectional(1.0, 0),
                ),
              ),
            ),
            Container(
              width: MediaQuery.sizeOf(context).width * 1.0,
              height: MediaQuery.sizeOf(context).height * 1.0,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [Color(0x00FFFFFF), Colors.white],
                  stops: [0.0, 0.2],
                  begin: AlignmentDirectional(0.0, -1.0),
                  end: AlignmentDirectional(0, 1.0),
                ),
              ),
            ),
            Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(
                      20.0, 70.0, 20.0, 0.0),
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      FlutterFlowIconButton(
                        borderColor: Colors.transparent,
                        borderRadius: 20.0,
                        borderWidth: 0.0,
                        buttonSize: 40.0,
                        icon: Icon(
                          Icons.close_rounded,
                          color: FlutterFlowTheme.of(context).primaryText,
                          size: 24.0,
                        ),
                        onPressed: () async {
                          context.safePop();
                        },
                      ),
                    ],
                  ),
                ),
                Flexible(
                  child: Container(
                    decoration: const BoxDecoration(),
                    child: Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(
                          40.0, 0.0, 40.0, 0.0),
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 10.0, 0.0, 0.0),
                              child: Text(
                                getRemoteConfigString(
                                    'chyrpe_standard_new_headline'),
                                textAlign: TextAlign.center,
                                style: FlutterFlowTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'BT Beau Sans',
                                      fontSize: 27.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.bold,
                                      useGoogleFonts: false,
                                      lineHeight: 1.29,
                                    ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 35.0, 0.0, 0.0),
                              child: Builder(
                                builder: (context) {
                                  final benefitListVar = functions
                                      .getStringListFromJson(getRemoteConfigString(
                                          'freepaid_benefitlist_standard_benefitList'))
                                      .toList();

                                  return ListView.separated(
                                    padding: EdgeInsets.zero,
                                    primary: false,
                                    shrinkWrap: true,
                                    scrollDirection: Axis.vertical,
                                    itemCount: benefitListVar.length,
                                    separatorBuilder: (_, __) =>
                                        const SizedBox(height: 20.0),
                                    itemBuilder:
                                        (context, benefitListVarIndex) {
                                      final benefitListVarItem =
                                          benefitListVar[benefitListVarIndex];
                                      return Row(
                                        mainAxisSize: MainAxisSize.max,
                                        children: [
                                          Padding(
                                              padding:
                                                  const EdgeInsetsDirectional
                                                      .fromSTEB(
                                                      0.0, 0.0, 20.0, 0.0),
                                              child: ShaderMask(
                                                shaderCallback: (Rect bounds) =>
                                                    const LinearGradient(
                                                  begin: Alignment.centerLeft,
                                                  end: Alignment.centerRight,
                                                  colors: [
                                                    Color.fromRGBO(
                                                        255, 158, 222, 1),
                                                    Color.fromRGBO(
                                                        145, 178, 251, 1)
                                                  ],
                                                  tileMode: TileMode.mirror,
                                                ).createShader(bounds),
                                                child: const FaIcon(
                                                  FontAwesomeIcons.checkCircle,
                                                  color: Colors.white,
                                                  size: 28.0,
                                                ),
                                              )),
                                          Flexible(
                                            child: Text(
                                              benefitListVarItem,
                                              textAlign: TextAlign.start,
                                              style:
                                                  FlutterFlowTheme.of(context)
                                                      .bodyMedium
                                                      .override(
                                                        fontFamily:
                                                            'BT Beau Sans',
                                                        fontSize: 16.0,
                                                        letterSpacing: 0.0,
                                                        useGoogleFonts: false,
                                                      ),
                                            ),
                                          ),
                                        ],
                                      );
                                    },
                                  );
                                },
                              ),
                            ),
                            Align(
                              alignment: const AlignmentDirectional(0.0, -1.0),
                              child: Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 40.0, 0.0, 30.0),
                                child: InkWell(
                                  splashColor: Colors.transparent,
                                  focusColor: Colors.transparent,
                                  hoverColor: Colors.transparent,
                                  highlightColor: Colors.transparent,
                                  onTap: () async {
                                    await revenue_cat.restorePurchases();
                                    if (revenue_cat.activeEntitlementIds
                                        .contains('standard_access')) {
                                      context.pushNamed('Discovery');
                                    }
                                  },
                                  child: Text(
                                    'Restore purchase',
                                    textAlign: TextAlign.center,
                                    style: FlutterFlowTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'BT Beau Sans',
                                          color: FlutterFlowTheme.of(context)
                                              .secondaryText,
                                          fontSize: 12.0,
                                          letterSpacing: 0.0,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                Padding(
                  padding:
                      const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 30.0),
                  child: Container(
                    width: MediaQuery.sizeOf(context).width * 1.0,
                    decoration: BoxDecoration(
                      color: FlutterFlowTheme.of(context).primaryBackground,
                      border: Border.all(
                        color: const Color(0xFFD4D8DE),
                      ),
                    ),
                    alignment: const AlignmentDirectional(0.0, 1.0),
                    child: Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(
                          14.0, 17.0, 14.0, 0.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (getRemoteConfigBool(
                              'show_chyrpe_standard_new_weekly'))
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 0.0, 0.0, 15.0),
                              child: InkWell(
                                splashColor: Colors.transparent,
                                focusColor: Colors.transparent,
                                hoverColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                onTap: () async {
                                  _model.selectedOption =
                                      ChyrpeStandardOptions.weekly;
                                  setState(() {});
                                },
                                child: Container(
                                  width: MediaQuery.sizeOf(context).width * 1.0,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(11.0),
                                    border: Border.all(
                                      color: const Color(0xFFBFC1C5),
                                    ),
                                  ),
                                  child: Padding(
                                    padding:
                                        const EdgeInsetsDirectional.fromSTEB(
                                            8.0, 20.0, 8.0, 20.0),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Flexible(
                                          child: Column(
                                            mainAxisSize: MainAxisSize.max,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                getRemoteConfigString(
                                                    'chyrpe_standard_new_weekly_title'),
                                                style: FlutterFlowTheme.of(
                                                        context)
                                                    .bodyMedium
                                                    .override(
                                                      fontFamily:
                                                          'BT Beau Sans',
                                                      color: _model
                                                                  .selectedOption ==
                                                              ChyrpeStandardOptions
                                                                  .weekly
                                                          ? FlutterFlowTheme.of(
                                                                  context)
                                                              .primaryText
                                                          : FlutterFlowTheme.of(
                                                                  context)
                                                              .secondaryText,
                                                      fontSize: 18.0,
                                                      letterSpacing: 0.0,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      useGoogleFonts: false,
                                                    ),
                                              ),
                                              if (getRemoteConfigBool(
                                                  'show_chyrpe_standard_new_weekly_subtitle'))
                                                Text(
                                                  getRemoteConfigString(
                                                      'chyrpe_standard_new_weekly_subtitle'),
                                                  textAlign: TextAlign.start,
                                                  style: FlutterFlowTheme.of(
                                                          context)
                                                      .bodyMedium
                                                      .override(
                                                        fontFamily:
                                                            'BT Beau Sans',
                                                        color:
                                                            FlutterFlowTheme.of(
                                                                    context)
                                                                .secondaryText,
                                                        fontSize: 14.0,
                                                        letterSpacing: 0.0,
                                                        fontWeight:
                                                            FontWeight.normal,
                                                        useGoogleFonts: false,
                                                      ),
                                                ),
                                            ],
                                          ),
                                        ),
                                        Row(
                                          mainAxisSize: MainAxisSize.max,
                                          children: [
                                            Text(
                                              '${revenue_cat.offerings!.current!.getPackage('chyrpe_standard_1w')!.storeProduct.priceString}/wk',
                                              style: FlutterFlowTheme.of(
                                                      context)
                                                  .bodyMedium
                                                  .override(
                                                    fontFamily: 'BT Beau Sans',
                                                    color: _model
                                                                .selectedOption ==
                                                            ChyrpeStandardOptions
                                                                .weekly
                                                        ? FlutterFlowTheme.of(
                                                                context)
                                                            .primaryText
                                                        : FlutterFlowTheme.of(
                                                                context)
                                                            .secondaryText,
                                                    fontSize: 15.0,
                                                    letterSpacing: 0.0,
                                                    fontWeight: FontWeight.bold,
                                                    useGoogleFonts: false,
                                                  ),
                                            ),
                                            if (_model.selectedOption ==
                                                ChyrpeStandardOptions.weekly)
                                              Padding(
                                                padding:
                                                    const EdgeInsetsDirectional
                                                        .fromSTEB(
                                                        18.0, 0.0, 0.0, 0.0),
                                                child: FaIcon(
                                                  FontAwesomeIcons.check,
                                                  color: FlutterFlowTheme.of(
                                                          context)
                                                      .primaryText,
                                                  size: 24.0,
                                                ),
                                              ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          if (getRemoteConfigBool(
                              'show_chyrpe_standard_new_lifetime'))
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 0.0, 0.0, 20.0),
                              child: InkWell(
                                splashColor: Colors.transparent,
                                focusColor: Colors.transparent,
                                hoverColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                onTap: () async {
                                  _model.selectedOption =
                                      ChyrpeStandardOptions.lifetime;
                                  setState(() {});
                                },
                                child: Container(
                                  width: MediaQuery.sizeOf(context).width * 1.0,
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        valueOrDefault<Color>(
                                          _model.selectedOption ==
                                                  ChyrpeStandardOptions.lifetime
                                              ? const Color(0xFFFF9EDE)
                                              : Colors.white,
                                          Colors.white,
                                        ),
                                        valueOrDefault<Color>(
                                          _model.selectedOption ==
                                                  ChyrpeStandardOptions.lifetime
                                              ? const Color(0xFF91B2FB)
                                              : Colors.white,
                                          Colors.white,
                                        )
                                      ],
                                      stops: const [0.0, 1.0],
                                      begin: const AlignmentDirectional(
                                          -1.0, -0.87),
                                      end:
                                          const AlignmentDirectional(1.0, 0.87),
                                    ),
                                    borderRadius: BorderRadius.circular(11.0),
                                    border: Border.all(
                                      color: const Color(0xFFBFC1C5),
                                    ),
                                  ),
                                  child: Padding(
                                    padding:
                                        const EdgeInsetsDirectional.fromSTEB(
                                            8.0, 20.0, 8.0, 20.0),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Flexible(
                                          child: Column(
                                            mainAxisSize: MainAxisSize.max,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              if (_model.selectedOption !=
                                                  ChyrpeStandardOptions
                                                      .lifetime)
                                                GradientText(
                                                  getRemoteConfigString(
                                                      'chyrpe_standard_new_lifetime_title'),
                                                  style: FlutterFlowTheme.of(
                                                          context)
                                                      .bodyMedium
                                                      .override(
                                                        fontFamily:
                                                            'BT Beau Sans',
                                                        fontSize: 18.0,
                                                        letterSpacing: 0.0,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        useGoogleFonts: false,
                                                      ),
                                                  colors: const [
                                                    Color(0xFFFF67CB),
                                                    Color(0xFF80A6FA)
                                                  ],
                                                  gradientDirection:
                                                      GradientDirection.ltr,
                                                  gradientType:
                                                      GradientType.linear,
                                                ),
                                              if (_model.selectedOption ==
                                                  ChyrpeStandardOptions
                                                      .lifetime)
                                                Text(
                                                  getRemoteConfigString(
                                                      'chyrpe_standard_new_lifetime_title'),
                                                  style: FlutterFlowTheme.of(
                                                          context)
                                                      .bodyMedium
                                                      .override(
                                                        fontFamily:
                                                            'BT Beau Sans',
                                                        color: Colors.white,
                                                        fontSize: 18.0,
                                                        letterSpacing: 0.0,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        useGoogleFonts: false,
                                                      ),
                                                ),
                                              if (getRemoteConfigBool(
                                                  'show_chyrpe_standard_new_lifetime_subtitle'))
                                                Align(
                                                  alignment:
                                                      const AlignmentDirectional(
                                                          1.0, 1.0),
                                                  child: Text(
                                                    getRemoteConfigString(
                                                        'chyrpe_standard_new_lifetime_subtitle'),
                                                    textAlign: TextAlign.start,
                                                    style: FlutterFlowTheme.of(
                                                            context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          color: valueOrDefault<
                                                              Color>(
                                                            _model.selectedOption ==
                                                                    ChyrpeStandardOptions
                                                                        .lifetime
                                                                ? Colors.white
                                                                : FlutterFlowTheme.of(
                                                                        context)
                                                                    .secondaryText,
                                                            Colors.white,
                                                          ),
                                                          fontSize: 14.0,
                                                          letterSpacing: 0.0,
                                                          fontWeight:
                                                              FontWeight.normal,
                                                          useGoogleFonts: false,
                                                        ),
                                                  ),
                                                ),
                                            ],
                                          ),
                                        ),
                                        Row(
                                          mainAxisSize: MainAxisSize.max,
                                          children: [
                                            if (_model.selectedOption !=
                                                ChyrpeStandardOptions.lifetime)
                                              GradientText(
                                                '${revenue_cat.offerings!.current!.getPackage('chyrpe_standard_lifetime')!.storeProduct.priceString} once',
                                                style:
                                                    FlutterFlowTheme.of(context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          color: FlutterFlowTheme
                                                                  .of(context)
                                                              .primaryText,
                                                          fontSize: 15.0,
                                                          letterSpacing: 0.0,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                          useGoogleFonts: false,
                                                        ),
                                                colors: const [
                                                  Color(0xFFFF67CB),
                                                  Color(0xFF80A6FA)
                                                ],
                                                gradientDirection:
                                                    GradientDirection.ltr,
                                                gradientType:
                                                    GradientType.linear,
                                              ),
                                            if (_model.selectedOption ==
                                                ChyrpeStandardOptions.lifetime)
                                              Text(
                                                '${revenue_cat.offerings!.current!.getPackage('chyrpe_standard_lifetime')!.storeProduct.priceString} once',
                                                style:
                                                    FlutterFlowTheme.of(context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily:
                                                              'BT Beau Sans',
                                                          color: Colors.white,
                                                          fontSize: 15.0,
                                                          letterSpacing: 0.0,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                          useGoogleFonts: false,
                                                        ),
                                              ),
                                            if (_model.selectedOption ==
                                                ChyrpeStandardOptions.lifetime)
                                              const Padding(
                                                padding: EdgeInsetsDirectional
                                                    .fromSTEB(
                                                        18.0, 0.0, 0.0, 0.0),
                                                child: FaIcon(
                                                  FontAwesomeIcons.check,
                                                  color: Colors.white,
                                                  size: 24.0,
                                                ),
                                              ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          Builder(
                            builder: (context) => Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 14.0, 0.0, 30.0),
                              child: wrapWithModel(
                                model: _model.gradientButtonModel,
                                updateCallback: () => setState(() {}),
                                child: GradientButtonWidget(
                                  title: 'Continue',
                                  action: () async {
                                    try {
                                      if (isAndroid) {
                                        analytics.logEvent(
                                            'Attempted to buy Standard Screen Android');
                                      } else {
                                        analytics.logEvent(
                                            'Attempted to buy Standard Screen iOS');
                                      }
                                      analytics.logEvent(
                                          'Attempted to buy Standard Screen');
                                    } catch (e) {}
                                    var shouldSetState = false;
                                    if (_model.selectedOption ==
                                        ChyrpeStandardOptions.weekly) {
                                      _model.weeklyPurchase =
                                          await revenue_cat.purchasePackage(
                                              'chyrpe_standard_1w');
                                      shouldSetState = true;
                                      if (!_model.weeklyPurchase!) {
                                        await showDialog(
                                          context: context,
                                          builder: (dialogContext) {
                                            return Dialog(
                                              elevation: 0,
                                              insetPadding: EdgeInsets.zero,
                                              backgroundColor:
                                                  Colors.transparent,
                                              alignment:
                                                  const AlignmentDirectional(
                                                          0.0, 0.0)
                                                      .resolve(
                                                          Directionality.of(
                                                              context)),
                                              child: GestureDetector(
                                                onTap: () =>
                                                    FocusScope.of(dialogContext)
                                                        .unfocus(),
                                                child: const GeneralPopupWidget(
                                                  alertTitle:
                                                      'Something went wrong',
                                                  alertText: 'Please try again',
                                                ),
                                              ),
                                            );
                                          },
                                        );

                                        if (shouldSetState) setState(() {});
                                        return;
                                      }
                                    } else {
                                      _model.lifetimePurchase =
                                          await revenue_cat.purchasePackage(
                                              'chyrpe_standard_lifetime');
                                      shouldSetState = true;
                                      if (!_model.lifetimePurchase!) {
                                        await showDialog(
                                          context: context,
                                          builder: (dialogContext) {
                                            return Dialog(
                                              elevation: 0,
                                              insetPadding: EdgeInsets.zero,
                                              backgroundColor:
                                                  Colors.transparent,
                                              alignment:
                                                  const AlignmentDirectional(
                                                          0.0, 0.0)
                                                      .resolve(
                                                          Directionality.of(
                                                              context)),
                                              child: GestureDetector(
                                                onTap: () =>
                                                    FocusScope.of(dialogContext)
                                                        .unfocus(),
                                                child: const GeneralPopupWidget(
                                                  alertTitle:
                                                      'Something went wrong',
                                                  alertText: 'Please try again',
                                                ),
                                              ),
                                            );
                                          },
                                        );

                                        if (shouldSetState) setState(() {});
                                        return;
                                      }
                                    }

                                    context.pushNamed(
                                      'Discovery',
                                      extra: <String, dynamic>{
                                        kTransitionInfoKey:
                                            const TransitionInfo(
                                          hasTransition: true,
                                          transitionType:
                                              PageTransitionType.topToBottom,
                                        ),
                                      },
                                    );

                                    if (shouldSetState) setState(() {});
                                  },
                                ),
                              ),
                            ),
                          ),
                          Align(
                            alignment: const AlignmentDirectional(0.0, 0.0),
                            child: FFButtonWidget(
                              onPressed: () async {
                                await showModalBottomSheet(
                                  isScrollControlled: true,
                                  backgroundColor: Colors.transparent,
                                  enableDrag: false,
                                  context: context,
                                  builder: (context) {
                                    return GestureDetector(
                                      onTap: () =>
                                          FocusScope.of(context).unfocus(),
                                      child: Padding(
                                        padding:
                                            MediaQuery.viewInsetsOf(context),
                                        child: InfoSheetScrollableWidget(
                                          title: getRemoteConfigString(
                                              'discovery_standard_view_legal_information_sheet_title'),
                                          body: () {
                                            if (_model.selectedOption ==
                                                ChyrpeStandardOptions.weekly) {
                                              return getRemoteConfigString(
                                                  'chyrpe_standard_new_weekly_disclaimer');
                                            } else if (_model.selectedOption ==
                                                ChyrpeStandardOptions
                                                    .lifetime) {
                                              return getRemoteConfigString(
                                                  'chyrpe_standard_new_lifetime_disclaimer');
                                            } else {
                                              return 'By tapping “Continue”, you will be charged. You also agree to our Terms.';
                                            }
                                          }(),
                                        ),
                                      ),
                                    );
                                  },
                                ).then((value) => safeSetState(() {}));
                              },
                              text: getRemoteConfigString(
                                  'discovery_standard_view_legal_information_button_title'),
                              options: FFButtonOptions(
                                height: 40.0,
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    24.0, 0.0, 24.0, 0.0),
                                iconPadding:
                                    const EdgeInsetsDirectional.fromSTEB(
                                        0.0, 0.0, 0.0, 0.0),
                                color: Colors.white,
                                textStyle: FlutterFlowTheme.of(context)
                                    .titleSmall
                                    .override(
                                      fontFamily: 'BT Beau Sans',
                                      color: FlutterFlowTheme.of(context)
                                          .primaryText,
                                      fontSize: 12.0,
                                      letterSpacing: 0.0,
                                      decoration: TextDecoration.underline,
                                      useGoogleFonts: false,
                                    ),
                                elevation: 0.0,
                                borderSide: const BorderSide(
                                  color: Colors.transparent,
                                  width: 0.0,
                                ),
                                borderRadius: BorderRadius.circular(100.0),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const Stack(
                  children: [],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
