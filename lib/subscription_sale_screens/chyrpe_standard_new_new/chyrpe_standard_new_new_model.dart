import 'package:chyrpe/general/gradient_button_flex_flex_font/gradient_button_flex_flex_font_model.dart';

import '/backend/schema/enums/enums.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import 'chyrpe_standard_new_new_widget.dart' show ChyrpeStandardNewNewWidget;
import 'package:flutter/material.dart';

class ChyrpeStandardNewNewModel
    extends FlutterFlowModel<ChyrpeStandardNewNewWidget> {
  ///  Local state fields for this page.

  ChyrpeStandardOptions? selectedOption = ChyrpeStandardOptions.lifetime;

  ///  State fields for stateful widgets in this page.

  // Model for GradientButton component.
  late GradientButtonFlexFlexFontModel gradientButtonModel;
  // Stores action output result for [RevenueCat - Purchase] action in GradientButton widget.
  bool? weeklyPurchase;
  // Stores action output result for [RevenueCat - Purchase] action in GradientButton widget.
  bool? lifetimePurchase;

  @override
  void initState(BuildContext context) {
    gradientButtonModel = createModel(context, () => GradientButtonFlexFlexFontModel());
  }

  @override
  void dispose() {
    gradientButtonModel.dispose();
  }
}
