import 'package:chyrpe/general/gradient_button_flex_flex_font/gradient_button_flex_flex_font_widget.dart';

import '/backend/schema/enums/enums.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import '/general/info_sheet_scrollable/info_sheet_scrollable_widget.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import '/flutter_flow/revenue_cat_util.dart' as revenue_cat;
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'chyrpe_standard_new_new_model.dart';
export 'chyrpe_standard_new_new_model.dart';
import 'package:flutter/services.dart'; // Import this for vibration support
import 'package:chyrpe/amplitudeConfig.dart';
import '/subscription_sale_screens/android_notice.dart';
import '/discovery/discovery/utils/daily_like_limit.dart';

class ChyrpeStandardNewNewWidget extends StatefulWidget {
  const ChyrpeStandardNewNewWidget({super.key});

  @override
  State<ChyrpeStandardNewNewWidget> createState() =>
      _ChyrpeStandardNewNewWidgetState();
}

class _ChyrpeStandardNewNewWidgetState extends State<ChyrpeStandardNewNewWidget>
    with DailyLikeLimit, WeeklyLikeLimit, LikeLimitInterpolator {
  late ChyrpeStandardNewNewModel _model;
  final ScrollController _scrollController = ScrollController();

  final scaffoldKey = GlobalKey<ScaffoldState>();

  bool introEligible = false;

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => ChyrpeStandardNewNewModel());

    Future<bool> freeTrialEligibility() async {
      if (isiOS) {
        try {
          var introEligible =
              await Purchases.checkTrialOrIntroductoryPriceEligibility(
                  ['chy_standard_1m_241011']);
          var firstSub = introEligible.entries.first.value;
          var eligibilityStatus = firstSub.status;
          return eligibilityStatus ==
              IntroEligibilityStatus.introEligibilityStatusEligible;
        } catch (e) {
          return false;
        }
      } else if (!(isiOS)) {
        try {
          var offer = revenue_cat.offerings!.current!
              .getPackage('chyrpe_standard_1m')!
              .storeProduct
              .subscriptionOptions!;
          print(offer.toString());
          // Assuming subscriptionOptions is a list of objects with an 'id' property
          bool containsOffer =
              offer.toString().contains("chy-standard-1w-241011-free");
          return containsOffer;
        } catch (e) {
          return false;
        }
      }
      return false;
    }

    SchedulerBinding.instance.addPostFrameCallback((_) async {
      introEligible = await freeTrialEligibility();
      safeSetState(() {});
    });
  }

  String getButtonTitle() {
    if (isiOS) {
      return "Continue";
    } else if ((!isiOS)) {
      if (introEligible &&
          _model.selectedOption == ChyrpeStandardOptions.weekly) {
        return "Start 1 week trial - then ${revenue_cat.offerings!.current!.getPackage('chyrpe_standard_1m')!.storeProduct.priceString}/month";
      } else if (_model.selectedOption == ChyrpeStandardOptions.weekly) {
        return "Continue - ${revenue_cat.offerings!.current!.getPackage('chyrpe_standard_1m')!.storeProduct.priceString}/month";
      } else if (_model.selectedOption == ChyrpeStandardOptions.lifetime) {
        return "Continue - ${revenue_cat.offerings!.current!.getPackage('chyrpe_standard_lifetime')!.storeProduct.priceString} once";
      }
    }

    return "Continue";
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  void centerItem(int index) {
    double screenWidth = MediaQuery.of(context).size.width;
    double itemWidth = 145.0; // Replace with your actual item width
    double targetPosition =
        (index * itemWidth) - (screenWidth / 2) + (itemWidth / 2);

    _scrollController.animateTo(
      targetPosition,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        body: Stack(
          children: [
            Container(
              width: MediaQuery.sizeOf(context).width * 1.0,
              height: MediaQuery.sizeOf(context).height * 1.0,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [Color(0xFFFF9EDE), Color(0xFF91B2FB)],
                  stops: [0.0, 1.0],
                  begin: AlignmentDirectional(-1.0, 0.0),
                  end: AlignmentDirectional(1.0, 0),
                ),
              ),
            ),
            Container(
              width: MediaQuery.sizeOf(context).width * 1.0,
              height: MediaQuery.sizeOf(context).height * 1.0,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [Color(0x00FFFFFF), Colors.white],
                  stops: [0.0, 0.2],
                  begin: AlignmentDirectional(0.0, -1.0),
                  end: AlignmentDirectional(0, 1.0),
                ),
              ),
            ),
            Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(
                      20.0, 70.0, 20.0, 0.0),
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      FlutterFlowIconButton(
                        borderColor: Colors.transparent,
                        borderRadius: 20.0,
                        borderWidth: 0.0,
                        buttonSize: 40.0,
                        icon: Icon(
                          Icons.close_rounded,
                          color: FlutterFlowTheme.of(context).primaryText,
                          size: 24.0,
                        ),
                        onPressed: () async {
                          context.safePop();
                        },
                      ),
                    ],
                  ),
                ),
                Flexible(
                  child: Container(
                    decoration: const BoxDecoration(),
                    child: Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(
                          0.0, 0.0, 0.0, 0.0),
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  40.0, 10.0, 40.0, 0.0),
                              child: Text(
                                getRemoteConfigString(
                                    'purchases_standard_new_tagline'),
                                textAlign: TextAlign.center,
                                style: FlutterFlowTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'BT Beau Sans',
                                      fontSize: 27.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.bold,
                                      useGoogleFonts: false,
                                      lineHeight: 1.29,
                                    ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 49.0, 0.0, 0.0),
                              child: SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    InkWell(
                                      splashColor: Colors.transparent,
                                      focusColor: Colors.transparent,
                                      hoverColor: Colors.transparent,
                                      highlightColor: Colors.transparent,
                                      onTap: () async {
                                        HapticFeedback.selectionClick();
                                        centerItem(0);
                                        _model.selectedOption =
                                            ChyrpeStandardOptions.lifetime;
                                        safeSetState(() {});
                                      },
                                      child: Container(
                                        width: 145.0,
                                        height: 121.0,
                                        decoration: BoxDecoration(
                                          gradient: LinearGradient(
                                            colors: [
                                              valueOrDefault<Color>(
                                                _model.selectedOption ==
                                                        ChyrpeStandardOptions
                                                            .lifetime
                                                    ? const Color(0xFFFF9EDE)
                                                    : const Color(0xFFE5E5E5),
                                                Colors.white,
                                              ),
                                              valueOrDefault<Color>(
                                                _model.selectedOption ==
                                                        ChyrpeStandardOptions
                                                            .lifetime
                                                    ? const Color(0xFF91B2FB)
                                                    : const Color(0xFFE5E5E5),
                                                Colors.white,
                                              )
                                            ],
                                            stops: const [0.0, 1.0],
                                            begin: const AlignmentDirectional(
                                                -1.0, 0.0),
                                            end: const AlignmentDirectional(
                                                1.0, 0),
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(10.0),
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsetsDirectional
                                              .fromSTEB(3.0, 3.0, 3.0, 3.0),
                                          child: Column(
                                            mainAxisSize: MainAxisSize.max,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Flexible(
                                                child: Padding(
                                                  padding:
                                                      const EdgeInsetsDirectional
                                                          .fromSTEB(
                                                          0.0, 0.0, 0.0, 3.0),
                                                  child: Column(
                                                    mainAxisSize:
                                                        MainAxisSize.max,
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .center,
                                                    children: [
                                                      Align(
                                                        alignment:
                                                            const AlignmentDirectional(
                                                                0.0, 0.0),
                                                        child: Text(
                                                          'Buy once!',
                                                          style: FlutterFlowTheme
                                                                  .of(context)
                                                              .bodyMedium
                                                              .override(
                                                                fontFamily:
                                                                    'BT Beau Sans',
                                                                color:
                                                                    valueOrDefault<
                                                                        Color>(
                                                                  _model.selectedOption ==
                                                                          ChyrpeStandardOptions
                                                                              .lifetime
                                                                      ? Colors
                                                                          .white
                                                                      : FlutterFlowTheme.of(
                                                                              context)
                                                                          .primaryText,
                                                                  Colors.white,
                                                                ),
                                                                fontSize: 12.0,
                                                                letterSpacing:
                                                                    0.0,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w500,
                                                                useGoogleFonts:
                                                                    false,
                                                              ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                              Container(
                                                width: 142.0,
                                                height: 85.0,
                                                decoration: const BoxDecoration(
                                                  color: Colors.white,
                                                  borderRadius:
                                                      BorderRadius.only(
                                                    bottomLeft:
                                                        Radius.circular(9.0),
                                                    bottomRight:
                                                        Radius.circular(9.0),
                                                    topLeft:
                                                        Radius.circular(0.0),
                                                    topRight:
                                                        Radius.circular(0.0),
                                                  ),
                                                ),
                                                alignment:
                                                    const AlignmentDirectional(
                                                        0.0, 0.0),
                                                child: Column(
                                                  mainAxisSize:
                                                      MainAxisSize.max,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    Align(
                                                      alignment:
                                                          const AlignmentDirectional(
                                                              0.0, 0.0),
                                                      child: Text(
                                                        'Lifetime',
                                                        textAlign:
                                                            TextAlign.center,
                                                        style: FlutterFlowTheme
                                                                .of(context)
                                                            .bodyMedium
                                                            .override(
                                                              fontFamily:
                                                                  'BT Beau Sans',
                                                              fontSize: 13.0,
                                                              letterSpacing:
                                                                  0.0,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                              useGoogleFonts:
                                                                  false,
                                                            ),
                                                      ),
                                                    ),
                                                    Align(
                                                      alignment:
                                                          const AlignmentDirectional(
                                                              0.0, 0.0),
                                                      child: Text(
                                                        revenue_cat
                                                            .offerings!.current!
                                                            .getPackage(
                                                                'chyrpe_standard_lifetime')!
                                                            .storeProduct
                                                            .priceString,
                                                        style: FlutterFlowTheme
                                                                .of(context)
                                                            .bodyMedium
                                                            .override(
                                                              fontFamily:
                                                                  'BT Beau Sans',
                                                              fontSize: 16.0,
                                                              letterSpacing:
                                                                  0.0,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                              useGoogleFonts:
                                                                  false,
                                                            ),
                                                      ),
                                                    ),
                                                  ].divide(const SizedBox(
                                                      height: 2.0)),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                    InkWell(
                                      splashColor: Colors.transparent,
                                      focusColor: Colors.transparent,
                                      hoverColor: Colors.transparent,
                                      highlightColor: Colors.transparent,
                                      onTap: () async {
                                        HapticFeedback.selectionClick();
                                        centerItem(1);
                                        _model.selectedOption =
                                            ChyrpeStandardOptions.weekly;
                                        safeSetState(() {});
                                      },
                                      child: Container(
                                        width: 145.0,
                                        height: 121.0,
                                        decoration: BoxDecoration(
                                          gradient: LinearGradient(
                                            colors: [
                                              valueOrDefault<Color>(
                                                _model.selectedOption ==
                                                        ChyrpeStandardOptions
                                                            .weekly
                                                    ? const Color(0xFFFF9EDE)
                                                    : const Color(0xFFE5E5E5),
                                                Colors.white,
                                              ),
                                              valueOrDefault<Color>(
                                                _model.selectedOption ==
                                                        ChyrpeStandardOptions
                                                            .weekly
                                                    ? const Color(0xFF91B2FB)
                                                    : const Color(0xFFE5E5E5),
                                                Colors.white,
                                              )
                                            ],
                                            stops: const [0.0, 1.0],
                                            begin: const AlignmentDirectional(
                                                -1.0, 0.0),
                                            end: const AlignmentDirectional(
                                                1.0, 0),
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(10.0),
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsetsDirectional
                                              .fromSTEB(3.0, 3.0, 3.0, 3.0),
                                          child: Column(
                                            mainAxisSize: MainAxisSize.max,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Flexible(
                                                child: Padding(
                                                  padding:
                                                      const EdgeInsetsDirectional
                                                          .fromSTEB(
                                                          0.0, 0.0, 0.0, 3.0),
                                                  child: Column(
                                                    mainAxisSize:
                                                        MainAxisSize.max,
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .center,
                                                    children: [
                                                      Align(
                                                        alignment:
                                                            const AlignmentDirectional(
                                                                0.0, 0.0),
                                                        child: Text(
                                                          'Very flexible',
                                                          style: FlutterFlowTheme
                                                                  .of(context)
                                                              .bodyMedium
                                                              .override(
                                                                fontFamily:
                                                                    'BT Beau Sans',
                                                                color:
                                                                    valueOrDefault<
                                                                        Color>(
                                                                  _model.selectedOption ==
                                                                          ChyrpeStandardOptions
                                                                              .weekly
                                                                      ? Colors
                                                                          .white
                                                                      : FlutterFlowTheme.of(
                                                                              context)
                                                                          .primaryText,
                                                                  Colors.white,
                                                                ),
                                                                fontSize: 12.0,
                                                                letterSpacing:
                                                                    0.0,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w500,
                                                                useGoogleFonts:
                                                                    false,
                                                              ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                              Container(
                                                width: 142.0,
                                                height: 85.0,
                                                decoration: const BoxDecoration(
                                                  color: Colors.white,
                                                  borderRadius:
                                                      BorderRadius.only(
                                                    bottomLeft:
                                                        Radius.circular(9.0),
                                                    bottomRight:
                                                        Radius.circular(9.0),
                                                    topLeft:
                                                        Radius.circular(0.0),
                                                    topRight:
                                                        Radius.circular(0.0),
                                                  ),
                                                ),
                                                alignment:
                                                    const AlignmentDirectional(
                                                        0.0, 0.0),
                                                child: Column(
                                                  mainAxisSize:
                                                      MainAxisSize.max,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    Align(
                                                      alignment:
                                                          const AlignmentDirectional(
                                                              0.0, 0.0),
                                                      child: Text(
                                                        '1 month',
                                                        textAlign:
                                                            TextAlign.center,
                                                        style: FlutterFlowTheme
                                                                .of(context)
                                                            .bodyMedium
                                                            .override(
                                                              fontFamily:
                                                                  'BT Beau Sans',
                                                              fontSize: 13.0,
                                                              letterSpacing:
                                                                  0.0,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                              useGoogleFonts:
                                                                  false,
                                                            ),
                                                      ),
                                                    ),
                                                    Align(
                                                      alignment:
                                                          const AlignmentDirectional(
                                                              0.0, 0.0),
                                                      child: Text(
                                                        revenue_cat
                                                            .offerings!.current!
                                                            .getPackage(
                                                                'chyrpe_standard_1m')!
                                                            .storeProduct
                                                            .priceString,
                                                        style: FlutterFlowTheme
                                                                .of(context)
                                                            .bodyMedium
                                                            .override(
                                                              fontFamily:
                                                                  'BT Beau Sans',
                                                              fontSize: 16.0,
                                                              letterSpacing:
                                                                  0.0,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                              useGoogleFonts:
                                                                  false,
                                                            ),
                                                      ),
                                                    ),
                                                  ].divide(const SizedBox(
                                                      height: 2.0)),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ]
                                      .divide(const SizedBox(width: 12.0))
                                      .addToStart(const SizedBox(width: 40.0))
                                      .addToEnd(const SizedBox(width: 40.0)),
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  40.0, 39.0, 40.0, 0.0),
                              child: Builder(
                                builder: (context) {
                                  final benefitListVar = functions
                                      .getStringListFromJson(
                                          interpolateLikeLimitPlaceholders(
                                              getRemoteConfigString(
                                                  'freepaid_benefitlist_standard_benefitList_placeholder')))
                                      .toList();

                                  return ListView.separated(
                                    padding: EdgeInsets.zero,
                                    primary: false,
                                    shrinkWrap: true,
                                    scrollDirection: Axis.vertical,
                                    itemCount: benefitListVar.length,
                                    separatorBuilder: (_, __) =>
                                        const SizedBox(height: 20.0),
                                    itemBuilder:
                                        (context, benefitListVarIndex) {
                                      final benefitListVarItem =
                                          benefitListVar[benefitListVarIndex];
                                      return Row(
                                        mainAxisSize: MainAxisSize.max,
                                        children: [
                                          Padding(
                                            padding: const EdgeInsetsDirectional
                                                .fromSTEB(0.0, 0.0, 20.0, 0.0),
                                            child: ShaderMask(
                                              shaderCallback: (Rect bounds) =>
                                                  const LinearGradient(
                                                begin: Alignment.centerLeft,
                                                end: Alignment.centerRight,
                                                colors: [
                                                  Color(0xFFFF9EDE),
                                                  Color(0xFF91B2FB)
                                                ],
                                                tileMode: TileMode.clamp,
                                              ).createShader(bounds),
                                              child: const FaIcon(
                                                FontAwesomeIcons.checkCircle,
                                                color: Colors.white,
                                                size: 28.0,
                                              ),
                                            ),
                                          ),
                                          Flexible(
                                            child: Text(
                                              benefitListVarItem,
                                              textAlign: TextAlign.start,
                                              style:
                                                  FlutterFlowTheme.of(context)
                                                      .bodyMedium
                                                      .override(
                                                        fontFamily:
                                                            'BT Beau Sans',
                                                        fontSize: 16.0,
                                                        letterSpacing: 0.0,
                                                        useGoogleFonts: false,
                                                      ),
                                            ),
                                          ),
                                        ],
                                      );
                                    },
                                  );
                                },
                              ),
                            ),
                            Align(
                              alignment: const AlignmentDirectional(0.0, -1.0),
                              child: Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 40.0, 0.0, 30.0),
                                child: InkWell(
                                  splashColor: Colors.transparent,
                                  focusColor: Colors.transparent,
                                  hoverColor: Colors.transparent,
                                  highlightColor: Colors.transparent,
                                  onTap: () async {
                                    await revenue_cat.restorePurchases();
                                    if (revenue_cat.activeEntitlementIds
                                        .contains('standard_access')) {
                                      context.pushNamed('Discovery');
                                    }
                                  },
                                  child: Text(
                                    'Restore purchase',
                                    textAlign: TextAlign.center,
                                    style: FlutterFlowTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'BT Beau Sans',
                                          color: FlutterFlowTheme.of(context)
                                              .secondaryText,
                                          fontSize: 12.0,
                                          letterSpacing: 0.0,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                Padding(
                  padding:
                      const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 30.0),
                  child: Container(
                    width: MediaQuery.sizeOf(context).width * 1.0,
                    decoration: BoxDecoration(
                      color: FlutterFlowTheme.of(context).primaryBackground,
                      border: Border.all(
                        color: const Color(0xFFD4D8DE),
                      ),
                    ),
                    alignment: const AlignmentDirectional(0.0, 1.0),
                    child: Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(
                          14.0, 17.0, 14.0, 0.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if ((!isiOS) &&
                              getRemoteConfigBool(
                                  'subscription_disclaimer_android_show'))
                            Text(
                                (introEligible &&
                                        (!isiOS) &&
                                        _model.selectedOption ==
                                            ChyrpeStandardOptions.weekly)
                                    ? getRemoteConfigString(
                                        'standard_disclaimer_freetrial_android')
                                    : (_model.selectedOption ==
                                            ChyrpeStandardOptions.weekly)
                                        ? getRemoteConfigString(
                                            'chyrpe_standard_new_weekly_disclaimer')
                                        : (_model.selectedOption ==
                                                ChyrpeStandardOptions.lifetime)
                                            ? getRemoteConfigString(
                                                'chyrpe_standard_new_lifetime_disclaimer')
                                            : 'By tapping “Continue”, you will be charged. You also agree to our Terms.',
                                textAlign: TextAlign.center,
                                style: FlutterFlowTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'BT Beau Sans',
                                      color: FlutterFlowTheme.of(context)
                                          .primaryText,
                                      fontSize: 10.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.normal,
                                      fontStyle: FontStyle.normal,
                                      useGoogleFonts: false,
                                    )),
                          Builder(
                            builder: (context) => Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 14.0, 0.0, 30.0),
                              child: wrapWithModel(
                                model: _model.gradientButtonModel,
                                updateCallback: () => safeSetState(() {}),
                                child: GradientButtonFlexFlexFontWidget(
                                  fontSize: (introEligible &&
                                          (!isiOS) &&
                                          _model.selectedOption ==
                                              ChyrpeStandardOptions.weekly)
                                      ? 14.0
                                      : 16.0,
                                  title: getButtonTitle(),
                                  action: () async {
                                    try {
                                      if ((!isiOS)) {
                                        analytics.logEvent(
                                            'Attempted to buy Standard Screen Android New');
                                      } else {
                                        analytics.logEvent(
                                            'Attempted to buy Standard Screen iOS New');
                                      }
                                      analytics.logEvent(
                                          'Attempted to buy Standard Screen New');
                                    } catch (e) {}
                                    var shouldSetState = false;
                                    if (_model.selectedOption ==
                                        ChyrpeStandardOptions.weekly) {
                                      _model.weeklyPurchase =
                                          await revenue_cat.purchasePackage(
                                              'chyrpe_standard_1m');
                                      shouldSetState = true;
                                      if (!_model.weeklyPurchase!) {
                                        await showDialog(
                                          context: context,
                                          builder: (dialogContext) {
                                            return Dialog(
                                              elevation: 0,
                                              insetPadding: EdgeInsets.zero,
                                              backgroundColor:
                                                  Colors.transparent,
                                              alignment:
                                                  const AlignmentDirectional(
                                                          0.0, 0.0)
                                                      .resolve(
                                                          Directionality.of(
                                                              context)),
                                              child: GestureDetector(
                                                onTap: () =>
                                                    FocusScope.of(dialogContext)
                                                        .unfocus(),
                                                child: const GeneralPopupWidget(
                                                  alertTitle:
                                                      'Something went wrong',
                                                  alertText: 'Please try again',
                                                ),
                                              ),
                                            );
                                          },
                                        );

                                        if (shouldSetState) {
                                          safeSetState(() {});
                                        }
                                        return;
                                      }
                                    } else {
                                      _model.lifetimePurchase =
                                          await revenue_cat.purchasePackage(
                                              'chyrpe_standard_lifetime');
                                      shouldSetState = true;
                                      if (!_model.lifetimePurchase!) {
                                        await showDialog(
                                          context: context,
                                          builder: (dialogContext) {
                                            return Dialog(
                                              elevation: 0,
                                              insetPadding: EdgeInsets.zero,
                                              backgroundColor:
                                                  Colors.transparent,
                                              alignment:
                                                  const AlignmentDirectional(
                                                          0.0, 0.0)
                                                      .resolve(
                                                          Directionality.of(
                                                              context)),
                                              child: GestureDetector(
                                                onTap: () =>
                                                    FocusScope.of(dialogContext)
                                                        .unfocus(),
                                                child: const GeneralPopupWidget(
                                                  alertTitle:
                                                      'Something went wrong',
                                                  alertText: 'Please try again',
                                                ),
                                              ),
                                            );
                                          },
                                        );

                                        if (shouldSetState) {
                                          safeSetState(() {});
                                        }
                                        return;
                                      }
                                    }

                                    context.goNamed(
                                      'Discovery',
                                      extra: <String, dynamic>{
                                        kTransitionInfoKey:
                                            const TransitionInfo(
                                          hasTransition: true,
                                          transitionType:
                                              PageTransitionType.topToBottom,
                                        ),
                                      },
                                    );

                                    if (shouldSetState) safeSetState(() {});
                                  },
                                ),
                              ),
                            ),
                          ),
                          if (!((!isiOS) &&
                              getRemoteConfigBool(
                                  'subscription_disclaimer_android_show')))
                            Align(
                              alignment: const AlignmentDirectional(0.0, 0.0),
                              child: FFButtonWidget(
                                onPressed: () async {
                                  await showModalBottomSheet(
                                    isScrollControlled: true,
                                    backgroundColor: Colors.transparent,
                                    enableDrag: false,
                                    context: context,
                                    builder: (context) {
                                      return GestureDetector(
                                        onTap: () =>
                                            FocusScope.of(context).unfocus(),
                                        child: Padding(
                                          padding:
                                              MediaQuery.viewInsetsOf(context),
                                          child: InfoSheetScrollableWidget(
                                            title: getRemoteConfigString(
                                                'discovery_standard_view_legal_information_sheet_title'),
                                            body: () {
                                              if (_model.selectedOption ==
                                                  ChyrpeStandardOptions
                                                      .weekly) {
                                                return getRemoteConfigString(
                                                    'chyrpe_standard_new_weekly_disclaimer');
                                              } else if (_model
                                                      .selectedOption ==
                                                  ChyrpeStandardOptions
                                                      .lifetime) {
                                                return getRemoteConfigString(
                                                    'chyrpe_standard_new_lifetime_disclaimer');
                                              } else {
                                                return 'By tapping “Continue”, you will be charged. You also agree to our Terms.';
                                              }
                                            }(),
                                          ),
                                        ),
                                      );
                                    },
                                  ).then((value) => safeSetState(() {}));
                                },
                                text: getRemoteConfigString(
                                    'discovery_standard_view_legal_information_button_title'),
                                options: FFButtonOptions(
                                  height: 40.0,
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      24.0, 0.0, 24.0, 0.0),
                                  iconPadding:
                                      const EdgeInsetsDirectional.fromSTEB(
                                          0.0, 0.0, 0.0, 0.0),
                                  color: Colors.white,
                                  textStyle: FlutterFlowTheme.of(context)
                                      .titleSmall
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        color: FlutterFlowTheme.of(context)
                                            .primaryText,
                                        fontSize: 12.0,
                                        letterSpacing: 0.0,
                                        decoration: TextDecoration.underline,
                                        useGoogleFonts: false,
                                      ),
                                  elevation: 0.0,
                                  borderSide: const BorderSide(
                                    color: Colors.transparent,
                                    width: 0.0,
                                  ),
                                  borderRadius: BorderRadius.circular(100.0),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
                const Stack(
                  children: [],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
