import 'package:chyrpe/subscription_sale_screens/android_notice.dart';

import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/general/gradient_button_flex_flex_font/gradient_button_flex_flex_font_widget.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import '/flutter_flow/revenue_cat_util.dart' as revenue_cat;
import 'package:flutter/scheduler.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';
import 'chyrpe_standard_sheet_l_a_model.dart';
export 'chyrpe_standard_sheet_l_a_model.dart';
import 'package:gradient_borders/gradient_borders.dart';
import 'package:chyrpe/amplitudeConfig.dart';
import '/discovery/discovery/utils/daily_like_limit.dart';

class ChyrpeStandardSheetLAWidget extends StatefulWidget {
  const ChyrpeStandardSheetLAWidget({super.key});

  @override
  State<ChyrpeStandardSheetLAWidget> createState() =>
      _ChyrpeStandardSheetLAWidgetState();
}

class _ChyrpeStandardSheetLAWidgetState
    extends State<ChyrpeStandardSheetLAWidget> with DailyLikeLimit, WeeklyLikeLimit, LikeLimitInterpolator {
  late ChyrpeStandardSheetLAModel _model;
  bool introEligible = false;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
     _model = createModel(context, () => ChyrpeStandardSheetLAModel());
     Future<bool> freeTrialEligibility() async {
    if (isiOS) {
      try {
      var introEligible = await Purchases.checkTrialOrIntroductoryPriceEligibility(['chy_standard_1m_241011']);
      var firstSub = introEligible.entries.first.value;
      var eligibilityStatus = firstSub.status;
      return eligibilityStatus == IntroEligibilityStatus.introEligibilityStatusEligible;
      } catch (e) {
        return false;
      }

    } else if (!(isiOS)) {
      try {
        var offer = revenue_cat.offerings!.current!.getPackage('chyrpe_standard_1m')!.storeProduct.subscriptionOptions!;
        // Assuming subscriptionOptions is a list of objects with an 'id' property
        bool containsOffer = offer.toString().contains("chy-standard-1w-241011-free");
        return containsOffer;
      } catch(e) {
        return false;
      }
    }
    return false;
  }

  SchedulerBinding.instance.addPostFrameCallback((_) async {
  introEligible = await freeTrialEligibility();
  safeSetState(() {});
  });
  }

    String getButtonTitle() {
    if (isiOS) {
      return _model.selectedSubscription ==
                                                ChyrpeStandardOptions.weekly && introEligible
                                            ? getRemoteConfigString(
                                                'standard_signup_la_btn_title_trial')
                                            : getRemoteConfigString(
                                                'standard_signup_la_btn_title_notrial');
    } else if ((!isiOS)) {
      if (introEligible && _model.selectedSubscription == ChyrpeStandardOptions.weekly) {
        return "Start 1 week trial - then ${revenue_cat.offerings!.current!.getPackage('chyrpe_standard_1m')!.storeProduct.priceString}/month";
      } else  if (_model.selectedSubscription == ChyrpeStandardOptions.weekly) {
        return "Continue - ${revenue_cat.offerings!.current!.getPackage('chyrpe_standard_1m')!.storeProduct.priceString}/month";
      } else if (_model.selectedSubscription == ChyrpeStandardOptions.lifetime) {
        return "Continue - ${revenue_cat.offerings!.current!.getPackage('chyrpe_standard_lifetime')!.storeProduct.priceString} once";
      }
    }

    return "Continue";
  }


  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
      child: Container(
        width: MediaQuery.sizeOf(context).width * 1.0,
        height: MediaQuery.sizeOf(context).height * 1.0,
        decoration: BoxDecoration(
          color: FlutterFlowTheme.of(context).primaryBackground,
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(0.0),
            bottomRight: Radius.circular(0.0),
            topLeft: Radius.circular(10.0),
            topRight: Radius.circular(10.0),
          ),
        ),
        child: Stack(
          children: [
          Stack(
            alignment: const AlignmentDirectional(0.0, 0.0),
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8.0),
                child: Image.asset(
                  'assets/images/LineArtCouple1.webp',
                  width: MediaQuery.sizeOf(context).width * 1.0,
                  height: MediaQuery.sizeOf(context).height * 0.5,
                  fit: BoxFit.contain,
                ),
              ),
              Container(
                width: MediaQuery.sizeOf(context).width * 1.0,
                height: MediaQuery.sizeOf(context).height * 0.6,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Color(0xA9FFFFFF), Colors.white],
                    stops: [0.0, 1.0],
                    begin: AlignmentDirectional(0.0, -1.0),
                    end: AlignmentDirectional(0, 1.0),
                  ),
                ),
              ),
              ],
          ),
          Column(
            children: [
              Flexible(
                child: SingleChildScrollView(
                  child: Column(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              Padding(
                                padding:
                                    const EdgeInsetsDirectional.fromSTEB(0.0, 15.0, 0.0, 0.0),
                                child: FFButtonWidget(
                                  onPressed: () async {
                                    await currentUserReference!
                                        .update(createUsersRecordData(
                                      standardDecisionMade: dateTimeFormat(
                                        "yyyyMMdd",
                                        getCurrentTimestamp,
                                      ),
                                    ));
                                    Navigator.pop(context);

                                    if (!isiOS || getRemoteConfigBool('standard_lite_remove')) {
                                      return;
                                    }
                                    
                                     try {
                               analytics.logEvent('Navigated to Like Standard Purchase from Signup Standard LA - Wants free');
                               } catch(e) {}
                                    
                                    context.goNamed('LiteStandard');
                                  },
                                  text: getRemoteConfigString(
                                      'standard_signup_btn_liteswitch'),
                                  options: FFButtonOptions(
                                    height: 40.0,
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        16.0, 0.0, 16.0, 0.0),
                                    iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                        0.0, 0.0, 0.0, 0.0),
                                    color: const Color(0x00FFFFFF),
                                    textStyle: FlutterFlowTheme.of(context)
                                        .titleSmall
                                        .override(
                                          fontFamily: 'BT Beau Sans',
                                          color:
                                              FlutterFlowTheme.of(context).primaryText,
                                          fontSize: 15.0,
                                          letterSpacing: 0.0,
                                          useGoogleFonts: false,
                                        ),
                                    elevation: 0.0,
                                    borderRadius: BorderRadius.circular(8.0),
                                  ),
                                ),
                              ),
                            ],
                          ),      
                            
                          Column(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 0.0, 0.0, 9.0),
                                child: Text(
                                  getRemoteConfigString('standard_signup_la_title'),
                                  style: FlutterFlowTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        fontSize: 24.0,
                                        letterSpacing: 0.0,
                                        fontWeight: FontWeight.bold,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 0.0, 0.0, 25.0),
                                child: Text(
                                  getRemoteConfigString(
                                      'standard_signup_la_subtitle'),
                                  style: FlutterFlowTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        fontSize: 15.0,
                                        letterSpacing: 0.0,
                                        fontWeight: FontWeight.bold,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    50.0, 0.0, 50.0, 35.0),
                                child: Builder(
                                  builder: (context) {
                                    final benefitlist = functions
                                        .getStringListFromJson(interpolateLikeLimitPlaceholders(getRemoteConfigString(
                                            'standard_signup_la_benefitlist_placeholder')))
                                        .toList();
                                  
                                    return ListView.separated(
                                      primary: false,
                                      padding: EdgeInsets.zero,
                                      shrinkWrap: true,
                                      scrollDirection: Axis.vertical,
                                      itemCount: benefitlist.length,
                                      separatorBuilder: (_, __) =>
                                          const SizedBox(height: 10.0),
                                      itemBuilder: (context, benefitlistIndex) {
                                        final benefitlistItem =
                                            benefitlist[benefitlistIndex];
                                        return Row(
                                          mainAxisSize: MainAxisSize.max,
                                          children: [
                                            ShaderMask(
                                    shaderCallback: (Rect bounds) => const LinearGradient(
                                       begin: Alignment.centerLeft,
                                       end: Alignment.centerRight,
                                       colors: [
                                  Color(0xFFFF9EDE),
                                  Color(0xFF91B2FB)
                                       ],
                                       tileMode: TileMode.clamp,
                                    ).createShader(bounds),
                                              child: const FaIcon(
                                                FontAwesomeIcons.check,
                                                color: Colors.white,
                                                size: 18.0,
                                              ),
                                            ),
                                            Text(
                                              benefitlistItem,
                                              style: FlutterFlowTheme.of(context)
                                                  .bodyMedium
                                                  .override(
                                                    fontFamily: 'BT Beau Sans',
                                                    fontSize: 14.0,
                                                    letterSpacing: 0.0,
                                                    useGoogleFonts: false,
                                                  ),
                                            ),
                                          ].divide(const SizedBox(width: 23.0)),
                                        );
                                      },
                                    );
                                  },
                                ),
                              ),
                              ],
                          ),
                        ],
                      ),
                ),
              ),
              Column(
                children: [
                  Divider(
                                  thickness: 1.0,
                                  indent: 23.0,
                                  endIndent: 23.0,
                                  color: FlutterFlowTheme.of(context).alternate,
                                ),
                                
                                Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      0.0, 0.0, 0.0, 20.0),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Stack(
                                        children: [
                                          Padding(
                                            padding: const EdgeInsetsDirectional.fromSTEB(
                                                0.0, 14.0, 0.0, 0.0),
                                            child:  InkWell(
                                              splashColor: Colors.transparent,
                                              focusColor: Colors.transparent,
                                              hoverColor: Colors.transparent,
                                              highlightColor: Colors.transparent,
                                              onTap: () async {
                                                _model.selectedSubscription =
                                                    ChyrpeStandardOptions.lifetime;
                                                safeSetState(() {});
                                              },
                                              child: Container(
                                                width: 140.0,
                                                height: 100.0,
                                                decoration: BoxDecoration(
                                                  color: FlutterFlowTheme.of(context)
                                                      .primaryBackground,
                                                  borderRadius:
                                                      BorderRadius.circular(10.0),
                                                  border: _model.selectedSubscription ==
                                                            ChyrpeStandardOptions.lifetime ? const GradientBoxBorder(
                                                          gradient: LinearGradient(colors: [Color(0xFF67B0E5), Color(0xFFF49BD1)]),
                                                          width: 2,
                                                        ) : const GradientBoxBorder(
                                                          gradient: LinearGradient(colors: [Color(0xC3C3C3C3), Color(0xC3C3C3C3)]),
                                                          width: 1,
                                                        ),
                                                  ),
                                                child: Align(
                                                  alignment:
                                                      const AlignmentDirectional(0.0, 0.0),
                                                  child: GradientText(
                                                    '${revenue_cat.offerings!.current!.getPackage('chyrpe_standard_lifetime')!.storeProduct.priceString} for life',
                                                    style: FlutterFlowTheme.of(context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily: 'BT Beau Sans',
                                                          fontSize: 13.0,
                                                          letterSpacing: 0.0,
                                                          fontWeight: FontWeight.bold,
                                                          useGoogleFonts: false,
                                                        ),
                                                    colors: const [
                                                      Color(0xFFFF9EDE),
                                                      Color(0xFF91B2FB)
                                                    ],
                                                    gradientDirection:
                                                        GradientDirection.ltr,
                                                    gradientType: GradientType.linear,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      Stack(
                                        alignment: const AlignmentDirectional(0.0, -1.0),
                                        children: [
                                          Padding(
                                            padding: const EdgeInsetsDirectional.fromSTEB(
                                                0.0, 14.0, 0.0, 0.0),
                                             child: InkWell(
                                              splashColor: Colors.transparent,
                                              focusColor: Colors.transparent,
                                              hoverColor: Colors.transparent,
                                              highlightColor: Colors.transparent,
                                              onTap: () async {
                                                _model.selectedSubscription =
                                                    ChyrpeStandardOptions.weekly;
                                                safeSetState(() {});
                                              },
                                              child: Container(
                                                width: 140.0,
                                                height: 100.0,
                                                decoration: BoxDecoration(
                                                  color: FlutterFlowTheme.of(context)
                                                      .primaryBackground,
                                                  borderRadius:
                                                      BorderRadius.circular(10.0),
                                                  border: _model.selectedSubscription ==
                                                            ChyrpeStandardOptions.weekly ? const GradientBoxBorder(
                                                          gradient: LinearGradient(colors: [Color(0xFF67B0E5), Color(0xFFF49BD1)]),
                                                          width: 2,
                                                        ) : const GradientBoxBorder(
                                                          gradient: LinearGradient(colors: [Color(0xC3C3C3C3), Color(0xC3C3C3C3)]),
                                                          width: 1,
                                                        ),
                                                ),
                                                child: Align(
                                                  alignment:
                                                      const AlignmentDirectional(0.0, 0.0),
                                                  child: GradientText(
                                                    '${revenue_cat.offerings!.current!.getPackage('chyrpe_standard_1m')!.storeProduct.priceString}/month',
                                                    style: FlutterFlowTheme.of(context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily: 'BT Beau Sans',
                                                          fontSize: 13.0,
                                                          letterSpacing: 0.0,
                                                          fontWeight: FontWeight.bold,
                                                          useGoogleFonts: false,
                                                        ),
                                                    colors: const [
                                                      Color(0xFFFF9EDE),
                                                      Color(0xFF91B2FB)
                                                    ],
                                                    gradientDirection:
                                                        GradientDirection.ltr,
                                                    gradientType: GradientType.linear,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                          if (introEligible)
                                          Align(
                                            alignment: const AlignmentDirectional(0.0, -1.0),
                                            child: Container(
                                              width: 100.0,
                                              height: 28.0,
                                              decoration: BoxDecoration(
                                                gradient: const LinearGradient(
                                                  colors: [
                                                    Color(0xFFFF9EDE),
                                                    Color(0xFF91B2FB)
                                                  ],
                                                  stops: [0.0, 1.0],
                                                  begin:
                                                      AlignmentDirectional(-1.0, 0.0),
                                                  end: AlignmentDirectional(1.0, 0),
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(24.0),
                                              ),
                                              alignment:
                                                  const AlignmentDirectional(0.0, -1.0),
                                              child: Align(
                                                alignment:
                                                    const AlignmentDirectional(0.0, 0.0),
                                                child: Text(
                                                  getRemoteConfigString(
                                                      'standard_signup_la_trial_caption'),
                                                  style: FlutterFlowTheme.of(context)
                                                      .bodyMedium
                                                      .override(
                                                        fontFamily: 'BT Beau Sans',
                                                        color:
                                                            FlutterFlowTheme.of(context)
                                                                .primaryBackground,
                                                        fontSize: 11.0,
                                                        letterSpacing: 0.0,
                                                        fontWeight: FontWeight.w500,
                                                        useGoogleFonts: false,
                                                      ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ].divide(const SizedBox(width: 20.0)),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      8.0, 20.0, 8.0, 20.0),
                                  child: Text(
  (introEligible && (!isiOS) && _model.selectedSubscription == ChyrpeStandardOptions.weekly)
      ? getRemoteConfigString('standard_disclaimer_freetrial_android')
      : (_model.selectedSubscription == ChyrpeStandardOptions.weekly)
          ? getRemoteConfigString('chyrpe_standard_new_weekly_disclaimer')
          : (_model.selectedSubscription == ChyrpeStandardOptions.lifetime)
              ? getRemoteConfigString('chyrpe_standard_new_lifetime_disclaimer')
              : 'By tapping “Continue”, you will be charged. You also agree to our Terms.',
                          textAlign: TextAlign.center,
                              style: FlutterFlowTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'BT Beau Sans',
                                    color: FlutterFlowTheme.of(context).primaryText,
                                    fontSize: 10.0,
                                    letterSpacing: 0.0,
                                    fontWeight: FontWeight.normal,
                                    fontStyle: FontStyle.normal,
                                    
                                    useGoogleFonts: false,
                                  )),),                      
                                Builder(
                                  builder: (context) => Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        20.0, 0.0, 20.0, 40.0),
                                    child: wrapWithModel(
                                      model: _model.gradientButtonFlexFlexFontModel,
                                      updateCallback: () => safeSetState(() {}),
                                      updateOnChange: true,
                                      child: GradientButtonFlexFlexFontWidget(
                                        title: getButtonTitle(),
                                        height: 55.0,
                                        fontSize: ((!isiOS) && introEligible) ? 11 : 14.0,
                                        action: () async {
                                          try {
                               analytics.logEvent('Attempted Purchase from Signup Standard LA');
                               } catch(e) {}
                  
                                          var shouldSetState = false;
                                          _model.standardPurchase =
                                              await revenue_cat.purchasePackage(
                                                  _model.selectedSubscription ==
                                                          ChyrpeStandardOptions.weekly
                                                      ? 'chyrpe_standard_1m'
                                                      : 'chyrpe_standard_lifetime');
                                          shouldSetState = true;
                                          if (_model.standardPurchase!) {
                                            await currentUserReference!
                                                .update(createUsersRecordData(
                                              standardDecisionMade: dateTimeFormat(
                                                "yyyyMMdd",
                                                getCurrentTimestamp,
                                              ),
                                            ));
                                            Navigator.pop(context);
                                          } else {
                                            await showDialog(
                                              context: context,
                                              builder: (dialogContext) {
                                                return Dialog(
                                                  elevation: 0,
                                                  insetPadding: EdgeInsets.zero,
                                                  backgroundColor: Colors.transparent,
                                                  alignment: const AlignmentDirectional(
                                                          0.0, 0.0)
                                                      .resolve(
                                                          Directionality.of(context)),
                                                  child: const GeneralPopupWidget(
                                                    alertTitle: 'Something went wrong',
                                                    alertText:
                                                        'Please try again <NAME_EMAIL>',
                                                  ),
                                                );
                                              },
                                            );
                  
                                            if (shouldSetState) safeSetState(() {});
                                            return;
                                          }
                  
                                          if (shouldSetState) safeSetState(() {});
                                        },
                                      ),
                                    ),
                                  ),
                                ),
                ],
              ),
            ],
          ),
                      
          
          ]
        ),
      ),
    );
  }
}
