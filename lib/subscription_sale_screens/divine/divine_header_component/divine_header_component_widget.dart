import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import '/flutter_flow/revenue_cat_util.dart' as revenue_cat;
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'divine_header_component_model.dart';
export 'divine_header_component_model.dart';

class DivineHeaderComponentWidget extends StatefulWidget {
  const DivineHeaderComponentWidget({
    super.key,
    bool? clickable,
    int? clickableTextSize,
    int? nonClickableTextSize,
  })  : clickable = clickable ?? false,
        clickableTextSize = clickableTextSize ?? 16,
        nonClickableTextSize = nonClickableTextSize ?? 21;

  final bool clickable;
  final int clickableTextSize;
  final int nonClickableTextSize;

  @override
  State<DivineHeaderComponentWidget> createState() =>
      _DivineHeaderComponentWidgetState();
}

class _DivineHeaderComponentWidgetState
    extends State<DivineHeaderComponentWidget> {
  late DivineHeaderComponentModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => DivineHeaderComponentModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      splashColor: Colors.transparent,
      focusColor: Colors.transparent,
      hoverColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: () async {
        if (widget.clickable) {
          context.pushNamed(
            'DivineSubscription',
            extra: <String, dynamic>{
              kTransitionInfoKey: const TransitionInfo(
                hasTransition: true,
                transitionType: PageTransitionType.bottomToTop,
                duration: Duration(milliseconds: 150),
              ),
            },
          );
        }
      },
      child: Container(
        width: MediaQuery.sizeOf(context).width * 1.0,
        height: 200.0,
        decoration: BoxDecoration(
          color: FlutterFlowTheme.of(context).secondaryBackground,
          image: DecorationImage(
            fit: BoxFit.cover,
            image: CachedNetworkImageProvider(
              functions
                  .getImageFromString(getRemoteConfigString('divine_title_bg')),
            ),
          ),
          borderRadius: BorderRadius.circular(22.0),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8.0),
              child: Image.network(
                functions
                    .getImageFromString(getRemoteConfigString('divine_logo')),
                width: 121.0,
                height: 20.0,
                fit: BoxFit.cover,
              ),
            ),
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(30.0, 11.0, 30.0, 0.0),
              child: Text(
                getRemoteConfigString('divine_title_slogan'),
                textAlign: TextAlign.center,
                style: FlutterFlowTheme.of(context).bodyMedium.override(
                      fontFamily: 'BT Beau Sans',
                      color: FlutterFlowTheme.of(context).info,
                      fontSize: widget.clickable
                          ? widget.clickableTextSize.toDouble()
                          : widget.nonClickableTextSize.toDouble(),
                      letterSpacing: 0.0,
                      fontWeight: FontWeight.bold,
                      useGoogleFonts: false,
                      lineHeight: 1.49,
                    ),
              ),
            ),
            if (widget.clickable)
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(0.0, 28.0, 0.0, 10.0),
                child: Container(
                  width: 180.0,
                  height: 40.0,
                  decoration: BoxDecoration(
                    color: FlutterFlowTheme.of(context).primaryBackground,
                    borderRadius: BorderRadius.circular(100.0),
                  ),
                  child: Align(
                    alignment: const AlignmentDirectional(0.0, 0.0),
                    child: Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(15.0, 0.0, 15.0, 0.0),
                      child: Text(
                        'Upgrade from ${revenue_cat.offerings!.current!.getPackage('chyrpe_divine_1w')!.storeProduct.priceString}',
                        textAlign: TextAlign.center,
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'BT Beau Sans',
                              fontSize: 11.0,
                              letterSpacing: 0.0,
                              fontWeight: FontWeight.bold,
                              useGoogleFonts: false,
                            ),
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
