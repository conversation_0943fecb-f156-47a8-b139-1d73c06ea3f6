import '/auth/firebase_auth/auth_util.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/backend/backend.dart';
import '/backend/schema/structs/index.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/subscription_sale_screens/divine/divine_header_component/divine_header_component_widget.dart';
import '/subscription_sale_screens/subscription_selector_tile_new/subscription_selector_tile_new_widget.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import 'divine_subscription_widget.dart' show DivineSubscriptionWidget;
import 'package:flutter/material.dart';

class DivineSubscriptionModel
    extends FlutterFlowModel<DivineSubscriptionWidget> {
  ///  Local state fields for this page.

  PurchaseDurationStruct? selectedSusbcription = functions
      .getPurchaseDurationStructFromString(
          getRemoteConfigString('purchases_divine_offer_string'))
      .toList()[0];
  void updateSelectedSusbcriptionStruct(
      Function(PurchaseDurationStruct) updateFn) {
    updateFn(selectedSusbcription ??= PurchaseDurationStruct());
  }

  ///  State fields for stateful widgets in this page.

  // Model for DivineHeaderComponent component.
  late DivineHeaderComponentModel divineHeaderComponentModel;
  // Models for SubscriptionSelectorTileNew dynamic component.
  late FlutterFlowDynamicModels<SubscriptionSelectorTileNewModel>
      subscriptionSelectorTileNewModels;

  bool? divinePurchase;
  CreatePurchaseIntentionAlertCloudFunctionCallResponse?
      divineSubscriptionLogCF;

  bool onePage = valueOrDefault(currentUserDocument?.fiveTestGroup, 0) <=
      getRemoteConfigInt('divine_onepage_t');

  @override
  void initState(BuildContext context) {
    divineHeaderComponentModel =
        createModel(context, () => DivineHeaderComponentModel());
    subscriptionSelectorTileNewModels =
        FlutterFlowDynamicModels(() => SubscriptionSelectorTileNewModel());
  }

  @override
  void dispose() {
    divineHeaderComponentModel.dispose();
    subscriptionSelectorTileNewModels.dispose();
  }
}
