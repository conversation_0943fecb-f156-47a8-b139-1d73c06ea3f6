import '/auth/firebase_auth/auth_util.dart';
import '/backend/schema/enums/enums.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/components/divine_subscription_welcome_widget.dart';
import '/components/general_popup_widget.dart';
import '/general/info_sheet_scrollable/info_sheet_scrollable_widget.dart';
import '/subscription_sale_screens/divine/divine_subscription_step2/divine_subscription_step2_widget.dart';
import '/subscription_sale_screens/divine/divine_header_component/divine_header_component_widget.dart';
import '/subscription_sale_screens/subscription_selector_tile_new/subscription_selector_tile_new_widget.dart';
import 'dart:async';
import '/flutter_flow/custom_functions.dart' as functions;
import 'package:flutter/material.dart';
import 'divine_subscription_model.dart';
export 'divine_subscription_model.dart';
import 'package:cloud_functions/cloud_functions.dart';
import '/flutter_flow/revenue_cat_util.dart' as revenue_cat;
import 'package:chyrpe/amplitudeConfig.dart';
import '/subscription_sale_screens/android_notice.dart';
import '/discovery/discovery/utils/daily_like_limit.dart';

class DivineSubscriptionWidget extends StatefulWidget {
  const DivineSubscriptionWidget({
    super.key,
    bool? navigateBack,
  }) : navigateBack = navigateBack ?? false;

  final bool navigateBack;

  @override
  State<DivineSubscriptionWidget> createState() =>
      _DivineSubscriptionWidgetState();
}

class _DivineSubscriptionWidgetState extends State<DivineSubscriptionWidget>
    with DailyLikeLimit, WeeklyLikeLimit, LikeLimitInterpolator {
  late DivineSubscriptionModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => DivineSubscriptionModel());

    try {
      analytics.logEvent('Purchases: Visited Divine');
      if (currentUserDocument?.gender == Gender.Male) {
        context.safePop();
      }
    } catch (e) {}
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        body: SafeArea(
          top: true,
          child: Stack(
            children: [
              SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              0.0, 0.0, 20.0, 0.0),
                          child: FlutterFlowIconButton(
                            borderRadius: 8.0,
                            buttonSize: 40.0,
                            fillColor:
                                FlutterFlowTheme.of(context).primaryBackground,
                            icon: Icon(
                              Icons.close_rounded,
                              color: FlutterFlowTheme.of(context).primaryText,
                              size: 14.0,
                            ),
                            onPressed: () async {
                              context.safePop();
                            },
                          ),
                        ),
                      ],
                    ),
                    Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(
                          13.0, 0.0, 13.0, 0.0),
                      child: wrapWithModel(
                        model: _model.divineHeaderComponentModel,
                        updateCallback: () => safeSetState(() {}),
                        child: const DivineHeaderComponentWidget(),
                      ),
                    ),
                    Flexible(
                      child: Align(
                        alignment: const AlignmentDirectional(0.0, -1.0),
                        child: Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              0.0, 19.0, 0.0, 0.0),
                          child: Visibility(
                            visible: _model.onePage,
                            child: Container(
                              width: MediaQuery.sizeOf(context).width * 1.0,
                              height: 93.0,
                              decoration: const BoxDecoration(),
                              child: Builder(
                                builder: (context) {
                                  final availableSubscription = functions
                                      .getPurchaseDurationStructFromString(
                                          getRemoteConfigString(
                                              'purchases_divine_offer_string'))
                                      .toList();

                                  return ListView.separated(
                                    padding: const EdgeInsets.fromLTRB(
                                      30.0,
                                      0,
                                      30.0,
                                      0,
                                    ),
                                    shrinkWrap: true,
                                    scrollDirection: Axis.horizontal,
                                    itemCount: availableSubscription.length,
                                    separatorBuilder: (_, __) =>
                                        const SizedBox(width: 11.0),
                                    itemBuilder:
                                        (context, availableSubscriptionIndex) {
                                      final availableSubscriptionItem =
                                          availableSubscription[
                                              availableSubscriptionIndex];
                                      return wrapWithModel(
                                        model: _model
                                            .subscriptionSelectorTileNewModels
                                            .getModel(
                                          availableSubscriptionItem.packageId,
                                          availableSubscriptionIndex,
                                        ),
                                        updateCallback: () =>
                                            safeSetState(() {}),
                                        updateOnChange: true,
                                        child:
                                            SubscriptionSelectorTileNewWidget(
                                          key: Key(
                                            'Keyyfd_${availableSubscriptionItem.packageId}',
                                          ),
                                          selected: availableSubscriptionItem
                                                  .packageId ==
                                              _model.selectedSusbcription
                                                  ?.packageId,
                                          selectorObject:
                                              availableSubscriptionItem,
                                          callback: () async {
                                            _model.selectedSusbcription =
                                                availableSubscriptionItem;
                                            safeSetState(() {});
                                          },
                                        ),
                                      );
                                    },
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    Align(
                      alignment: const AlignmentDirectional(0.0, -1.0),
                      child: Padding(
                        padding: const EdgeInsetsDirectional.fromSTEB(
                            27.0, 20.0, 27.0, 0.0),
                        child: Builder(
                          builder: (context) {
                            final benefit = functions
                                .getSymbolicBenefitlistFromJson(
                                    interpolateLikeLimitPlaceholders(
                                        getRemoteConfigString(
                                            'divine_symbolicBenefitlist_placeholder')))
                                .toList();

                            return ListView.separated(
                              padding: const EdgeInsets.fromLTRB(
                                0,
                                0,
                                0,
                                40.0,
                              ),
                              primary: false,
                              shrinkWrap: true,
                              scrollDirection: Axis.vertical,
                              itemCount: benefit.length,
                              separatorBuilder: (_, __) =>
                                  const SizedBox(height: 18.0),
                              itemBuilder: (context, benefitIndex) {
                                final benefitItem = benefit[benefitIndex];
                                return Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Container(
                                      width: 37.0,
                                      height: 37.0,
                                      decoration: const BoxDecoration(
                                        color: Color(0xFFD9D9D9),
                                        shape: BoxShape.circle,
                                      ),
                                      child: ClipRRect(
                                        borderRadius:
                                            BorderRadius.circular(8.0),
                                        child: Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: Image.network(
                                            benefitItem.symbolLink,
                                            width: 24.0,
                                            height: 24.0,
                                            fit: BoxFit.contain,
                                          ),
                                        ),
                                      ),
                                    ),
                                    Text(
                                      benefitItem.benefit,
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            fontFamily: 'BT Beau Sans',
                                            letterSpacing: 0.0,
                                            fontWeight: FontWeight.bold,
                                            useGoogleFonts: false,
                                          ),
                                    ),
                                  ].divide(const SizedBox(width: 15.0)),
                                );
                              },
                            );
                          },
                        ),
                      ),
                    ),
                  ].addToEnd(Container(height: 200)),
                ),
              ),
              Align(
                alignment: const AlignmentDirectional(0.0, 1.0),
                child: IgnorePointer(
                  child: Container(
                    width: MediaQuery.sizeOf(context).width * 1.0,
                    height: 250.0,
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Color(0x00FFFFFF), Colors.white],
                        stops: [0, 0.6],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                    ),
                  ),
                ),
              ),
              Align(
                alignment: const AlignmentDirectional(0.0, 1.0),
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    (_model.onePage)
                        ? const Padding(
                            padding: EdgeInsets.fromLTRB(8.0, 0, 8, 12),
                            child: AndroidNotice(),
                          )
                        : Container(),
                    Container(
                      clipBehavior: Clip.antiAlias,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(100),
                      ),
                      child: ShaderMask(
                        shaderCallback: (Rect bounds) => const RadialGradient(
                          radius: 2.5,
                          colors: [
                            Color(0xFFA963E0),
                            Color.fromARGB(255, 241, 222, 255)
                          ],
                          tileMode: TileMode.mirror,
                        ).createShader(bounds),
                        blendMode: BlendMode.screen,
                        child: FFButtonWidget(
                          onPressed: () async {
                            if (_model.onePage) {
                              unawaited(
                                () async {
                                  try {
                                    if (!isiOS) {
                                      analytics.logEvent(
                                          'Attempted to buy Divine Android');
                                    } else {
                                      analytics.logEvent(
                                          'Attempted to buy Divine iOS');
                                    }
                                    analytics
                                        .logEvent('Attempted to buy Divine');
                                  } catch (e) {}
                                  try {
                                    final result =
                                        await FirebaseFunctions.instanceFor(
                                                region: 'europe-west2')
                                            .httpsCallable(
                                                'createPurchaseIntentionAlert')
                                            .call({
                                      "name": valueOrDefault(
                                          currentUserDocument?.name, ''),
                                      "gender":
                                          currentUserDocument!.gender!.name,
                                      "time": dateTimeFormat(
                                        "d/M h:mm a",
                                        getCurrentTimestamp,
                                        locale: FFLocalizations.of(context)
                                            .languageCode,
                                      ),
                                      "userUID": currentUserUid,
                                      "subscriptionType": 'Divine Subscription',
                                    });
                                    _model.divineSubscriptionLogCF =
                                        CreatePurchaseIntentionAlertCloudFunctionCallResponse(
                                      succeeded: true,
                                    );
                                  } on FirebaseFunctionsException catch (error) {
                                    _model.divineSubscriptionLogCF =
                                        CreatePurchaseIntentionAlertCloudFunctionCallResponse(
                                      errorCode: error.code,
                                      succeeded: false,
                                    );
                                  }
                                }(),
                              );
                              _model.divinePurchase =
                                  await revenue_cat.purchasePackage(
                                      _model.selectedSusbcription!.packageId);
                              if (_model.divinePurchase!) {
                                await showDialog(
                                  context: context,
                                  builder: (dialogContext) {
                                    return Dialog(
                                      elevation: 0,
                                      insetPadding: EdgeInsets.zero,
                                      backgroundColor: Colors.transparent,
                                      alignment: const AlignmentDirectional(
                                              0.0, 0.0)
                                          .resolve(Directionality.of(context)),
                                      child: GestureDetector(
                                        onTap: () =>
                                            FocusScope.of(dialogContext)
                                                .unfocus(),
                                        child:
                                            const DivineSubscriptionWelcomeWidget(),
                                      ),
                                    );
                                  },
                                );

                                await Future.delayed(
                                    const Duration(milliseconds: 2000));
                                Navigator.pop(context);

                                if (widget.navigateBack) {
                                  context.pop();
                                } else {
                                  context.goNamed(
                                    'ProfileHome',
                                    extra: <String, dynamic>{
                                      kTransitionInfoKey: const TransitionInfo(
                                        hasTransition: true,
                                        transitionType:
                                            PageTransitionType.topToBottom,
                                      ),
                                    },
                                  );
                                }
                              } else {
                                await showDialog(
                                  context: context,
                                  builder: (dialogContext) {
                                    return Dialog(
                                      elevation: 0,
                                      insetPadding: EdgeInsets.zero,
                                      backgroundColor: Colors.transparent,
                                      alignment: const AlignmentDirectional(
                                              0.0, 0.0)
                                          .resolve(Directionality.of(context)),
                                      child: GestureDetector(
                                        onTap: () =>
                                            FocusScope.of(dialogContext)
                                                .unfocus(),
                                        child: const GeneralPopupWidget(
                                          alertTitle:
                                              'Purchase was not completed',
                                          alertText: 'Please try again',
                                        ),
                                      ),
                                    );
                                  },
                                );
                              }

                              safeSetState(() {});
                            } else {
                              await showModalBottomSheet(
                                isScrollControlled: true,
                                backgroundColor: Colors.transparent,
                                enableDrag: false,
                                context: context,
                                builder: (context) {
                                  return GestureDetector(
                                    onTap: () {
                                      FocusScope.of(context).unfocus();
                                      FocusManager.instance.primaryFocus
                                          ?.unfocus();
                                    },
                                    child: Padding(
                                      padding: MediaQuery.viewInsetsOf(context),
                                      child: SizedBox(
                                          height: MediaQuery.of(context)
                                                  .size
                                                  .height *
                                              0.95,
                                          child:
                                              const DivineSubscriptionStep2Widget()),
                                    ),
                                  );
                                },
                              );
                            }
                          },
                          text: (_model.onePage && (!isiOS))
                              ? 'Continue  - ${revenue_cat.offerings!.current!.getPackage(_model.selectedSusbcription!.packageId)!.storeProduct.priceString}'
                              : 'Continue',
                          options: FFButtonOptions(
                            width: 300.0,
                            height: 46.0,
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                16.0, 0.0, 16.0, 0.0),
                            iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 0.0, 0.0, 0.0),
                            color: Colors.black,
                            textStyle: FlutterFlowTheme.of(context)
                                .titleSmall
                                .override(
                                  fontFamily: 'BT Beau Sans',
                                  color: Colors.white,
                                  fontSize: 13.0,
                                  letterSpacing: 0.0,
                                  useGoogleFonts: false,
                                ),
                            elevation: 0.0,
                            borderRadius: BorderRadius.circular(100.0),
                          ),
                        ),
                      ),
                    ),
                    if (!((!isiOS) &&
                            getRemoteConfigBool(
                                'subscription_disclaimer_android_show')) &&
                        (_model.onePage))
                      Padding(
                        padding: const EdgeInsetsDirectional.fromSTEB(
                            0.0, 10.0, 0.0, 0.0),
                        child: FFButtonWidget(
                          onPressed: () async {
                            await showModalBottomSheet(
                              isScrollControlled: true,
                              backgroundColor: Colors.transparent,
                              enableDrag: false,
                              context: context,
                              builder: (context) {
                                return GestureDetector(
                                  onTap: () {
                                    FocusScope.of(context).unfocus();
                                    FocusManager.instance.primaryFocus
                                        ?.unfocus();
                                  },
                                  child: Padding(
                                    padding: MediaQuery.viewInsetsOf(context),
                                    child: InfoSheetScrollableWidget(
                                      title: getRemoteConfigString(
                                          'discovery_standard_view_legal_information_sheet_title'),
                                      body: getRemoteConfigString(
                                          'chyrpe_standard_new_weekly_disclaimer'),
                                    ),
                                  ),
                                );
                              },
                            ).then((value) => safeSetState(() {}));
                          },
                          text: getRemoteConfigString(
                              'discovery_standard_view_legal_information_button_title'),
                          options: FFButtonOptions(
                            height: 40.0,
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                24.0, 0.0, 24.0, 0.0),
                            iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 0.0, 0.0, 0.0),
                            color: Colors.white,
                            textStyle: FlutterFlowTheme.of(context)
                                .titleSmall
                                .override(
                                  fontFamily: 'BT Beau Sans',
                                  color:
                                      FlutterFlowTheme.of(context).primaryText,
                                  fontSize: 12.0,
                                  letterSpacing: 0.0,
                                  decoration: TextDecoration.underline,
                                  useGoogleFonts: false,
                                ),
                            elevation: 0.0,
                            borderSide: const BorderSide(
                              color: Colors.transparent,
                              width: 0.0,
                            ),
                            borderRadius: BorderRadius.circular(100.0),
                          ),
                        ),
                      ),
                  ].addToEnd(Container(height: 30)),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
