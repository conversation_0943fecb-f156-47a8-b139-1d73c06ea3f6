import '/backend/backend.dart';
import '/backend/schema/structs/index.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import 'divine_subscription_step2_widget.dart'
    show DivineSubscriptionStep2Widget;
import 'package:flutter/material.dart';

class DivineSubscriptionStep2Model
    extends FlutterFlowModel<DivineSubscriptionStep2Widget> {
  ///  Local state fields for this component.

  PurchaseDurationStruct? selectedSubscription = functions
                                          .getPurchaseDurationStructFromString(
                                              getRemoteConfigString(
                                                  'purchases_divine_offer_string'))
                                          .toList()[0];

  bool? divinePurchase;
    CreatePurchaseIntentionAlertCloudFunctionCallResponse?
      divineSubscriptionLogCF;

  void updateSelectedSubscriptionStruct(
      Function(PurchaseDurationStruct) updateFn) {
    updateFn(selectedSubscription ??= PurchaseDurationStruct());
  }

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {}
}
