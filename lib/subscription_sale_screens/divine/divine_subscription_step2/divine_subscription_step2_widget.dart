import '/auth/firebase_auth/auth_util.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/components/divine_subscription_welcome_widget.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import 'package:gradient_borders/gradient_borders.dart';
import '/general/info_sheet_scrollable/info_sheet_scrollable_widget.dart';
import '/subscription_sale_screens/subscription_selector_tile_new_long/subscription_selector_tile_new_long_widget.dart';
import 'dart:async';
import '/flutter_flow/custom_functions.dart' as functions;
import 'package:cloud_functions/cloud_functions.dart';
import '/flutter_flow/revenue_cat_util.dart' as revenue_cat;
import 'package:flutter/material.dart';
import 'divine_subscription_step2_model.dart';
export 'divine_subscription_step2_model.dart';
import 'package:chyrpe/amplitudeConfig.dart';
import '/subscription_sale_screens/android_notice.dart';

class DivineSubscriptionStep2Widget extends StatefulWidget {
  const DivineSubscriptionStep2Widget({super.key});

  @override
  State<DivineSubscriptionStep2Widget> createState() =>
      _DivineSubscriptionStep2WidgetState();
}

class _DivineSubscriptionStep2WidgetState
    extends State<DivineSubscriptionStep2Widget> {
  late DivineSubscriptionStep2Model _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => DivineSubscriptionStep2Model());

    try {
      analytics.logEvent('Purchases: Visited Divine Step 2');
    } catch (e) {}
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.sizeOf(context).width * 1.0,
      height: MediaQuery.sizeOf(context).height * 1.0,
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).primaryBackground,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(0.0),
          bottomRight: Radius.circular(0.0),
          topLeft: Radius.circular(11.0),
          topRight: Radius.circular(11.0),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Padding(
                padding:
                    const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 15.0, 0.0),
                child: FlutterFlowIconButton(
                  borderRadius: 8.0,
                  buttonSize: 40.0,
                  fillColor: FlutterFlowTheme.of(context).primaryBackground,
                  icon: Icon(
                    Icons.close_rounded,
                    color: FlutterFlowTheme.of(context).primaryText,
                    size: 14.0,
                  ),
                  onPressed: () async {
                    context.safePop();
                  },
                ),
              ),
            ],
          ),
          Flexible(
            child: Padding(
              padding:
                  const EdgeInsetsDirectional.fromSTEB(15.0, 0.0, 15.0, 0.0),
              child: Container(
                width: MediaQuery.sizeOf(context).width * 1.0,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(11.0),
                  border: const GradientBoxBorder(
                    gradient: LinearGradient(
                        colors: [Colors.white, Color(0xFFBD88E7)],
                        begin: Alignment.bottomCenter,
                        end: Alignment.topCenter),
                    width: 1,
                  ),
                ),
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: const EdgeInsetsDirectional.fromSTEB(
                            0.0, 40.0, 0.0, 0.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(8.0),
                              child: Image.network(
                                functions.getImageFromString(
                                    getRemoteConfigString('divine_logo_dark')),
                                width: 121.0,
                                height: 20.0,
                                fit: BoxFit.cover,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 20.0, 0.0, 0.0),
                              child: Text(
                                getRemoteConfigString('divine_p2_slogan1'),
                                textAlign: TextAlign.center,
                                style: FlutterFlowTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'BT Beau Sans',
                                      fontSize: 20.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.w500,
                                      useGoogleFonts: false,
                                    ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 20.0, 0.0, 0.0),
                              child: Text(
                                getRemoteConfigString('divine_p2_slogan2'),
                                textAlign: TextAlign.center,
                                style: FlutterFlowTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'BT Beau Sans',
                                      fontSize: 12.0,
                                      letterSpacing: 0.0,
                                      useGoogleFonts: false,
                                    ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 20.0, 0.0, 0.0),
                              child: InkWell(
                                splashColor: Colors.transparent,
                                focusColor: Colors.transparent,
                                hoverColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                onTap: () async {
                                  Navigator.pop(context);
                                },
                                child: Container(
                                  width: 150.0,
                                  height: 29.0,
                                  decoration: BoxDecoration(
                                    color: FlutterFlowTheme.of(context)
                                        .secondaryBackground,
                                    borderRadius: BorderRadius.circular(20.0),
                                  ),
                                  child: Align(
                                    alignment:
                                        const AlignmentDirectional(0.0, 0.0),
                                    child: Text(
                                      getRemoteConfigString(
                                          'divine_p2_more_btn'),
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            fontFamily: 'BT Beau Sans',
                                            fontSize: 12.0,
                                            letterSpacing: 0.0,
                                            useGoogleFonts: false,
                                          ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsetsDirectional.fromSTEB(
                            0.0, 30.0, 0.0, 0.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 0.0, 0.0, 24.0),
                              child: Text(
                                getRemoteConfigString(
                                    'divine_p2_benefitstitle'),
                                style: FlutterFlowTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'BT Beau Sans',
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.w500,
                                      useGoogleFonts: false,
                                    ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  10.0, 0.0, 10.0, 0.0),
                              child: Builder(
                                builder: (context) {
                                  final availableSubscription = functions
                                      .getPurchaseDurationStructFromString(
                                          getRemoteConfigString(
                                              'purchases_divine_offer_string'))
                                      .toList();

                                  return ListView.separated(
                                    padding: EdgeInsets.zero,
                                    primary: false,
                                    shrinkWrap: true,
                                    scrollDirection: Axis.vertical,
                                    itemCount: availableSubscription.length,
                                    separatorBuilder: (_, __) =>
                                        const SizedBox(height: 21.0),
                                    itemBuilder:
                                        (context, availableSubscriptionIndex) {
                                      final availableSubscriptionItem =
                                          availableSubscription[
                                              availableSubscriptionIndex];
                                      return SubscriptionSelectorTileNewLongWidget(
                                        key: Key(
                                            'Keytvd_${availableSubscriptionIndex}_of_${availableSubscription.length}'),
                                        selected: _model.selectedSubscription
                                                ?.packageId ==
                                            availableSubscriptionItem.packageId,
                                        selectorObject:
                                            availableSubscriptionItem,
                                        callback: () async {
                                          setState(() =>
                                              _model.selectedSubscription =
                                                  availableSubscriptionItem);
                                        },
                                      );
                                    },
                                  );
                                },
                              ),
                            ),
                          ].addToEnd(Container(height: 30)),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          Align(
            alignment: const AlignmentDirectional(0.0, 1.0),
            child: Padding(
              padding:
                  const EdgeInsetsDirectional.fromSTEB(0.0, 30.0, 0.0, 0.0),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  const Padding(
                    padding: EdgeInsets.fromLTRB(8.0, 0, 8, 12),
                    child: AndroidNotice(),
                  ),
                  Container(
                    clipBehavior: Clip.antiAlias,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(100),
                    ),
                    child: ShaderMask(
                      shaderCallback: (Rect bounds) => const RadialGradient(
                        radius: 2.5,
                        colors: [
                          Color(0xFFA963E0),
                          Color.fromARGB(255, 241, 222, 255)
                        ],
                        tileMode: TileMode.mirror,
                      ).createShader(bounds),
                      blendMode: BlendMode.screen,
                      child: FFButtonWidget(
                        onPressed: () async {
                          unawaited(
                            () async {
                              try {
                                if (!isiOS) {
                                  analytics.logEvent(
                                      'Attempted to buy Divine Android');
                                } else {
                                  analytics
                                      .logEvent('Attempted to buy Divine iOS');
                                }
                                analytics.logEvent('Attempted to buy Divine');
                              } catch (e) {}
                              try {
                                final result =
                                    await FirebaseFunctions.instanceFor(
                                            region: 'europe-west2')
                                        .httpsCallable(
                                            'createPurchaseIntentionAlert')
                                        .call({
                                  "name": valueOrDefault(
                                      currentUserDocument?.name, ''),
                                  "gender": currentUserDocument!.gender!.name,
                                  "time": dateTimeFormat(
                                    "d/M h:mm a",
                                    getCurrentTimestamp,
                                    locale: FFLocalizations.of(context)
                                        .languageCode,
                                  ),
                                  "userUID": currentUserUid,
                                  "subscriptionType": 'Divine Subscription',
                                });
                                _model.divineSubscriptionLogCF =
                                    CreatePurchaseIntentionAlertCloudFunctionCallResponse(
                                  succeeded: true,
                                );
                              } on FirebaseFunctionsException catch (error) {
                                _model.divineSubscriptionLogCF =
                                    CreatePurchaseIntentionAlertCloudFunctionCallResponse(
                                  errorCode: error.code,
                                  succeeded: false,
                                );
                              }
                            }(),
                          );
                          _model.divinePurchase =
                              await revenue_cat.purchasePackage(
                                  _model.selectedSubscription!.packageId);
                          if (_model.divinePurchase!) {
                            await showDialog(
                              context: context,
                              builder: (dialogContext) {
                                return Dialog(
                                  elevation: 0,
                                  insetPadding: EdgeInsets.zero,
                                  backgroundColor: Colors.transparent,
                                  alignment:
                                      const AlignmentDirectional(0.0, 0.0)
                                          .resolve(Directionality.of(context)),
                                  child: GestureDetector(
                                    onTap: () =>
                                        FocusScope.of(dialogContext).unfocus(),
                                    child:
                                        const DivineSubscriptionWelcomeWidget(),
                                  ),
                                );
                              },
                            );

                            await Future.delayed(
                                const Duration(milliseconds: 2000));
                            Navigator.pop(context);

                            context.goNamed(
                              'ProfileHome',
                              extra: <String, dynamic>{
                                kTransitionInfoKey: const TransitionInfo(
                                  hasTransition: true,
                                  transitionType:
                                      PageTransitionType.topToBottom,
                                ),
                              },
                            );
                          } else {
                            await showDialog(
                              context: context,
                              builder: (dialogContext) {
                                return Dialog(
                                  elevation: 0,
                                  insetPadding: EdgeInsets.zero,
                                  backgroundColor: Colors.transparent,
                                  alignment:
                                      const AlignmentDirectional(0.0, 0.0)
                                          .resolve(Directionality.of(context)),
                                  child: GestureDetector(
                                    onTap: () =>
                                        FocusScope.of(dialogContext).unfocus(),
                                    child: const GeneralPopupWidget(
                                      alertTitle: 'Purchase was not completed',
                                      alertText: 'Please try again',
                                    ),
                                  ),
                                );
                              },
                            );
                          }

                          safeSetState(() {});
                        },
                        text: (!isiOS)
                            ? 'Continue  - ${revenue_cat.offerings!.current!.getPackage(_model.selectedSubscription!.packageId)!.storeProduct.priceString}'
                            : 'Continue',
                        options: FFButtonOptions(
                          width: 300.0,
                          height: 46.0,
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              16.0, 0.0, 16.0, 0.0),
                          iconPadding: const EdgeInsetsDirectional.fromSTEB(
                              0.0, 0.0, 0.0, 0.0),
                          color: Colors.black,
                          textStyle:
                              FlutterFlowTheme.of(context).titleSmall.override(
                                    fontFamily: 'BT Beau Sans',
                                    color: Colors.white,
                                    fontSize: 13.0,
                                    letterSpacing: 0.0,
                                    useGoogleFonts: false,
                                  ),
                          elevation: 0.0,
                          borderRadius: BorderRadius.circular(100.0),
                        ),
                      ),
                    ),
                  ),
                  if (!((!isiOS) &&
                      getRemoteConfigBool(
                          'subscription_disclaimer_android_show')))
                    Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(
                          0.0, 10.0, 0.0, 0.0),
                      child: FFButtonWidget(
                        onPressed: () async {
                          await showModalBottomSheet(
                            isScrollControlled: true,
                            backgroundColor: Colors.transparent,
                            enableDrag: false,
                            context: context,
                            builder: (context) {
                              return Padding(
                                padding: MediaQuery.viewInsetsOf(context),
                                child: InfoSheetScrollableWidget(
                                  title: getRemoteConfigString(
                                      'discovery_standard_view_legal_information_sheet_title'),
                                  body: getRemoteConfigString(
                                      'chyrpe_standard_new_weekly_disclaimer'),
                                ),
                              );
                            },
                          ).then((value) => safeSetState(() {}));
                        },
                        text: getRemoteConfigString(
                            'discovery_standard_view_legal_information_button_title'),
                        options: FFButtonOptions(
                          height: 40.0,
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              24.0, 0.0, 24.0, 0.0),
                          iconPadding: const EdgeInsetsDirectional.fromSTEB(
                              0.0, 0.0, 0.0, 0.0),
                          color: Colors.white,
                          textStyle: FlutterFlowTheme.of(context)
                              .titleSmall
                              .override(
                                fontFamily: 'BT Beau Sans',
                                color: FlutterFlowTheme.of(context).primaryText,
                                fontSize: 12.0,
                                letterSpacing: 0.0,
                                decoration: TextDecoration.underline,
                                useGoogleFonts: false,
                              ),
                          elevation: 0.0,
                          borderSide: const BorderSide(
                            color: Colors.transparent,
                            width: 0.0,
                          ),
                          borderRadius: BorderRadius.circular(100.0),
                        ),
                      ),
                    ),
                ].addToEnd(Container(height: 30)),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
