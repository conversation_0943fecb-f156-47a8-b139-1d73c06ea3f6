import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button_evolve/gradient_button_evolve_widget.dart';
import 'evolved_subscription_widget.dart' show EvolvedSubscriptionWidget;
import 'package:flutter/material.dart';


class EvolvedSubscriptionModel
    extends FlutterFlowModel<EvolvedSubscriptionWidget> {
  ///  Local state fields for this page.

  int? selectedSubscription = 1;

  ///  State fields for stateful widgets in this page.

  final unfocusNode = FocusNode();
  // Model for GradientButtonEvolve component.
  late GradientButtonEvolveModel gradientButtonEvolveModel;
  // Stores action output result for [Cloud Function - createPurchaseIntentionAlert] action in GradientButtonEvolve widget.
  CreatePurchaseIntentionAlertCloudFunctionCallResponse?
      evolvedSubscriptionLogCF;
  // Stores action output result for [RevenueCat - Purchase] action in GradientButtonEvolve widget.
  bool? evolvedPurchase;

  /// Initialization and disposal methods.

  @override
  void initState(BuildContext context) {
    gradientButtonEvolveModel =
        createModel(context, () => GradientButtonEvolveModel());
  }

  @override
  void dispose() {
    unfocusNode.dispose();
    gradientButtonEvolveModel.dispose();
  }

  /// Action blocks are added here.

  /// Additional helper methods are added here.
}
