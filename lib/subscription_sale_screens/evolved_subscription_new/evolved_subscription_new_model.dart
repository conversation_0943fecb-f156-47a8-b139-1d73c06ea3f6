import '/backend/backend.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/backend/schema/structs/index.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button_evolve/gradient_button_evolve_widget.dart';
import '/subscription_sale_screens/subscription_selector_tile/subscription_selector_tile_widget.dart';
import '/subscription_sale_screens/subscription_selector_tile_n_s/subscription_selector_tile_n_s_widget.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import 'evolved_subscription_new_widget.dart' show EvolvedSubscriptionNewWidget;
import 'package:flutter/material.dart';
import '/subscription_sale_screens/standard_included_badge/standard_included_badge_widget.dart';

class EvolvedSubscriptionNewModel
    extends FlutterFlowModel<EvolvedSubscriptionNewWidget> {
  ///  Local state fields for this page.

  PurchaseDurationStruct? selectedSubscription = functions
      .getPurchaseDurationStructFromString(
          getRemoteConfigString('purchases_evolved_offer_string'))
      .toList()[0];
  void updateSelectedSubscriptionStruct(
      Function(PurchaseDurationStruct) updateFn) {
    updateFn(selectedSubscription ??= PurchaseDurationStruct());
  }

  ///  State fields for stateful widgets in this page.

  // Models for SubscriptionSelectorTile dynamic component.
  late FlutterFlowDynamicModels<SubscriptionSelectorTileModel>
      subscriptionSelectorTileModels;
  // Models for SubscriptionSelectorTileNS dynamic component.
  late FlutterFlowDynamicModels<SubscriptionSelectorTileNSModel>
      subscriptionSelectorTileNSModels;
  // Model for GradientButtonEvolve component.
  late GradientButtonEvolveModel gradientButtonEvolveModel;
  // Stores action output result for [Cloud Function - createPurchaseIntentionAlert] action in GradientButtonEvolve widget.
  CreatePurchaseIntentionAlertCloudFunctionCallResponse?
      evolvedSubscriptionLogCF;
  late StandardIncludedBadgeModel standardIncludedBadgeModel;
  // Stores action output result for [RevenueCat - Purchase] action in GradientButtonEvolve widget.
  bool? evolvedPurchase;

  @override
  void initState(BuildContext context) {
    subscriptionSelectorTileModels =
        FlutterFlowDynamicModels(() => SubscriptionSelectorTileModel());
    subscriptionSelectorTileNSModels =
        FlutterFlowDynamicModels(() => SubscriptionSelectorTileNSModel());
    gradientButtonEvolveModel =
        createModel(context, () => GradientButtonEvolveModel());
    standardIncludedBadgeModel =
        createModel(context, () => StandardIncludedBadgeModel());
  }

  @override
  void dispose() {
    subscriptionSelectorTileModels.dispose();
    subscriptionSelectorTileNSModels.dispose();
    gradientButtonEvolveModel.dispose();
    standardIncludedBadgeModel.dispose();
  }
}
