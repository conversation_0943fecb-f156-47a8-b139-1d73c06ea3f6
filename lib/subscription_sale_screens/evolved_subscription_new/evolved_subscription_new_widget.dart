import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/backend/schema/enums/enums.dart';
import '/backend/schema/structs/index.dart';
import '/components/evolved_subscription_welcome_widget.dart';
import '/components/general_popup_widget.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/general/gradient_button_evolve/gradient_button_evolve_widget.dart';
import '/general/info_sheet_scrollable/info_sheet_scrollable_widget.dart';
import '/subscription_sale_screens/subscription_selector_tile/subscription_selector_tile_widget.dart';
import '/subscription_sale_screens/subscription_selector_tile_n_s/subscription_selector_tile_n_s_widget.dart';
import 'dart:async';
import '/flutter_flow/custom_functions.dart' as functions;
import '/flutter_flow/revenue_cat_util.dart' as revenue_cat;
import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'evolved_subscription_new_model.dart';
export 'evolved_subscription_new_model.dart';
import 'package:flutter/services.dart'; // Import this for vibration support
import 'package:chyrpe/amplitudeConfig.dart';
import '/subscription_sale_screens/standard_included_badge/standard_included_badge_widget.dart';
import '/subscription_sale_screens/android_notice.dart';
import '/discovery/discovery/utils/daily_like_limit.dart';

class EvolvedSubscriptionNewWidget extends StatefulWidget {
  const EvolvedSubscriptionNewWidget({
    super.key,
    bool? navigateBack,
  }) : navigateBack = navigateBack ?? false;

  final bool navigateBack;

  @override
  State<EvolvedSubscriptionNewWidget> createState() =>
      _EvolvedSubscriptionNewWidgetState();
}

class _EvolvedSubscriptionNewWidgetState
    extends State<EvolvedSubscriptionNewWidget>
    with DailyLikeLimit, WeeklyLikeLimit, LikeLimitInterpolator {
  late EvolvedSubscriptionNewModel _model;
  final ScrollController _scrollController = ScrollController();

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => EvolvedSubscriptionNewModel());

    if (currentUserDocument?.gender == Gender.Female &&
        valueOrDefault(currentUserDocument?.fiveTestGroup, 0) >
            valueOrDefault(getRemoteConfigInt('divine_legacy_t'), 0)) {
      context.pushReplacementNamed(
        'DivineSubscription',
        extra: <String, dynamic>{
          kTransitionInfoKey: const TransitionInfo(
            hasTransition: true,
            transitionType: PageTransitionType.fade,
            duration: Duration(milliseconds: 0),
          ),
        },
      );
    }
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  void centerItem(int index) {
    double screenWidth = MediaQuery.of(context).size.width;
    double itemWidth = 145.0; // Replace with your actual item width
    double targetPosition =
        (index * itemWidth) - (screenWidth / 2) + (itemWidth / 2);

    _scrollController.animateTo(
      targetPosition,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        body: Stack(
          alignment: const AlignmentDirectional(0.0, -1.0),
          children: [
            Stack(
              alignment: const AlignmentDirectional(0.0, 1.0),
              children: [
                Stack(
                  alignment: const AlignmentDirectional(0.0, 0.0),
                  children: [
                    Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(
                          0.0, 150.0, 0.0, 180.0),
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  15.0, 0.0, 15.0, 0.0),
                              child: Text(
                                getRemoteConfigString(
                                    'purchases_evolved_new_tagline'),
                                textAlign: TextAlign.center,
                                style: FlutterFlowTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'BT Beau Sans',
                                      fontSize: 26.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.bold,
                                      useGoogleFonts: false,
                                    ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 49.0, 0.0, 39.0),
                              child: Builder(
                                builder: (context) {
                                  final evolvedPurchases = functions
                                      .getPurchaseDurationStructFromString(
                                          getRemoteConfigString(
                                              'purchases_evolved_offer_string'))
                                      .toList();

                                  return SingleChildScrollView(
                                    scrollDirection: Axis.horizontal,
                                    controller: _scrollController,
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      children: List.generate(
                                              evolvedPurchases.length,
                                              (evolvedPurchasesIndex) {
                                        final evolvedPurchasesItem =
                                            evolvedPurchases[
                                                evolvedPurchasesIndex];
                                        return Builder(
                                          builder: (context) {
                                            if (evolvedPurchasesItem
                                                    .packageId ==
                                                _model.selectedSubscription
                                                    ?.packageId) {
                                              return wrapWithModel(
                                                model: _model
                                                    .subscriptionSelectorTileModels
                                                    .getModel(
                                                  evolvedPurchasesItem
                                                      .packageId,
                                                  evolvedPurchasesIndex,
                                                ),
                                                updateCallback: () =>
                                                    safeSetState(() {}),
                                                updateOnChange: true,
                                                child:
                                                    SubscriptionSelectorTileWidget(
                                                  key: Key(
                                                    'Key0m3_${evolvedPurchasesItem.packageId}',
                                                  ),
                                                  colour1:
                                                      const Color(0xFFA12CFD),
                                                  colour2:
                                                      const Color(0xFFFF6C3E),
                                                  selectorObject:
                                                      evolvedPurchasesItem,
                                                  callback: () async {
                                                    HapticFeedback
                                                        .selectionClick();
                                                    centerItem(
                                                        evolvedPurchasesIndex);
                                                    _model.selectedSubscription =
                                                        PurchaseDurationStruct(
                                                      packageId:
                                                          evolvedPurchasesItem
                                                              .packageId,
                                                      duration:
                                                          evolvedPurchasesItem
                                                              .duration,
                                                    );
                                                    safeSetState(() {});
                                                  },
                                                ),
                                              );
                                            } else {
                                              return wrapWithModel(
                                                model: _model
                                                    .subscriptionSelectorTileNSModels
                                                    .getModel(
                                                  evolvedPurchasesItem
                                                      .packageId,
                                                  evolvedPurchasesIndex,
                                                ),
                                                updateCallback: () =>
                                                    safeSetState(() {}),
                                                updateOnChange: true,
                                                child:
                                                    SubscriptionSelectorTileNSWidget(
                                                  key: Key(
                                                    'Keymx9_${evolvedPurchasesItem.packageId}',
                                                  ),
                                                  selectorObject:
                                                      evolvedPurchasesItem,
                                                  callback: () async {
                                                    HapticFeedback
                                                        .selectionClick();
                                                    centerItem(
                                                        evolvedPurchasesIndex);
                                                    _model.selectedSubscription =
                                                        evolvedPurchasesItem;
                                                    safeSetState(() {});
                                                  },
                                                ),
                                              );
                                            }
                                          },
                                        );
                                      })
                                          .divide(const SizedBox(width: 12.0))
                                          .addToStart(
                                              const SizedBox(width: 15.0))
                                          .addToEnd(
                                              const SizedBox(width: 15.0)),
                                    ),
                                  );
                                },
                              ),
                            ),
                            if ((valueOrDefault<bool>(
                                        currentUserDocument?.bGroup, false)
                                    ? functions
                                        .getStringListFromJson(
                                            getRemoteConfigString(
                                                'premium_standard_badge_visibility'))
                                        .contains('bGroup')
                                    : functions
                                        .getStringListFromJson(
                                            getRemoteConfigString(
                                                'premium_standard_badge_visibility'))
                                        .contains('aGroup')) &&
                                !(revenue_cat.activeEntitlementIds
                                        .contains('paid_standard_lifetime') ||
                                    revenue_cat.activeEntitlementIds
                                        .contains('paid_standard_1w')) &&
                                (currentUserDocument?.gender == Gender.Male))
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    15.0, 0.0, 15.0, 14.0),
                                child: AuthUserStreamWidget(
                                  builder: (context) => wrapWithModel(
                                    model: _model.standardIncludedBadgeModel,
                                    updateCallback: () => safeSetState(() {}),
                                    child: const StandardIncludedBadgeWidget(),
                                  ),
                                ),
                              ),
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  15.0, 0.0, 15.0, 0.0),
                              child: Stack(
                                children: [
                                  Padding(
                                    padding:
                                        const EdgeInsetsDirectional.fromSTEB(
                                            0.0, 13.0, 0.0, 0.0),
                                    child: Container(
                                      width: MediaQuery.sizeOf(context).width *
                                          1.0,
                                      decoration: BoxDecoration(
                                        borderRadius:
                                            BorderRadius.circular(11.0),
                                        border: Border.all(
                                          color: const Color(0xFFB9BFC8),
                                          width: 1.0,
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsetsDirectional
                                            .fromSTEB(0.0, 6.0, 0.0, 30.0),
                                        child: AuthUserStreamWidget(
                                          builder: (context) => Builder(
                                            builder: (context) {
                                              final benefitlist = currentUserDocument
                                                          ?.gender ==
                                                      Gender.Male
                                                  ? functions
                                                      .getStringListFromJson(
                                                          interpolateLikeLimitPlaceholders(
                                                              getRemoteConfigString(
                                                                  ('evolved_benefitlist_m_placeholder'))))
                                                      .where((e) => () {
                                                            if ((revenue_cat
                                                                        .activeEntitlementIds
                                                                        .contains(
                                                                            'paid_standard_lifetime') ||
                                                                    revenue_cat
                                                                        .activeEntitlementIds
                                                                        .contains(
                                                                            'paid_standard_1w')) ||
                                                                (currentUserDocument
                                                                        ?.gender !=
                                                                    Gender
                                                                        .Male)) {
                                                              return (e !=
                                                                  getRemoteConfigString(
                                                                      'premium_includes_standard_badge_title'));
                                                            } else if (valueOrDefault<bool>(
                                                                    currentUserDocument
                                                                        ?.bGroup,
                                                                    false)
                                                                ? functions
                                                                    .getStringListFromJson(
                                                                        getRemoteConfigString(
                                                                            'premium_standard_badge_visibility'))
                                                                    .contains(
                                                                        'bGroup')
                                                                : functions
                                                                    .getStringListFromJson(
                                                                        getRemoteConfigString(
                                                                            'premium_standard_badge_visibility'))
                                                                    .contains(
                                                                        'aGroup')) {
                                                              return (e !=
                                                                  getRemoteConfigString(
                                                                      'premium_includes_standard_badge_title'));
                                                            } else {
                                                              return true;
                                                            }
                                                          }())
                                                      .toList()
                                                  : functions
                                                      .getStringListFromJson(
                                                          interpolateLikeLimitPlaceholders(
                                                              getRemoteConfigString(
                                                                  'evolved_benefitlist_w_placeholder')))
                                                      .where((e) => () {
                                                            if ((revenue_cat
                                                                        .activeEntitlementIds
                                                                        .contains(
                                                                            'paid_standard_lifetime') ||
                                                                    revenue_cat
                                                                        .activeEntitlementIds
                                                                        .contains(
                                                                            'paid_standard_1w')) ||
                                                                (currentUserDocument
                                                                        ?.gender !=
                                                                    Gender
                                                                        .Male)) {
                                                              return (e !=
                                                                  getRemoteConfigString(
                                                                      'premium_includes_standard_badge_title'));
                                                            } else if (valueOrDefault<bool>(
                                                                    currentUserDocument
                                                                        ?.bGroup,
                                                                    false)
                                                                ? functions
                                                                    .getStringListFromJson(
                                                                        getRemoteConfigString(
                                                                            'premium_standard_badge_visibility'))
                                                                    .contains(
                                                                        'bGroup')
                                                                : functions
                                                                    .getStringListFromJson(
                                                                        getRemoteConfigString(
                                                                            'premium_standard_badge_visibility'))
                                                                    .contains(
                                                                        'aGroup')) {
                                                              return (e !=
                                                                  getRemoteConfigString(
                                                                      'premium_includes_standard_badge_title'));
                                                            } else {
                                                              return true;
                                                            }
                                                          }())
                                                      .toList();

                                              return Column(
                                                mainAxisSize: MainAxisSize.max,
                                                children: List.generate(
                                                    benefitlist.length,
                                                    (benefitlistIndex) {
                                                  final benefitlistItem =
                                                      benefitlist[
                                                          benefitlistIndex];
                                                  return Padding(
                                                    padding:
                                                        const EdgeInsetsDirectional
                                                            .fromSTEB(35.0,
                                                            30.0, 35.0, 0.0),
                                                    child: Row(
                                                      mainAxisSize:
                                                          MainAxisSize.max,
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        ShaderMask(
                                                          shaderCallback: (Rect
                                                                  bounds) =>
                                                              const LinearGradient(
                                                            begin: Alignment
                                                                .centerLeft,
                                                            end: Alignment
                                                                .centerRight,
                                                            colors: [
                                                              Color(0xFFFB6A47),
                                                              Color(0xFFAC34E8)
                                                            ],
                                                            tileMode:
                                                                TileMode.clamp,
                                                          ).createShader(
                                                                  bounds),
                                                          child: const Icon(
                                                            Icons.check_sharp,
                                                            color: Colors.white,
                                                            size: 26.0,
                                                          ),
                                                        ),
                                                        Flexible(
                                                          child: Padding(
                                                            padding:
                                                                const EdgeInsetsDirectional
                                                                    .fromSTEB(
                                                                    26.0,
                                                                    0.0,
                                                                    0.0,
                                                                    0.0),
                                                            child: Column(
                                                              mainAxisSize:
                                                                  MainAxisSize
                                                                      .max,
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .start,
                                                              children: [
                                                                Padding(
                                                                  padding:
                                                                      const EdgeInsetsDirectional
                                                                          .fromSTEB(
                                                                          0.0,
                                                                          3.0,
                                                                          0.0,
                                                                          0.0),
                                                                  child: Text(
                                                                    benefitlistItem,
                                                                    style: FlutterFlowTheme.of(
                                                                            context)
                                                                        .bodyMedium
                                                                        .override(
                                                                          fontFamily:
                                                                              'BT Beau Sans',
                                                                          fontSize:
                                                                              16.0,
                                                                          letterSpacing:
                                                                              0.0,
                                                                          fontWeight:
                                                                              FontWeight.bold,
                                                                          useGoogleFonts:
                                                                              false,
                                                                        ),
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  );
                                                }),
                                              );
                                            },
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  Align(
                                    alignment:
                                        const AlignmentDirectional(0.0, 0.0),
                                    child: Container(
                                      width: 260.0,
                                      height: 25.0,
                                      decoration: BoxDecoration(
                                        color: FlutterFlowTheme.of(context)
                                            .primaryBackground,
                                        borderRadius:
                                            BorderRadius.circular(100.0),
                                        border: Border.all(
                                          color: const Color(0xFFB9BFC8),
                                        ),
                                      ),
                                      child: Align(
                                        alignment: const AlignmentDirectional(
                                            0.0, 0.0),
                                        child: Text(
                                          'Benefits of Chyrpe evolved',
                                          style: FlutterFlowTheme.of(context)
                                              .bodyMedium
                                              .override(
                                                fontFamily: 'BT Beau Sans',
                                                fontSize: 11.0,
                                                letterSpacing: 0.0,
                                                fontWeight: FontWeight.w500,
                                                useGoogleFonts: false,
                                              ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ].addToEnd(const SizedBox(height: 50.0)),
                        ),
                      ),
                    ),
                  ],
                ),
                Container(
                  width: MediaQuery.sizeOf(context).width * 1.0,
                  height: 180.0,
                  decoration: BoxDecoration(
                    color: FlutterFlowTheme.of(context).primaryBackground,
                    border: Border.all(
                      color: const Color(0xFFD4D8DE),
                    ),
                  ),
                  alignment: const AlignmentDirectional(0.0, 1.0),
                  child: Padding(
                    padding: const EdgeInsetsDirectional.fromSTEB(
                        14.0, 17.0, 14.0, 0.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        AndroidNotice(),
                        Builder(
                          builder: (context) => Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 14.0, 0.0, 0.0),
                            child: wrapWithModel(
                              model: _model.gradientButtonEvolveModel,
                              updateCallback: () => safeSetState(() {}),
                              updateOnChange: true,
                              child: GradientButtonEvolveWidget(
                                title:
                                    'Upgrade - ${revenue_cat.offerings!.current!.getPackage(_model.selectedSubscription!.packageId)!.storeProduct.currencyCode} ${(revenue_cat.offerings!.current!.getPackage(_model.selectedSubscription!.packageId)!.storeProduct.price.toStringAsFixed(2))} total',
                                action: () async {
                                  unawaited(
                                    () async {
                                      try {
                                        if ((!isiOS)) {
                                          analytics.logEvent(
                                              'Attempted to buy Evolved New Android');
                                        } else {
                                          analytics.logEvent(
                                              'Attempted to buy Evolved New iOS');
                                        }
                                        analytics.logEvent(
                                            'Attempted to buy Evolved New');
                                      } catch (e) {}
                                      try {
                                        final result = await FirebaseFunctions
                                                .instanceFor(
                                                    region: 'europe-west2')
                                            .httpsCallable(
                                                'createPurchaseIntentionAlert')
                                            .call({
                                          "name": valueOrDefault(
                                              currentUserDocument?.name, ''),
                                          "gender":
                                              currentUserDocument!.gender!.name,
                                          "time": dateTimeFormat(
                                            "d/M h:mm a",
                                            getCurrentTimestamp,
                                            locale: FFLocalizations.of(context)
                                                .languageCode,
                                          ),
                                          "userUID": currentUserUid,
                                          "subscriptionType":
                                              'Evolved Subscription',
                                        });
                                        _model.evolvedSubscriptionLogCF =
                                            CreatePurchaseIntentionAlertCloudFunctionCallResponse(
                                          succeeded: true,
                                        );
                                      } on FirebaseFunctionsException catch (error) {
                                        _model.evolvedSubscriptionLogCF =
                                            CreatePurchaseIntentionAlertCloudFunctionCallResponse(
                                          errorCode: error.code,
                                          succeeded: false,
                                        );
                                      }
                                    }(),
                                  );
                                  _model.evolvedPurchase =
                                      await revenue_cat.purchasePackage(_model
                                          .selectedSubscription!.packageId);
                                  if (_model.evolvedPurchase!) {
                                    await showDialog(
                                      context: context,
                                      builder: (dialogContext) {
                                        return Dialog(
                                          elevation: 0,
                                          insetPadding: EdgeInsets.zero,
                                          backgroundColor: Colors.transparent,
                                          alignment: const AlignmentDirectional(
                                                  0.0, 0.0)
                                              .resolve(
                                                  Directionality.of(context)),
                                          child: GestureDetector(
                                            onTap: () =>
                                                FocusScope.of(dialogContext)
                                                    .unfocus(),
                                            child:
                                                const EvolvedSubscriptionWelcomeWidget(),
                                          ),
                                        );
                                      },
                                    );

                                    await Future.delayed(
                                        const Duration(milliseconds: 2000));
                                    Navigator.pop(context);

                                    if (widget.navigateBack) {
                                      context.pop();
                                    } else {
                                      context.goNamed(
                                        'ProfileHome',
                                        extra: <String, dynamic>{
                                          kTransitionInfoKey:
                                              const TransitionInfo(
                                            hasTransition: true,
                                            transitionType:
                                                PageTransitionType.topToBottom,
                                          ),
                                        },
                                      );
                                    }
                                  } else {
                                    await showDialog(
                                      context: context,
                                      builder: (dialogContext) {
                                        return Dialog(
                                          elevation: 0,
                                          insetPadding: EdgeInsets.zero,
                                          backgroundColor: Colors.transparent,
                                          alignment: const AlignmentDirectional(
                                                  0.0, 0.0)
                                              .resolve(
                                                  Directionality.of(context)),
                                          child: GestureDetector(
                                            onTap: () =>
                                                FocusScope.of(dialogContext)
                                                    .unfocus(),
                                            child: const GeneralPopupWidget(
                                              alertTitle:
                                                  'Purchase was not completed',
                                              alertText: 'Please try again',
                                            ),
                                          ),
                                        );
                                      },
                                    );
                                  }

                                  safeSetState(() {});
                                },
                              ),
                            ),
                          ),
                        ),
                        if (!((!isiOS) &&
                            getRemoteConfigBool(
                                'subscription_disclaimer_android_show')))
                          Align(
                            alignment: const AlignmentDirectional(0.0, 0.0),
                            child: Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 10.0, 0.0, 0.0),
                              child: FFButtonWidget(
                                onPressed: () async {
                                  await showModalBottomSheet(
                                    isScrollControlled: true,
                                    backgroundColor: Colors.transparent,
                                    enableDrag: false,
                                    context: context,
                                    builder: (context) {
                                      return GestureDetector(
                                        onTap: () =>
                                            FocusScope.of(context).unfocus(),
                                        child: Padding(
                                          padding:
                                              MediaQuery.viewInsetsOf(context),
                                          child: InfoSheetScrollableWidget(
                                            title: getRemoteConfigString(
                                                'discovery_standard_view_legal_information_sheet_title'),
                                            body: getRemoteConfigString(
                                                'chyrpe_standard_new_weekly_disclaimer'),
                                          ),
                                        ),
                                      );
                                    },
                                  ).then((value) => safeSetState(() {}));
                                },
                                text: getRemoteConfigString(
                                    'discovery_standard_view_legal_information_button_title'),
                                options: FFButtonOptions(
                                  height: 40.0,
                                  padding: const EdgeInsetsDirectional.fromSTEB(
                                      24.0, 0.0, 24.0, 0.0),
                                  iconPadding:
                                      const EdgeInsetsDirectional.fromSTEB(
                                          0.0, 0.0, 0.0, 0.0),
                                  color: Colors.white,
                                  textStyle: FlutterFlowTheme.of(context)
                                      .titleSmall
                                      .override(
                                        fontFamily: 'BT Beau Sans',
                                        color: FlutterFlowTheme.of(context)
                                            .primaryText,
                                        fontSize: 12.0,
                                        letterSpacing: 0.0,
                                        decoration: TextDecoration.underline,
                                        useGoogleFonts: false,
                                      ),
                                  elevation: 0.0,
                                  borderSide: const BorderSide(
                                    color: Colors.transparent,
                                    width: 0.0,
                                  ),
                                  borderRadius: BorderRadius.circular(100.0),
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            Container(
              width: MediaQuery.sizeOf(context).width * 1.0,
              height: 150.0,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFFDBAEFF),
                    FlutterFlowTheme.of(context).primaryBackground,
                    const Color(0x88FFFFFF)
                  ],
                  stops: const [0.0, 0.8, 1.0],
                  begin: const AlignmentDirectional(0.0, -1.0),
                  end: const AlignmentDirectional(0, 1.0),
                ),
              ),
              child: Stack(
                children: [
                  Stack(
                    children: [
                      Container(
                        width: MediaQuery.sizeOf(context).width * 1.0,
                        height: MediaQuery.sizeOf(context).height * 1.0,
                        decoration: const BoxDecoration(
                          gradient: LinearGradient(
                            colors: [Color(0xFFF76752), Color(0xFFDBAEFF)],
                            stops: [0.0, 1.0],
                            begin: AlignmentDirectional(-1.0, 0.0),
                            end: AlignmentDirectional(1.0, 0),
                          ),
                        ),
                      ),
                      Container(
                        width: MediaQuery.sizeOf(context).width * 1.0,
                        height: MediaQuery.sizeOf(context).height * 1.0,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              const Color(0x00FFFFFF),
                              FlutterFlowTheme.of(context).primaryBackground
                            ],
                            stops: const [0.0, 0.8],
                            begin: const AlignmentDirectional(0.0, -1.0),
                            end: const AlignmentDirectional(0, 1.0),
                          ),
                        ),
                      ),
                    ],
                  ),
                  Align(
                    alignment: const AlignmentDirectional(0.0, 0.0),
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'chyrpe',
                          style:
                              FlutterFlowTheme.of(context).bodyMedium.override(
                                    fontFamily: 'BT Beau Sans',
                                    fontSize: 28.0,
                                    letterSpacing: 0.0,
                                    fontWeight: FontWeight.w600,
                                    useGoogleFonts: false,
                                  ),
                        ),
                        Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              5.0, 0.0, 0.0, 0.0),
                          child: Container(
                            width: 65.0,
                            height: 21.0,
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [Color(0xFFA12CFD), Color(0xFFFF6C3E)],
                                stops: [0.0, 1.0],
                                begin: AlignmentDirectional(1.0, 0.0),
                                end: AlignmentDirectional(-1.0, 0),
                              ),
                              borderRadius: BorderRadius.circular(100.0),
                            ),
                            child: Align(
                              alignment: const AlignmentDirectional(0.0, 0.0),
                              child: Text(
                                'EVOLVED',
                                textAlign: TextAlign.center,
                                style: FlutterFlowTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'BT Beau Sans',
                                      color: FlutterFlowTheme.of(context).info,
                                      fontSize: 12.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.normal,
                                      useGoogleFonts: false,
                                    ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Align(
                    alignment: const AlignmentDirectional(-1.0, 0.0),
                    child: Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(
                          28.0, 0.0, 0.0, 0.0),
                      child: InkWell(
                        splashColor: Colors.transparent,
                        focusColor: Colors.transparent,
                        hoverColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        onTap: () async {
                          context.safePop();
                        },
                        child: Icon(
                          Icons.close_rounded,
                          color: FlutterFlowTheme.of(context).primaryText,
                          size: 24.0,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
