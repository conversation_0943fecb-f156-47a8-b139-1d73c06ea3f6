import '/backend/backend.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/backend/schema/structs/index.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button_evolve/gradient_button_evolve_widget.dart';
import '/general/gradient_button_gold/gradient_button_gold_widget.dart';
import '/subscription_sale_screens/subscription_selector_tile/subscription_selector_tile_widget.dart';
import '/subscription_sale_screens/subscription_selector_tile_n_s/subscription_selector_tile_n_s_widget.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import 'gold_evolved_subscription_new_widget.dart'
    show GoldEvolvedSubscriptionNewWidget;
import 'package:flutter/material.dart';
import '/subscription_sale_screens/standard_included_badge/standard_included_badge_widget.dart';


class GoldEvolvedSubscriptionNewModel
    extends FlutterFlowModel<GoldEvolvedSubscriptionNewWidget> {
  ///  Local state fields for this page.

  String subscriptionType = 'Gold';

  PurchaseDurationStruct? selectedSubscriptionGold = functions
                                          .getPurchaseDurationStructFromString(
                                              getRemoteConfigString(
                                                  'purchases_gold_offer_string'))
                                          .toList()[0];

  void updateSelectedSubscriptionGoldStruct(
      Function(PurchaseDurationStruct) updateFn) {
    updateFn(selectedSubscriptionGold ??= PurchaseDurationStruct());
  }

  PurchaseDurationStruct? selectedSubscriptionEvolved = functions
                                          .getPurchaseDurationStructFromString(
                                              getRemoteConfigString(
                                                  'purchases_evolved_offer_string'))
                                          .toList()[0];
                                          
  void updateSelectedSubscriptionEvolvedStruct(
      Function(PurchaseDurationStruct) updateFn) {
    updateFn(selectedSubscriptionEvolved ??= PurchaseDurationStruct());
  }

  ///  State fields for stateful widgets in this page.

  // Models for SubscriptionSelectorTile dynamic component.
  late FlutterFlowDynamicModels<SubscriptionSelectorTileModel>
      subscriptionSelectorTileModels1;
  // Model for GradientButtonGold component.
  late GradientButtonGoldModel gradientButtonGoldModel;
  // Stores action output result for [Cloud Function - createPurchaseIntentionAlert] action in GradientButtonGold widget.
  CreatePurchaseIntentionAlertCloudFunctionCallResponse? goldSubscriptionLogCF;
  // Stores action output result for [RevenueCat - Purchase] action in GradientButtonGold widget.
  bool? goldPurchase;
  // Models for SubscriptionSelectorTile dynamic component.
  late FlutterFlowDynamicModels<SubscriptionSelectorTileModel>
      subscriptionSelectorTileModels2;
  // Models for SubscriptionSelectorTileNS dynamic component.
  late FlutterFlowDynamicModels<SubscriptionSelectorTileNSModel>
      subscriptionSelectorTileNSModels2;
  // Model for GradientButtonEvolve component.
  late GradientButtonEvolveModel gradientButtonEvolveModel;
  late StandardIncludedBadgeModel standardIncludedBadgeModel1;
  late StandardIncludedBadgeModel standardIncludedBadgeModel2;
  // Stores action output result for [Cloud Function - createPurchaseIntentionAlert] action in GradientButtonEvolve widget.
  CreatePurchaseIntentionAlertCloudFunctionCallResponse?
      evolvedSubscriptionLogCF;
  // Stores action output result for [RevenueCat - Purchase] action in GradientButtonEvolve widget.
  bool? evolvedPurchase2;
  

  @override
  void initState(BuildContext context) {
    standardIncludedBadgeModel1 =
        createModel(context, () => StandardIncludedBadgeModel());
    standardIncludedBadgeModel2 =
        createModel(context, () => StandardIncludedBadgeModel());
    subscriptionSelectorTileModels1 =
        FlutterFlowDynamicModels(() => SubscriptionSelectorTileModel());
    gradientButtonGoldModel =
        createModel(context, () => GradientButtonGoldModel());
    subscriptionSelectorTileModels2 =
        FlutterFlowDynamicModels(() => SubscriptionSelectorTileModel());
    subscriptionSelectorTileNSModels2 =
        FlutterFlowDynamicModels(() => SubscriptionSelectorTileNSModel());
    gradientButtonEvolveModel =
        createModel(context, () => GradientButtonEvolveModel());
  }

  @override
  void dispose() {
    subscriptionSelectorTileModels1.dispose();
    standardIncludedBadgeModel1.dispose();
    standardIncludedBadgeModel2.dispose();
    gradientButtonGoldModel.dispose();
    subscriptionSelectorTileModels2.dispose();
    subscriptionSelectorTileNSModels2.dispose();
    gradientButtonEvolveModel.dispose();
  }
}
