import '/backend/backend.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/backend/schema/structs/index.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button_gold/gradient_button_gold_widget.dart';
import '/subscription_sale_screens/subscription_selector_tile/subscription_selector_tile_widget.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import 'gold_subscription_new_widget.dart' show GoldSubscriptionNewWidget;
import 'package:flutter/material.dart';
import '/subscription_sale_screens/standard_included_badge/standard_included_badge_widget.dart';

class GoldSubscriptionNewModel
    extends FlutterFlowModel<GoldSubscriptionNewWidget> {
  ///  Local state fields for this page.

  PurchaseDurationStruct? selectedSubscription = functions
                                          .getPurchaseDurationStructFromString(
                                              getRemoteConfigString(
                                                  'purchases_gold_offer_string'))
                                          .toList()[0];
  void updateSelectedSubscriptionStruct(
      Function(PurchaseDurationStruct) updateFn) {
    updateFn(selectedSubscription ??= PurchaseDurationStruct());
  }

  ///  State fields for stateful widgets in this page.

  // Models for SubscriptionSelectorTile dynamic component.
  late FlutterFlowDynamicModels<SubscriptionSelectorTileModel>
      subscriptionSelectorTileModels;
  // Model for GradientButtonGold component.
  late GradientButtonGoldModel gradientButtonGoldModel;
  // Stores action output result for [Cloud Function - createPurchaseIntentionAlert] action in GradientButtonGold widget.
  CreatePurchaseIntentionAlertCloudFunctionCallResponse? goldSubscriptionLogCF;
  late StandardIncludedBadgeModel standardIncludedBadgeModel;
  // Stores action output result for [RevenueCat - Purchase] action in GradientButtonGold widget.
  bool? goldPurchase;

  @override
  void initState(BuildContext context) {
    subscriptionSelectorTileModels =
        FlutterFlowDynamicModels(() => SubscriptionSelectorTileModel());
    standardIncludedBadgeModel =
        createModel(context, () => StandardIncludedBadgeModel());
    gradientButtonGoldModel =
        createModel(context, () => GradientButtonGoldModel());
  }

  @override
  void dispose() {
    subscriptionSelectorTileModels.dispose();
    standardIncludedBadgeModel.dispose();
    gradientButtonGoldModel.dispose();
  }
}
