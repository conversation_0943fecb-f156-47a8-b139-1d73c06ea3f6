import '/backend/backend.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/backend/schema/structs/index.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button_gold/gradient_button_gold_widget.dart';
import '/subscription_sale_screens/standard_included_badge/standard_included_badge_widget.dart';
import 'gold_subscription_offer_widget.dart' show GoldSubscriptionOfferWidget;
import 'package:flutter/material.dart';


class GoldSubscriptionOfferModel
    extends FlutterFlowModel<GoldSubscriptionOfferWidget> {
  ///  Local state fields for this page.

  PurchaseDurationStruct? selectedSubscription;
  void updateSelectedSubscriptionStruct(
      Function(PurchaseDurationStruct) updateFn) {
    updateFn(selectedSubscription ??= PurchaseDurationStruct());
  }

  ///  State fields for stateful widgets in this page.

  // Model for StandardIncludedBadge component.
  late StandardIncludedBadgeModel standardIncludedBadgeModel;
  // Model for GradientButtonGold component.
  late GradientButtonGoldModel gradientButtonGoldModel;
  // Stores action output result for [Cloud Function - createPurchaseIntentionAlert] action in GradientButtonGold widget.
  CreatePurchaseIntentionAlertCloudFunctionCallResponse? goldSubscriptionLogCF;
  // Stores action output result for [RevenueCat - Purchase] action in GradientButtonGold widget.
  bool? goldPurchase;

  @override
  void initState(BuildContext context) {
    standardIncludedBadgeModel =
        createModel(context, () => StandardIncludedBadgeModel());
    gradientButtonGoldModel =
        createModel(context, () => GradientButtonGoldModel());
  }

  @override
  void dispose() {
    standardIncludedBadgeModel.dispose();
    gradientButtonGoldModel.dispose();
  }
}
