import 'package:chyrpe/amplitudeConfig.dart';

import '/auth/firebase_auth/auth_util.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/backend/schema/enums/enums.dart';
import '/components/general_popup_widget.dart';
import '/components/gold_subscription_welcome_widget.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/general/gradient_button_gold/gradient_button_gold_widget.dart';
import '/general/info_sheet_scrollable/info_sheet_scrollable_widget.dart';
import '/subscription_sale_screens/standard_included_badge/standard_included_badge_widget.dart';
import 'dart:async';
import '/custom_code/widgets/index.dart' as custom_widgets;
import '/flutter_flow/custom_functions.dart' as functions;
import '/flutter_flow/revenue_cat_util.dart' as revenue_cat;
import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'gold_subscription_offer_model.dart';
export 'gold_subscription_offer_model.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import '/discovery/discovery/utils/daily_like_limit.dart';

class GoldSubscriptionOfferWidget extends StatefulWidget {
  const GoldSubscriptionOfferWidget({super.key});

  @override
  State<GoldSubscriptionOfferWidget> createState() =>
      _GoldSubscriptionOfferWidgetState();
}

class _GoldSubscriptionOfferWidgetState
    extends State<GoldSubscriptionOfferWidget> with WidgetsBindingObserver, DailyLikeLimit, WeeklyLikeLimit, LikeLimitInterpolator {
  late GoldSubscriptionOfferModel _model;

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    if (state == AppLifecycleState.resumed) {
      await revenue_cat.restorePurchases();
      await Purchases.syncPurchases();
      if (revenue_cat.activeEntitlementIds.contains('gold_access')) {
                                    await showDialog(
                                      context: context,
                                      builder: (dialogContext) {
                                        return Dialog(
                                          elevation: 0,
                                          insetPadding: EdgeInsets.zero,
                                          backgroundColor: Colors.transparent,
                                          alignment: const AlignmentDirectional(
                                                  0.0, 0.0)
                                              .resolve(
                                                  Directionality.of(context)),
                                          child: GestureDetector(
                                            onTap: () =>
                                                FocusScope.of(dialogContext)
                                                    .unfocus(),
                                            child:
                                                const GoldSubscriptionWelcomeWidget(),
                                          ),
                                        );
                                      },
                                    );

                                    await Future.delayed(
                                        const Duration(milliseconds: 2000));
                                    Navigator.pop(context);

                                    context.goNamed(
                                      'ProfileHome',
                                      extra: <String, dynamic>{
                                        kTransitionInfoKey: const TransitionInfo(
                                          hasTransition: true,
                                          transitionType:
                                              PageTransitionType.topToBottom,
                                        ),
                                      },
                                    );
        }
    }
  }

  

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _model = createModel(context, () => GoldSubscriptionOfferModel());
  }

  @override
  void dispose() {
    _model.dispose();
    WidgetsBinding.instance.removeObserver(this);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        body: Stack(
          alignment: const AlignmentDirectional(0.0, -1.0),
          children: [
            Stack(
              alignment: const AlignmentDirectional(0.0, 1.0),
              children: [
                Stack(
                  alignment: const AlignmentDirectional(0.0, 0.0),
                  children: [
                    Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(
                          0.0, 150.0, 0.0, 160.0),
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                             if (valueOrDefault<bool>(
                                    currentUserDocument?.bGroup, false)
                                ? functions
                                    .getStringListFromJson(
                                        getRemoteConfigString(
                                            'gold_offer50_tagline_visible'))
                                    .contains('bGroup')
                                : functions
                                    .getStringListFromJson(
                                        getRemoteConfigString(
                                            'gold_offer50_tagline_visible'))
                                    .contains('aGroup'))
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  15.0, 0.0, 15.0, 49.0),
                              child: Text(
                                getRemoteConfigString(
                                    'purchases_gold_new_tagline'),
                                textAlign: TextAlign.center,
                                style: FlutterFlowTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'BT Beau Sans',
                                      fontSize: 26.0,
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.bold,
                                      useGoogleFonts: false,
                                    ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 0.0, 0.0, 39.0),
                              child: Column(
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        15.0, 0.0, 15.0, 0.0),
                                    child: Container(
                                      width: MediaQuery.sizeOf(context).width *
                                          1.0,
                                      decoration: BoxDecoration(
                                        gradient: const LinearGradient(
                                          colors: [
                                            Color(0xFFFFCC10),
                                            Color(0xFFBE9F04)
                                          ],
                                          stops: [0.0, 1.0],
                                          begin:
                                              AlignmentDirectional(0.0, -1.0),
                                          end: AlignmentDirectional(0, 1.0),
                                        ),
                                        borderRadius:
                                            BorderRadius.circular(10.0),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsetsDirectional.fromSTEB(
                                            3.0, 3.0, 3.0, 3.0),
                                        child: Column(
                                          mainAxisSize: MainAxisSize.max,
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Padding(
                                              padding: const EdgeInsetsDirectional
                                                  .fromSTEB(
                                                      0.0, 21.0, 0.0, 21.0),
                                              child: Column(
                                                mainAxisSize: MainAxisSize.max,
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  Align(
                                                    alignment:
                                                        const AlignmentDirectional(
                                                            0.0, 0.0),
                                                    child: Text(
                                                      'Limited Time Offer!',
                                                      style: FlutterFlowTheme
                                                              .of(context)
                                                          .bodyMedium
                                                          .override(
                                                            fontFamily:
                                                                'BT Beau Sans',
                                                            color: FlutterFlowTheme
                                                                    .of(context)
                                                                .primaryBackground,
                                                            fontSize: 24.0,
                                                            letterSpacing: 0.0,
                                                            fontWeight:
                                                                FontWeight.w500,
                                                            useGoogleFonts:
                                                                false,
                                                          ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Padding(
                                              padding: const EdgeInsetsDirectional
                                                  .fromSTEB(0.0, 2.0, 0.0, 0.0),
                                              child: Container(
                                                width:
                                                    MediaQuery.sizeOf(context)
                                                            .width *
                                                        1.0,
                                                decoration: BoxDecoration(
                                                  color: FlutterFlowTheme.of(
                                                          context)
                                                      .primaryBackground,
                                                  borderRadius:
                                                      const BorderRadius.only(
                                                    bottomLeft:
                                                        Radius.circular(9.0),
                                                    bottomRight:
                                                        Radius.circular(9.0),
                                                    topLeft:
                                                        Radius.circular(0.0),
                                                    topRight:
                                                        Radius.circular(0.0),
                                                  ),
                                                ),
                                                alignment: const AlignmentDirectional(
                                                    0.0, 0.0),
                                                child: Padding(
                                                  padding: const EdgeInsetsDirectional
                                                      .fromSTEB(
                                                          25.0, 25.0, 25.0, 20.0),
                                                  child: Column(
                                                    mainAxisSize:
                                                        MainAxisSize.max,
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .center,
                                                    children: [
                                                      Container(
                                                        child: custom_widgets
                                                            .MarkdownText(
                                                          text: getRemoteConfigString(
                                                              'gold_offer50_headline'),
                                                        ),
                                                      ),
                                                      Align(
                                                        alignment:
                                                            const AlignmentDirectional(
                                                                0.0, 0.0),
                                                        child: Padding(
                                                          padding:
                                                              const EdgeInsetsDirectional
                                                                  .fromSTEB(
                                                                      0.0,
                                                                      40.0,
                                                                      0.0,
                                                                      0.0),
                                                          child: Text(
                                                            'First month ${revenue_cat
                                                        .offerings!.current!
                                                        .getPackage(
                                                            'chyrpe_gold_1m')!
                                                        .storeProduct
                                                        .currencyCode} ${(revenue_cat
                                                        .offerings!.current!
                                                        .getPackage(
                                                            'chyrpe_gold_1m')!
                                                        .storeProduct.price/2).toStringAsFixed(2)}',
                                                            style: FlutterFlowTheme
                                                                    .of(context)
                                                                .bodyMedium
                                                                .override(
                                                                  fontFamily:
                                                                      'BT Beau Sans',
                                                                  fontSize:
                                                                      18.0,
                                                                  letterSpacing:
                                                                      0.0,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold,
                                                                  useGoogleFonts:
                                                                      false,
                                                                ),
                                                          ),
                                                        ),
                                                      ),
                                                      Align(
                                                        alignment:
                                                            const AlignmentDirectional(
                                                                0.0, 0.0),
                                                        child: Padding(
                                                          padding:
                                                              const EdgeInsetsDirectional
                                                                  .fromSTEB(
                                                                      0.0,
                                                                      0.0,
                                                                      0.0,
                                                                      40.0),
                                                          child: Text(
                                                            'Normally ${revenue_cat
                                                        .offerings!.current!
                                                        .getPackage(
                                                            'chyrpe_gold_1m')!
                                                        .storeProduct
                                                        .currencyCode} ${(revenue_cat
                                                        .offerings!.current!
                                                        .getPackage(
                                                            'chyrpe_gold_1m')!
                                                        .storeProduct.price/1).toStringAsFixed(2)}',
                                                            style: FlutterFlowTheme
                                                                    .of(context)
                                                                .bodyMedium
                                                                .override(
                                                                  fontFamily:
                                                                      'BT Beau Sans',
                                                                  fontSize:
                                                                      16.0,
                                                                  letterSpacing:
                                                                      0.0,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .normal,
                                                                  useGoogleFonts:
                                                                      false,
                                                                ),
                                                          ),
                                                        ),
                                                      ),
                                                      Align(
                                                        alignment:
                                                            const AlignmentDirectional(
                                                                0.0, 0.0),
                                                        child: Row(
                                                          mainAxisSize:
                                                              MainAxisSize.max,
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .center,
                                                          children: [
                                                            Align(
                                                              alignment:
                                                                  const AlignmentDirectional(
                                                                      0.0, 0.0),
                                                              child: Padding(
                                                                padding: const EdgeInsetsDirectional
                                                                  .fromSTEB(
                                                                      0.0,
                                                                      0.0,
                                                                      0.0,
                                                                      2.0),
                                                                child: Text(
                                                                  'Offer ends in ',
                                                                  style: FlutterFlowTheme.of(
                                                                          context)
                                                                      .bodyMedium
                                                                      .override(
                                                                        fontFamily:
                                                                            'BT Beau Sans',
                                                                        fontSize:
                                                                            18.0,
                                                                        letterSpacing:
                                                                            0.0,
                                                                        fontWeight:
                                                                            FontWeight
                                                                                .normal,
                                                                        useGoogleFonts:
                                                                            false,
                                                                      ),
                                                                ),
                                                              ),
                                                            ),
                                                            Container(
                                                              child: custom_widgets
                                                                  .TimerWidgetDiscountScreen(
                                                                initialTime: currentUserDocument!.subscriptionOfferDeadline -
                                                                    DateTime.now()
                                                                        .millisecondsSinceEpoch,
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ].divide(
                                                        const SizedBox(height: 2.0)),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            if ((valueOrDefault<bool>(
                                        currentUserDocument?.bGroup, false)
                                    ? functions
                                        .getStringListFromJson(
                                            getRemoteConfigString(
                                                'premium_standard_badge_visibility'))
                                        .contains('bGroup')
                                    : functions
                                        .getStringListFromJson(
                                            getRemoteConfigString(
                                                'premium_standard_badge_visibility'))
                                        .contains('aGroup')) &&
                                !(revenue_cat.activeEntitlementIds
                                        .contains('paid_standard_lifetime') ||
                                    revenue_cat.activeEntitlementIds
                                        .contains('paid_standard_1w')) &&
                                (currentUserDocument?.gender == Gender.Male))
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    15.0, 0.0, 15.0, 14.0),
                                child: AuthUserStreamWidget(
                                  builder: (context) => wrapWithModel(
                                    model: _model.standardIncludedBadgeModel,
                                    updateCallback: () => safeSetState(() {}),
                                    child: const StandardIncludedBadgeWidget(),
                                  ),
                                ),
                              ),
                            Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  15.0, 0.0, 15.0, 0.0),
                              child: Stack(
                                children: [
                                  Padding(
                                    padding: const EdgeInsetsDirectional.fromSTEB(
                                        0.0, 13.0, 0.0, 0.0),
                                    child: Container(
                                      width: MediaQuery.sizeOf(context).width *
                                          1.0,
                                      decoration: BoxDecoration(
                                        borderRadius:
                                            BorderRadius.circular(11.0),
                                        border: Border.all(
                                          color: const Color(0xFFB9BFC8),
                                          width: 1.0,
                                        ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsetsDirectional.fromSTEB(
                                            0.0, 6.0, 0.0, 30.0),
                                        child: AuthUserStreamWidget(
                                          builder: (context) => Builder(
                                            builder: (context) {
                                              final benefitlist = (currentUserDocument
                                                          ?.gender ==
                                                      Gender.Male
                                                  ? functions.getStringListFromJson(
                                                      getRemoteConfigString(interpolateLikeLimitPlaceholders(
                                                          'gold_benefitlist_m_placeholder')))
                                                  : functions
                                                      .getStringListFromJson(interpolateLikeLimitPlaceholders(
                                                          getRemoteConfigString(
                                                              'gold_benefitlist_w_placeholder'))))
                                                      .where((e) => () {
                                                            if ((revenue_cat
                                                                        .activeEntitlementIds
                                                                        .contains(
                                                                            'paid_standard_lifetime') ||
                                                                    revenue_cat
                                                                        .activeEntitlementIds
                                                                        .contains(
                                                                            'paid_standard_1w')) ||
                                                                (currentUserDocument
                                                                        ?.gender !=
                                                                    Gender
                                                                        .Male)) {
                                                              return (e !=
                                                                  getRemoteConfigString(
                                                                      'premium_includes_standard_badge_title'));
                                                            } else if (valueOrDefault<bool>(
                                                                    currentUserDocument
                                                                        ?.bGroup,
                                                                    false)
                                                                ? functions
                                                                    .getStringListFromJson(
                                                                        getRemoteConfigString(
                                                                            'premium_standard_badge_visibility'))
                                                                    .contains(
                                                                        'bGroup')
                                                                : functions
                                                                    .getStringListFromJson(
                                                                        getRemoteConfigString(
                                                                            'premium_standard_badge_visibility'))
                                                                    .contains(
                                                                        'aGroup')) {
                                                              return (e !=
                                                                  getRemoteConfigString(
                                                                      'premium_includes_standard_badge_title'));
                                                            } else {
                                                              return true;
                                                            }
                                                          }())
                                                      .toList();

                                              return Column(
                                                mainAxisSize: MainAxisSize.max,
                                                children: List.generate(
                                                    benefitlist.length,
                                                    (benefitlistIndex) {
                                                  final benefitlistItem =
                                                      benefitlist[
                                                          benefitlistIndex];
                                                  return Padding(
                                                    padding:
                                                        const EdgeInsetsDirectional
                                                            .fromSTEB(
                                                                35.0,
                                                                30.0,
                                                                35.0,
                                                                0.0),
                                                    child: Row(
                                                      mainAxisSize:
                                                          MainAxisSize.max,
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        const Icon(
                                                          Icons.check_sharp,
                                                          color:
                                                              Color(0xFFFFB800),
                                                          size: 26.0,
                                                        ),
                                                        Flexible(
                                                          child: Padding(
                                                            padding:
                                                                const EdgeInsetsDirectional
                                                                    .fromSTEB(
                                                                        26.0,
                                                                        0.0,
                                                                        0.0,
                                                                        0.0),
                                                            child: Column(
                                                              mainAxisSize:
                                                                  MainAxisSize
                                                                      .max,
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .start,
                                                              children: [
                                                                Padding(
                                                                  padding: const EdgeInsetsDirectional
                                                                      .fromSTEB(
                                                                          0.0,
                                                                          3.0,
                                                                          0.0,
                                                                          0.0),
                                                                  child: Text(
                                                                    benefitlistItem,
                                                                    style: FlutterFlowTheme.of(
                                                                            context)
                                                                        .bodyMedium
                                                                        .override(
                                                                          fontFamily:
                                                                              'BT Beau Sans',
                                                                          fontSize:
                                                                              16.0,
                                                                          letterSpacing:
                                                                              0.0,
                                                                          fontWeight:
                                                                              FontWeight.bold,
                                                                          useGoogleFonts:
                                                                              false,
                                                                        ),
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  );
                                                }),
                                              );
                                            },
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  Align(
                                    alignment: const AlignmentDirectional(0.0, 0.0),
                                    child: Container(
                                      width: 260.0,
                                      height: 25.0,
                                      decoration: BoxDecoration(
                                        color: FlutterFlowTheme.of(context)
                                            .primaryBackground,
                                        borderRadius:
                                            BorderRadius.circular(100.0),
                                        border: Border.all(
                                          color: const Color(0xFFB9BFC8),
                                        ),
                                      ),
                                      child: Align(
                                        alignment:
                                            const AlignmentDirectional(0.0, 0.0),
                                        child: Text(
                                          'Benefits of chyrpe Gold',
                                          style: FlutterFlowTheme.of(context)
                                              .bodyMedium
                                              .override(
                                                fontFamily: 'BT Beau Sans',
                                                fontSize: 11.0,
                                                letterSpacing: 0.0,
                                                fontWeight: FontWeight.w500,
                                                useGoogleFonts: false,
                                              ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ].addToEnd(const SizedBox(height: 50.0)),
                        ),
                      ),
                    ),
                  ],
                ),
                Container(
                  width: MediaQuery.sizeOf(context).width * 1.0,
                  height: 160.0,
                  decoration: BoxDecoration(
                    color: FlutterFlowTheme.of(context).primaryBackground,
                    border: Border.all(
                      color: const Color(0xFFD4D8DE),
                    ),
                  ),
                  alignment: const AlignmentDirectional(0.0, 1.0),
                  child: Padding(
                    padding:
                        const EdgeInsetsDirectional.fromSTEB(14.0, 17.0, 14.0, 0.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Builder(
                          builder: (context) => Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 14.0, 0.0, 0.0),
                            child: wrapWithModel(
                              model: _model.gradientButtonGoldModel,
                              updateCallback: () => safeSetState(() {}),
                              child: GradientButtonGoldWidget(
                                title: 'Upgrade',
                                action: () async {
                                  try {
                                    if (isAndroid) {
                                      analytics.logEvent('Attempted to buy Gold offer Android');
                                    } else {
                                      analytics.logEvent('Attempted to buy Gold offer iOS');
                                    }
                                    analytics.logEvent('Attempted to buy Gold offer');
                                  } catch(e) {}
                                  unawaited(
                                    () async {
                                      try {
                                        final result = await FirebaseFunctions
                                                .instanceFor(
                                                    region: 'europe-west2')
                                            .httpsCallable(
                                                'createPurchaseIntentionAlert')
                                            .call({
                                          "name": valueOrDefault(
                                              currentUserDocument?.name, ''),
                                          "gender":
                                              currentUserDocument!.gender!.name,
                                          "time": dateTimeFormat(
                                            "d/M h:mm a",
                                            getCurrentTimestamp,
                                            locale: FFLocalizations.of(context)
                                                .languageCode,
                                          ),
                                          "userUID": currentUserUid,
                                          "subscriptionType":
                                              'Gold Subscription',
                                        });
                                        _model.goldSubscriptionLogCF =
                                            CreatePurchaseIntentionAlertCloudFunctionCallResponse(
                                          succeeded: true,
                                        );
                                      } on FirebaseFunctionsException catch (error) {
                                        _model.goldSubscriptionLogCF =
                                            CreatePurchaseIntentionAlertCloudFunctionCallResponse(
                                          errorCode: error.code,
                                          succeeded: false,
                                        );
                                      }
                                    }(),
                                  );

                                  if (isiOS) {
                                  await launchURL(currentUserDocument!.appleSubOfferLink);
                                  } else {
                                    if (isAndroid) {
                                      Future<bool> purchaseOfferPackageAndroid() async {
                                        
  try {
final revenueCatPackage = revenue_cat.offerings!.current!.getPackage('chyrpe_gold_1m')!.storeProduct.subscriptionOptions!.firstWhere((e) => e.id==currentUserDocument!.androidSubOfferString);
    var customerInfo = await Purchases.purchaseSubscriptionOption(revenue_cat.offerings!.current!.getPackage('chyrpe_gold_1m')!.storeProduct.subscriptionOptions!.firstWhere((e) => e.id==currentUserDocument!.androidSubOfferString));
    return true;
  } catch (_) {
    return false;
  }
}
                                     _model.goldPurchase = await purchaseOfferPackageAndroid();

                                     if (_model.goldPurchase!) {
                                    await showDialog(
                                      context: context,
                                      builder: (dialogContext) {
                                        return Dialog(
                                          elevation: 0,
                                          insetPadding: EdgeInsets.zero,
                                          backgroundColor: Colors.transparent,
                                          alignment: const AlignmentDirectional(
                                                  0.0, 0.0)
                                              .resolve(
                                                  Directionality.of(context)),
                                          child: GestureDetector(
                                            onTap: () =>
                                                FocusScope.of(dialogContext)
                                                    .unfocus(),
                                            child:
                                                const GoldSubscriptionWelcomeWidget(),
                                          ),
                                        );
                                      },
                                    );

                                    await Future.delayed(
                                        const Duration(milliseconds: 2000));
                                    Navigator.pop(context);

                                    context.goNamed(
                                      'ProfileHome',
                                      extra: <String, dynamic>{
                                        kTransitionInfoKey: const TransitionInfo(
                                          hasTransition: true,
                                          transitionType:
                                              PageTransitionType.topToBottom,
                                        ),
                                      },
                                    );
                                  } else {
                                    await showDialog(
                                      context: context,
                                      builder: (dialogContext) {
                                        return Dialog(
                                          elevation: 0,
                                          insetPadding: EdgeInsets.zero,
                                          backgroundColor: Colors.transparent,
                                          alignment: const AlignmentDirectional(
                                                  0.0, 0.0)
                                              .resolve(
                                                  Directionality.of(context)),
                                          child: GestureDetector(
                                            onTap: () =>
                                                FocusScope.of(dialogContext)
                                                    .unfocus(),
                                            child: const GeneralPopupWidget(
                                              alertTitle:
                                                  'Purchase was not completed',
                                              alertText: 'Please try again',
                                            ),
                                          ),
                                        );
                                      },
                                    );
                                  }

                                  safeSetState(() {});
                                    }
                                  }

                                  safeSetState(() {});
                                },
                              ),
                            ),
                          ),
                        ),
                        Align(
                          alignment: const AlignmentDirectional(0.0, 0.0),
                          child: Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 10.0, 0.0, 0.0),
                            child: FFButtonWidget(
                              onPressed: () async {
                                await showModalBottomSheet(
                                  isScrollControlled: true,
                                  backgroundColor: Colors.transparent,
                                  enableDrag: false,
                                  context: context,
                                  builder: (context) {
                                    return GestureDetector(
                                      onTap: () =>
                                          FocusScope.of(context).unfocus(),
                                      child: Padding(
                                        padding:
                                            MediaQuery.viewInsetsOf(context),
                                        child: InfoSheetScrollableWidget(
                                          title: getRemoteConfigString(
                                              'discovery_standard_view_legal_information_sheet_title'),
                                          body: getRemoteConfigString(
                                              'chyrpe_standard_new_weekly_disclaimer'),
                                        ),
                                      ),
                                    );
                                  },
                                ).then((value) => safeSetState(() {}));
                              },
                              text: getRemoteConfigString(
                                  'discovery_standard_view_legal_information_button_title'),
                              options: FFButtonOptions(
                                height: 40.0,
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    24.0, 0.0, 24.0, 0.0),
                                iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                    0.0, 0.0, 0.0, 0.0),
                                color: Colors.white,
                                textStyle: FlutterFlowTheme.of(context)
                                    .titleSmall
                                    .override(
                                      fontFamily: 'BT Beau Sans',
                                      color: FlutterFlowTheme.of(context)
                                          .primaryText,
                                      fontSize: 12.0,
                                      letterSpacing: 0.0,
                                      decoration: TextDecoration.underline,
                                      useGoogleFonts: false,
                                    ),
                                elevation: 0.0,
                                borderSide: const BorderSide(
                                  color: Colors.transparent,
                                  width: 0.0,
                                ),
                                borderRadius: BorderRadius.circular(100.0),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            Container(
              width: MediaQuery.sizeOf(context).width * 1.0,
              height: 150.0,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFFFEDA73),
                    FlutterFlowTheme.of(context).primaryBackground,
                    const Color(0x88FFFFFF)
                  ],
                  stops: const [0.0, 0.8, 1.0],
                  begin: const AlignmentDirectional(0.0, -1.0),
                  end: const AlignmentDirectional(0, 1.0),
                ),
              ),
              child: Stack(
                children: [
                  Align(
                    alignment: const AlignmentDirectional(0.0, 0.0),
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'chyrpe',
                          style:
                              FlutterFlowTheme.of(context).bodyMedium.override(
                                    fontFamily: 'BT Beau Sans',
                                    fontSize: 28.0,
                                    letterSpacing: 0.0,
                                    fontWeight: FontWeight.w600,
                                    useGoogleFonts: false,
                                  ),
                        ),
                        Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              5.0, 0.0, 0.0, 0.0),
                          child: Container(
                            width: 55.0,
                            height: 21.0,
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [Color(0xFFFFCC10), Color(0xFFBE9F04)],
                                stops: [0.0, 1.0],
                                begin: AlignmentDirectional(1.0, 0.0),
                                end: AlignmentDirectional(-1.0, 0),
                              ),
                              borderRadius: BorderRadius.circular(100.0),
                            ),
                            child: Align(
                              alignment: const AlignmentDirectional(0.0, 0.0),
                              child: Text(
                                                          'GOLD',
                                                          textAlign: TextAlign.center,
                                                          style: FlutterFlowTheme.of(context)
                                                              .bodyMedium
                                                              .override(
                                                                fontFamily: 'BT Beau Sans',
                                                                color: FlutterFlowTheme.of(context).info,
                                                                fontSize: 12.0,
                                                                letterSpacing: 0.0,
                                                                fontWeight: FontWeight.normal,
                                                                useGoogleFonts: false,
                                                              ),
                                                        ),
                          ),
                        ),
                        ),
                      ],
                    ),
                  ),
                  Align(
                    alignment: const AlignmentDirectional(-1.0, 0.0),
                    child: Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(28.0, 0.0, 0.0, 0.0),
                      child: InkWell(
                        splashColor: Colors.transparent,
                        focusColor: Colors.transparent,
                        hoverColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        onTap: () async {
                          context.safePop();
                        },
                        child: Icon(
                          Icons.close_rounded,
                          color: FlutterFlowTheme.of(context).primaryText,
                          size: 24.0,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
