import '/backend/backend.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/backend/schema/structs/index.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button_evolve/gradient_button_evolve_widget.dart';
import '/general/gradient_button_silver/gradient_button_silver_widget.dart';
import '/subscription_sale_screens/standard_included_badge/standard_included_badge_widget.dart';
import '/subscription_sale_screens/subscription_selector_tile/subscription_selector_tile_widget.dart';
import '/subscription_sale_screens/subscription_selector_tile_n_s/subscription_selector_tile_n_s_widget.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import 'plus_evolved_subscription_new_widget.dart'
    show PlusEvolvedSubscriptionNewWidget;
import 'package:flutter/material.dart';

class PlusEvolvedSubscriptionNewModel
    extends FlutterFlowModel<PlusEvolvedSubscriptionNewWidget> {
  ///  Local state fields for this page.

  String subscriptionType = 'Plus';

  PurchaseDurationStruct? selectedSubscriptionPlus = functions
                                          .getPurchaseDurationStructFromString(
                                              getRemoteConfigString(
                                                  'purchases_plus_offer_string'))
                                          .toList()[0];

  void updateSelectedSubscriptionPlusStruct(
      Function(PurchaseDurationStruct) updateFn) {
    updateFn(selectedSubscriptionPlus ??= PurchaseDurationStruct());
  }

  PurchaseDurationStruct? selectedSubscriptionEvolved = functions
                                          .getPurchaseDurationStructFromString(
                                              getRemoteConfigString(
                                                  'purchases_evolved_offer_string'))
                                          .toList()[0];
                                          
  void updateSelectedSubscriptionEvolvedStruct(
      Function(PurchaseDurationStruct) updateFn) {
    updateFn(selectedSubscriptionEvolved ??= PurchaseDurationStruct());
  }

  ///  State fields for stateful widgets in this page.

  // Models for SubscriptionSelectorTile dynamic component.
  late FlutterFlowDynamicModels<SubscriptionSelectorTileModel>
      subscriptionSelectorTileModels1;
  // Models for SubscriptionSelectorTileNS dynamic component.
  late FlutterFlowDynamicModels<SubscriptionSelectorTileNSModel>
      subscriptionSelectorTileNSModels1;
  // Model for GradientButtonSilver component.
  late GradientButtonSilverModel gradientButtonSilverModel;
  // Stores action output result for [Cloud Function - createPurchaseIntentionAlert] action in GradientButtonSilver widget.
  CreatePurchaseIntentionAlertCloudFunctionCallResponse? plusSubscriptionLogCF;
  // Stores action output result for [RevenueCat - Purchase] action in GradientButtonSilver widget.
  bool? plusPurchase;
  // Models for SubscriptionSelectorTile dynamic component.
  late FlutterFlowDynamicModels<SubscriptionSelectorTileModel>
      subscriptionSelectorTileModels2;
  // Models for SubscriptionSelectorTileNS dynamic component.
  late FlutterFlowDynamicModels<SubscriptionSelectorTileNSModel>
      subscriptionSelectorTileNSModels2;
  // Model for StandardIncludedBadge component.
  late StandardIncludedBadgeModel standardIncludedBadgeModel;
  // Model for GradientButtonEvolve component.
  late GradientButtonEvolveModel gradientButtonEvolveModel;
  // Stores action output result for [Cloud Function - createPurchaseIntentionAlert] action in GradientButtonEvolve widget.
  CreatePurchaseIntentionAlertCloudFunctionCallResponse?
      evolvedSubscriptionLogCF;
  // Stores action output result for [RevenueCat - Purchase] action in GradientButtonEvolve widget.
  bool? evolvedPurchase2;

  @override
  void initState(BuildContext context) {
    subscriptionSelectorTileModels1 =
        FlutterFlowDynamicModels(() => SubscriptionSelectorTileModel());
    subscriptionSelectorTileNSModels1 =
        FlutterFlowDynamicModels(() => SubscriptionSelectorTileNSModel());
    gradientButtonSilverModel =
        createModel(context, () => GradientButtonSilverModel());
    subscriptionSelectorTileModels2 =
        FlutterFlowDynamicModels(() => SubscriptionSelectorTileModel());
    subscriptionSelectorTileNSModels2 =
        FlutterFlowDynamicModels(() => SubscriptionSelectorTileNSModel());
    standardIncludedBadgeModel =
        createModel(context, () => StandardIncludedBadgeModel());
    gradientButtonEvolveModel =
        createModel(context, () => GradientButtonEvolveModel());
  }

  @override
  void dispose() {
    subscriptionSelectorTileModels1.dispose();
    subscriptionSelectorTileNSModels1.dispose();
    gradientButtonSilverModel.dispose();
    subscriptionSelectorTileModels2.dispose();
    subscriptionSelectorTileNSModels2.dispose();
    standardIncludedBadgeModel.dispose();
    gradientButtonEvolveModel.dispose();
  }
}
