import '/backend/backend.dart';
import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/backend/schema/structs/index.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button_silver/gradient_button_silver_widget.dart';
import '/subscription_sale_screens/subscription_selector_tile/subscription_selector_tile_widget.dart';
import '/subscription_sale_screens/subscription_selector_tile_n_s/subscription_selector_tile_n_s_widget.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import 'plus_subscription_new_widget.dart' show PlusSubscriptionNewWidget;
import 'package:flutter/material.dart';

class PlusSubscriptionNewModel
    extends FlutterFlowModel<PlusSubscriptionNewWidget> {
  ///  Local state fields for this page.

  PurchaseDurationStruct? selectedSubscription = functions
      .getPurchaseDurationStructFromString(
          getRemoteConfigString('purchases_plus_offer_string'))
      .toList()[0];
  void updateSelectedSubscriptionStruct(
      Function(PurchaseDurationStruct) updateFn) {
    updateFn(selectedSubscription ??= PurchaseDurationStruct());
  }

  ///  State fields for stateful widgets in this page.

  // Models for SubscriptionSelectorTile dynamic component.
  late FlutterFlowDynamicModels<SubscriptionSelectorTileModel>
      subscriptionSelectorTileModels;
  // Models for SubscriptionSelectorTileNS dynamic component.
  late FlutterFlowDynamicModels<SubscriptionSelectorTileNSModel>
      subscriptionSelectorTileNSModels;
  // Model for GradientButtonSilver component.
  late GradientButtonSilverModel gradientButtonSilverModel;
  // Stores action output result for [Cloud Function - createPurchaseIntentionAlert] action in GradientButtonSilver widget.
  CreatePurchaseIntentionAlertCloudFunctionCallResponse? plusSubscriptionLogCF;
  // Stores action output result for [RevenueCat - Purchase] action in GradientButtonSilver widget.
  bool? plusPurchase;

  @override
  void initState(BuildContext context) {
    subscriptionSelectorTileModels =
        FlutterFlowDynamicModels(() => SubscriptionSelectorTileModel());
    subscriptionSelectorTileNSModels =
        FlutterFlowDynamicModels(() => SubscriptionSelectorTileNSModel());
    gradientButtonSilverModel =
        createModel(context, () => GradientButtonSilverModel());
  }

  @override
  void dispose() {
    subscriptionSelectorTileModels.dispose();
    subscriptionSelectorTileNSModels.dispose();
    gradientButtonSilverModel.dispose();
  }
}
