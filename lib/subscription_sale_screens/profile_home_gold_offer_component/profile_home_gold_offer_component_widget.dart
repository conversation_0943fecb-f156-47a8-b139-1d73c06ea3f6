import 'package:chyrpe/amplitudeConfig.dart';
import '/auth/firebase_auth/auth_util.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button_gold_flex/gradient_button_gold_flex_widget.dart';
import '/custom_code/widgets/index.dart' as custom_widgets;
import '/flutter_flow/revenue_cat_util.dart' as revenue_cat;
import 'package:flutter/material.dart';
import 'profile_home_gold_offer_component_model.dart';
export 'profile_home_gold_offer_component_model.dart';

class ProfileHomeGoldOfferComponentWidget extends StatefulWidget {
  const ProfileHomeGoldOfferComponentWidget({super.key});

  @override
  State<ProfileHomeGoldOfferComponentWidget> createState() =>
      _ProfileHomeGoldOfferComponentWidgetState();
}

class _ProfileHomeGoldOfferComponentWidgetState
    extends State<ProfileHomeGoldOfferComponentWidget> {
  late ProfileHomeGoldOfferComponentModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => ProfileHomeGoldOfferComponentModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: !(revenue_cat.activeEntitlementIds.contains('gold_access') ||
          revenue_cat.activeEntitlementIds.contains('evolved_access')),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [const Color(0xFFFEDA73), FlutterFlowTheme.of(context).info],
            stops: const [0.0, 0.4],
            begin: const AlignmentDirectional(0.0, -1.0),
            end: const AlignmentDirectional(0, 1.0),
          ),
          borderRadius: BorderRadius.circular(9.0),
          border: Border.all(
            color: const Color(0xFFBE9F04),
            width: 1.0,
          ),
        ),
        child: Padding(
          padding: const EdgeInsetsDirectional.fromSTEB(23.0, 11.0, 23.0, 20.0),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Align(
                alignment: const AlignmentDirectional(0.0, 0.0),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'chyrpe',
                      style: FlutterFlowTheme.of(context).bodyMedium.override(
                            fontFamily: 'BT Beau Sans',
                            fontSize: 28.0,
                            letterSpacing: 0.0,
                            fontWeight: FontWeight.w600,
                            useGoogleFonts: false,
                          ),
                    ),
                    Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(5.0, 0.0, 0.0, 0.0),
                      child: Container(
                        width: 55.0,
                        height: 19.0,
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [Color(0xFFFFCC10), Color(0xFFBE9F04)],
                            stops: [0.0, 1.0],
                            begin: AlignmentDirectional(1.0, 0.0),
                            end: AlignmentDirectional(-1.0, 0),
                          ),
                          borderRadius: BorderRadius.circular(100.0),
                        ),
                        child: Align(
                          alignment: const AlignmentDirectional(0.0, 0.0),
                          child: Text(
                            'GOLD',
                            textAlign: TextAlign.center,
                            style: FlutterFlowTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'BT Beau Sans',
                                  color: FlutterFlowTheme.of(context).info,
                                  fontSize: 10.0,
                                  letterSpacing: 0.0,
                                  fontWeight: FontWeight.normal,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Align(
                alignment: const AlignmentDirectional(0.0, 0.0),
                child: Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(0.0, 8.0, 0.0, 8.0),
                  child: Text(
                    getRemoteConfigString('gold_offer50_profile_home_headline'),
                    textAlign: TextAlign.center,
                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                          fontFamily: 'BT Beau Sans',
                          fontSize: 16.0,
                          letterSpacing: 0.0,
                          fontWeight: FontWeight.bold,
                          useGoogleFonts: false,
                        ),
                  ),
                ),
              ),
              Align(
                alignment: const AlignmentDirectional(0.0, 0.0),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Align(
                      alignment: const AlignmentDirectional(0.0, 0.0),
                      child: Padding(
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 2.0),
                        child: Text(
                          'Offer ends in ',
                          style:
                              FlutterFlowTheme.of(context).bodyMedium.override(
                                    fontFamily: 'BT Beau Sans',
                                    fontSize: 16.0,
                                    letterSpacing: 0.0,
                                    fontWeight: FontWeight.normal,
                                    useGoogleFonts: false,
                                  ),
                        ),
                      ),
                    ),
                    AuthUserStreamWidget(
                      builder: (context) => Container(
                        child: custom_widgets.TimerWidgetDiscountScreen(
                          initialTime: valueOrDefault(
                                  currentUserDocument
                                      ?.subscriptionOfferDeadline,
                                  0) -
                              DateTime.now().millisecondsSinceEpoch,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                width: 220.0,
                decoration: const BoxDecoration(
                  color: Colors.transparent,
                ),
                child: wrapWithModel(
                  model: _model.gradientButtonGoldFlexModel,
                  updateCallback: () => safeSetState(() {}),
                  updateOnChange: true,
                  child: GradientButtonGoldFlexWidget(
                    title: getRemoteConfigString('purchases_gold_btn'),
                    height: 45.0,
                    action: () async {
                      try {
                      analytics.logEvent('Navigated to Gold Offer from Profile Home');
                      } catch(e) {}
                      context.pushNamed('GoldSubscriptionOffer');
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
