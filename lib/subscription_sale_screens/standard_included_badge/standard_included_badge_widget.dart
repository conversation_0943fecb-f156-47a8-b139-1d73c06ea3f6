import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';
import 'standard_included_badge_model.dart';
export 'standard_included_badge_model.dart';
import 'package:gradient_borders/gradient_borders.dart';


class StandardIncludedBadgeWidget extends StatefulWidget {
  const StandardIncludedBadgeWidget({super.key});

  @override
  State<StandardIncludedBadgeWidget> createState() =>
      _StandardIncludedBadgeWidgetState();
}

class _StandardIncludedBadgeWidgetState
    extends State<StandardIncludedBadgeWidget> {
  late StandardIncludedBadgeModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => StandardIncludedBadgeModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.sizeOf(context).width * 1.0,
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).primaryBackground,
        borderRadius: BorderRadius.circular(8.0),
        border: const GradientBoxBorder(
      gradient: LinearGradient(colors: [ Color(0xFFFF9EDE), Color(0xFF91B2FB)]),
      width: 1,
    ),
        ),
      child: Padding(
        padding: const EdgeInsetsDirectional.fromSTEB(0.0, 17.0, 0.0, 18.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(35.0, 0.0, 35.0, 0.0),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ShaderMask(
  shaderCallback: (Rect bounds) => const LinearGradient(
     begin: Alignment.centerLeft,
     end: Alignment.centerRight,
     colors: [
        Color(0xFFFF9EDE), Color(0xFF91B2FB)
     ],
     tileMode: TileMode.clamp,
  ).createShader(bounds),
  child: const Icon(
                    Icons.check_sharp,
                    color: Colors.white,
                    size: 26.0,
  ),                 ),
                  Flexible(
                    child: Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(26.0, 0.0, 0.0, 0.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                0.0, 3.0, 0.0, 0.0),
                            child: GradientText(
                              getRemoteConfigString(
                                  'premium_standard_badge_title'),
                              style: FlutterFlowTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'BT Beau Sans',
                                    fontSize: 16.0,
                                    letterSpacing: 0.0,
                                    fontWeight: FontWeight.bold,
                                    useGoogleFonts: false,
                                  ),
                              colors: const [Color(0xFFFF9EDE), Color(0xFF91B2FB)],
                              gradientDirection: GradientDirection.ltr,
                              gradientType: GradientType.linear,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
