import '/backend/custom_cloud_functions/custom_cloud_function_response_manager.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/general/gradient_button/gradient_button_widget.dart';
import 'standard_lifetime_upgrade_widget.dart' show StandardLifetimeUpgradeWidget;
import 'package:flutter/material.dart';

class StandardLifetimeUpgradeModel extends FlutterFlowModel<StandardLifetimeUpgradeWidget> {
  ///  State fields for stateful widgets in this page.

  final unfocusNode = FocusNode();
  // State field(s) for PageView widget.
  PageController? pageViewController;

  int get pageViewCurrentIndex => pageViewController != null &&
          pageViewController!.hasClients &&
          pageViewController!.page != null
      ? pageViewController!.page!.round()
      : 0;
  // Model for GradientButton component.
  late GradientButtonModel gradientButtonModel1;
  // Stores action output result for [Cloud Function - createPurchaseIntentionAlert] action in GradientButton widget.
  CreatePurchaseIntentionAlertCloudFunctionCallResponse? plusSubscriptionLogCF;
  // Stores action output result for [RevenueCat - Purchase] action in GradientButton widget.
  bool? lifetimePurchase;
  // Model for GradientButton component.
  late GradientButtonModel gradientButtonModel2;

  @override
  void initState(BuildContext context) {
    gradientButtonModel1 = createModel(context, () => GradientButtonModel());
    gradientButtonModel2 = createModel(context, () => GradientButtonModel());
  }

  @override
  void dispose() {
    unfocusNode.dispose();
    gradientButtonModel1.dispose();
    gradientButtonModel2.dispose();
  }
}