import '/backend/schema/structs/index.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'subscription_selector_tile_model.dart';
export 'subscription_selector_tile_model.dart';
import '/flutter_flow/revenue_cat_util.dart' as revenue_cat;

class SubscriptionSelectorTileWidget extends StatefulWidget {
  const SubscriptionSelectorTileWidget({
    super.key,
    required this.selectorObject,
    required this.colour1,
    required this.colour2,
    required this.callback,
  });

  final PurchaseDurationStruct? selectorObject;
  final Color? colour1;
  final Color? colour2;
  final Future Function()? callback;

  @override
  State<SubscriptionSelectorTileWidget> createState() =>
      _SubscriptionSelectorTileWidgetState();
}

class _SubscriptionSelectorTileWidgetState
    extends State<SubscriptionSelectorTileWidget> {
  late SubscriptionSelectorTileModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => SubscriptionSelectorTileModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      splashColor: Colors.transparent,
      focusColor: Colors.transparent,
      hoverColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: () async {
        await widget.callback?.call();
      },
      child: Container(
        width: 145.0,
        height: 121.0,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              valueOrDefault<Color>(
                widget.colour1,
                const Color(0xFFF3E17F),
              ),
              valueOrDefault<Color>(
                widget.colour2,
                const Color(0xFFFFB800),
              )
            ],
            stops: const [0.0, 1.0],
            begin: const AlignmentDirectional(-1.0, 0.0),
            end: const AlignmentDirectional(1.0, 0),
          ),
          borderRadius: BorderRadius.circular(10.0),
        ),
        child: Padding(
          padding: const EdgeInsetsDirectional.fromSTEB(3.0, 3.0, 3.0, 3.0),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 3.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Align(
                        alignment: const AlignmentDirectional(0.0, 0.0),
                        child: Text(
                          widget.selectorObject!.durationWeeks == 1 ? "Popular" : 'Save ${((1-((revenue_cat
                                                        .offerings!.current!
                                                        .getPackage(
                                                            widget.selectorObject!.packageId)!
                                                        .storeProduct.price/widget.selectorObject!.durationWeeks)/(revenue_cat
                                                        .offerings!.current!
                                                        .getPackage(
                                                            widget.selectorObject!.weeklyAlternativeId)!
                                                        .storeProduct.price/1)))*100).round()}%',
                          style:
                              FlutterFlowTheme.of(context).bodyMedium.override(
                                    fontFamily: 'BT Beau Sans',
                                    color: Colors.white,
                                    fontSize: 12.0,
                                    letterSpacing: 0.0,
                                    fontWeight: FontWeight.w500,
                                    useGoogleFonts: false,
                                  ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Container(
                width: 142.0,
                height: 85.0,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(9.0),
                    bottomRight: Radius.circular(9.0),
                    topLeft: Radius.circular(0.0),
                    topRight: Radius.circular(0.0),
                  ),
                ),
                alignment: const AlignmentDirectional(0.0, 0.0),
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Align(
                      alignment: const AlignmentDirectional(0.0, 0.0),
                      child: Text(
                        valueOrDefault<String>(
                          widget.selectorObject?.duration,
                          '1 week',
                        ),
                        textAlign: TextAlign.center,
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'BT Beau Sans',
                              fontSize: 13.0,
                              letterSpacing: 0.0,
                              fontWeight: FontWeight.w500,
                              useGoogleFonts: false,
                            ),
                      ),
                    ),
                    Align(
                      alignment: const AlignmentDirectional(0.0, 0.0),
                      child: Text(
                        '${revenue_cat
                                                        .offerings!.current!
                                                        .getPackage(
                                                            widget.selectorObject!.packageId)!
                                                        .storeProduct
                                                        .currencyCode}${(revenue_cat
                                                        .offerings!.current!
                                                        .getPackage(
                                                            widget.selectorObject!.packageId)!
                                                        .storeProduct.price/widget.selectorObject!.durationWeeks).toStringAsFixed(2)}/wk',
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'BT Beau Sans',
                              fontSize: 16.0,
                              letterSpacing: 0.0,
                              fontWeight: FontWeight.bold,
                              useGoogleFonts: false,
                            ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
