import '/auth/firebase_auth/auth_util.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import 'subscription_selector_tile_new_widget.dart'
    show SubscriptionSelectorTileNewWidget;
import 'package:flutter/material.dart';

class SubscriptionSelectorTileNewModel
    extends FlutterFlowModel<SubscriptionSelectorTileNewWidget> {
  @override
  void initState(BuildContext context) {}

  bool dayReplace = valueOrDefault(
    functions.getIntListFromJson(getRemoteConfigString('divine_show_days')).contains(valueOrDefault(
                                      currentUserDocument?.fiveTestGroup, 0)), false
  );

  String replaceWeekString(inputString) {
    try{
    var replacementDict =  jsonDecode(getRemoteConfigString('divine_day_replace'));
    return replacementDict[inputString];
    } catch(e) {
      return inputString;
    }
  }

  @override
  void dispose() {}
}
