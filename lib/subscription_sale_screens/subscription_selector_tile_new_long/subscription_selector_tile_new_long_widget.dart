import '/backend/schema/structs/index.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'subscription_selector_tile_new_long_model.dart';
export 'subscription_selector_tile_new_long_model.dart';
import '/flutter_flow/revenue_cat_util.dart' as revenue_cat;

class SubscriptionSelectorTileNewLongWidget extends StatefulWidget {
  const SubscriptionSelectorTileNewLongWidget({
    super.key,
    required this.selectorObject,
    required this.callback,
    bool? selected,
    Color? selectedColor,
  })  : selected = selected ?? false,
        selectedColor = selectedColor ?? const Color(0xFFBD88E7);

  final PurchaseDurationStruct? selectorObject;
  final Future Function()? callback;
  final bool selected;
  final Color selectedColor;

  @override
  State<SubscriptionSelectorTileNewLongWidget> createState() =>
      _SubscriptionSelectorTileNewLongWidgetState();
}

class _SubscriptionSelectorTileNewLongWidgetState
    extends State<SubscriptionSelectorTileNewLongWidget> {
  late SubscriptionSelectorTileNewLongModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => SubscriptionSelectorTileNewLongModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      splashColor: Colors.transparent,
      focusColor: Colors.transparent,
      hoverColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: () async {
        await widget.callback?.call();
      },
      child: Container(
        width: MediaQuery.sizeOf(context).width * 1.0,
        height: 67.0,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(11.0),
          border: Border.all(
            color: valueOrDefault<Color>(
              widget.selected ? widget.selectedColor : const Color(0xFFD8DBE0),
              const Color(0xFFBD88E7),
            ),
            width: valueOrDefault<double>(
              widget.selected ? 2.0 : 1.0,
              2.0,
            ),
          ),
        ),
        child: Padding(
          padding: const EdgeInsetsDirectional.fromSTEB(20.0, 0.0, 20.0, 0.0),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Align(
                alignment: const AlignmentDirectional(0.0, 0.0),
                child: Text(
                  valueOrDefault<String>(
                        _model.dayReplace ?
                          _model.replaceWeekString(widget.selectorObject?.duration) : widget.selectorObject?.duration,
                        '1 week',
                      ),
                  textAlign: TextAlign.center,
                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                        fontFamily: 'BT Beau Sans',
                        color: FlutterFlowTheme.of(context).secondaryText,
                        fontSize: 15.0,
                        letterSpacing: 0.0,
                        fontWeight: FontWeight.w500,
                        useGoogleFonts: false,
                      ),
                ),
              ),
              Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  if (widget.selectorObject?.durationWeeks != 1)
                    Padding(
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 8.0, 0.0),
                      child: Container(
                        width: 65.0,
                        height: 25.0,
                        decoration: BoxDecoration(
                          color: const Color(0xFFBD88E7),
                          borderRadius: BorderRadius.circular(11.0),
                        ),
                        child: Align(
                          alignment: const AlignmentDirectional(0.0, 0.0),
                          child: Text(
                            'Save ${((1-((revenue_cat
                                                        .offerings!.current!
                                                        .getPackage(
                                                            widget.selectorObject!.packageId)!
                                                        .storeProduct.price/widget.selectorObject!.durationWeeks)/(revenue_cat
                                                        .offerings!.current!
                                                        .getPackage(
                                                            widget.selectorObject!.weeklyAlternativeId)!
                                                        .storeProduct.price/1)))*100).round()}%',
                            textAlign: TextAlign.center,
                            style: FlutterFlowTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'BT Beau Sans',
                                  color: FlutterFlowTheme.of(context)
                                      .primaryBackground,
                                  fontSize: 10.0,
                                  letterSpacing: 0.0,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                      ),
                    ),
                  Align(
                    alignment: const AlignmentDirectional(0.0, 0.0),
                    child: Text(
                      revenue_cat
                                                        .offerings!.current!
                                                        .getPackage(
                                                            widget.selectorObject!.packageId)!
                                                        .storeProduct.priceString,
                      textAlign: TextAlign.center,
                      style: FlutterFlowTheme.of(context).bodyMedium.override(
                            fontFamily: 'BT Beau Sans',
                            color:  const Color(0xFFBD88E7),
                            fontSize: 17.0,
                            letterSpacing: 0.0,
                            fontWeight: FontWeight.w500,
                            useGoogleFonts: false,
                          ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
