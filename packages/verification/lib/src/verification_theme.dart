import 'package:flutter/material.dart';

///  base theme for Verification package
///
///
class VerificationThemeExtension
    extends ThemeExtension<VerificationThemeExtension> {
  const VerificationThemeExtension({
    required this.primaryColor,
    required this.tertiaryColor,
    required this.titleTextStyle,
    required this.subTitleStyle,
    required this.tileTitleStyle,
    required this.tileDescriptionTextStyle,
    required this.tileHintTextStyle,
  });

  final Color primaryColor;
  final Color tertiaryColor;

  ///
  final TextStyle titleTextStyle;

  ///
  final TextStyle subTitleStyle;

  /// verification item  textStyle
  final TextStyle tileTitleStyle;

  ///
  final TextStyle tileDescriptionTextStyle;

  /// verification item  textStyle
  ///  have underscore, e.g onTap
  final TextStyle tileHintTextStyle;

  static VerificationThemeExtension day() {
    return const VerificationThemeExtension(
      primaryColor: Color(0xFFC79CE4),
      tertiaryColor: Color(0xFF747E90),
      titleTextStyle: TextStyle(
        fontFamily: 'BT Beau Sans',
        fontWeight: FontWeight.bold,
        fontSize: 21,
        height: 1.25,
        color: Color(0xFF262A36),
      ),
      subTitleStyle: TextStyle(
        fontFamily: 'BT Beau Sans',
        fontWeight: FontWeight.w500,
        color: Colors.black,
        fontSize: 12,
        height: 1.25,
      ),
      tileTitleStyle: TextStyle(
        fontFamily: 'BT Beau Sans',
        fontWeight: FontWeight.w500,
        fontSize: 12,
        height: 1.25,
        color: Colors.black,
      ),
      tileDescriptionTextStyle: TextStyle(
        fontFamily: 'BT Beau Sans',
        fontWeight: FontWeight.w400,
        fontSize: 11,
        height: 1.54,
        color: Color(0xFF57636C),
      ),
      tileHintTextStyle: TextStyle(
        fontFamily: 'BT Beau Sans',
        fontWeight: FontWeight.w400,
        fontSize: 11,
        height: 1.54,
        color: Color(0xFF57636C),
        decoration: TextDecoration.underline,
      ),
    );
  }

  @override
  VerificationThemeExtension copyWith({
    Color? primaryColor,
    Color? tertiaryColor,
    TextStyle? titleTextStyle,
    TextStyle? subTitleStyle,
    TextStyle? tileTitleStyle,
    TextStyle? tileDescriptionTextStyle,
    TextStyle? tileHintTextStyle,
    ButtonStyle? buttonStyle,
  }) {
    return VerificationThemeExtension(
      primaryColor: primaryColor ?? this.primaryColor,
      tertiaryColor: tertiaryColor ?? this.tertiaryColor,
      titleTextStyle: titleTextStyle ?? this.titleTextStyle,
      subTitleStyle: subTitleStyle ?? this.subTitleStyle,
      tileTitleStyle: tileTitleStyle ?? this.tileTitleStyle,
      tileDescriptionTextStyle:
          tileDescriptionTextStyle ?? this.tileDescriptionTextStyle,
      tileHintTextStyle: tileHintTextStyle ?? this.tileHintTextStyle,
    );
  }

  @override
  VerificationThemeExtension lerp(
    ThemeExtension<VerificationThemeExtension>? other,
    double t,
  ) {
    if (other is! VerificationThemeExtension) return this;

    return VerificationThemeExtension(
      primaryColor: Color.lerp(primaryColor, other.primaryColor, t)!,
      tertiaryColor: Color.lerp(tertiaryColor, other.tertiaryColor, t)!,
      titleTextStyle: TextStyle.lerp(titleTextStyle, other.titleTextStyle, t)!,
      subTitleStyle: TextStyle.lerp(subTitleStyle, other.subTitleStyle, t)!,
      tileTitleStyle: TextStyle.lerp(tileTitleStyle, other.tileTitleStyle, t)!,
      tileDescriptionTextStyle: TextStyle.lerp(
          tileDescriptionTextStyle, other.tileDescriptionTextStyle, t)!,
      tileHintTextStyle:
          TextStyle.lerp(tileHintTextStyle, other.tileHintTextStyle, t)!,
    );
  }
}
