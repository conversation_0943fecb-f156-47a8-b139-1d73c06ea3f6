name: chyrpe
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html


version: 1.1.208+308

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  aligned_tooltip: 0.0.1
  dio: ^5.0.0
  gradient_borders: ^1.0.1
  image_cropper: ^8.0.2
  auto_size_text: 3.0.0
  badges: 2.0.2
  cached_network_image: 3.3.1
  camera: 0.10.5+9
  camera_android_camerax: 0.5.0+26
  camera_avfoundation: 0.9.13+10
  camera_platform_interface: 2.7.2
  carousel_slider: 4.2.1
  cloud_firestore: 4.17.3
  cloud_firestore_platform_interface: 6.2.3
  cloud_firestore_web: 3.12.3
  cloud_functions: 4.7.4
  cloud_functions_platform_interface: 5.5.26
  cloud_functions_web: 4.9.4
  collection: 1.18.0
  connectivity_plus: ^6.1.0
  contacts_service: ^0.6.3
  csv: 6.0.0
  easy_debounce: 2.0.1
  equatable: 2.0.5
  expandable: 5.0.1
  file_picker: 8.0.3
  firebase_app_check: 0.2.2+5
  firebase_analytics: 10.10.5
  firebase_app_check_platform_interface: 0.1.0+27
  firebase_app_check_web: 0.1.2+5
  firebase_auth: 4.19.5
  firebase_auth_platform_interface: 7.2.6
  firebase_auth_web: 5.11.5
  firebase_core: 2.31.0
  firebase_core_platform_interface: 5.0.0
  firebase_core_web: 2.17.0
  firebase_messaging: 14.9.2
  firebase_messaging_platform_interface: 4.5.35
  firebase_messaging_web: 3.8.5
  firebase_performance: 0.9.4+5
  firebase_performance_platform_interface: 0.1.4+33
  firebase_performance_web: 0.1.6+5
  firebase_remote_config: 4.4.5
  firebase_remote_config_platform_interface: 1.4.33
  firebase_remote_config_web: 1.6.5
  firebase_storage: 11.7.5
  firebase_storage_platform_interface: 5.1.20
  firebase_storage_web: 3.9.5
  flutter_animate: 4.5.0
  flutter_cache_manager: 3.3.1
  flutter_card_swiper: 6.0.0
  flutter_linkify: ^6.0.0
  flutter_markdown: ^0.7.3
  flutter_native_splash: 2.3.1
  flutter_plugin_android_lifecycle: 2.0.20
  flutter_rating_bar: 4.0.1
  flutter_secure_storage: 9.2.2
  flutter_secure_storage_linux: 1.2.1
  flutter_secure_storage_macos: 3.1.2
  flutter_secure_storage_platform_interface: 1.1.2
  flutter_secure_storage_web: 1.2.1
  flutter_secure_storage_windows: 3.1.2
  flutter_spinkit: 5.2.0
  flutter_svg: 2.0.9
  font_awesome_flutter: 10.7.0
  from_css_color: 2.0.0
  geocoding: ^2.1.1
  geocoding_android: ^2.1.2
  geocoding_ios: ^2.1.1
  geocoding_platform_interface: ^2.0.0
  geolocator: 12.0.0
  geolocator_android: 4.6.1
  geolocator_apple: 2.3.7
  geolocator_platform_interface: 4.2.3
  geolocator_web: 4.0.0
  go_router: 12.1.3
  google_fonts: 6.1.0
  google_maps: 7.1.0
  google_maps_flutter: 2.6.1
  google_maps_flutter_android: 2.8.1
  google_maps_flutter_ios: 2.6.1
  google_maps_flutter_platform_interface: 2.7.1
  google_maps_flutter_web: 0.5.7
  google_sign_in: 6.1.5
  google_sign_in_android: 6.1.20
  google_sign_in_ios: 5.6.4
  google_sign_in_platform_interface: 2.4.2
  google_sign_in_web: 0.12.1
  http: 1.2.1
  image: ^4.0.15
  image_picker: 1.0.4
  image_picker_android: 0.8.8
  image_picker_for_web: 3.0.1
  image_picker_ios: 0.8.8+2
  image_picker_platform_interface: 2.9.1
  in_app_review_platform_interface: ^2.0.5
  infinite_scroll_pagination: 4.0.0
  intl: 0.19.0
  json_path: 0.7.2
  likes:
    path: packages/likes
  hobbies:
    path: packages/hobbies
  verification:
    path: packages/verification
  lottie: 2.7.0
  mask_text_input_formatter: 2.9.0
  mime_type: 1.0.0
  ntp: ^2.0.0
  page_transition: 2.1.0
  path_provider: 2.1.3
  path_provider_android: 2.2.5
  path_provider_foundation: 2.4.0
  path_provider_platform_interface: 2.1.2
  permission_handler: 11.3.0
  pin_code_fields: 8.0.1
  plugin_platform_interface: 2.1.8
  provider: 6.1.2
  purchases_flutter: ^8.7.4
  rive: ^0.13.14
  rive_common: ^0.4.11
  rxdart: 0.27.7
  share_plus: 9.0.0
  shared_preferences: 2.2.2
  shared_preferences_android: 2.2.1
  shared_preferences_foundation: 2.3.4
  shared_preferences_platform_interface: 2.3.1
  shared_preferences_web: 2.2.1
  sign_in_with_apple: 4.3.0
  sign_in_with_apple_platform_interface: 1.0.0
  sign_in_with_apple_web: 1.0.1
  simple_gradient_text: 1.2.3
  stop_watch_timer: 3.0.2
  smooth_page_indicator: 1.1.0
  sqflite: 2.2.6
  stream_transform: 2.1.0
  styled_divider: 1.0.4
  synchronized: 3.1.0
  text_search: 1.0.1
  timeago: 3.6.1
  universal_io: 2.2.2
  upgrader: ^10.1.0
  url_launcher: 6.3.1
  url_launcher_android: 6.3.4
  url_launcher_ios: 6.3.1
  url_launcher_platform_interface: 2.3.2
  video_player: 2.8.6
  video_player_android: 2.4.16
  video_player_avfoundation: 2.6.1
  video_player_platform_interface: 6.2.2
  video_player_web: 2.3.1
  xml: 6.5.0
  swipable_stack: ^2.0.0


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.0
  in_app_review: ^2.0.9
  visibility_detector: ^0.4.0+2
  appsflyer_sdk: ^6.14.3
  app_tracking_transparency: ^2.0.5
  amplitude_flutter: ^3.16.2
  flutter_branch_sdk: ^7.1.0
  app_settings: ^5.1.1
  flutter_image_compress: ^2.3.0
  flutter_image_compress_common: ^1.0.5
  flutter_image_compress_platform_interface: ^1.0.5
  wheel_picker: ^0.2.1
  reorderable_grid: ^1.0.10
  blitzllama_flutter: 0.5.3

dependency_overrides:
  http: 1.2.1
  uuid: ^4.0.0
  win32: 5.5.1

dev_dependencies:
  flutter_launcher_icons: 0.13.1
  flutter_lints: 4.0.0
  image: 4.0.17
  lints: 4.0.0

  flutter_test:
    sdk: flutter


flutter_launcher_icons:
  android: true
  ios: true
  remove_alpha_ios: true
  web:
    generate: true
  image_path: 'assets/images/app_launcher_icon.png'


# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/fonts/
    - assets/images/
    - assets/videos/
    - assets/audios/
    - assets/rive_animations/
    - assets/rive_animations/fs_tutorial/
    - assets/pdfs/
    - assets/jsons/

  fonts:
    - family: 'BT Beau Sans'
      fonts:
        - asset: assets/fonts/BT-BeauSans-Light.ttf
          weight: 300
        - asset: assets/fonts/BT-BeauSans-LightItalic.ttf
          weight: 300
          style: italic
        - asset: assets/fonts/BT-BeauSans-Regular.ttf
        - asset: assets/fonts/BT-BeauSans-Italic.ttf
          style: italic
        - asset: assets/fonts/BT-BeauSans-Medium.ttf
          weight: 500
        - asset: assets/fonts/BT-BeauSans-MediumItalic.ttf
          weight: 500
          style: italic
        - asset: assets/fonts/BT-BeauSans-Bold.ttf
          weight: 700
        - asset: assets/fonts/BT-BeauSans-BoldItalic.ttf
          weight: 700
          style: italic
        - asset: assets/fonts/BT-BeauSans-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/BT-BeauSans-ExtraBoldItalic.ttf
          weight: 800
          style: italic
    - family: 'Lora'
      fonts:
        - asset: assets/fonts/Lora-Regular.ttf
        - asset: assets/fonts/Lora-Italic.ttf
          style: italic
        - asset: assets/fonts/Lora-Medium.ttf
          weight: 500
        - asset: assets/fonts/Lora-MediumItalic.ttf
          weight: 500
          style: italic
        - asset: assets/fonts/Lora-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Lora-SemiBoldItalic.ttf
          weight: 600
          style: italic
        - asset: assets/fonts/Lora-Bold.ttf
          weight: 700
        - asset: assets/fonts/Lora-BoldItalic.ttf
          weight: 700
          style: italic

    - family: ChyrpeIconsV2
      fonts:
        - asset: assets/fonts/chyrpe-icons-v2.ttf
    - family: DiamondSymbol
      fonts:
        - asset: assets/fonts/diamond_symbol.ttf
    - family: RoseIcon
      fonts:
        - asset: assets/fonts/RoseIcon.ttf
    - family: NewLikeIcons
      fonts:
        - asset: assets/fonts/NewLikeIcons.ttf
    - family: ArrowIcon
      fonts:
        - asset: assets/fonts/ArrowIcon.ttf
    - family: ProfileIcons
      fonts:
        - asset: assets/fonts/profileIcons.ttf
    - family: Logosymbol
      fonts:
        - asset: assets/fonts/logosymbol.ttf
    - family: Settings
      fonts:
        - asset: assets/fonts/settings.ttf


  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

